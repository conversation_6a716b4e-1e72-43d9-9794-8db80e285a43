# Scripts Directory

This directory contains utility scripts for the jetstar-hotels-ui project.

## Test Sharding Scripts

### `test-shards.sh`

Main utility script for Jest test sharding. Supports both local development and CI pipeline generation.

**Usage:**
```bash
# Run all browser test shards sequentially
./scripts/test-shards.sh browser

# Run all server test shards sequentially  
./scripts/test-shards.sh server

# Run a specific browser test shard
./scripts/test-shards.sh browser 0 4

# Run a specific server test shard
./scripts/test-shards.sh server 1 2

# Generate Buildkite pipeline steps
./scripts/test-shards.sh generate-pipeline browser
./scripts/test-shards.sh generate-pipeline server
```

**Features:**
- Automatically detects shard configuration (4 for browser, 2 for server)
- Validates shard indices
- Provides clear output with progress indicators
- Can generate CI pipeline YAML

### `run-test-shard.sh`

CI-focused script for running individual test shards. Designed to be called from Buildkite with environment variables.

**Usage:**
```bash
# Run browser test shard (uses environment variables)
SHARD_INDEX=0 TOTAL_SHARDS=4 TEST_TYPE=browser ./scripts/run-test-shard.sh

# Run server test shard
SHARD_INDEX=1 TOTAL_SHARDS=2 TEST_TYPE=server ./scripts/run-test-shard.sh

# Can also pass test type as argument
./scripts/run-test-shard.sh browser  # Uses env vars for shard config
```

**Environment Variables:**
- `SHARD_INDEX`: Zero-based index of current shard (required)
- `TOTAL_SHARDS`: Total number of shards (optional, uses defaults)
- `TEST_TYPE`: Type of tests to run - 'browser' or 'server' (optional)

### `generate-test-pipeline.sh`

Simple script that generates Buildkite pipeline steps for test sharding. Alternative to the generate-pipeline function in test-shards.sh.

**Usage:**
```bash
./scripts/generate-test-pipeline.sh
```

**Output:**
Generates YAML for both browser (4 shards) and server (2 shards) test steps.

## Configuration

### Shard Counts
- **Browser tests**: 4 shards (configurable in scripts)
- **Server tests**: 2 shards (configurable in scripts)

### Concurrency
- Browser tests run with concurrency = 4 (matching shard count)
- Server tests run with concurrency = 2 (matching shard count)

## Integration with CI

The scripts are integrated with the Buildkite pipeline using matrix builds:

```yaml
- label: ':jest: Test Browser Shard {{matrix}}'
  command: SHARD_INDEX={{matrix}} TOTAL_SHARDS=4 TEST_TYPE=browser ./scripts/run-test-shard.sh
  matrix: ["0", "1", "2", "3"]
  concurrency: 4

- label: ':jest: Test Server Shard {{matrix}}'  
  command: SHARD_INDEX={{matrix}} TOTAL_SHARDS=2 TEST_TYPE=server ./scripts/run-test-shard.sh
  matrix: ["0", "1"]
  concurrency: 2
```

## Modifying Shard Counts

To change the number of shards:

1. Update the shard count variables in the scripts:
   - `test-shards.sh`: Update `DEFAULT_TOTAL_SHARDS` values
   - `generate-test-pipeline.sh`: Update `BROWSER_SHARDS` and `SERVER_SHARDS`
   - `run-test-shard.sh`: Update `DEFAULT_TOTAL_SHARDS` values

2. Update the Buildkite pipeline matrix values in `.buildkite/pipeline.yml`

3. Update the documentation in `docs/test-sharding.md`

## Troubleshooting

### Script Permissions
If you get permission denied errors:
```bash
chmod +x scripts/*.sh
```

### Environment Variables Not Set
The scripts will use sensible defaults if environment variables are not set, but CI requires explicit configuration.

### Shard Index Out of Range
Scripts validate that `SHARD_INDEX` is within the valid range (0 to TOTAL_SHARDS-1).
