#!/bin/bash

# Generate Buildkite pipeline steps for Jest test sharding
# This script generates dynamic pipeline steps based on shard configuration

set -e

# Configuration
BROWSER_SHARDS=4
SERVER_SHARDS=2

# Generate browser test shards
echo "# Browser test shards (${BROWSER_SHARDS} shards)"
for ((i=0; i<BROWSER_SHARDS; i++)); do
    cat << EOF
  - label: ':jest: Test Browser Shard $((i + 1))/${BROWSER_SHARDS}'
    <<: *run-base
    command: SHARD_INDEX=${i} TOTAL_SHARDS=${BROWSER_SHARDS} yarn run test:shard
    concurrency_group: \${APP_NAME}-testgroup
    concurrency: ${BROWSER_SHARDS}
    retry:
      automatic:
        limit: 2

EOF
done

echo "# Server test shards (${SERVER_SHARDS} shards)"
for ((i=0; i<SERVER_SHARDS; i++)); do
    cat << EOF
  - label: ':jest: Test Server Shard $((i + 1))/${SERVER_SHARDS}'
    <<: *run-base
    command: SHARD_INDEX=${i} TOTAL_SHARDS=${SERVER_SHARDS} yarn run test:server:shard
    concurrency_group: \${APP_NAME}-server-testgroup
    concurrency: ${SERVER_SHARDS}
    retry:
      automatic:
        limit: 2

EOF
done
