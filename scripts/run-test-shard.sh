#!/bin/bash

# Run a specific test shard in CI
# This script is designed to be called from Buildkite with environment variables
# Usage: ./scripts/run-test-shard.sh [browser|server]

set -e

# Get test type from argument or environment
TEST_TYPE="${1:-${TEST_TYPE:-browser}}"

# Get shard configuration from environment variables
SHARD_INDEX="${SHARD_INDEX:-0}"
TOTAL_SHARDS="${TOTAL_SHARDS}"

# Set defaults based on test type
if [ "$TEST_TYPE" = "browser" ]; then
    DEFAULT_TOTAL_SHARDS=4
    SCRIPT_NAME="test:shard"
elif [ "$TEST_TYPE" = "server" ]; then
    DEFAULT_TOTAL_SHARDS=2
    SCRIPT_NAME="test:server:shard"
else
    echo "Error: TEST_TYPE must be 'browser' or 'server'"
    exit 1
fi

# Use default if TOTAL_SHARDS not set
if [ -z "$TOTAL_SHARDS" ]; then
    TOTAL_SHARDS=$DEFAULT_TOTAL_SHARDS
fi

# Validate shard index
if ! [[ "$SHARD_INDEX" =~ ^[0-9]+$ ]] || [ "$SHARD_INDEX" -ge "$TOTAL_SHARDS" ]; then
    echo "Error: SHARD_INDEX must be a number between 0 and $((TOTAL_SHARDS - 1))"
    echo "Current values: SHARD_INDEX=$SHARD_INDEX, TOTAL_SHARDS=$TOTAL_SHARDS"
    exit 1
fi

echo "========================================="
echo "Running $TEST_TYPE tests"
echo "Shard: $((SHARD_INDEX + 1))/$TOTAL_SHARDS"
echo "========================================="

# Run the test shard
SHARD_INDEX=$SHARD_INDEX TOTAL_SHARDS=$TOTAL_SHARDS yarn run $SCRIPT_NAME
