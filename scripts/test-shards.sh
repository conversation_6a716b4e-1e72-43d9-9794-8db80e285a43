#!/bin/bash

# Test sharding utility script for CI and local development
# Usage:
#   ./scripts/test-shards.sh [browser|server] [shard_index] [total_shards]
#   ./scripts/test-shards.sh generate-pipeline [browser|server]

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Default values
TEST_TYPE="browser"
SHARD_INDEX=""
TOTAL_SHARDS=""
COMMAND=""

# Parse arguments
if [ $# -ge 1 ]; then
    if [ "$1" = "generate-pipeline" ]; then
        COMMAND="generate-pipeline"
        if [ $# -ge 2 ]; then
            TEST_TYPE="$2"
        fi
    else
        TEST_TYPE="$1"
    fi
fi

if [ $# -ge 2 ] && [ "$COMMAND" != "generate-pipeline" ]; then
    SHARD_INDEX="$2"
fi

if [ $# -ge 3 ] && [ "$COMMAND" != "generate-pipeline" ]; then
    TOTAL_SHARDS="$3"
fi

# Validate test type
if [ "$TEST_TYPE" != "browser" ] && [ "$TEST_TYPE" != "server" ]; then
    echo "Error: Test type must be 'browser' or 'server'"
    echo "Usage: $0 [browser|server] [shard_index] [total_shards]"
    echo "       $0 generate-pipeline [browser|server]"
    exit 1
fi

# Set defaults based on test type
if [ "$TEST_TYPE" = "browser" ]; then
    DEFAULT_TOTAL_SHARDS=4
    SCRIPT_NAME="test:shard"
    CONCURRENCY_GROUP="\${APP_NAME}-testgroup"
else
    DEFAULT_TOTAL_SHARDS=2
    SCRIPT_NAME="test:server:shard"
    CONCURRENCY_GROUP="\${APP_NAME}-server-testgroup"
fi

# Use defaults if not provided
if [ -z "$TOTAL_SHARDS" ]; then
    TOTAL_SHARDS=$DEFAULT_TOTAL_SHARDS
fi

cd "$PROJECT_ROOT"

# Function to generate Buildkite pipeline steps
generate_pipeline() {
    local test_type=$1
    local total_shards=$2
    local script_name=$3
    local concurrency_group=$4

    for ((i=0; i<total_shards; i++)); do
        # Capitalise first letter of test_type
        if [ "$test_type" = "browser" ]; then
            test_type_cap="Browser"
        else
            test_type_cap="Server"
        fi
        cat << EOF
  - label: ':jest: Test ${test_type_cap} Shard $((i + 1))/${total_shards}'
    <<: *run-base
    command: SHARD_INDEX=${i} TOTAL_SHARDS=${total_shards} yarn run ${script_name}
    concurrency_group: ${concurrency_group}
    concurrency: ${total_shards}
    retry:
      automatic:
        limit: 2

EOF
    done
}

# Function to run a single shard
run_shard() {
    local index=$1
    local total=$2
    echo "Running $TEST_TYPE tests - Shard $((index + 1))/$total"
    SHARD_INDEX=$index TOTAL_SHARDS=$total yarn run $SCRIPT_NAME
}

# Function to run all shards
run_all_shards() {
    local total=$1
    echo "Running all $TEST_TYPE test shards (1-$total)"

    for ((i=0; i<total; i++)); do
        echo ""
        echo "========================================="
        echo "Running Shard $((i + 1))/$total"
        echo "========================================="
        run_shard $i $total
    done

    echo ""
    echo "========================================="
    echo "All $TEST_TYPE test shards completed!"
    echo "========================================="
}

# Main execution
if [ "$COMMAND" = "generate-pipeline" ]; then
    echo "# Generated pipeline steps for $TEST_TYPE tests with $DEFAULT_TOTAL_SHARDS shards"
    generate_pipeline "$TEST_TYPE" "$DEFAULT_TOTAL_SHARDS" "$SCRIPT_NAME" "$CONCURRENCY_GROUP"
elif [ -n "$SHARD_INDEX" ]; then
    # Validate shard index
    if ! [[ "$SHARD_INDEX" =~ ^[0-9]+$ ]] || [ "$SHARD_INDEX" -ge "$TOTAL_SHARDS" ]; then
        echo "Error: Shard index must be a number between 0 and $((TOTAL_SHARDS - 1))"
        exit 1
    fi

    run_shard "$SHARD_INDEX" "$TOTAL_SHARDS"
else
    run_all_shards "$TOTAL_SHARDS"
fi
