import { QTA_SCREENSIZES, QTA_ENABLED } from 'config';

if (QTA_ENABLED) {
  QTA_SCREENSIZES.forEach((screenSize) => {
    describe(`QTA property page layout for ${screenSize}`, () => {
      before(() => cy.visitQtaPropertyWithStayAttributes());

      beforeEach(() => cy.viewport(screenSize));

      describe('Dream Planner link', () => {
        it('should render correctly', () => {
          cy.findByTestId('dream-planner').should('include.text', 'Dream Planner');
        });

        it('should link to the correct href', () => {
          cy.findByTestId('dream-planner').should(
            'have.attr',
            'href',
            'https://www.qantas.com/au/en/frequent-flyer/my-account/dream-planner.html',
          );
        });

        it('should open in a new window', () => {
          cy.findByTestId('dream-planner').should('have.attr', 'target', '_blank');
        });

        it('should not be open to CORS attacks', () => {
          cy.findByTestId('dream-planner').should('have.attr', 'rel', 'noopener noreferrer');
        });

        it('should have the correct title', () => {
          cy.findByTestId('dream-planner').should('have.attr', 'title', 'Link opens in a new tab');
        });
      });

      describe('Get directions link', () => {
        it('should render correctly', () => {
          cy.findByLabelText('Get directions on Google Maps').should('include.text', 'Get directions');
        });

        it('should link to the correct href', () => {
          cy.findByLabelText('Get directions on Google Maps').should('have.attr', 'href').and('include', 'http://www.google.com/maps/dir/');
        });

        it('should open in a new window', () => {
          cy.findByLabelText('Get directions on Google Maps').should('have.attr', 'target', '_blank');
        });

        it('should not be open to CORS attacks', () => {
          cy.findByLabelText('Get directions on Google Maps').should('have.attr', 'rel', 'noopener noreferrer');
        });

        it('should have the correct title', () => {
          cy.findByLabelText('Get directions on Google Maps').should('have.attr', 'title', 'Link opens in a new tab');
        });
      });
    });
  });
}
