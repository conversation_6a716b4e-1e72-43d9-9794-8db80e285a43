import { HOTELS_PATH, POINTS_EARN_ENABLED, QTA_SCREENSIZES, QTA_ENABLED } from 'config';
import { submitPaymentOptionsForm, submitPersonalDetailsForm, submitQantasPointsForm } from '../checkout/common';

if (QTA_ENABLED) {
  describe('QTA checkout layout', () => {
    before(() => {
      cy.intercept('POST', `${HOTELS_PATH}/api/ui/quotes`);
      cy.clearAllDomainCookies();
      cy.visitQtaCheckoutWithStayAttributes();

      submitPersonalDetailsForm();
      if (POINTS_EARN_ENABLED) {
        submitQantasPointsForm();
      }
      submitPaymentOptionsForm();
    });

    QTA_SCREENSIZES.forEach((screenSize) => {
      describe(`Privacy Policy for ${screenSize}`, () => {
        beforeEach(() => cy.viewport(screenSize));
        it('should render correctly', () => {
          cy.findByLabelText('View privacy policy in a new tab').should('include.text', 'Privacy Policy.');
        });

        it('should link to the correct href', () => {
          cy.findByLabelText('View privacy policy in a new tab').should(
            'have.attr',
            'href',
            'https://www.qantas.com/au/en/support/privacy-and-security.html',
          );
        });

        it('should open in a new window', () => {
          cy.findByLabelText('View privacy policy in a new tab').should('have.attr', 'target', '_blank');
        });

        it('should not be open to CORS attacks', () => {
          cy.findByLabelText('View privacy policy in a new tab').should('have.attr', 'rel', 'noopener noreferrer');
        });

        it('should have the correct title', () => {
          cy.findByLabelText('View privacy policy in a new tab').should('have.attr', 'title', 'Link opens in a new tab');
        });
      });
    });
  });
}
