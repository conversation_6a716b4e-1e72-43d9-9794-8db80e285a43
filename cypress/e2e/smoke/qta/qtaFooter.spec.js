import { QTA_SCREENSIZES, QTA_ENABLED } from 'config';

if (QTA_ENABLED) {
  QTA_SCREENSIZES.forEach((screenSize) => {
    describe(`QTA footer for ${screenSize}`, () => {
      before(() => cy.visitQtaPropertyWithStayAttributes());

      beforeEach(() => cy.viewport(screenSize));

      it('should have the Frequent Flyer | Qantas Hotels link', () => {
        cy.findByTestId('footer-logo').should('exist');
      });

      it('should have the scroll to top button', () => {
        cy.findByTestId('scroll-to-top').should('exist');
      });

      it('should not have footer header links', () => {
        cy.findAllByTestId('footer-links-header').should('not.exist');
      });

      it('should have Manage Hotels Bookings link', () => {
        cy.findByTestId('qta-manage-bookings-link').should('include.text', 'Manage Hotels Bookings');
      });

      it('should have FAQs link', () => {
        cy.findByTestId('qta-faqs-link').should('include.text', 'FAQs');
      });

      it('should have the Contact Us link', () => {
        cy.findByTestId('qta-contact-link').should('include.text', 'Contact Us');
      });

      it('should not have the Destinations link', () => {
        cy.findAllByTestId('destinations-mobile-link').should('not.exist');
      });

      it('should not have the Hotels Deals link', () => {
        cy.findAllByTestId('deals-link').should('not.exist');
      });

      it('should not have the Airbnb link', () => {
        cy.findAllByTestId('airbnb-link').should('not.exist');
      });

      it('should not have the Exclusive Offers link', () => {
        cy.findAllByTestId('luxe-link').should('not.exist');
      });

      it('should not have destination links', () => {
        cy.findByTestId('melbourne-destinations-link').should('not.exist');
        cy.findByTestId('surfers-paradise-destinations-link').should('not.exist');
      });

      it('should have the Acknowledgement text', () => {
        cy.findByTestId('acknowledgement-text').should(
          'include.text',
          'We acknowledge the Aboriginal and Torres Strait Islander Traditional Custodians of the land on which we work, live and fly.',
        );
        cy.findByTestId('acknowledgement-text').should('include.text', 'We pay respect to Elders past, present and emerging.');
      });

      it('should have the Qantas Airways ABN text', () => {
        cy.findByTestId('abn').should('include.text', 'Qantas Airways Limited ABN **************');
      });

      describe('Privacy & Security link', () => {
        it('should render correctly', () => {
          cy.findByTestId('privacy-security-link').should('include.text', 'Privacy & Security');
        });

        it('should link to the correct href', () => {
          cy.findByTestId('privacy-security-link').should(
            'have.attr',
            'href',
            'https://www.qantas.com/au/en/support/privacy-and-security.html',
          );
        });

        it('should open in a new window', () => {
          cy.findByTestId('privacy-security-link').should('have.attr', 'target', '_blank');
        });

        it('should not be open to CORS attacks', () => {
          cy.findByTestId('privacy-security-link').should('have.attr', 'rel', 'noopener noreferrer');
        });
      });

      describe('Terms of use link', () => {
        it('should render correctly', () => {
          cy.findByTestId('term-of-use-link').should('include.text', 'Terms of use');
        });

        it('should link to the correct href', () => {
          cy.findByTestId('term-of-use-link').should('have.attr', 'href', 'https://www.qantas.com/au/en/support/terms-of-use.html');
        });

        it('should open in a new window', () => {
          cy.findByTestId('term-of-use-link').should('have.attr', 'target', '_blank');
        });

        it('should not be open to CORS attacks', () => {
          cy.findByTestId('term-of-use-link').should('have.attr', 'rel', 'noopener noreferrer');
        });
      });
    });
  });
}
