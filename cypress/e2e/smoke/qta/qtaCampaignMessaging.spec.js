import { QTA_SCREENSIZES, QTA_ENABLED } from 'config';

if (QTA_ENABLED) {
  QTA_SCREENSIZES.forEach((screenSize) => {
    describe(`QTA Campaign Messaging link for ${screenSize}`, () => {
      before(() => cy.visitQtaSearchWithStayAttributes());

      beforeEach(() => cy.viewport(screenSize));

      it('should render correctly', () => {
        cy.findByTestId('link-wrapper').should('include.text', 'Book with Points Plus Pay');
      });

      it('should link to the correct href', () => {
        cy.findByTestId('link-wrapper').should('have.attr', 'href', 'https://www.qantas.com/hotels/campaigns/more-in-every-point');
      });

      it('should open in a new window', () => {
        cy.findByTestId('link-wrapper').should('have.attr', 'target', '_blank');
      });

      it('should not be open to CORS attacks', () => {
        cy.findByTestId('link-wrapper').should('have.attr', 'rel', 'noopener noreferrer');
      });

      it('should have the correct title', () => {
        cy.findByTestId('link-wrapper').should('have.attr', 'title', 'Link opens in a new tab');
      });
    });
  });
}
