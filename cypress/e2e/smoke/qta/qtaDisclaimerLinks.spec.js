import { QTA_SCREENSIZES, QTA_ENABLED } from 'config';

if (QTA_ENABLED) {
  QTA_SCREENSIZES.forEach((screenSize) => {
    describe('QTA Disclaimer Links', () => {
      before(() => cy.visitQtaSearchWithStayAttributes());

      beforeEach(() => cy.viewport(screenSize));

      describe(`QFF disclaimer for ${screenSize}`, () => {
        it('should render correctly', () => {
          cy.findByTestId('pointsPlusPayViewFullTermsAndConditionsLink').should('include.text', 'View full terms and conditions here');
        });

        it('should link to the correct href', () => {
          cy.findByTestId('pointsPlusPayViewFullTermsAndConditionsLink').should(
            'have.attr',
            'href',
            'https://www.qantas.com/au/en/book-a-trip/hotels-and-airbnb/terms-and-conditions.html',
          );
        });

        it('should open in a new window', () => {
          cy.findByTestId('pointsPlusPayViewFullTermsAndConditionsLink').should('have.attr', 'target', '_blank');
        });

        it('should not be open to CORS attacks', () => {
          cy.findByTestId('pointsPlusPayViewFullTermsAndConditionsLink').should('have.attr', 'rel', 'noopener noreferrer');
        });

        it('should have the correct title', () => {
          cy.findByTestId('pointsPlusPayViewFullTermsAndConditionsLink').should('have.attr', 'title', 'Link opens in a new tab');
        });
      });

      describe('QBR disclaimer', () => {
        it('should render correctly', () => {
          cy.findByTestId('businessRewardConditionApplyLink').should('include.text', 'Conditions apply');
        });

        it('should link to the correct href', () => {
          cy.findByTestId('businessRewardConditionApplyLink').should(
            'have.attr',
            'href',
            'https://www.qantas.com/au/en/book-a-trip/hotels-and-airbnb/terms-and-conditions.html',
          );
        });

        it('should open in a new window', () => {
          cy.findByTestId('businessRewardConditionApplyLink').should('have.attr', 'target', '_blank');
        });

        it('should not be open to CORS attacks', () => {
          cy.findByTestId('businessRewardConditionApplyLink').should('have.attr', 'rel', 'noopener noreferrer');
        });

        it('should have the correct title', () => {
          cy.findByTestId('businessRewardConditionApplyLink').should('have.attr', 'title', 'Link opens in a new tab');
        });
      });
    });
  });
}
