import { HOTELS_PATH, POINTS_EARN_ENABLED, QTA_SCREENSIZES, CONTACT_US, QTA_ENABLED } from 'config';
import { fillAdyenPaymentForm, submitPaymentOptionsForm, submitPersonalDetailsForm, submitQantasPointsForm } from '../checkout/common';

if (QTA_ENABLED) {
  describe('QTA booking confirmation layout', () => {
    before(() => {
      cy.intercept('POST', `${HOTELS_PATH}/api/ui/quotes`);
      cy.clearAllDomainCookies();
      cy.visitQtaCheckoutWithStayAttributes();

      submitPersonalDetailsForm();
      if (POINTS_EARN_ENABLED) {
        submitQantasPointsForm();
      }
      submitPaymentOptionsForm();
      fillAdyenPaymentForm();

      cy.findByTestId('confirm-and-pay-form').within(() => {
        cy.findByLabelText(/I agree/i).check();
        cy.findByTestId('payment-button').click();
      });
    });

    QTA_SCREENSIZES.forEach((screenSize) => {
      describe(`the contact us link for ${screenSize}`, () => {
        beforeEach(() => cy.viewport(screenSize));
        it('should render correctly', () => {
          cy.findByTestId('contact-us').should('include.text', 'contact us');
        });

        it('should link to the correct href', () => {
          cy.findByTestId('contact-us').should('have.attr', 'href', `${HOTELS_PATH}${CONTACT_US}`);
        });

        it('should open in a new window', () => {
          cy.findByTestId('contact-us').should('have.attr', 'target', '_blank');
        });

        it('should not be open to CORS attacks', () => {
          cy.findByTestId('contact-us').should('have.attr', 'rel', 'noopener noreferrer');
        });

        it('should have the correct title', () => {
          cy.findByTestId('contact-us').should('have.attr', 'title', 'Link opens in a new tab');
        });
      });

      describe(`the Manage Your Booking link for ${screenSize}`, () => {
        beforeEach(() => cy.viewport(screenSize));
        it('should render correctly', () => {
          cy.findByTestId('manage-your-booking').should('include.text', 'Manage your booking');
        });

        it('should link to the correct href', () => {
          cy.findByTestId('manage-your-booking')
            .should('have.attr', 'href')
            .and('include', `${HOTELS_PATH}/manage/bookings`)
            .and('include', '?source=hotels-booking-confirmation');
        });

        it('should open in a new window', () => {
          cy.findByTestId('manage-your-booking').should('have.attr', 'target', '_blank');
        });

        it('should not be open to CORS attacks', () => {
          cy.findByTestId('manage-your-booking').should('have.attr', 'rel', 'noopener noreferrer');
        });
      });
    });
  });
}
