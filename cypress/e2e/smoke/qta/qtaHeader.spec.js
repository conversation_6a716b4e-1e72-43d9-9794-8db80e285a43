import { QTA_SCREENSIZES, QTA_ENABLED } from 'config';

if (QTA_ENABLED) {
  describe('QTA Header', () => {
    QTA_SCREENSIZES.forEach((screenSize) => {
      describe('Menu link', () => {
        it(`mobile header menu button should not be present for ${screenSize}`, () => {
          cy.viewport(screenSize);
          cy.visitQtaSearchWithStayAttributes();
          cy.findByTestId('menu-button').should('not.exist');
        });
      });
    });
  });
}
