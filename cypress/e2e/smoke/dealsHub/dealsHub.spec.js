describe.skip('dealsHub', () => {
  before(() => {
    cy.visitDealsHub();
  });

  it('renders the campaign banner', () => {
    cy.findAllByTestId('campaign-banner-wrapper').first().should('exist');
  });

  it('renders the header heading', () => {
    cy.findAllByTestId('deals-header-heading').should('exist');
  });

  it('renders the modal button', () => {
    cy.findAllByTestId('open-modal-button').should('exist');
  });

  it('renders the deals', () => {
    cy.findAllByTestId('filter-option-all_deals').should('have.text', 'All deals');
    cy.findAllByTestId('filter-option-best_deals').should('have.text', 'Best deals');
    cy.findAllByTestId('filter-option-luxury_offers').should('have.text', 'Luxury offers');
  });

  it('renders the heading for best deals', () => {
    cy.findAllByTestId('deals-type-heading').first().should('exist');
  });

  describe('renders the deals card', () => {
    it('renders deals card', () => {
      cy.findAllByTestId('stacked-card').first().should('exist');
    });

    it('renders the link for deals card', () => {
      cy.findAllByTestId('stacked-card')
        .first()
        .should('have.attr', 'href')
        .and('match', /hotels\/properties/);
    });

    it('renders the Australian destinations', () => {
      cy.findByTestId('list-regions-title').should('have.text', 'Australian destinations');
      cy.findByTestId('list-regions-cta').should('have.attr', 'href', '/hotels/australia');
    });

    it('renders the discover destinations', () => {
      cy.findByTestId('popular-destination-header').should('have.text', 'Discover Destinations');
      cy.findAllByTestId('destinations-links-header').should('have.length', 8);
      cy.get('[data-testid="destinations-links-header"]').eq(0).should('have.text', 'Queensland');
      cy.get('[data-testid="destinations-links-header"]').eq(1).should('have.text', 'South Australia');
      cy.get('[data-testid="destinations-links-header"]').eq(2).should('have.text', 'New South Wales');
      cy.get('[data-testid="destinations-links-header"]').eq(3).should('have.text', 'Western Australia');
      cy.get('[data-testid="destinations-links-header"]').eq(4).should('have.text', 'Victoria');
      cy.get('[data-testid="destinations-links-header"]').eq(5).should('have.text', 'Tasmania');
      cy.get('[data-testid="destinations-links-header"]').eq(6).should('have.text', 'Northern Territory');
      cy.get('[data-testid="destinations-links-header"]').eq(7).should('have.text', 'Australian Capital Territory');
    });
  });
});
