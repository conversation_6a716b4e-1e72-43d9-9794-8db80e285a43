import { testId } from '../../../support/selectors';
import { QFF_ACCOUNT_MANAGEMENT, ACCOUNT_MANAGEMENT_TYPES } from 'config/flags';

if (QFF_ACCOUNT_MANAGEMENT === ACCOUNT_MANAGEMENT_TYPES.APP_WIDE) {
  describe('sso login', () => {
    beforeEach(() => {
      cy.clearAllDomainCookies();
      cy.visitEmptySearch();
    });

    it('can log in & out', () => {
      const ssoLoginBtn = testId('qff-login-tab');
      const ssoLogoutBtn = testId('logout-button');
      const navAuthBtn = testId('qff-menu-tab');

      let loginButton = cy.get(ssoLoginBtn).first();
      loginButton.should('be.visible');

      cy.loginViaSSO();

      let navAuthButton = cy.get(navAuthBtn);
      navAuthButton.should('be.visible');

      navAuthButton.click();

      let logoutButton = cy.get(ssoLogoutBtn).first();
      logoutButton.should('be.visible');

      cy.logoutViaSSO();

      cy.get(ssoLoginBtn).should('be.visible');
    });
  });

  describe('oauth login', () => {
    beforeEach(() => {
      cy.clearAllDomainCookies();
      cy.visitEmptySearch();
    });

    it('can log in & out', () => {
      const ssoLoginBtn = testId('qff-login-tab');
      const ssoLogoutBtn = testId('logout-button');
      const navAuthBtn = testId('qff-menu-tab');

      let loginButton = cy.get(ssoLoginBtn).first();
      loginButton.should('be.visible');

      cy.loginViaSSO();
      cy.wait(5000);

      let navAuthButton = cy.get(navAuthBtn);
      navAuthButton.should('be.visible');

      navAuthButton.click();

      let logoutButton = cy.get(ssoLogoutBtn).first();
      logoutButton.should('be.visible');

      cy.logoutViaSSO();

      cy.get(ssoLoginBtn).should('be.visible');
    });
  });
}
