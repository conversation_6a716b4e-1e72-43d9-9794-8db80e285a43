import { HOTELS_PATH } from 'config/env';
import { PROPERTY_PAGE_RX, testId } from '../../../support/helpers';

const SEARCH_MAPS_PATH = `${HOTELS_PATH}/search/map`;
const SEARCH_LIST_PATH = `${HOTELS_PATH}/search/list`;

const googleMapsStaticImage = testId('google-maps-static-image');
const propertyMarker = testId('google-maps-property-marker');
const positionedPropertyCard = `${testId('stacked-card')}` ?? `${testId('inline-card')}`;
const backToListLink = testId('back-link');

const getMapsImage = () => cy.get(googleMapsStaticImage).first();

describe('maps', () => {
  describe('on the search page', () => {
    beforeEach(() => cy.visitEmptySearch());

    it('map image appears on search page', () => {
      getMapsImage().should('be.visible');
    });

    describe('click on the map image', () => {
      let searchQueryString;

      beforeEach(() => {
        cy.location().then((loc) => (searchQueryString = loc.search));
        getMapsImage().click();
      });

      it('routes to the maps page', () => {
        cy.location().should((loc) => {
          expect(loc.pathname).to.eq(SEARCH_MAPS_PATH);
        });
      });

      it('with the same querystring', () => {
        cy.url().should('include', searchQueryString);
      });
    });
  });

  describe('maps page', () => {
    const expectedSearchKeys = ['checkIn', 'checkOut', 'adults', 'children', 'infants', 'location', 'sortBy'];

    describe('params', () => {
      beforeEach(() => cy.visitMapsSearch());

      it('has the expected params', () => {
        cy.assertQueryParam((params) => expect(params).to.have.keys(expectedSearchKeys));
      });
    });

    describe('click on the close button', () => {
      let searchQueryString;

      beforeEach(() => {
        cy.visitMapsSearch();
        cy.location().then((loc) => (searchQueryString = loc.search));
        cy.get(backToListLink).first().click();
      });

      it('routes to the search page', () => {
        cy.location().should((loc) => {
          expect(loc.pathname).to.eq(SEARCH_LIST_PATH);
        });
      });

      it('with the same querystring', () => {
        cy.url().should('include', searchQueryString);
      });
    });

    describe('click on the property marker', () => {
      beforeEach(() => {
        cy.visitMapsSearch();
        cy.get(propertyMarker).last().click();
      });

      it('displays the property card', () => {
        cy.get(positionedPropertyCard).first().should('exist');
      });

      describe('click on the property card', () => {
        const expectedSearchKeys = ['checkIn', 'checkOut', 'adults', 'children', 'infants', 'location', 'payWith', 'searchType', 'sortBy'];

        beforeEach(() => {
          cy.get(testId('inline-card')).first().invoke('removeAttr', 'target').click();
        });

        it('routes to the property page', () => {
          cy.location().should((loc) => {
            expect(loc.pathname).to.match(PROPERTY_PAGE_RX);
          });
        });

        it('has the expected params', () => {
          cy.assertQueryParam((params) => expect(params).to.have.keys(expectedSearchKeys));
        });
      });
    });

    it('opens and closes the card as expected', { testCaseID: 'HOT-61' }, () => {
      cy.visitMapsSearch();

      cy.get('[data-testid="filter-popper"]').should('have.length', 0);
      cy.get('button[data-testid="filter-button"]').first().click();
      cy.get('[data-testid="filter-popper"]').should('have.length', 1);
      cy.get('button[data-testid="filter-button"]').first().click();
      cy.get('[data-testid="filter-popper"]').should('have.length', 0);
      cy.get('button[data-testid="filter-button"]').first().click();
      cy.get('[data-testid="filter-popper"]').should('have.length', 1);
      cy.get('button[data-testid="filter-button"]').eq(1).click();
      cy.get('[data-testid="filter-popper"]').should('have.length', 1);
      cy.get('button[data-testid="filter-button"]').eq(1).click();
      cy.get('[data-testid="filter-popper"]').should('have.length', 0);
    });
  });
});
