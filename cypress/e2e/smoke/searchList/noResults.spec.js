import { testId } from '../../../support/helpers';
import { format, addDays, startOfDay } from 'date-fns';
import { QUERY_DATE_FORMAT } from '../../../support/commonParams';
import { HOTELS_PATH } from 'config/env';

const visitSearchListWithNoResults = () => {
  const checkInDate = format(startOfDay(addDays(Date.now(), 1)), QUERY_DATE_FORMAT);
  const checkOutDate = format(startOfDay(addDays(Date.now(), 2)), QUERY_DATE_FORMAT);
  cy.log(checkInDate, checkOutDate);
  // Force search to return 0 results by searching for tomorrow with Deposit Pay enabled
  // Deposit pay is only valid for bookings 28 days out, so this query will always return nothing.
  cy.visit(
    `${HOTELS_PATH}/search/list?adults=2&checkIn=${checkInDate}&checkOut=${checkOutDate}&children=0&facilities=parking,room-service,reception&infants=0&location=Melbourne, VIC, Australia&minStarRating=4&minTripadvisorRating=3&page=1&propertyTypes=hotels,motels&sortBy=popularity&&minPrice=200&depositPay=true`,
  );
};

describe('no results', { testIsolation: false }, () => {
  before(() => {
    visitSearchListWithNoResults();
    cy.setupWaitSearchResults();
  });

  it('renders the remove filter buttons', () => {
    cy.get(`${testId('remove-filter-buttons')} button`)
      .should('be.visible')
      .should('have.length', 9);
  });

  it('renders the edit query buttons', () => {
    cy.get(`${testId('edit-query-buttons')} button`)
      .should('be.visible')
      .should('have.length', 3);
  });

  it('focuses to the location search input when the change destination button is clicked', () => {
    cy.get(`${testId('change-destination-button')}`).click();
    cy.focused().should('have.attr', 'data-testid', 'location-search-input');
  });

  it('focuses to the calendar input when the change dates button is clicked', () => {
    cy.get(`${testId('change-dates-button')}`).click();
    cy.focused().should('have.attr', 'data-testid', 'select-date-input');
  });

  it('removes the respective filter when a remove filter button is clicked', () => {
    visitSearchListWithNoResults();
    const filterNames = ['hotels', 'motels', 'minStarRating', 'minTripadvisorRating', 'parking', 'room-service', 'depositPay'];
    filterNames.forEach((filterName) => {
      const clearButtonSelector = `${testId(`clear-${filterName}-button`)}`;
      cy.get(clearButtonSelector).click();
      cy.get(clearButtonSelector).should('not.exist');
    });
  });

  it('removes the all filters when the reset filters button is clicked', () => {
    visitSearchListWithNoResults();
    cy.setupWaitSearchResults();
    cy.get(`${testId('reset-all-filters-button')}`).click();
    cy.get(`${testId('location-header')}`).should('not.contain.text', "We couldn't find any hotels matching your search");
  });
});
