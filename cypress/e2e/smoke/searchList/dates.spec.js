import { format, addDays, startOfDay, differenceInCalendarMonths } from 'date-fns';
import { testId, clickTimes } from '../../../support/helpers';
import { QUERY_DATE_FORMAT, defaultCheckIn, defaultCheckOut } from '../../../support/commonParams';

const DATE_INPUT_FORMAT = 'EEE d MMM, yyyy';
const MONTH_TITLE_FORMAT = 'MMM yyyy';

const DATES_INPUT = testId('select-date-input');
const DATE_RANGE_PICKER = `${testId('stay-date-picker')} ${testId('date-range-picker')}`;
const GO_BACK_1_MONTH = `${DATE_RANGE_PICKER} ${testId('go-back-1-month')}`;
const GO_FORWARD_1_MONTH = `${DATE_RANGE_PICKER} ${testId('go-forward-1-month')}`;

const CALENDAR_MONTH = `${DATE_RANGE_PICKER} ${testId('calendar-month')}`;
const DONE_BUTTON = `${testId('stay-date-picker')} ${testId('done-button')}`;

const scrollMonthIntoView = (date) => {
  cy.get(CALENDAR_MONTH).then(($el) => {
    const leftMostDate = new Date($el.attr('data-date'));
    const diff = differenceInCalendarMonths(date, leftMostDate);
    if (diff > 0) {
      clickTimes(cy.get(GO_FORWARD_1_MONTH), diff);
    }

    if (diff < 0) {
      clickTimes(cy.get(GO_BACK_1_MONTH), -diff);
    }
  });
};

const clickCalendarDay = (date) => {
  scrollMonthIntoView(date);
  const month = format(date, MONTH_TITLE_FORMAT);
  const day = format(date, 'd');
  cy.get(DATE_RANGE_PICKER).contains(month).parent().contains('button', day).click();
};

const selectDatesWithMouse = (checkIn, checkOut) => {
  cy.log(`Selecting dates ${format(checkIn, QUERY_DATE_FORMAT)} to ${format(checkOut, QUERY_DATE_FORMAT)}`);
  cy.get(DATES_INPUT).click();
  clickCalendarDay(checkIn);
  clickCalendarDay(checkOut);
  cy.get(DONE_BUTTON).click();
};

const assertSelectedDates = (checkIn, checkOut) => {
  cy.assertRoutedToQueryParam('checkIn', format(checkIn, QUERY_DATE_FORMAT));
  cy.assertRoutedToQueryParam('checkOut', format(checkOut, QUERY_DATE_FORMAT));
  cy.get(DATES_INPUT).should('have.value', `${format(checkIn, DATE_INPUT_FORMAT)} - ${format(checkOut, DATE_INPUT_FORMAT)}`);
};

describe('date picker', () => {
  beforeEach(() => cy.visitEmptySearch());

  it('has default check-in and check-out dates', () => {
    cy.get(DATES_INPUT).should(
      'have.value',
      `${format(new Date(defaultCheckIn), DATE_INPUT_FORMAT)} - ${format(new Date(defaultCheckOut), DATE_INPUT_FORMAT)}`,
    );
  });

  it('updates search using mouse', () => {
    const checkInDate = startOfDay(addDays(Date.now(), 10));
    const checkOutDate = startOfDay(addDays(Date.now(), 20));

    cy.setupWaitSearchResults();

    selectDatesWithMouse(checkInDate, checkOutDate);
    cy.waitSearchResults();
    assertSelectedDates(checkInDate, checkOutDate);
  });

  describe.skip('when navigating back', () => {
    it('updates dates', () => {
      const checkInDate1 = startOfDay(addDays(Date.now(), 10));
      const checkOutDate1 = startOfDay(addDays(Date.now(), 20));
      const checkInDate2 = startOfDay(addDays(Date.now(), 1));
      const checkOutDate2 = startOfDay(addDays(Date.now(), 3));

      selectDatesWithMouse(checkInDate1, checkOutDate1);
      assertSelectedDates(checkInDate1, checkOutDate1);

      selectDatesWithMouse(checkInDate2, checkOutDate2);
      assertSelectedDates(checkInDate2, checkOutDate2);

      cy.go('back');
      assertSelectedDates(checkInDate1, checkOutDate1);
    });
  });
});
