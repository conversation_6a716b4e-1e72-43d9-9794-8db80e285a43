import { testId } from '../../../support/selectors';
import { defaultPayWith } from '../../../support/commonParams';
import { PAYWITH_TOGGLE_ENABLED } from '../../../../src/config/flags';

if (PAYWITH_TOGGLE_ENABLED) {
  describe('pay with', () => {
    before(() => {
      cy.visitEmptySearch();
    });

    const PAY_WITH_INPUT = 'input[name="pay-with-button-group"]';
    const CHECKED_PAY_WITH_INPUT = `${PAY_WITH_INPUT}:checked`;
    const PAY_WITH_LABEL = `${PAY_WITH_INPUT} + label`;
    const SEARCH_RESULT_CURRENCY = `${testId('search-result')} ${testId('currency')}`;
    const USE_CASH = 'CASH';
    const USE_POINTS = 'POINTS';

    it('has default value', () => {
      cy.get(CHECKED_PAY_WITH_INPUT).should('have.value', defaultPayWith);
    });

    describe('selecting pay with points', { testIsolation: false }, () => {
      before(() => {
        cy.visitEmptySearch();
        cy.setupWaitSearchResults('payWith=points');
        cy.get(PAY_WITH_LABEL).contains(USE_POINTS).click({ force: true });
        cy.waitSearchResults();
      });

      it('updates search params', () => {
        cy.assertRoutedToQueryParam('payWith', 'points');
      });

      it('show points as active in pay with toggle', () => {
        cy.get(CHECKED_PAY_WITH_INPUT).should('have.value', 'points');
      });

      it('shows points value in search results', () => {
        cy.get(SEARCH_RESULT_CURRENCY).each((el) => {
          cy.wrap(el)
            .invoke('text')
            .should('match', /(\d|,)+/);
        });
      });
    });

    describe('selecting pay with cash', { testIsolation: false }, () => {
      before(() => {
        cy.visitEmptySearch();
        // switch to points first
        cy.setupWaitSearchResults();
        cy.get(PAY_WITH_LABEL).contains(USE_POINTS).click({ force: true });
        cy.waitSearchResults();

        // switch back to cash
        cy.get(PAY_WITH_LABEL).contains(USE_CASH).click({ force: true });
        cy.waitSearchResults();
      });

      it('updates search params', () => {
        cy.assertRoutedToQueryParam('payWith', 'cash');
      });

      it('show cash as active in pay with toggle', () => {
        cy.get(CHECKED_PAY_WITH_INPUT).should('have.value', 'cash');
      });

      it('shows cash value in search results', () => {
        cy.get(SEARCH_RESULT_CURRENCY).each((el) => {
          cy.wrap(el)
            .invoke('text')
            .should('match', /^\$(\d|,)+$/);
        });
      });
    });
  });
}
