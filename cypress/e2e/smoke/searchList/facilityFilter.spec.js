import { testId } from '../../../support/helpers';

describe('Facility filter', () => {
  const QUERY_KEY = 'facilities';
  const FACILITY_FILTER = `${testId('facility-filter')} input[type="checkbox"]`;
  const TOTAL_RESULTS = testId('result-count');
  const RESET_FILTERS = testId('clear-filters-button');

  const getFacilityFilterAtIndex = (filterIndex, callback) => {
    cy.setupWaitSearchResults();
    cy.get(FACILITY_FILTER).eq(filterIndex).should('have.attr', 'name').then(callback);
  };

  const clickAndWait = (facilities) => {
    cy.setupWaitSearchResults();
    facilities.forEach((facility) => {
      cy.get(`${FACILITY_FILTER}[name="${facility}"]`).first().click();
      cy.waitSearchResults();
    });
  };

  beforeEach(() => cy.visitEmptySearch());

  it('defaults to empty', () => {
    cy.setupWaitSearchResults();
    cy.get(`${FACILITY_FILTER}:checked`).should('have.length', 0);
    cy.assertQueryParam((params) => expect(params).to.not.have.property(QUERY_KEY));
  });

  [0, 1].forEach((filterIndex) => {
    describe(`when clicking the facility filters at the ${filterIndex} position`, () => {
      it('the facility filters UI reflects checked state set by the query params', () => {
        getFacilityFilterAtIndex(filterIndex, (facility) => {
          clickAndWait([facility]);
          cy.assertRoutedToQueryParam(QUERY_KEY, facility);
          cy.get(`${FACILITY_FILTER}:checked`).should('have.attr', 'name', facility);
        });
      });

      xit('the facility filters totals are reflected in the results count', () => {
        getFacilityFilterAtIndex(filterIndex, (facility) => {
          cy.get(`${testId('facility-filter')} label[name="${facility}"]>div:last-child`)
            .first()
            .invoke('text')
            .then((facilitiesTotal) => {
              clickAndWait([facility]);
              cy.get(TOTAL_RESULTS)
                .first()
                .invoke('text')
                .then((resultCount) => {
                  expect(facilitiesTotal).to.equal(resultCount);
                });
            });
        });
      });
    });
  });

  describe('when clicking the first and second facility filters', () => {
    let SAVED_FACILITIES = [];
    before(() => {
      cy.visitEmptySearch();
      cy.get(FACILITY_FILTER).then((allFacilities) => {
        SAVED_FACILITIES = Object.values(allFacilities)
          .slice(0, 2)
          .map((facility) => facility.getAttribute('name'));
      });
    });

    it('the facility filters UI reflects checked state set by the query params', () => {
      clickAndWait(SAVED_FACILITIES);
      cy.assertRoutedToQueryParam(QUERY_KEY, SAVED_FACILITIES.join());
      SAVED_FACILITIES.forEach((facility) => {
        cy.get(`${FACILITY_FILTER}[name="${facility}"]:checked`).should('exist');
      });
    });

    xit('the facility filters totals are reflected in the results count', () => {
      const firstFacility = SAVED_FACILITIES[0];
      const secondFacility = SAVED_FACILITIES[1];
      clickAndWait([firstFacility]);
      cy.get(`${testId('facility-filter')} label[name="${secondFacility}"]>div:last-child`)
        .first()
        .invoke('text')
        .then((facilitiesTotal) => {
          clickAndWait([secondFacility]);
          cy.get(TOTAL_RESULTS)
            .first()
            .invoke('text')
            .then((resultCount) => {
              expect(facilitiesTotal).to.equal(resultCount);
            });
        });
    });

    it('will reset the facilities when reset all filters is clicked', () => {
      clickAndWait(SAVED_FACILITIES);
      cy.assertRoutedToQueryParam(QUERY_KEY, SAVED_FACILITIES.join());
      cy.get(RESET_FILTERS).first().click();
      SAVED_FACILITIES.forEach((facility) => {
        cy.get(`${FACILITY_FILTER}[name="${facility}"]:checked`).should('not.exist');
      });
    });
  });
});
