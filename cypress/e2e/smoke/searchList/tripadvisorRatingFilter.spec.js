import { testId } from '../../../support/helpers';

describe('Tripadvisor rating filter', () => {
  const QUERY_KEY = 'minTripadvisorRating';
  const TRIPADVISOR_RATING_FILTER = `${testId('tripadvisor-rating-filter')} input[type="radio"][name="${QUERY_KEY}"]`;

  const clickAndWait = (ratingValue) => {
    cy.setupWaitSearchResults(`${QUERY_KEY}=${ratingValue}`);
    cy.get(TRIPADVISOR_RATING_FILTER).filter(`[value="${ratingValue}"]`).first().parent().click();
    cy.waitSearchResults();
  };

  beforeEach(() => cy.visitEmptySearch());

  it('defaults to all', () => {
    cy.get(TRIPADVISOR_RATING_FILTER).filter(':checked').should('have.value', '');
    cy.assertQueryParam((params) => expect(params).to.not.have.property(QUERY_KEY));
  });

  ['3', '4', '5'].forEach((rating) => {
    describe(`when rating ${rating}`, () => {
      beforeEach(() => {
        clickAndWait(rating);
      });

      it('updates the search', () => {
        cy.assertRoutedToQueryParam(QUERY_KEY, rating);
        cy.get(TRIPADVISOR_RATING_FILTER).filter(':checked').should('have.value', rating);
      });
    });
  });
});
