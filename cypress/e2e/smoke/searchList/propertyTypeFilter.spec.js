import { testId } from '../../../support/helpers';

describe('Property type filter', () => {
  const QUERY_KEY = 'propertyTypes';
  const PROPERTY_TYPE_FILTER = `${testId('property-type-filter')} input[type="checkbox"]`;
  const RESET_FILTERS = testId('clear-filters-button');

  const clickAndWait = (typeValue) => {
    cy.setupWaitSearchResults(`${QUERY_KEY}=${typeValue}`);
    cy.get(PROPERTY_TYPE_FILTER).filter(`[name="${typeValue}"]`).first().click();
    cy.waitSearchResults();
  };

  beforeEach(() => cy.visitEmptySearch());

  it('defaults to empty', () => {
    cy.get(PROPERTY_TYPE_FILTER).filter(':checked').should('have.length', 0);
    cy.assertQueryParam((params) => expect(params).to.not.have.property(QUERY_KEY));
  });

  describe(`when clicking property type filters`, () => {
    [0, 1].forEach((index) => {
      it(`updates the search for the filter item in the ${index} position`, () => {
        cy.get(PROPERTY_TYPE_FILTER)
          .eq(index)
          .invoke('attr', 'name')
          .then((propertyType) => {
            clickAndWait(propertyType);
            cy.assertRoutedToQueryParam(QUERY_KEY, propertyType);
            cy.get(PROPERTY_TYPE_FILTER).filter(':checked').should('have.attr', 'name', propertyType);
          });
      });
    });
  });

  describe(`when clicking the first and second filters`, () => {
    beforeEach(() => {
      cy.setupWaitSearchResults();
      cy.get(PROPERTY_TYPE_FILTER).eq(0).first().click();
      cy.waitSearchResults();
      cy.get(PROPERTY_TYPE_FILTER).eq(1).first().click();
      cy.waitSearchResults();
    });

    it('updates the search', () => {
      let visiblePropertyTypes = [];
      cy.get(PROPERTY_TYPE_FILTER)
        .each((el) => {
          cy.wrap(el)
            .invoke('attr', 'name')
            .then((name) => visiblePropertyTypes.push(name));
        })
        .then(() => {
          cy.assertRoutedToQueryParam(QUERY_KEY, visiblePropertyTypes.slice(0, 2).toString());
          cy.get(PROPERTY_TYPE_FILTER).filter(`[name=${visiblePropertyTypes[0]}]:checked`).first().should('exist');
          cy.get(PROPERTY_TYPE_FILTER).filter(`[name=${visiblePropertyTypes[1]}]:checked`).first().should('exist');
        });
    });

    it('will reset the property types when reset all filters is clicked', () => {
      let visiblePropertyTypes = [];
      cy.get(PROPERTY_TYPE_FILTER)
        .each((el) => {
          cy.wrap(el)
            .invoke('attr', 'name')
            .then((name) => visiblePropertyTypes.push(name));
        })
        .then(() => {
          cy.get(RESET_FILTERS).first().click();
          cy.get(`${PROPERTY_TYPE_FILTER}[name=${visiblePropertyTypes[0]}]:checked`).should('not.exist');
          cy.get(`${PROPERTY_TYPE_FILTER}[name=${visiblePropertyTypes[1]}]:checked`).should('not.exist');
        });
    });
  });
});
