import {
  defaultCheckIn,
  defaultCheckOut,
  defaultLocation,
  defaultAdults,
  defaultChildren,
  defaultInfants,
  defaultSortBy,
} from '../../../support/commonParams';

describe('redirect', { testIsolation: false }, () => {
  const expectedSearchKeys = ['checkIn', 'checkOut', 'adults', 'children', 'infants', 'location', 'sortBy'];

  before(() => {
    cy.visitEmptySearch();
  });

  describe('with empty search query, the default redirect', () => {
    it('has expected params keys', () => {
      cy.assertQueryParam((params) => expect(params).to.have.keys(expectedSearchKeys));
    });

    it('sets location', () => {
      cy.assertRoutedToQueryParam('location', defaultLocation);
    });

    it('sets adults', () => {
      cy.assertRoutedToQueryParam('adults', defaultAdults);
    });

    it('sets children', () => {
      cy.assertRoutedToQueryParam('children', defaultChildren);
    });

    it('sets infants', () => {
      cy.assertRoutedToQueryParam('infants', defaultInfants);
    });

    it('sets checkIn', () => {
      cy.assertRoutedToQueryParam('checkIn', defaultCheckIn);
    });

    it('sets checkOut', () => {
      cy.assertRoutedToQueryParam('checkOut', defaultCheckOut);
    });

    it('sets sortBy', () => {
      cy.assertRoutedToQueryParam('sortBy', defaultSortBy);
    });
  });
});
