import { testId } from '../../../support/helpers';
import { defaultSortBy } from '../../../support/commonParams';

describe('sort', () => {
  beforeEach(() => {
    cy.visitEmptySearch();
  });

  const SORT_SELECT = testId('sort-select');
  const SEARCH_RESULT_CURRENCY = `${testId('search-result')} ${testId('currency')}`;

  const domElementToNumber = (el) => parseInt(el.textContent.replace(/([^\d])/g, ''), 10);

  it('has default value', () => {
    cy.get(SORT_SELECT).should('have.value', defaultSortBy);
  });

  describe('price ascending', () => {
    beforeEach(() => {
      cy.setupWaitSearchResults();
      cy.get(SORT_SELECT).filter(':visible').first().select('Lowest Price');
      cy.waitSearchResults();
      cy.assertRoutedToQueryParam('sortBy', 'price_asc');
    });

    it('displays results in expected order', () => {
      cy.get(SEARCH_RESULT_CURRENCY).should(($results) => {
        expect($results.length).to.be.greaterThan(0);
        let last = 0;
        $results.each((_, el) => {
          const dollarValue = domElementToNumber(el);
          expect(dollarValue).to.be.at.least(last);
          last = dollarValue;
        });
      });
    });
  });

  describe('price descending', () => {
    beforeEach(() => {
      cy.setupWaitSearchResults();
      cy.get(SORT_SELECT).filter(':visible').first().select('Highest Price');
      cy.waitSearchResults();
      cy.assertRoutedToQueryParam('sortBy', 'price_desc');
    });

    it('displays results in expected order', () => {
      cy.get(SEARCH_RESULT_CURRENCY).should(($results) => {
        expect($results.length).to.be.greaterThan(0);
        let last = Infinity;
        $results.each((_, el) => {
          const dollarValue = domElementToNumber(el);
          expect(dollarValue).to.be.at.most(last);
          last = dollarValue;
        });
      });
    });
  });
});
