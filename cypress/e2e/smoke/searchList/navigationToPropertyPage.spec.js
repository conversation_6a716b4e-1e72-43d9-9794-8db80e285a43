import { PROPERTY_PAGE_RX } from '../../../support/helpers';
import { parse as parseQueryString } from 'query-string';
import { HOTELS_PATH } from 'config/env';

describe('when click on a property card', () => {
  const PROPERTY_CARD = '[data-testid="search-result"] a';
  const EXPECTED_SEARCH_KEYS = ['checkIn', 'checkOut', 'adults', 'children', 'infants', 'location', 'sortBy'];

  let searchLocation = {};
  let propertyLocation = {};

  beforeEach(() => {
    cy.visitEmptySearch();
    cy.setupWaitSearchResults();
    cy.intercept(`${HOTELS_PATH}/api/ui/properties/*/availability*`).as('property-availability-header');
    cy.location().then((loc) => (searchLocation = loc));

    cy.get(PROPERTY_CARD).first().invoke('removeAttr', 'target').click();

    cy.waitPropertyPage();

    cy.location().then((loc) => (propertyLocation = loc));
  });

  it('routes to the property page', () => {
    expect(propertyLocation.pathname).to.match(PROPERTY_PAGE_RX);
  });

  it('with the correct params', () => {
    const searchQueryString = parseQueryString(searchLocation.search);
    const propertyQueryString = parseQueryString(propertyLocation.search);

    EXPECTED_SEARCH_KEYS.forEach((key) => {
      expect(propertyQueryString[key]).to.eq(searchQueryString[key]);
    });
  });
});
