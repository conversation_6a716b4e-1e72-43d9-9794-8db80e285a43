import { defaultLocation } from '../../../support/commonParams';
import { HOTELS_PATH } from 'config/env';

const LOCATION_INPUT = 'location-search-input';
const LOCATION_RESULT = 'location-search-result';

const searchForLocation = (searchText) => {
  const locationKey = `search-locations-${searchText}`;
  const propertiesKey = `search-properties-${searchText}`;

  cy.intercept(`${HOTELS_PATH}/api/ui/locations?name=${searchText}`).as(locationKey);
  cy.intercept(`${HOTELS_PATH}/api/ui/properties?name=${searchText}`).as(propertiesKey);
  cy.findByTestId(LOCATION_INPUT)
    .click()
    .should('have.value', '')
    .type(searchText)
    .wait([`@${locationKey}`, `@${propertiesKey}`]);
};

const searchForLocationAndSelect = (value) => {
  searchForLocation(value);
  cy.findAllByTestId(LOCATION_RESULT).contains(new RegExp(value, 'i')).click();
  cy.findByTestId(LOCATION_INPUT).should('not.be.focused');
};

const assertLocationUpdated = (value) => {
  cy.findByTestId(LOCATION_INPUT).should(($el) => expect($el.attr('placeholder')).to.match(new RegExp(value, 'i')));
  cy.get('title').should(($el) => expect($el.text()).to.match(new RegExp(value, 'i')));
};

describe('location', () => {
  beforeEach(() => cy.visitEmptySearch());

  it('has default value', () => {
    cy.findByTestId(LOCATION_INPUT).should('have.attr', 'placeholder', defaultLocation);
  });

  it('typing a new location and selecting a result updates the search', () => {
    const newSearchText = 'hobart';
    const newLocation = 'Hobart, TAS, Australia';
    cy.setupWaitSearchResults();
    searchForLocation(newSearchText);
    cy.findAllByTestId(LOCATION_RESULT).should('have.length.greaterThan', 5).contains(newLocation).click();
    cy.findByTestId(LOCATION_INPUT).should('have.attr', 'placeholder', newLocation);
    cy.waitSearchResults();
    cy.assertRoutedToQueryParam('location', newLocation);
    assertLocationUpdated(newSearchText);
  });

  it('typing a new location and not selecting a result does not update the search', () => {
    const newSearchText = 'hobart';
    searchForLocation(newSearchText);
    cy.get('body').click({ force: true });
    cy.findByTestId(LOCATION_INPUT).should('have.attr', 'placeholder', defaultLocation);
  });
});

describe('back navigation', () => {
  beforeEach(() => cy.visitEmptySearch());

  it('updates the location input', () => {
    const newSearchText = 'hobart';
    searchForLocationAndSelect(newSearchText);
    assertLocationUpdated(newSearchText);
    cy.go('back');
    assertLocationUpdated(defaultLocation);
  });
});
