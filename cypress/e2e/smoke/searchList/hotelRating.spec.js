import { testId } from '../../../support/helpers';

describe('hotel rating', { testIsolation: false }, () => {
  const QUERY_KEY = 'minStarRating';
  const HOTEL_RATING_FILTER = `${testId('hotel-rating-filter')} input[type="radio"][name="${QUERY_KEY}"]`;

  const clickAndWait = (ratingValue) => {
    cy.setupWaitSearchResults(`${QUERY_KEY}=${ratingValue}`);
    cy.get(HOTEL_RATING_FILTER).filter(`[value="${ratingValue}"]`).first().parent().click();
    cy.waitSearchResults();
  };

  before(() => cy.visitEmptySearch());

  it('defaults to all', () => {
    cy.get(HOTEL_RATING_FILTER).filter(':checked').should('have.value', '');
    cy.assertQueryParam((params) => expect(params).to.not.have.property(QUERY_KEY));
  });

  ['3', '4', '5'].forEach((rating) => {
    describe(`when rating ${rating}`, () => {
      before(() => {
        clickAndWait(rating);
      });

      it('updates the search', () => {
        cy.assertRoutedToQueryParam(QUERY_KEY, rating);
        cy.get(HOTEL_RATING_FILTER).filter(':checked').should('have.value', rating);
      });
    });
  });
});
