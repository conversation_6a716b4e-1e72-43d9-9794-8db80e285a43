import { testId } from '../../../support/helpers';

describe('pagination', () => {
  const PREVIOUS_LINK = `${testId('pagination')} ${testId('prev-link')}`;
  const NEXT_LINK = `${testId('pagination')} ${testId('next-link')}`;
  const PAGE_LINK = `${testId('pagination')} ${testId('page-link')}`;
  const ACTIVE_PAGE_LINK = `${testId('pagination')} ${testId('active-page')}`;

  const assertActivePageNumber = (pageNumber) => {
    cy.get(ACTIVE_PAGE_LINK).should('have.text', pageNumber);
  };

  beforeEach(() => {
    cy.visitEmptySearch();
  });

  it('starts at page 1', () => {
    assertActivePageNumber('1');
  });

  describe('when clicking page 2 link', () => {
    beforeEach(() => {
      cy.setupWaitSearchResults();
      cy.contains(PAGE_LINK, '2').click();
      cy.waitSearchResults();
    });

    it('loads results for page 2', () => {
      cy.assertRoutedToQueryParam('page', '2');
      assertActivePageNumber('2');
    });
  });

  describe('when clicking next link', () => {
    beforeEach(() => {
      cy.setupWaitSearchResults();
      cy.get(NEXT_LINK).click();
      cy.waitSearchResults();
    });

    it('loads next page', () => {
      cy.assertRoutedToQueryParam('page', '2');
      assertActivePageNumber('2');
    });
  });

  describe('when clicking previous link', () => {
    beforeEach(() => {
      cy.setupWaitSearchResults();
      cy.contains(PAGE_LINK, '2').click();
      cy.waitSearchResults();
      cy.get(PREVIOUS_LINK).click();
      cy.waitSearchResults();
    });

    it('loads previous page', () => {
      cy.assertRoutedToQueryParam('page', '1');
      assertActivePageNumber('1');
    });
  });
});
