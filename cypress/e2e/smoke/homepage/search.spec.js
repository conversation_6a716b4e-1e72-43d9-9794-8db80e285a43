import { testId, clickTimes } from '../../../support/helpers';
import { QUERY_DATE_FORMAT } from '../../../support/commonParams';
import { HOTELS_PATH } from 'config/env';
import { format, addDays, startOfDay, differenceInCalendarMonths } from 'date-fns';
//import { AIRBNB_INTERSTITIAL_PAGE_URL } from 'config';

const MONTH_TITLE_FORMAT = 'MMM yyyy';
const LOCATION_INPUT = 'location-search-input';
const LOCATION_RESULT = 'location-search-result';
const DATES_INPUT = testId('select-date-input');
const DATE_RANGE_PICKER = `${testId('stay-date-picker')} ${testId('date-range-picker')}`;
const GO_BACK_1_MONTH = `${DATE_RANGE_PICKER} ${testId('go-back-1-month')}`;
const GO_FORWARD_1_MONTH = `${DATE_RANGE_PICKER} ${testId('go-forward-1-month')}`;
const CALENDAR_MONTH = `${DATE_RANGE_PICKER} ${testId('calendar-month')}`;
const DONE_BUTTON = `${testId('stay-date-picker')} ${testId('done-button')}`;
const HOTELS_TAB = testId('hotels-panel');
const AIRBNB_TAB = testId('airbnb-panel');
const OCCUPANT_PICKER_INPUT = `${HOTELS_TAB} ${testId('occupant-picker-input')}`;
const OCCUPANT_PICKER_CHILDREN = testId('occupant-picker-children');
const OCCUPANT_INCREMENT = testId('increment');
const CHILDREN_INCREMENT = `${OCCUPANT_PICKER_CHILDREN} ${OCCUPANT_INCREMENT}`;
const DONE = testId('done-button');

const searchForLocation = (searchText) => {
  const locationKey = `search-locations-${searchText}`;
  const propertiesKey = `search-properties-${searchText}`;

  cy.intercept(`${HOTELS_PATH}/api/ui/locations?name=${searchText}`).as(locationKey);
  cy.intercept(`${HOTELS_PATH}/api/ui/properties?name=${searchText}`).as(propertiesKey);
  cy.findAllByTestId(LOCATION_INPUT)
    .first()
    .click()
    .should('have.value', '')
    .type(searchText)
    .wait([`@${locationKey}`, `@${propertiesKey}`]);
};

const searchForAirbnbLocation = (searchText) => {
  const locationKey = `search-locations-${searchText}`;

  cy.intercept(`${HOTELS_PATH}/api/ui/locations?name=${searchText}`).as(locationKey);
  cy.findAllByTestId(LOCATION_INPUT)
    .last()
    .click()
    .should('have.value', '')
    .type(searchText)
    .wait([`@${locationKey}`]);
};

const assertLocationUpdated = (value) => {
  cy.findByTestId(LOCATION_INPUT).should(($el) => expect($el.attr('placeholder')).to.match(new RegExp(value, 'i')));
  cy.get('title').should(($el) => expect($el.text()).to.match(new RegExp(value, 'i')));
};

const scrollMonthIntoView = (date) => {
  cy.get(CALENDAR_MONTH).then(($el) => {
    const leftMostDate = new Date($el.attr('data-date'));
    const diff = differenceInCalendarMonths(date, leftMostDate);
    if (diff > 0) {
      clickTimes(cy.get(GO_FORWARD_1_MONTH), diff);
    }

    if (diff < 0) {
      clickTimes(cy.get(GO_BACK_1_MONTH), -diff);
    }
  });
};

const clickCalendarDay = (date) => {
  scrollMonthIntoView(date);
  const month = format(date, MONTH_TITLE_FORMAT);
  const day = format(date, 'd');
  cy.get(DATE_RANGE_PICKER).contains(month).parent().contains('button', day).click();
};

const selectDatesWithMouse = (checkIn, checkOut) => {
  cy.log(`Selecting dates ${format(checkIn, QUERY_DATE_FORMAT)} to ${format(checkOut, QUERY_DATE_FORMAT)}`);
  clickCalendarDay(checkIn);
  clickCalendarDay(checkOut);
  cy.get(DONE_BUTTON).click();
};

describe('homepage search', () => {
  beforeEach(() => {
    cy.visitHomepage();
  });

  it('navigates to the search results page', () => {
    const newSearchText = 'hobart';
    const newLocation = 'Hobart, TAS, Australia';
    cy.setupWaitSearchResults();

    // Set the search location to hobart
    searchForLocation(newSearchText);
    cy.findAllByTestId(LOCATION_RESULT).should('have.length.greaterThan', 5).contains(newLocation).click();
    cy.findAllByTestId(LOCATION_INPUT).first().should('have.attr', 'placeholder', newLocation);

    // Set the check in and check out dates
    const checkInDate = startOfDay(addDays(Date.now(), 10));
    const checkOutDate = startOfDay(addDays(Date.now(), 20));
    cy.get(DATES_INPUT).first().click();
    selectDatesWithMouse(checkInDate, checkOutDate);

    // Add 2 children to the stay
    cy.get(OCCUPANT_PICKER_INPUT).last().click();
    clickTimes(cy.get(CHILDREN_INCREMENT), 3);
    cy.get(DONE).click();

    // Search for hobart hotels...
    cy.findByTestId('search-hotels-cta').invoke('removeAttr', 'target').click();
    cy.waitSearchResults();

    cy.assertRoutedToQueryParam('location', newLocation);
    assertLocationUpdated(newSearchText);

    cy.assertRoutedToQueryParam('checkIn', format(checkInDate, QUERY_DATE_FORMAT));
    cy.assertRoutedToQueryParam('checkOut', format(checkOutDate, QUERY_DATE_FORMAT));

    cy.assertRoutedToQueryParam('adults', '2');
    cy.assertRoutedToQueryParam('children', '3');
  });

  it('navigates to the property page', () => {
    const newSearchText = 'hilton-sydney';
    const newLocation = 'Hilton Sydney, Sydney, Australia';
    cy.setupWaitPropertyPage();

    // Set the search location to the Hilton Sydney
    searchForLocation(newSearchText);
    cy.findAllByTestId(LOCATION_RESULT).should('have.length.greaterThan', 1).contains(newLocation).click();
    cy.findAllByTestId(LOCATION_INPUT).first().should('have.attr', 'placeholder', newLocation);

    // Set the check in and check out dates
    const checkInDate = startOfDay(addDays(Date.now(), 10));
    const checkOutDate = startOfDay(addDays(Date.now(), 20));
    cy.get(DATES_INPUT).first().click();
    selectDatesWithMouse(checkInDate, checkOutDate);

    // Add 2 children to the stay
    cy.get(OCCUPANT_PICKER_INPUT).last().click();
    clickTimes(cy.get(CHILDREN_INCREMENT), 3);
    cy.get(DONE).click();

    // Search for offers in the Hilton Sydney...
    cy.findByTestId('search-hotels-cta').click();
    cy.waitPropertyPage();

    cy.location().should((location) => {
      expect(location.pathname).to.eq('/au/en/hotels/properties/109435');
    });

    cy.assertRoutedToQueryParam('checkIn', format(checkInDate, QUERY_DATE_FORMAT));
    cy.assertRoutedToQueryParam('checkOut', format(checkOutDate, QUERY_DATE_FORMAT));

    cy.assertRoutedToQueryParam('adults', '2');
    cy.assertRoutedToQueryParam('children', '3');
  });

  it('navigates to the airbnb page', () => {
    const newSearchText = 'hobart';
    const newLocation = 'Hobart, TAS, Australia';

    cy.findByTestId('airbnb-tab').click();

    // Set the search location to hobart
    searchForAirbnbLocation(newSearchText);
    cy.findAllByTestId(LOCATION_RESULT).should('have.length.greaterThan', 5).contains(newLocation).click();
    cy.findAllByTestId(LOCATION_INPUT).last().should('have.attr', 'placeholder', newLocation);

    // Set the check in and check out dates
    const checkInDate = startOfDay(addDays(Date.now(), 10));
    const checkOutDate = startOfDay(addDays(Date.now(), 20));
    cy.get(DATES_INPUT).last().click();
    selectDatesWithMouse(checkInDate, checkOutDate);

    // Add 2 children to the stay
    const AIRBNB_OCCUPANT_INPUT = `${AIRBNB_TAB} ${testId('occupant-picker-input')}`;
    cy.get(AIRBNB_OCCUPANT_INPUT).last().click();
    clickTimes(cy.get(CHILDREN_INCREMENT), 3);
    cy.get(DONE).click();

    // const checkin = format(checkInDate, QUERY_DATE_FORMAT);
    // const checkout = format(checkOutDate, QUERY_DATE_FORMAT);

    cy.findByTestId('join-for-free-cta').click();
    //TODO: Fix once login issue have been resolved
    // cy.enterLoginDetails();

    // cy.findByTestId('search-airbnb-cta').click(
    //   'area-label',
    //   'href',
    //   `${AIRBNB_INTERSTITIAL_PAGE_URL}?checkin=${checkin}&checkout=${checkout}&guests=5&location=Hobart%2C%20TAS%2C%20Australia`,
    // );
  });
});
