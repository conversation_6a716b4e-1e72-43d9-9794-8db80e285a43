import { testId } from '../../../support/helpers';
import { PRIVACY_AND_SECURITY_URL, TERMS_OF_USE_URL } from 'config/brand';

describe('homepage', () => {
  beforeEach(() => {
    cy.visitHomepage();
  });

  it.skip('renders the simplicity menu', () => {
    cy.findAllByTestId('qantas-universal-nav-wrapper').first().should('exist');
  });

  it.skip('renders the navigation menu', () => {
    cy.findAllByTestId('hotels-link')
      .first()
      .should('have.attr', 'href')
      .and('match', /hotels\/search\/list/);

    cy.findAllByTestId('destinations-desktop-link').first().should('have.attr', 'href', '/hotels/australia');

    cy.findAllByTestId('deals-link')
      .first()
      .should('have.attr', 'href')
      .and('match', /hotels\/deals/);

    cy.findAllByTestId('luxe-link')
      .first()
      .should('have.attr', 'href')
      .and('match', /holidays\/deals/);
  });

  it('renders the carousel of featured campaigns', () => {
    cy.findAllByTestId('carousel-item').should('have.length', 2);
    cy.findAllByTestId('carousel-item').eq(0).should('have.attr', 'aria-hidden', 'false');
    cy.findAllByTestId('carousel-item').eq(1).should('have.attr', 'aria-hidden', 'true');

    cy.findByTestId('next-carousel-btn').click();
    cy.findAllByTestId('carousel-item').eq(0).should('have.attr', 'aria-hidden', 'true');
    cy.findAllByTestId('carousel-item').eq(1).should('have.attr', 'aria-hidden', 'false');

    cy.findByTestId('prev-carousel-btn').click();
    cy.findAllByTestId('carousel-item').eq(0).should('have.attr', 'aria-hidden', 'false');
    cy.findAllByTestId('carousel-item').eq(1).should('have.attr', 'aria-hidden', 'true');

    cy.findAllByTestId('breadcrumb-carousel-btn').eq(1).click();
    cy.findAllByTestId('carousel-item').eq(0).should('have.attr', 'aria-hidden', 'true');
    cy.findAllByTestId('carousel-item').eq(1).should('have.attr', 'aria-hidden', 'false');
  });

  it('renders the list of regions', () => {
    cy.findByTestId('list-regions-title').should('exist');
    cy.findAllByTestId('region-link').should('have.length.greaterThan', 1);
  });

  it('renders the value propositions', () => {
    cy.findAllByTestId('slimline-layout').should('have.length', 4);
  });

  it('renders the offers list', () => {
    cy.findAllByTestId('offer-link').should('have.length', 3);
  });

  it('renders the destination links', () => {
    cy.findByTestId('popular-destination-title').should('have.text', 'Find accommodation in popular destinations');
    const regionLinks = '[data-testid="popular-destination-footer"] [data-testid="region-links-header"]';
    cy.get(regionLinks).should('have.length', 7);
    // cy.get(regionLinks).eq(0).should('have.text', 'South Australia');
    // cy.get(regionLinks).eq(1).should('have.text', 'Victoria');
    // cy.get(regionLinks).eq(2).should('have.text', 'Queensland');
    // cy.get(regionLinks).eq(3).should('have.text', 'New South Wales');
    // cy.get(regionLinks).eq(4).should('have.text', 'Tasmania');
    // cy.get(regionLinks).eq(5).should('have.text', 'Northern Territory');
    // cy.get(regionLinks).eq(6).should('have.text', 'Western Australia');
  });

  describe.skip('mobile', () => {
    before(() => {
      cy.viewport('iphone-x');
      cy.findByTestId('menu-button').click();
    });

    it.skip('renders the simplicity menu', () => {
      cy.get(`${testId('phone-menus')} ${testId('qantas-universal-nav-wrapper')}`)
        .first()
        .should('exist');
    });

    it.skip('renders the navigation menu', () => {
      cy.get(`${testId('phone-menus')} ${testId('manage-bookings-link')}`)
        .first()
        .should('have.attr', 'href')
        .and('match', /hotels\/manage\/bookings/);

      cy.get(`${testId('phone-menus')} ${testId('hotels-link')}`)
        .first()
        .should('have.attr', 'href')
        .and('match', /hotels\/search\/list/);

      cy.get(`${testId('phone-menus')} ${testId('destinations-mobile-link')}`)
        .first()
        .should('have.attr', 'href', '/hotels/australia');

      cy.get(`${testId('phone-menus')} ${testId('deals-link')}`)
        .first()
        .should('have.attr', 'href')
        .and('match', /hotels\/deals/);

      cy.get(`${testId('phone-menus')} ${testId('luxe-link')}`)
        .first()
        .should('have.attr', 'href')
        .and('match', /holidays\/deals/);

      cy.get(`${testId('phone-menus')} ${testId('phone-contact-link')}`)
        .first()
        .should('have.attr', 'href')
        .and('match', /hotels\/contact-us/);

      cy.get(`${testId('phone-menus')} ${testId('phone-privacy-security-link')}`)
        .first()
        .should('have.attr', 'href', PRIVACY_AND_SECURITY_URL);

      cy.get(`${testId('phone-menus')} ${testId('phone-term-of-use-link')}`)
        .first()
        .should('have.attr', 'href', TERMS_OF_USE_URL);

      cy.get(`${testId('phone-menus')} ${testId('phone-faqs-link')}`)
        .first()
        .should('have.attr', 'href')
        .and('match', /hotels\/faqs/);
    });
  });
});
