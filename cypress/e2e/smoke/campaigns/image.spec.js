import { CAMPAIGNS_ENABLED } from 'config/flags';

if (CAMPAIGNS_ENABLED) {
  describe('Campaigns Image section', () => {
    before(() => {
      cy.visitCampaignPage();
    });

    it('renders the image', () => {
      cy.get('#content').find('img').eq(3).should('have.attr', 'alt', 'Balloon');
    });
    it('renders the image caption', () => {
      cy.get('#content').find('span').eq(15).should('have.text', 'Balloon');
    });
  });
}
