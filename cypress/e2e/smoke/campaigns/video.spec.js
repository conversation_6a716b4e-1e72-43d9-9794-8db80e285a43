import { CAMPAIGNS_ENABLED } from 'config/flags';

if (CAMPAIGNS_ENABLED) {
  describe('Campaigns Video section', () => {
    before(() => {
      cy.visitCampaignPage();
    });

    it('renders an embedded video', () => {
      cy.get('#content').find('iframe').should('exist');
      cy.get('#content')
        .find('iframe')
        .should('have.attr', 'src')
        .and('match', /https:\/\/www.youtube.com\/embed\/fsQiaQhw_IM/);
    });
  });
}
