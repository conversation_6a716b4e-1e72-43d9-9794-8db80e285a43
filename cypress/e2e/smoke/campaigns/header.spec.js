import { CAMPAIGNS_ENABLED } from 'config/flags';

if (CAMPAIGNS_ENABLED) {
  describe('Campaigns Header Section', () => {
    before(() => {
      cy.visitCampaignPage();
    });

    it('renders the hero image', () => {
      cy.findByLabelText('Qantas image').should('exist');
    });

    it('renders the heading', () => {
      cy.get('#content').find('h1').eq(0).should('have.text', 'CYPRESS OFFER');
    });

    it('renders the summary', () => {
      cy.get('#content').find('span').eq(0).should('have.text', 'Ut scelerisque ornare tristique.');
    });

    it('renders the brand logo', () => {
      cy.get('#content').find('img').eq(0).should('have.attr', 'alt', 'Plane logo');
    });
  });
}
