import { CAMPAIGNS_ENABLED } from 'config/flags';

if (CAMPAIGNS_ENABLED) {
  describe('Campaigns Image List section', () => {
    before(() => {
      cy.visitCampaignPage();
    });

    it('renders the title', () => {
      cy.get('#content').find('h2').eq(2).should('have.text', 'Image List');
    });

    it('collapsible is true', () => {
      cy.findByTestId('collapsible-content').should('exist');
      cy.findByTestId('collapsible-action-button').should('have.text', 'Show more ');
    });

    it('renders a set of images', () => {
      cy.findByTestId('collapsible-content').find('img').should('have.length', 4);
    });

    it('renders an image', () => {
      cy.get('#content').find('img').eq(4).should('have.attr', 'alt', 'Pool and beach');
    });
  });
}
