import { CAMPAIGNS_ENABLED } from 'config/flags';

if (CAMPAIGNS_ENABLED) {
  describe('Campaigns Image With Text section', () => {
    before(() => {
      cy.visitCampaignPage();
    });

    it('renders the image', () => {
      cy.get('#content').find('img').eq(11).should('have.attr', 'alt', 'Park');
    });

    it('renders the content', () => {
      cy.get('#content').find('span').eq(16).should('have.text', 'Fun in the park.');
    });

    describe('cta', () => {
      it('renders the cta', () => {
        cy.get('#content').find('a').eq(10).should('have.text', 'Here ');
      });

      it('cta opens in a new tab', () => {
        cy.get('#content').find('a').eq(10).should('have.attr', 'target', '_blank');
        cy.get('#content').find('a').eq(10).should('have.attr', 'rel', 'noopener noreferrer');
      });
    });
  });
}
