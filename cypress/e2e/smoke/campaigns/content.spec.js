import { CAMPAIGNS_ENABLED } from 'config/flags';

if (CAMPAIGNS_ENABLED) {
  describe('Campaigns Content section', () => {
    before(() => {
      cy.visitCampaignPage();
    });

    it('renders the heading', () => {
      cy.get('#content').find('h1').eq(1).should('have.text', 'A little info');
    });

    it('renders the content', () => {
      cy.get('#content').find('span').eq(1).should('have.text', 'Lorem ipsum dolor sit amet.');
      cy.get('#content').find('span').eq(2).should('have.text', 'Integer iaculis semper metus mattis scelerisque.');
    });

    it('renders the cta', () => {
      cy.get('#content').find('a').eq(2).should('have.text', 'A little more info ');
    });

    it('renders the disclaimer title', () => {
      cy.get('#content').find('span>strong').eq(0).should('have.text', 'General terms & conditions');
    });

    it('renders the disclaimer', () => {
      cy.get('#content').find('span').eq(22).should('have.text', 'Donec malesuada risus id neque bibendum porta.');
    });
  });
}
