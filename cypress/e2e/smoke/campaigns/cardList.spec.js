import { CAMPAIGNS_ENABLED } from 'config/flags';

if (CAMPAIGNS_ENABLED) {
  describe('Campaigns Card List section', () => {
    before(() => {
      cy.visitCampaignPage();
    });

    it('renders the heading', () => {
      cy.get('#content').find('h1').eq(3).should('have.text', 'Properties');
    });

    describe('renders a card', () => {
      it('renders the image', () => {
        cy.get('#content').find('img').eq(1).should('have.attr', 'alt', 'Bathroom');
      });

      it('renders the heading', () => {
        cy.get('#content').find('h2').eq(0).should('have.text', 'Great bathroom');
      });

      it('renders the summary', () => {
        cy.get('#content').find('span').eq(5).should('have.text', 'Great bathroom.');
      });

      it('renders the cta', () => {
        cy.get('#content').find('a').eq(5).should('have.text', 'More about the bathroom ');
      });
    });
  });
}
