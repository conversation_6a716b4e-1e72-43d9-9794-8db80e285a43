import { CAMPAIGNS_ENABLED } from 'config/flags';

if (CAMPAIGNS_ENABLED) {
  describe('Campaigns Campaign Banner section', () => {
    before(() => {
      cy.visitCampaignPage();
    });

    it('renders the heading', () => {
      cy.get('#content').find('h1').eq(2).should('have.text', 'Campaign Banner');
    });

    it('renders the end date', () => {
      cy.get('#content').find('span').eq(3).should('have.text', 'SALE ENDS April 27 2022');
    });

    it('renders the description', () => {
      cy.get('#content').find('span').eq(4).should('have.text', 'Nunc rhoncus justo faucibus magna varius, eu tincidunt diam interdum.');
    });

    it('renders the hero image', () => {
      cy.findByLabelText('Pool image').should('exist');
    });

    it('renders the cta', () => {
      cy.get('#content').find('a').eq(3).should('have.text', 'This campaign ');
    });
  });
}
