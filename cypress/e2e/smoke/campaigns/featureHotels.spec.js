import { CAMPAIGNS_ENABLED } from 'config/flags';

if (CAMPAIGNS_ENABLED) {
  describe('Campaigns Feature Hotels section', () => {
    before(() => {
      cy.visitCampaignPage();
    });

    it('renders the heading', () => {
      cy.get('#content').find('h1').eq(4).should('have.text', 'More Properties');
    });

    describe('it renders a property', () => {
      it('renders the promotion name', () => {
        cy.get('#content').find('span').eq(7).should('have.text', 'Hunter Valley');
      });

      it('renders the property', () => {
        cy.get('#content').find('h3').eq(0).should('have.text', 'Sofitel Sydney Darling Harbour ');
      });

      it('renders the content', () => {
        cy.get('#content').find('span').eq(10).should('have.text', 'Sofitel magic.');
      });

      it('renders the cta', () => {
        cy.get('#content').find('a').eq(7).should('have.text', 'About this ');
      });

      it('renders the disclaimers link', () => {
        cy.get('#content').find('span>strong').eq(2).should('have.text', 'Sofitel Sydney Darling Harbour ');
      });

      it('renders the disclaimers', () => {
        cy.get('#content')
          .find('span')
          .eq(26)
          .should(
            'have.text',
            'Integer iaculis semper metus mattis scelerisque. Nunc rhoncus justo faucibus magna varius, eu tincidunt diam interdum.',
          );
        cy.get('#content').find('a').eq(8).should('have.text', 'Conditions apply');
      });
    });
  });
}
