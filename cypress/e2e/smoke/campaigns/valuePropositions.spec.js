import { CAMPAIGNS_ENABLED } from 'config/flags';

if (CAMPAIGNS_ENABLED) {
  describe('Campaigns Value Propositions section', () => {
    before(() => {
      cy.visitCampaignPage();
    });

    it('renders the selected layout style', () => {
      cy.findAllByTestId('slimline-layout').should('have.length', 3);
    });

    describe('renders a value proposition', () => {
      it('renders the image', () => {
        cy.get('#content').find('img').eq(8).should('have.attr', 'alt', 'Qantas Roo Logo');
      });

      it('renders the title', () => {
        cy.get('#content').find('p').eq(0).should('have.text', 'Earn 3 Qantas Points per $1');
      });

      describe('renders the terms and conditions', () => {
        it('renders the title', () => {
          cy.get('#content').find('span>strong').eq(3).should('have.text', 'Earn 3 Qantas Points per $1 T&Cs');
        });

        it('renders the disclaimers', () => {
          cy.get('#content').find('span').eq(28).should('have.text', 'Earn 3 Qantas Points per $1 Disclaimers 1');
        });
      });
    });
  });
}
