import {
  DOMESTIC_PHONE_NUMBER,
  HOTELS_PATH,
  INTERNATIONAL_PHONE_NUMBER,
  LIVE_CHAT_OPERATING_TIMES,
  PHONE_SUPPORT_OPERATING_TIMES,
  SELF_SERVICE_ENABLED,
} from 'config';

describe('Contact us page', () => {
  beforeEach(() => {
    cy.visit(`${HOTELS_PATH}/contact-us`);
  });

  it('has the title', () => {
    cy.findByTestId('title').should('exist');
  });

  it('has the correct heading', () => {
    cy.findByTestId('recommended-links-heading').should('exist');
  });

  describe('Contact-us links', () => {
    if (SELF_SERVICE_ENABLED) {
      describe('Change your booking block', () => {
        it('has the manage your booking link', () => {
          cy.findByTestId('manage-your-booking-link').should('exist');
        });
      });
    }

    if (SELF_SERVICE_ENABLED) {
      describe('Cancel your booking block', () => {
        it('has the cancel your booking link', () => {
          cy.findByTestId('cancel-your-booking-link').should('exist');
        });
      });
    }

    describe('Live chat block', { testIsolation: false }, () => {
      it('has the live chat opening times', () => {
        cy.findByTestId('live-chat-opening-hours').should('have.text', LIVE_CHAT_OPERATING_TIMES);
      });

      it('has the live chat opening days', () => {
        cy.findByTestId('opening-days').should('have.text', 'Monday to Friday');
      });

      it('has the live chat link badge', () => {
        cy.findByTestId('live-chat-available-badge').should('exist');
      });

      it('has the busy or unavailable live chat badge', () => {
        cy.get('#liveChatOffline').should('exist');
      });
    });

    describe('Call us block', { testIsolation: false }, () => {
      it('has the opening hours', () => {
        cy.findByTestId('phone-support-opening-hours').should('have.text', PHONE_SUPPORT_OPERATING_TIMES);
      });

      it('has the domestic phone number (desktop)', () => {
        cy.findByTestId('domestic-number-desktop').should('have.text', DOMESTIC_PHONE_NUMBER);
      });

      it('has the domestic phone number (mobile)', () => {
        cy.findByTestId('domestic-number-mobile').should('have.text', DOMESTIC_PHONE_NUMBER);
      });

      it('has the international phone number', () => {
        cy.findByTestId('intl-number').should('have.text', INTERNATIONAL_PHONE_NUMBER);
      });
    });

    describe('Email us block', () => {
      it('has the email us link', () => {
        cy.findByTestId('email-support-link').should('exist');
      });
    });

    if (SELF_SERVICE_ENABLED) {
      describe('Find your booking block', () => {
        it('has the view your booking link', () => {
          cy.findByTestId('view-your-booking-link').should('exist');
        });
      });
    }

    describe('Partner services block', () => {
      it('has email link', () => {
        cy.findByTestId('partner-services-email-link').within(() => {
          cy.findByRole('link').should('have.attr', 'href', `${HOTELS_PATH}/contact-us/email?enquiryType=supplier`);
        });
      });
    });
  });
});
