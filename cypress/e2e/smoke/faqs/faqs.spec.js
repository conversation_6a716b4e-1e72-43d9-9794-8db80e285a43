import { HOTELS_PATH } from 'config/env';
import { testId } from '../../../support/selectors';

describe('faqs page', { testIsolation: false }, () => {
  before(() => {
    cy.visit(`${HOTELS_PATH}/faqs`);
  });

  it('has the title', () => {
    cy.findByText(/Frequently Asked Questions/i).should('exist');
  });

  describe('Q&As links block', () => {
    before(() => {
      cy.visit(`${HOTELS_PATH}/faqs`);
    });

    it('has the title', () => {
      cy.findByText(/Popular topics/i).should('exist');
    });

    it('has the "Booking Questions" link', () => {
      cy.get(testId('booking-questions')).should('exist');
    });

    it('has the "Qantas Frequent Flyer Customers" link', () => {
      cy.get(testId('qantas-frequent-flyer-customers')).should('exist');
    });

    it('has the "Payments" link', () => {
      cy.get(testId('payments')).should('exist');
    });

    it('has the "Changes and cancellations" link', () => {
      cy.get(testId('changes-and-cancellations')).should('exist');
    });

    it('has the "General Enquiries and Feedback" link', () => {
      cy.get(testId('general-enquiries-and-feedback')).should('exist');
    });

    it('has the "Vouchers" link', () => {
      cy.get(testId('vouchers')).should('exist');
    });
  });

  describe('questions blocks', () => {
    it('has the "Booking Questions" block', () => {
      cy.get('#booking-questions').should('exist');
    });

    it('has the "Qantas Frequent Flyer Customers" question block', () => {
      cy.get('#qantas-frequent-flyer-customers').should('exist');
    });

    it('has the "Payments" question block', () => {
      cy.get('#payments').should('exist');
    });

    it('has the "Changes and cancellations" question block', () => {
      cy.get('#changes-and-cancellations').should('exist');
    });

    it('has the "General Enquires and Feedback" question block', () => {
      cy.get('#general-enquiries-and-feedback').should('exist');
    });

    it('has the "Vouchers" question block', () => {
      cy.get('#vouchers').should('exist');
    });
  });

  describe('question and answers', () => {
    it('initially displays collapsed content', () => {
      cy.get(testId('faqs-answer-block')).should('not.be.visible');
    });

    it('clicks the first question accordion and displays the answer', () => {
      cy.get(testId('question-answer-block')).first().click();
      cy.get(testId('faqs-answer-block')).first().should('be.visible');
    });
  });
});
