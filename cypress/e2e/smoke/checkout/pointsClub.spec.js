const textToFloat = (text) => parseFloat(text.replace(/([^\d.])/g, ''), 10);
const QFF_NUMBER = Cypress.env('POINTSCLUB_ACCOUNT_NUMBER');
const CC_NUMBER = '4111 1111 1111 1111'; // This number will always be accepted by the MasterCard staging environment

// For running points club tests in dev, ensure that you are connected to the VPN and the SSO host on qantas-hotels-api is https://api.services-stg.qantasloyalty.com

xdescribe('points club', () => {
  beforeEach(() => {
    cy.setupWaitCheckoutPage();
    cy.visitCheckoutWithStayAttributes();
  });

  it('can login and see increased points club values', () => {
    let initialPointsAmount;

    cy.findByTestId('personal-details-form').within(() => {
      cy.findByLabelText(/title/i).select('Mr');
      cy.findByLabelText(/first name/i).type('Percy');
      cy.findByLabelText(/last name/i).type('Points');
      cy.findByLabelText(/email address/i).type('<EMAIL>');
      cy.findByLabelText(/phone number/i).type('0404 123 456');
      cy.findByText(/continue/i).click();
    });

    cy.findByTestId('qantas-points-form').within(() => {
      cy.findByText(/Are you a Points Club member?/i).should('exist');
      cy.findByText(/Login now/i).click();
    });

    cy.findAllByTestId('total-qantas-points')
      .first()
      .invoke('text')
      .then((totalQantasPoints) => {
        initialPointsAmount = textToFloat(totalQantasPoints);
      });

    cy.enterPointsClubLoginDetails();

    cy.waitCheckoutPage();

    cy.findAllByTestId('total-qantas-points')
      .first()
      .invoke('text')
      .then((totalQantasPoints) => {
        expect(textToFloat(totalQantasPoints)).to.be.greaterThan(initialPointsAmount);
      });
  });

  it('warns if the QFF number is changed', () => {
    cy.findByTestId('personal-details-form').within(() => {
      cy.findByLabelText(/title/i).select('Mr');
      cy.findByLabelText(/first name/i).type('Percy');
      cy.findByLabelText(/last name/i).type('Points');
      cy.findByLabelText(/email address/i).type('<EMAIL>');
      cy.findByLabelText(/phone number/i).type('0404 123 456');
      cy.findByText(/continue/i).click();
    });

    cy.findByTestId('qantas-points-form').within(() => {
      cy.findByText(/Login now/i).click();
    });

    cy.enterPointsClubLoginDetails();
    cy.waitCheckoutPage();

    cy.findByTestId('qantas-points-form').within(() => {
      cy.findByTestId('qff-points-club-notice').should('not.exist');

      cy.findByLabelText(/Qantas Frequent Flyer number/i).type('123');
      cy.findByTestId('qff-points-club-notice').should('exist');

      cy.findByLabelText(/Qantas Frequent Flyer number/i).clear();
      cy.findByLabelText(/Qantas Frequent Flyer number/i).type(QFF_NUMBER);
      cy.findByTestId('qff-points-club-notice').should('not.exist');
    });
  });

  it('displays the points club summary in a points club booking confirmation', () => {
    let totalPointsAtCheckout;

    cy.findByTestId('personal-details-form').within(() => {
      cy.findByLabelText(/title/i).select('Mr');
      cy.findByLabelText(/first name/i).type('Percy');
      cy.findByLabelText(/last name/i).type('Points');
      cy.findByLabelText(/email address/i).type('<EMAIL>');
      cy.findByLabelText(/phone number/i).type('0404 123 456');
      cy.findByText(/continue/i).click();
    });

    cy.findByTestId('qantas-points-form').within(() => {
      cy.findByText(/Login now/i).click();
    });

    cy.enterPointsClubLoginDetails();

    cy.waitCheckoutPage();

    cy.findByTestId('qantas-points-form').within(() => {
      cy.findAllByTestId('total-qantas-points')
        .first()
        .invoke('text')
        .then((totalQantasPoints) => {
          totalPointsAtCheckout = textToFloat(totalQantasPoints);
        });
      cy.findByText(/continue/i).click();
    });

    cy.findByTestId('payment-options-form').within(() => {
      cy.findByText(/continue/i).click();
    });

    cy.findByTestId('confirm-and-pay-form').within(() => {
      cy.enterIframe('.gw-proxy-number', () => cy.get('input').type(CC_NUMBER));
      cy.enterIframe('.gw-proxy-securityCode', () => cy.get('input').type('111'));
      cy.findByLabelText(/name on card/i).type('Percy Points');
      cy.findByLabelText(/month/i).select('October');
      cy.findByLabelText(/year/i).select('2028');

      cy.get('[data-is-updating=false]').should('exist'); // delay the click until MasterCard session is resolved
      cy.findByLabelText(/I agree/i).check();
      cy.findByText('Pay Now').click();
    });

    cy.location('href', { timeout: 30000 }).should('match', /\/bookings\//);

    cy.findByTestId('points-summary').within(() => {
      cy.findAllByTestId('total_qff_points_earned')
        .first()
        .invoke('text')
        .then((totalQantasPoints) => {
          expect(textToFloat(totalQantasPoints)).to.equal(totalPointsAtCheckout);
        });

      cy.findByText(/Points Club/i).should('exist');
    });
  });
});
