import { CLASSIC_OFFERS_ENABLED } from 'config/flags';

if (CLASSIC_OFFERS_ENABLED) {
  describe('classic bookings', () => {
    before(() => {
      cy.visitCheckoutForClassic();
    });

    it('can make a classic booking', () => {
      cy.findByTestId('payment-options-form').should('not.exist');
      cy.findByTestId('qantas-points-form').should('not.exist');

      cy.findByText("Let's login").click();
      cy.enterLoginDetails();

      cy.findByTestId('personal-details-form').within(() => {
        cy.findByText(/continue/i).click();
      });

      cy.findByTestId('confirm-and-pay-form').within(() => {
        cy.findByLabelText(/I agree/i).check();
        cy.findByText('Pay Now').click();
      });

      cy.location('href', { timeout: 60000 }).should('match', /\/bookings\//);
      cy.findByText(/your booking has been confirmed/i).should('exist');
    });
  });

  describe.skip('classic bookings with oauth', () => {
    before(() => {
      cy.visitCheckoutForClassic();
    });

    it('can make a classic booking', () => {
      cy.findByTestId('payment-options-form').should('not.exist');
      cy.findByTestId('qantas-points-form').should('not.exist');

      cy.findByText("Let's login").click();
      cy.enterOAuthLoginDetails();

      cy.findByTestId('personal-details-form').within(() => {
        cy.findByText(/continue/i).click();
      });

      cy.findByTestId('confirm-and-pay-form').within(() => {
        cy.findByLabelText(/I agree/i).check();
        cy.findByText('Pay Now').click();
      });

      cy.location('href', { timeout: 60000 }).should('match', /\/bookings\//);
      cy.findByText(/your booking has been confirmed/i).should('exist');
    });
  });
}
