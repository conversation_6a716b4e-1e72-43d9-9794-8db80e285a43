import { POINTS_EARN_ENABLED, QFF_ACCOUNT_MANAGEMENT, ACCOUNT_MANAGEMENT_TYPES } from 'config/flags';
import { fillAdyenPaymentForm, submitPaymentOptionsForm, submitPersonalDetailsForm, submitQantasPointsForm } from './common';

(Cypress.env('ADYEN_TESTS_DISABLED') ? describe : describe)('Adyen checkout', () => {
  beforeEach(() => {
    cy.visitCheckoutWithStayAttributes();
  });

  it('can make a booking with cash', () => {
    submitPersonalDetailsForm();
    if (POINTS_EARN_ENABLED) {
      submitQantasPointsForm();
    }
    submitPaymentOptionsForm();
    fillAdyenPaymentForm();

    Cypress.on('uncaught:exception', () => {
      return false;
    });

    cy.findByTestId('confirm-and-pay-form').within(() => {
      cy.findByLabelText(/I agree/i).check();
      cy.findByTestId('payment-button').click();
    });

    cy.location('href').should('match', /\/bookings\//);
    cy.findByTestId('confirmation-header').should('exist');
  });

  if (QFF_ACCOUNT_MANAGEMENT === ACCOUNT_MANAGEMENT_TYPES.APP_WIDE) {
    it.skip('prefills your details when you login from the main header', () => {
      cy.findAllByTestId('qff-login-tab').first().click();
      cy.enterOAuthLoginDetails();

      cy.findByTestId('personal-details-form').within(() => {
        cy.findByLabelText(/title/i).should('have.value', 'Mr');
        cy.findByTestId('next-step-button').click();
      });

      if (POINTS_EARN_ENABLED) {
        cy.findByTestId('qantas-points-form').within(() => {
          cy.findByLabelText(/Qantas Frequent Flyer number/i).should('have.value', '**********');
        });
      }
    });

    it.skip('prefills your details when you login from the cta button', () => {
      cy.findAllByTestId('personal-details-login-cta').first().click();
      cy.enterOAuthLoginDetails();

      cy.findByTestId('personal-details-form').within(() => {
        cy.findByLabelText(/title/i).should('have.value', 'Mr');
      });
    });
  }
});
