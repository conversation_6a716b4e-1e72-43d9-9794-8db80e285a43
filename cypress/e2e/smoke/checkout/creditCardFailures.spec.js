import { POINTS_EARN_ENABLED } from 'config/flags';
import { fillAdyenPaymentForm, submitPaymentOptionsForm, submitPersonalDetailsForm, submitQantasPointsForm } from './common';

(Cypress.env('ADYEN_TESTS_DISABLED') ? describe : describe)('Adyen error codes', () => {
  beforeEach(() => {
    cy.visitCheckoutWithStayAttributes();

    submitPersonalDetailsForm();
    if (POINTS_EARN_ENABLED) {
      submitQantasPointsForm();
    }
    submitPaymentOptionsForm();
    fillAdyenPaymentForm();
  });

  // https://docs.adyen.com/development-resources/test-cards/result-code-testing/adyen-response-codes
  ['DECLINED', 'CARD_EXPIRED', 'NOT_ENOUGH_BALANCE'].forEach((code) => {
    it(code, () => {
      cy.findByTestId('confirm-and-pay-form').within(() => {
        cy.findByLabelText(/Name on card/i)
          .clear()
          .type(code);
        cy.findByLabelText(/I agree/i).check();
        cy.findByTestId('payment-button').click();
      });

      // wait for modal to appear and then disappear
      cy.findByText(/we're confirming your booking/i).should('exist');
      cy.findByText(/we're confirming your booking/i).should('not.exist');

      cy.findByTestId('error-description').should('contain.text', 'Please check your');
    });
  });
});
