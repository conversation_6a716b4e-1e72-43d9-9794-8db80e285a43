/**
 * This number will always be accepted by the staging environment
 */
export const CC_NUMBER = '4111 1111 1111 1111';

export const submitPersonalDetailsForm = () =>
  cy.findByTestId('personal-details-form').within(() => {
    cy.findByLabelText(/title/i).select('Ms');
    cy.findByLabelText(/first name/i)
      .clear()
      .type('Bobbi');
    cy.findByLabelText(/last name/i)
      .clear()
      .type('Booker');
    cy.findByLabelText(/email address/i)
      .clear()
      .type('<EMAIL>');
    cy.findByLabelText(/phone number/i)
      .clear()
      .type('0404 123 456');
    cy.findByLabelText(/special requests/i)
      .clear()
      .type('A box of puppies');
    cy.findByTestId('next-step-button').click();
  });

export const submitQantasPointsForm = () =>
  cy.findByTestId('qantas-points-form').within(() => {
    cy.findByLabelText(/Qantas Frequent Flyer number/i)
      .clear()
      .type('1234567890');
    cy.findByLabelText(/ABN for business trips/i)
      .clear()
      .type('***********');
    cy.findByTestId('next-step-button').click();
  });

export const submitPaymentOptionsForm = () =>
  cy.findByTestId('payment-options-form').within(() => {
    cy.findByTestId('continue-button').click();
  });

export const fillAdyenPaymentForm = () =>
  cy.findByTestId('confirm-and-pay-form').within(() => {
    cy.enterIframe('#card-number > iframe', () =>
      cy
        .findByLabelText(/Card number/)
        .click({ force: true }) // adyen library manipulates focus so we need to simulate a user clicking the field
        .type(CC_NUMBER),
    );
    cy.enterIframe('#expiry-date > iframe', () =>
      cy
        .findByLabelText(/Expiry date/)
        .click({ force: true }) // adyen library manipulates focus so we need to simulate a user clicking the field
        .type('0330'),
    );
    cy.enterIframe('#security-code > iframe', () =>
      cy
        .findByLabelText(/Security code/)
        .click({ force: true }) // adyen library manipulates focus so we need to simulate a user clicking the field
        .type('737'),
    );
    cy.findByLabelText(/Name on card/i)
      .focus()
      .type('Roberta Booker');
  });

export const submitPaymentForm = () =>
  cy.findByTestId('confirm-and-pay-form').within(() => {
    cy.findByLabelText(/I agree/i).check();
    cy.findByTestId('payment-button').click();
  });
