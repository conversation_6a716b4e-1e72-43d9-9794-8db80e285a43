import { DEPOSIT_PAY_ENABLED, POINTS_EARN_ENABLED } from 'config/flags';
import { fillAdyenPaymentForm } from './common';

if (DEPOSIT_PAY_ENABLED) {
  describe('Adyen checkout', () => {
    beforeEach(() => {
      cy.setupWaitLoggedIn();
      cy.visitCheckoutForDepositPay();
    });

    it('can make a deposit pay booking with voucher + points + cash', () => {
      cy.findByTestId('qff-login-tab').click();
      cy.enterLoginDetails();

      // Wait until the member-details have returned and populated the form before attempting to click the next button
      cy.waitLoggedIn();

      // Personal details form pre-filled in
      cy.findByTestId('personal-details-form').within(() => {
        cy.findByLabelText(/first name/i).should('have.value', 'Qantas PiRaFpzW');
        cy.findByLabelText(/last name/i).should('have.value', 'Hotels');
        cy.findByTestId('next-step-button').click();
      });

      // QFF number form pre-filled in
      if (POINTS_EARN_ENABLED) {
        cy.findByTestId('qantas-points-form').within(() => {
          cy.findByTestId('next-step-button').click();
        });
      }

      cy.findByTestId('payment-options-form').within(() => {
        cy.findByTestId('deposit-payment-button').click();

        cy.findByTestId('add-voucher-button').click();

        cy.generateVoucher().then((voucherCode) => {
          cy.findByLabelText(/voucher code/i).type(voucherCode);
        });

        cy.findByText(/apply/i).click();
        cy.findByTestId('voucher-amount').should('exist');
      });

      cy.findByTestId('payment-options-form').within(() => {
        cy.findByTestId('edit-points-button').click();
      });

      cy.findByTestId('modal-body').within(() => {
        cy.findByTestId('points-input').type('{selectall}{del}10000{enter}');
      });

      cy.findByTestId('modal-footer').within(() => {
        cy.findByText(/add points/i).click();
      });

      cy.findByTestId('payment-options-form').within(() => {
        cy.findByText(/continue/i).click();
      });

      fillAdyenPaymentForm();

      cy.findByTestId('confirm-and-pay-form').within(() => {
        cy.findByLabelText(/I agree/i).check();
        cy.findByTestId('payment-button').click();
      });

      cy.location('href', { timeout: 60000 }).should('match', /\/bookings\//);
      cy.findByText(/your booking has been confirmed/i).should('exist');

      cy.findByTestId('points-amount').within(() => {
        cy.findByTestId('amount')
          .invoke('text')
          .then((text) => expect(text).to.match(/10,00(0|1)/));
      });
    });
  });
}
