import { PAYWITH_TOGGLE_ENABLED } from 'config/flags';
import { parse as parseQueryString } from 'query-string';
import { PAY_WITH_LABEL, USE_POINTS_PLUS_PAY } from '../../../support/commonParams';
import { testId } from '../../../support/selectors';

if (PAYWITH_TOGGLE_ENABLED) {
  describe('Points+Pay', () => {
    const POINTS_PAY_WRAPPER = testId('points-pay-wrapper');
    const POINTS_AMOUNT_WRAPPER = testId('points-input');
    const CASH_AMOUNT_WRAPPER = testId('cash-input');
    const SELECT_OFFER_BUTTON = testId('offer-checkout-link');
    const INITIAL_CASH_ATTRIBUTE = 'initialCash';
    const SLIDER_CLASS_NAME = '.rc-slider';

    before(() => {
      cy.visitPropertyWithStayAttributes();
    });

    describe('click on points+pay checkbox', () => {
      before(() => {
        cy.scrollIntoViewAndClick(PAY_WITH_LABEL, USE_POINTS_PLUS_PAY);
      });

      it('Use points+Pay should be selected', () => {
        cy.get(PAY_WITH_LABEL).should('contain.text', USE_POINTS_PLUS_PAY);
      });

      it('shows points+pay sliders', () => {
        cy.get(POINTS_PAY_WRAPPER).should('exist');
      });

      describe('points+pay slider', () => {
        describe('points amount', () => {
          it('shows the points amount', () => {
            cy.get(POINTS_AMOUNT_WRAPPER).should('exist');
          });

          it('default points value should be greater than 5000', () => {
            cy.get(POINTS_AMOUNT_WRAPPER).should('have.value', '20,000');
          });
        });

        describe('cash amount', () => {
          it('shows the cash amount', () => {
            cy.get(CASH_AMOUNT_WRAPPER).should('exist');
          });
        });

        describe('select button', () => {
          let initialCashAmountInSelectButton;

          before(() => {
            cy.get(SELECT_OFFER_BUTTON)
              .first()
              .then((element) => {
                const checkoutLoc = new URL(element[0].href);
                initialCashAmountInSelectButton = parseFloat(parseQueryString(checkoutLoc.search)[INITIAL_CASH_ATTRIBUTE]).toFixed(2);
              });
          });

          it('should include the correct initialCash amount', () => {
            cy.get(CASH_AMOUNT_WRAPPER).should('have.value', initialCashAmountInSelectButton);
          });
        });

        describe('when moving the slider to the right', () => {
          let initialCashAmountInSlider;
          let initialPointsAmountInSlider;
          let finalCashAmountInSlider;
          let finalPointsAmountInSlider;
          let initialCashAmountInSelectButton;

          before(() => {
            cy.get(CASH_AMOUNT_WRAPPER).should(($input) => {
              initialCashAmountInSlider = $input.val();
            });

            cy.get(POINTS_AMOUNT_WRAPPER).should(($input) => {
              initialPointsAmountInSlider = $input.val();
            });

            cy.get(SLIDER_CLASS_NAME).first().click('right');

            cy.get(CASH_AMOUNT_WRAPPER).should(($input) => {
              finalCashAmountInSlider = $input.val();
            });

            cy.get(POINTS_AMOUNT_WRAPPER).should(($input) => {
              finalPointsAmountInSlider = $input.val();
            });

            cy.get(SELECT_OFFER_BUTTON)
              .first()
              .then((element) => {
                const checkoutLoc = new URL(element[0].href);
                initialCashAmountInSelectButton = parseFloat(parseQueryString(checkoutLoc.search)[INITIAL_CASH_ATTRIBUTE]);
              });
          });

          it('increases the cash', () => {
            expect(parseFloat(finalCashAmountInSlider)).to.be.greaterThan(parseFloat(initialCashAmountInSlider));
          });

          it('decreases the points', () => {
            expect(parseFloat(finalPointsAmountInSlider)).to.be.lessThan(parseFloat(initialPointsAmountInSlider));
          });

          it('select button should include the new initialCash amount', () => {
            expect(initialCashAmountInSelectButton).to.equal(parseFloat(finalCashAmountInSlider));
          });
        });

        describe('when moving the slider to the left', () => {
          let initialCashAmountInSlider;
          let initialPointsAmountInSlider;
          let finalCashAmountInSlider;
          let finalPointsAmountInSlider;
          let initialCashAmountInSelectButton;

          before(() => {
            cy.get(CASH_AMOUNT_WRAPPER).should(($input) => {
              initialCashAmountInSlider = $input.val();
            });

            cy.get(POINTS_AMOUNT_WRAPPER).should(($input) => {
              initialPointsAmountInSlider = $input.val();
            });

            cy.get(SLIDER_CLASS_NAME).first().click('left');

            cy.get(CASH_AMOUNT_WRAPPER).should(($input) => {
              finalCashAmountInSlider = $input.val();
            });

            cy.get(POINTS_AMOUNT_WRAPPER).should(($input) => {
              finalPointsAmountInSlider = $input.val();
            });

            cy.get(SELECT_OFFER_BUTTON)
              .first()
              .then((element) => {
                const checkoutLoc = new URL(element[0].href);
                initialCashAmountInSelectButton = parseFloat(parseQueryString(checkoutLoc.search)[INITIAL_CASH_ATTRIBUTE]);
              });
          });

          it('decreases the cash', () => {
            expect(parseFloat(finalCashAmountInSlider)).to.be.lessThan(parseFloat(initialCashAmountInSlider));
          });

          it('increases the points', () => {
            expect(parseFloat(finalPointsAmountInSlider)).to.be.greaterThan(parseFloat(initialPointsAmountInSlider));
          });

          it('select button should include the new initialCash amount', () => {
            expect(initialCashAmountInSelectButton).to.equal(parseFloat(finalCashAmountInSlider));
          });
        });
      });
    });
  });
}
