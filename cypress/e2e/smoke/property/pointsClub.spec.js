import { HOTELS_PATH } from 'config/env';

const textToFloat = (text) => parseFloat(text.replace(/([^\d.])/g, ''), 10);

// For running points club tests in dev, ensure that you are connected to the VPN and the SSO host on qantas-hotels-api is https://api.services-stg.qantasloyalty.com

xdescribe('points club', () => {
  beforeEach(() => {
    cy.clearAllDomainCookies();
    cy.visitPropertyWithStayAttributes();
    cy.intercept(`${HOTELS_PATH}/api/ui/properties/*/availability*`).as('property-availability-header');
  });

  it('can login and see custom points club banner', () => {
    cy.findByTestId('not-authenticated-message').within(() => {
      cy.findByText(/Login to access Qantas Frequent Flyer member deals/i).should('exist');
      cy.findByTestId('login-link').click();
    });

    cy.enterPointsClubLoginDetails();

    cy.findByTestId('authenticated-points-club-member').within(() => {
      cy.findByText(/Welcome Member. Enjoy 50% bonus Qantas Points on your booking/i).should('exist');
    });
  });

  it('can login and see increased points club values', () => {
    let initialPointsAmount;

    cy.findAllByTestId('offer-card')
      .first()
      .within(() => {
        cy.findAllByTestId('total-points-displayed')
          .first()
          .invoke('text')
          .then((totalQantasPoints) => {
            initialPointsAmount = textToFloat(totalQantasPoints);
          });
      });

    cy.findByTestId('not-authenticated-message').within(() => {
      cy.findByTestId('login-link').click();
    });

    cy.enterPointsClubLoginDetails();

    cy.wait('@property-availability-header');

    cy.findAllByTestId('offer-card')
      .first()
      .within(() => {
        cy.findAllByTestId('total-points-displayed')
          .first()
          .invoke('text')
          .then((totalQantasPoints) => {
            expect(textToFloat(totalQantasPoints)).to.be.greaterThan(initialPointsAmount);
          });
      });
  });
});
