import { testId } from '../../../support/helpers';

describe('open maps on property page', () => {
  const MAP_IMAGE = testId('show-map-button');
  const MAP_CONTAINER = testId('google-map-container');
  const MAP_HEADER = testId('modal-header');
  const MAP_CLOSE_BUTTON = testId('close');

  beforeEach(() => {
    cy.visitPropertyWithStayAttributes();
    cy.get(MAP_IMAGE).click();
  });

  describe('when click on the maps image', () => {
    it('shows the map', () => {
      cy.get(MAP_CONTAINER).should('be.visible');
    });
  });

  describe('when clicking on the close button', () => {
    it('hides the map', () => {
      cy.get(MAP_HEADER).within(() => {
        cy.get(MAP_CLOSE_BUTTON).click();
        cy.get(MAP_CONTAINER).should('not.exist');
      });
    });
  });
});
