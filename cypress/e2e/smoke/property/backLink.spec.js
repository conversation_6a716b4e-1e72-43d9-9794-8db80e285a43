import { HOTELS_PATH } from 'config/env';
import { parse as parseQueryString } from 'query-string';
import { testId } from '../../../support/helpers';

const PROPERTY_CARD = '[data-testid="search-result"] a';
const BACK_LINK = testId('back-link');
const EXPECTED_STAY_ATTRIBUTES = ['checkIn', 'checkOut', 'adults', 'children', 'infants'];

describe('Back to search link', () => {
  describe('landing on the page directly', () => {
    describe('without stay parameters', () => {
      let backToSearchLocation;

      before(() => {
        cy.visitPropertyWithoutStayAttributes();
        cy.get(BACK_LINK)
          .first()
          .then((element) => (backToSearchLocation = new URL(element[0].href)));
      });

      it('back link defaults to search list page', () => {
        expect(backToSearchLocation.pathname).to.eq(`${HOTELS_PATH}/search/list`);
      });

      it('with the location attribute', () => {
        const backLinkQueryString = parseQueryString(backToSearchLocation.search);
        expect(backLinkQueryString).to.have.property('location');
      });
    });

    describe('with valid stay parameters', () => {
      let backToSearchLocation;
      let backLinkQueryString;

      before(() => {
        cy.visitPropertyWithStayAttributes();
        cy.get(BACK_LINK)
          .first()
          .then((element) => {
            backToSearchLocation = new URL(element[0].href);
            backLinkQueryString = parseQueryString(backToSearchLocation.search);
          });
      });

      it('back link defaults to search list page', () => {
        expect(backToSearchLocation.pathname).to.eq(`${HOTELS_PATH}/search/list`);
      });

      it('with the location attribute', () => {
        expect(backLinkQueryString).to.have.property('location');
      });

      EXPECTED_STAY_ATTRIBUTES.forEach((attr) => {
        it(`with the ${attr} attribute`, () => {
          expect(backLinkQueryString).to.have.property(attr);
        });
      });
    });
  });

  describe('coming from the search page', () => {
    let backToSearchLocation;
    let backLinkQueryString;
    let propertyQueryString;

    before(() => {
      cy.visitEmptySearch();

      cy.get(PROPERTY_CARD).first().invoke('removeAttr', 'target').click();

      cy.location().then((loc) => (propertyQueryString = parseQueryString(loc.search)));

      cy.get(BACK_LINK)
        .first()
        .then((element) => {
          backToSearchLocation = new URL(element[0].href);
          backLinkQueryString = parseQueryString(backToSearchLocation.search);
        });
    });

    it('back link to search list page', () => {
      expect(backToSearchLocation.pathname).to.eq(`${HOTELS_PATH}/search/list`);
    });

    it('with the location attribute', () => {
      expect(backLinkQueryString).to.have.property('location');
    });

    EXPECTED_STAY_ATTRIBUTES.forEach((attr) => {
      describe(`with the ${attr} attribute`, () => {
        it('exists', () => {
          expect(backLinkQueryString).to.have.property(attr);
        });

        it(`equals to ${attr} in the property querystring`, () => {
          expect(backLinkQueryString[attr]).to.eq(propertyQueryString[attr]);
        });
      });
    });
  });

  describe('coming from the maps page', () => {
    let backToSearchLocation;
    let backLinkQueryString;
    let propertyQueryString;

    before(() => {
      cy.setupWaitPropertyPage();
      cy.visitMapsSearch();

      cy.get(testId('inline-card')).first().invoke('removeAttr', 'target').click();

      cy.waitPropertyPage();

      cy.location().then((loc) => (propertyQueryString = parseQueryString(loc.search)));

      cy.get(BACK_LINK)
        .first()
        .then((element) => {
          backToSearchLocation = new URL(element[0].href);
          backLinkQueryString = parseQueryString(backToSearchLocation.search);
        });
    });

    it('back link to search map page', () => {
      expect(backToSearchLocation.pathname).to.eq(`${HOTELS_PATH}/search/map`);
    });

    it('with the location attribute', () => {
      expect(backLinkQueryString).to.have.property('location');
    });

    EXPECTED_STAY_ATTRIBUTES.forEach((attr) => {
      describe(`with the ${attr} attribute`, () => {
        it('exists', () => {
          expect(backLinkQueryString).to.have.property(attr);
        });

        it(`equals to ${attr} in the property querystring`, () => {
          expect(backLinkQueryString[attr]).to.eq(propertyQueryString[attr]);
        });
      });
    });
  });
});
