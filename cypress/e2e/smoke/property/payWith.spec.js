import { testId } from '../../../support/selectors';
import { CHECKED_PAY_WITH_INPUT, PAY_WITH_LABEL, USE_CASH, USE_POINTS, defaultPayWith } from '../../../support/commonParams';
import { PAYWITH_TOGGLE_ENABLED } from 'config/flags';

if (PAYWITH_TOGGLE_ENABLED) {
  describe('pay with', () => {
    const OFFER_CARD = testId('offer-card');
    const CASH_SYMBOL = testId('currency-symbol');
    const POINTS_SYMBOL = testId('currency-text');

    describe('default value', () => {
      before(() => {
        cy.visitPropertyWithStayAttributes();
      });

      it(`defaults to ${defaultPayWith}`, () => {
        cy.get(CHECKED_PAY_WITH_INPUT).should('have.value', defaultPayWith);
      });
    });

    describe('selecting pay with points', () => {
      before(() => {
        cy.visitPropertyWithStayAttributes();

        cy.scrollIntoViewAndClick(PAY_WITH_LABEL, USE_POINTS);
      });

      it('updates search params', () => {
        cy.assertRoutedToQueryParam('payWith', 'points');
      });

      it('show points as active in pay with toggle', () => {
        cy.get(CHECKED_PAY_WITH_INPUT).should('have.value', 'points');
      });

      it('hides cash symbol in search results', () => {
        cy.get(OFFER_CARD)
          .first()
          .within(() => {
            cy.get(CASH_SYMBOL).should('not.exist');
          });
      });

      it('shows points symbol in search results', () => {
        cy.get(OFFER_CARD)
          .first()
          .within(() => {
            cy.get(POINTS_SYMBOL).should('exist');
          });
      });
    });

    describe('selecting pay with cash', () => {
      before(() => {
        cy.setupWaitPropertyPage();
        cy.visitPropertyWithStayAttributes();
        cy.waitPropertyPage();
        cy.scrollIntoViewAndClick(PAY_WITH_LABEL, USE_POINTS);
        cy.waitPropertyPage();
        cy.scrollIntoViewAndClick(PAY_WITH_LABEL, USE_CASH);
        cy.waitPropertyPage();
      });

      it('updates search params', () => {
        cy.assertRoutedToQueryParam('payWith', 'cash');
      });

      it('show cash as active in pay with toggle', () => {
        cy.get(CHECKED_PAY_WITH_INPUT).should('have.value', 'cash');
      });

      it('shows cash symbol in search results', () => {
        cy.get(OFFER_CARD)
          .first()
          .within(() => {
            cy.get(CASH_SYMBOL).should('exist');
          });
      });

      it('hides points symbol in search results', () => {
        cy.get(OFFER_CARD)
          .first()
          .within(() => {
            cy.get(POINTS_SYMBOL).should('not.exist');
          });
      });
    });
  });
}
