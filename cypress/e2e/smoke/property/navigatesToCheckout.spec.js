import { parse as parseQueryString } from 'query-string';
import { CHECKOUT_PAGE_RX, PROPERTY_PAGE_RX, testId } from '../../../support/helpers';
import { PAYWITH_TOGGLE_ENABLED, E2E_TIMEOUT } from 'config';
import { PAY_WITH_LABEL, USE_POINTS } from '../../../support/commonParams';

const SELECT_OFFER_BUTTON = testId('offer-checkout-link');
const BACK_BUTTON = testId('not-phone-label');
const PERSONAL_DETAILS_FORM = testId('personal-details-form');

describe('navigate to checkout page in cash mode', { testIsolation: false }, () => {
  const EXPECTED_STAY_ATTRIBUTES = ['checkIn', 'checkOut', 'adults', 'children', 'infants'];

  let propertyQueryString;
  let checkoutLoc;
  let checkoutQueryString;

  before(() => {
    cy.visitPropertyWithStayAttributes();

    cy.location().then((loc) => (propertyQueryString = parseQueryString(loc.search)));

    cy.get(SELECT_OFFER_BUTTON).first().click();

    cy.get(PERSONAL_DETAILS_FORM, { timeout: E2E_TIMEOUT }).should('be.visible');

    cy.location().then((loc) => {
      checkoutLoc = loc;
      checkoutQueryString = parseQueryString(checkoutLoc.search);
    });
  });

  it('navigates to checkout page', () => {
    expect(checkoutLoc.pathname).match(CHECKOUT_PAGE_RX);
  });

  it('navigates back to property page', () => {
    const backButton = cy.get(BACK_BUTTON).first();
    backButton.should('exist');
    backButton.click();
    cy.url().should('match', PROPERTY_PAGE_RX);

    cy.location().then((loc) => {
      expect(parseQueryString(loc.search)).to.deep.include(propertyQueryString);
      expect(loc.pathname).match(PROPERTY_PAGE_RX);
    });
  });

  EXPECTED_STAY_ATTRIBUTES.forEach((attr) => {
    describe(`with the ${attr} attribute`, () => {
      it('exists', () => {
        expect(checkoutQueryString).to.have.property(attr);
      });

      it(`equals to ${attr} in the querystring`, () => {
        expect(checkoutQueryString[attr]).to.eq(propertyQueryString[attr]);
      });
    });
  });

  describe('initialCash', () => {
    it('does not exist', () => {
      expect(checkoutQueryString).not.to.have.property(`initialCash`);
    });
  });
});

if (PAYWITH_TOGGLE_ENABLED) {
  describe('navigate to checkout page in points mode', { testIsolation: false }, () => {
    const EXPECTED_STAY_ATTRIBUTES = ['checkIn', 'checkOut', 'adults', 'children', 'infants'];

    let propertyQueryString;
    let checkoutLoc;
    let checkoutQueryString;

    before(() => {
      cy.setupWaitPropertyPage();
      cy.visitPropertyWithStayAttributes();

      cy.scrollIntoViewAndClick(PAY_WITH_LABEL, USE_POINTS);

      cy.waitPropertyPage();

      cy.location().then((loc) => (propertyQueryString = parseQueryString(loc.search)));

      cy.get(SELECT_OFFER_BUTTON)
        .first()
        .then((element) => {
          checkoutLoc = new URL(element[0].href);
          checkoutQueryString = parseQueryString(checkoutLoc.search);
        });
    });

    it('navigates to checkout page', () => {
      expect(checkoutLoc.pathname).match(CHECKOUT_PAGE_RX);
    });

    it.skip('navigates back to property page', () => {
      const backButton = cy.get(BACK_BUTTON).first();
      backButton.should('exist');
      backButton.click();

      cy.location().then((loc) => {
        expect(parseQueryString(loc.search)).to.deep.eq(propertyQueryString);
        expect(loc.pathname).match(PROPERTY_PAGE_RX);
      });
    });

    EXPECTED_STAY_ATTRIBUTES.forEach((attr) => {
      describe(`with the ${attr} attribute`, () => {
        it('exists', () => {
          expect(checkoutQueryString).to.have.property(attr);
        });

        it(`equals to ${attr} in the querystring`, () => {
          expect(checkoutQueryString[attr]).to.eq(propertyQueryString[attr]);
        });
      });
    });
  });
}
