import { testId, clickTimes } from '../../../support/helpers';

describe('occupant picker', () => {
  const OCCUPANT_PICKER_INPUT = testId('occupant-picker-input');
  const OCCUPANT_COUNT = testId('occupant-count');
  const OCCUPANT_PICKER_ADULTS = testId('occupant-picker-adults');
  const OCCUPANT_PICKER_CHILDREN = testId('occupant-picker-children');
  const OCCUPANT_PICKER_INFANTS = testId('occupant-picker-infants');
  const OCCUPANT_INCREMENT = testId('increment');
  const OCCUPANT_DECREMENT = testId('decrement');
  const ADULTS_COUNT = `${OCCUPANT_PICKER_ADULTS} ${OCCUPANT_COUNT}`;
  const ADULTS_INCREMENT = `${OCCUPANT_PICKER_ADULTS} ${OCCUPANT_INCREMENT}`;
  const ADULTS_DECREMENT = `${OCCUPANT_PICKER_ADULTS} ${OCCUPANT_DECREMENT}`;
  const CHILDREN_COUNT = `${OCCUPANT_PICKER_CHILDREN} ${OCCUPANT_COUNT}`;
  const CHILDREN_INCREMENT = `${OCCUPANT_PICKER_CHILDREN} ${OCCUPANT_INCREMENT}`;
  const CHILDREN_DECREMENT = `${OCCUPANT_PICKER_CHILDREN} ${OCCUPANT_DECREMENT}`;
  const INFANTS_COUNT = `${OCCUPANT_PICKER_INFANTS} ${OCCUPANT_COUNT}`;
  const INFANTS_INCREMENT = `${OCCUPANT_PICKER_INFANTS} ${OCCUPANT_INCREMENT}`;
  const INFANTS_DECREMENT = `${OCCUPANT_PICKER_INFANTS} ${OCCUPANT_DECREMENT}`;
  const DONE = testId('done-button');

  beforeEach(() => {
    cy.visitPropertyWithStayAttributes();
    cy.get(OCCUPANT_PICKER_INPUT).click();
  });

  it('shows adults, children & infants controls', () => {
    cy.get(OCCUPANT_COUNT).should('be.visible').should('have.length', 3);
  });

  it('updates current query', () => {
    cy.get(OCCUPANT_PICKER_INPUT).should('have.value', '2 guests');
    cy.get(ADULTS_COUNT).should('have.text', '2');
    cy.get(CHILDREN_COUNT).should('have.text', '0');
    cy.get(INFANTS_COUNT).should('have.text', '0');

    clickTimes(cy.get(ADULTS_INCREMENT), 3);
    clickTimes(cy.get(ADULTS_DECREMENT), 1);

    clickTimes(cy.get(CHILDREN_INCREMENT), 3);
    clickTimes(cy.get(CHILDREN_DECREMENT), 1);

    clickTimes(cy.get(INFANTS_INCREMENT), 3);
    clickTimes(cy.get(INFANTS_DECREMENT), 1);

    cy.get(ADULTS_COUNT).should('have.text', '4');
    cy.get(CHILDREN_COUNT).should('have.text', '2');
    cy.get(INFANTS_COUNT).should('have.text', '2');

    cy.get(OCCUPANT_PICKER_INPUT).should('have.value', '8 guests');

    cy.get(DONE).click();

    cy.assertRoutedToQueryParam('adults', '4');
    cy.assertRoutedToQueryParam('children', '2');
    cy.assertRoutedToQueryParam('infants', '2');
  });

  describe('back navigation', () => {
    it('should update input', () => {
      clickTimes(cy.get(ADULTS_INCREMENT), 3);
      cy.get(DONE).click();
      cy.get(OCCUPANT_PICKER_INPUT).should('have.value', '5 guests');
      cy.assertRoutedToQueryParam('adults', '5');
      cy.go('back');
      cy.get(OCCUPANT_PICKER_INPUT).should('have.value', '2 guests');
    });
  });

  describe('Booking for less than 4 guests', () => {
    it('Prompt message should not be visible', () => {
      cy.get(ADULTS_COUNT).should('have.text', '2');
      clickTimes(cy.get(ADULTS_INCREMENT), 1);
      cy.findByTestId('multi-room-prompt-message').should('not.exist');
    });
  });

  describe('Booking for more than 3 guests', () => {
    it('Prompt message should be visible', () => {
      cy.get(ADULTS_COUNT).should('have.text', '2');
      clickTimes(cy.get(ADULTS_INCREMENT), 2);
      cy.get(ADULTS_COUNT).should('have.text', '4');
      cy.findByTestId('multi-room-prompt-message')
        .should('exist')
        .should('include.text', 'For group bookings and multiple room reservations call ');
      cy.findByTestId('request-callback-number').should('exist').should('have.text', '13 70 66');
      cy.findByTestId('request-callback-cta').should('exist').should('have.text', 'request a call back');
    });
  });
});
