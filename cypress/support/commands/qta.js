import { HOTELS_PATH, BRAND_CODE } from 'config';
import { defaultLocation } from '../commonParams';
import qs from 'query-string';
import { snakeCaseKeys } from '../helpers';
import flatMap from 'lodash/flatMap';
import merge from 'lodash/merge';
import { getDefaults } from '../../../src/lib/enums/search';

const { AVA_URL, TEST_PROPERTY_ID } = Cypress.env();

// eslint-disable-next-line no-unused-vars
const { location, sortBy, ...defaultAttributes } = getDefaults();
const PROPERTY_ATTRIBUTES = {
  ...defaultAttributes,
  payWith: 'cash',
  client: 'qta',
};

const SEARCH_ATTRIBUTES = merge(PROPERTY_ATTRIBUTES, { location: encodeURIComponent(defaultLocation) });

Cypress.Commands.add('visitQtaSearchWithStayAttributes', () => {
  cy.setPointsChangeCookie();
  const querystring = Object.keys(SEARCH_ATTRIBUTES)
    .map((key) => `${key}=${SEARCH_ATTRIBUTES[key]}`)
    .join('&');

  const QTA_SEARCH_LIST_PATH = `${HOTELS_PATH}/search/list?${querystring}`;
  cy.visit(QTA_SEARCH_LIST_PATH);
});

Cypress.Commands.add('visitQtaPropertyWithStayAttributes', () => {
  const querystring = Object.keys(PROPERTY_ATTRIBUTES)
    .map((key) => `${key}=${PROPERTY_ATTRIBUTES[key]}`)
    .join('&');

  cy.visit(`${HOTELS_PATH}/properties/${TEST_PROPERTY_ID}?${querystring}`);
});

Cypress.Commands.add('visitQtaCheckoutWithStayAttributes', ({ payWith = 'cash' } = {}) => {
  const offerQuery = qs.stringify(snakeCaseKeys({ ...PROPERTY_ATTRIBUTES, payWith, brand: BRAND_CODE }));
  const checkoutQuery = qs.stringify(PROPERTY_ATTRIBUTES);

  cy.request(`${AVA_URL}/properties/${TEST_PROPERTY_ID}/offers?${offerQuery}`).then(({ body }) => {
    const offers = flatMap(body.property.room_types, 'offers');
    const offer = offers.find((offer) => offer.type === 'standard');

    cy.visit(`${HOTELS_PATH}/checkout?propertyId=${TEST_PROPERTY_ID}&offerId=${offer.id}&${checkoutQuery}`);
  });
});
