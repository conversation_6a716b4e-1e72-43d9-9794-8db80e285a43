import { HOTELS_PATH } from 'config/env';
import { getDefaults } from '../../../src/lib/enums/search';

const { TEST_PROPERTY_ID } = Cypress.env();

// eslint-disable-next-line no-unused-vars
const { location, sortBy, ...defaultAttributes } = getDefaults();
const ATTRIBUTES = {
  ...defaultAttributes,
  payWith: 'cash',
};

Cypress.Commands.add('setupWaitPropertyPage', () => {
  cy.intercept(`${HOTELS_PATH}/api/ui/properties/*/availability*`).as('property-availability-header');
});

Cypress.Commands.add('waitPropertyPage', () => {
  cy.wait('@property-availability-header');
});

Cypress.Commands.add('visitPropertyWithoutStayAttributes', () => {
  cy.visit(`${HOTELS_PATH}/properties/${TEST_PROPERTY_ID}`);
});

Cypress.Commands.add('visitPropertyWithStayAttributes', () => {
  const querystring = Object.keys(ATTRIBUTES)
    .map((key) => `${key}=${ATTRIBUTES[key]}`)
    .join('&');

  cy.visit(`${HOTELS_PATH}/properties/${TEST_PROPERTY_ID}?${querystring}`);
});
