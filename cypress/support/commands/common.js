import { parse as parseQueryString } from 'query-string';

Cypress.Commands.add('assertQueryParam', (assertFn) => {
  cy.location('search').should((search) => {
    assertFn(parseQueryString(search));
  });
});

Cypress.Commands.add('assertRoutedToQueryParam', (queryKey, queryValue) => {
  cy.location().should((location) => {
    const query = parseQueryString(location.search);
    expect(query).to.have.property(queryKey, queryValue);
  });
});

Cypress.Commands.add('scrollIntoViewAndClick', (container, content) => {
  const element = content ? cy.get(container).contains(content) : cy.get(container);

  element.scrollIntoView().click({ force: true });
});

Cypress.Commands.add('setPointsChangeCookie', () => {
  cy.session('Set points change cookie', () => {
    cy.setCookie('points_change_notice', 'true');
  });
});
