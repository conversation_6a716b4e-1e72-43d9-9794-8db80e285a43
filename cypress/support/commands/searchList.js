import { HOTELS_PATH } from 'config/env';
import { defaultLocation } from '../commonParams';

Cypress.Commands.add('setupWaitSearchResults', (searchSubString = '') => {
  cy.log(`>>>>>>>>>>>>>>>>>${searchSubString}<<<<<<<<<<<`);
  cy.log(`${HOTELS_PATH}/api/ui/locations/*/availability?*${searchSubString}*`);
  cy.intercept(`${HOTELS_PATH}/api/ui/locations/*/availability?*${searchSubString}*`).as('search-results');
});

Cypress.Commands.add('waitSearchResults', () => {
  cy.wait('@search-results');
});

Cypress.Commands.add('visitEmptySearch', () => {
  cy.setPointsChangeCookie();
  const SEARCH_LIST_PATH = `${HOTELS_PATH}/search/list?location=${encodeURIComponent(defaultLocation)}`;
  cy.visit(SEARCH_LIST_PATH);
});
