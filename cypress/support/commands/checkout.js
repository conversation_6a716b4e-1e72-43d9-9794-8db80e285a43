import qs from 'query-string';
import find from 'lodash/find';
import flatMap from 'lodash/flatMap';
import { snakeCaseKeys } from '../helpers';
import { getDefaults } from '../../../src/lib/enums/search';
import { Today } from '../../../src/lib/localDate';
import { addDays, parse, format } from 'date-fns';
import { HOTELS_PATH, BRAND_CODE } from 'config';

const {
  QFF_ACCOUNT_NUMBER,
  QFF_ACCOUNT_LASTNAME,
  QFF_ACCOUNT_PIN,
  QFF_SECURITY_QUESTION_1,
  QFF_SECURITY_QUESTION_2,
  QFF_SECURITY_QUESTION_3,
  VOUCHER_AUTH,
  VOUCHER_URL,
  AVA_URL,

  POINTSCLUB_ACCOUNT_NUMBER,
  POINTSCLUB_ACCOUNT_LASTNAME,
  POINTSCLUB_ACCOUNT_PIN,
  TEST_PROPERTY_ID,
} = Cypress.env();

// Set default attributes to the same defaults as in /src/lib/enums/search/DEFAULTS
// eslint-disable-next-line no-unused-vars
const { location, sortBy, ...ATTRIBUTES } = getDefaults();

const MAX_RETRIES = 4;
const incrementDay = (dateString) => format(addDays(parse(dateString, 'yyyy-MM-dd', new Date()), 1), 'yyyy-MM-dd');

// This function will look for the first available offer from a given search, if none is found, it will search on the next day recursively
const requestAvailability = (query, retryCount = 0) => {
  cy.request({ url: `${AVA_URL}/availability`, qs: query }).then(({ body }) => {
    const propertyId = body?.properties?.[0]?.id;
    const offerId = body?.properties?.[0]?.room_types?.[0]?.offers?.[0]?.id;

    if (propertyId && offerId) return { propertyId, offerId, checkIn: query.check_in, checkOut: query.check_out };
    if (retryCount >= MAX_RETRIES) throw new Error(`Could not find an available offer within ${MAX_RETRIES} attempts`);

    requestAvailability({ ...query, check_in: incrementDay(query.check_in), check_out: incrementDay(query.check_out) }, retryCount + 1); // recurse with updated days
  });
};

Cypress.Commands.add('visitCheckoutWithStayAttributes', ({ type = 'standard', payWith = 'cash' } = {}) => {
  const offerQuery = qs.stringify(snakeCaseKeys({ ...ATTRIBUTES, payWith, brand: BRAND_CODE }));
  const checkoutQuery = qs.stringify(ATTRIBUTES);

  cy.request(`${AVA_URL}/properties/${TEST_PROPERTY_ID}/offers?${offerQuery}`).then(({ body }) => {
    const offers = flatMap(body.property.room_types, 'offers');
    const offer = find(offers, { type });

    cy.visit(`${HOTELS_PATH}/checkout?propertyId=${TEST_PROPERTY_ID}&offerId=${offer.id}&${checkoutQuery}`);
  });
});

Cypress.Commands.add('visitCheckoutForClassic', () => {
  const offerQuery = snakeCaseKeys({
    ...ATTRIBUTES,
    payWith: 'points',
    brand: BRAND_CODE,
    region_name: location,
    classicOnly: true,
  });

  cy.wrap(offerQuery, { log: false })
    .then(requestAvailability)
    .then(({ propertyId, offerId, checkIn, checkOut }) => {
      cy.visit({ url: `${HOTELS_PATH}/checkout?propertyId=${propertyId}&offerId=${offerId}`, qs: { ...ATTRIBUTES, checkIn, checkOut } });
    });
});

Cypress.Commands.add('visitCheckoutForDepositPay', () => {
  const checkIn = Today().plus({ days: 40 }).toString();
  const checkOut = Today().plus({ days: 41 }).toString();
  const offerQuery = snakeCaseKeys({
    ...ATTRIBUTES,
    checkIn,
    checkOut,
    depositOnly: true,
    brand: BRAND_CODE,
    region_name: location,
  });

  cy.wrap(offerQuery, { log: false })
    .then(requestAvailability)
    .then(({ propertyId, offerId, checkIn, checkOut }) => {
      cy.visit({ url: `${HOTELS_PATH}/checkout?propertyId=${propertyId}&offerId=${offerId}`, qs: { ...ATTRIBUTES, checkIn, checkOut } });
    });
});

Cypress.Commands.add('enterLoginDetails', () => {
  const { get, findByLabelText, findByText } = cy;

  get('.modal-body').within(() => {
    findByLabelText(/membership number/i).type(QFF_ACCOUNT_NUMBER);
    findByLabelText(/last name/i).type(QFF_ACCOUNT_LASTNAME);
    findByLabelText(/pin/i).type(QFF_ACCOUNT_PIN);
    findByText(/log in/i).click();

    findByLabelText(/third grade teacher/i).type(QFF_SECURITY_QUESTION_1);
    findByLabelText(/childhood hero/i).type(QFF_SECURITY_QUESTION_2);
    findByLabelText(/oldest cousin/i).type(QFF_SECURITY_QUESTION_3);
    get('.ql-login-submit-button').click();
  });
});

Cypress.Commands.add('enterOAuthLoginDetails', () => {
  const { get, findByLabelText, findByText } = cy;

  findByLabelText(/membership number/i).type(QFF_ACCOUNT_NUMBER);
  findByLabelText(/last name/i).type(QFF_ACCOUNT_LASTNAME);
  findByLabelText(/pin/i).type(QFF_ACCOUNT_PIN);
  findByText('LOG IN').click();

  findByLabelText(/third grade teacher/i).type(QFF_SECURITY_QUESTION_1);
  findByLabelText(/childhood hero/i).type(QFF_SECURITY_QUESTION_2);
  findByLabelText(/oldest cousin/i).type(QFF_SECURITY_QUESTION_3);
  get('.ql-login-submit-button').click();
});

Cypress.Commands.add('enterPointsClubLoginDetails', () => {
  const { get, findByLabelText, findByText } = cy;

  get('.modal-body').within(() => {
    findByLabelText(/membership number/i).type(POINTSCLUB_ACCOUNT_NUMBER);
    findByLabelText(/last name/i).type(POINTSCLUB_ACCOUNT_LASTNAME);
    findByLabelText(/pin/i).type(POINTSCLUB_ACCOUNT_PIN);
    findByText(/log in/i).click();

    findByLabelText(/third grade teacher/i).type(QFF_SECURITY_QUESTION_1);
    findByLabelText(/childhood hero/i).type(QFF_SECURITY_QUESTION_2);
    findByLabelText(/oldest cousin/i).type(QFF_SECURITY_QUESTION_3);
    get('.ql-login-submit-button').click();
  });
});

Cypress.Commands.add('generateVoucher', () => {
  const requestData = {
    method: 'POST',
    url: VOUCHER_URL,
    headers: { Authorization: VOUCHER_AUTH },
    body: {
      voucher: {
        amount: '50.00',
        expiry: '01/01/2050',
        reason_type_code: 'marketing',
        settled_by_code: BRAND_CODE,
        brand_codes: [BRAND_CODE],
      },
    },
  };
  return cy.request(requestData).then((response) => response.body.vouchers[0].code);
});

Cypress.Commands.add('enterIframe', (selector, cb) => {
  cy.frameLoaded(selector);
  cy.enter(selector).then((getBody) => getBody().within(cb));
});

Cypress.Commands.add('setupWaitCheckoutPage', () => {
  cy.intercept('POST', `${HOTELS_PATH}/api/ui/quotes`).as('quotes-results');
});

Cypress.Commands.add('setupWaitLoggedIn', () => {
  cy.intercept(`${HOTELS_PATH}/api/ui/member-details/*`).as('member-details');
});

Cypress.Commands.add('waitCheckoutPage', () => {
  cy.wait('@quotes-results');
});

Cypress.Commands.add('waitLoggedIn', () => {
  cy.wait('@member-details');
});
