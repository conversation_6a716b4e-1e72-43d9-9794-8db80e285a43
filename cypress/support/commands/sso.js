import { testId } from '../selectors';

const {
  QFF_ACCOUNT_NUMBER,
  QFF_ACCOUNT_LASTNAME,
  QFF_ACCOUNT_PIN,
  QFF_SECURITY_QUESTION_1,
  QFF_SECURITY_QUESTION_2,
  QFF_SECURITY_QUESTION_3,
} = Cypress.env();

Cypress.Commands.add('loginViaSSO', () => {
  const loginBtn = cy.get(testId('qff-login-tab')).first();

  loginBtn.should('be.visible');
  loginBtn.click();

  cy.get('input[name=memberId]').type(QFF_ACCOUNT_NUMBER);
  cy.get('input[name=lastName]').type(QFF_ACCOUNT_LASTNAME);
  cy.get('input[name=memberPin]').type(QFF_ACCOUNT_PIN);

  cy.get('button[type=submit]').click();

  cy.wait(1000);
  cy.get('body').then(($body) => {
    if ($body.find('button.verify-another-way').length) {
      cy.get('input.ql-login-input').eq(0).type(QFF_SECURITY_QUESTION_1);
      cy.get('input.ql-login-input').eq(1).type(QFF_SECURITY_QUESTION_2);
      cy.get('input.ql-login-input').eq(2).type(QFF_SECURITY_QUESTION_3);
      cy.get('button[type=submit]').click();
    }
  });
});

Cypress.Commands.add('logoutViaSSO', () => {
  const logoutBtn = cy.get(testId('logout-button')).first();

  logoutBtn.should('be.visible');
  logoutBtn.click();
});
