import range from 'lodash/range';
import mapKeys from 'lodash/mapKeys';
import snakeCase from 'lodash/snakeCase';
import escapeRegExp from 'lodash/escapeRegExp';
import { HOTELS_PATH } from 'config/env';

export const testId = (id) => `[data-testid="${id}"]`;

export const clickTimes = (ctx, times) => range(times).reduce(() => ctx.click(), ctx);

export const snakeCaseKeys = (data) => mapKeys(data, (_, key) => snakeCase(key));

export const CHECKOUT_PAGE_RX = new RegExp('^' + escapeRegExp(`${HOTELS_PATH}/checkout`) + '[\\s\\S]*$');

export const PROPERTY_PAGE_RX = new RegExp(escapeRegExp(`${HOTELS_PATH}/properties/`) + '\\d+');
