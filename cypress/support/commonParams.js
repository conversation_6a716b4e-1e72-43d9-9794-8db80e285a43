import { getDefaults } from '../../src/lib/enums/search';
import { PAYMENT_METHODS } from '../../src/lib/enums/payment';

export const QUERY_DATE_FORMAT = 'yyyy-MM-dd';

const { checkIn, checkOut, adults, children, infants, sortBy } = getDefaults();
export const defaultCheckIn = checkIn;
export const defaultCheckOut = checkOut;
export const defaultLocation = 'Melbourne, VIC, Australia';
export const defaultAdults = String(adults);
export const defaultChildren = String(children);
export const defaultInfants = String(infants);
export const defaultSortBy = sortBy;
export const defaultPayWith = PAYMENT_METHODS.CASH;

export const PAY_WITH_INPUT = 'input[name="pay-with-button-group"]';
export const CHECKED_PAY_WITH_INPUT = `${PAY_WITH_INPUT}:checked`;
export const PAY_WITH_LABEL = `${PAY_WITH_INPUT} + label`;

export const USE_CASH = 'CASH';
export const USE_POINTS = 'POINTS';
export const USE_POINTS_PLUS_PAY = 'POINTS PLUS PAY';
