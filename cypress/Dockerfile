FROM 730011650125.dkr.ecr.ap-southeast-2.amazonaws.com/base/node:20.9.0-runtime-144

# Set user to root to set the timezone and install deps
USER root

# Set timezone
ENV TZ=UTC
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Install prerequisites before installing Google Chrome
RUN apt-get update \
    && apt-get install -y \
    curl \
    gnupg2

# Install Google Chrome & Cypress deps
RUN curl https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && sh -c 'echo "deb http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google-chrome.list' \
    && apt-get update \
    && apt-get install -y \
    google-chrome-stable \
    libgtk2.0-0 \
    libnotify-dev \
    libgconf-2-4 \
    libnss3 \
    libxss1 \
    libasound2 \
    xvfb

# Set user to hooroo.  This cannot go above, as the install/setup commands need to be ran as root.
USER hooroo

WORKDIR /application

COPY --chown=hooroo:hooroo package.json yarn.lock ./

RUN yarn install --cache-folder ./ycache; rm -rf ./ycache

COPY . .

ENTRYPOINT [ "/application/scripts/docker/docker-entrypoint-cypress.sh" ]

