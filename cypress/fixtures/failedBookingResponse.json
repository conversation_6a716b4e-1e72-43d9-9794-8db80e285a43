{"id": "01a639d5-abd8-4113-9a4a-f4854686b333", "state": "failed", "brand": "qantas", "channel": "qantas_hotels_website", "reference": "QARZD9QY6", "createdAt": "2020-04-28T02:12:08.603Z", "travelArranger": {"name": {"title": "Mr", "given": "<PERSON>", "family": "Citizen"}, "email": "<EMAIL>", "phone": "+***********", "businessRegistrationNumber": ""}, "account": {"id": "**********", "type": "qff"}, "bookingTotal": {"creditCard": {"total": "348.00"}, "points": {"totalPoints": 0}, "voucher": {"total": "0.00"}, "creditNote": {"total": "0.00"}}, "reservation": {"id": "356a58d7-c434-4ced-bfce-0d4bf1037b79", "createdAt": "2020-04-28T02:12:08.766+00:00", "propertyBookingReference": null, "cancellationPolicy": null, "qffClaims": [], "qbrClaims": [], "property": {"id": "240380", "propertyUrl": "www.qantas.com/hotels/properties/240380", "name": "QT Melbourne", "phone": null, "email": null, "category": "hotels", "checkInAfter": "14:00", "checkOutBefore": "11:00", "timeZone": "Australia/Melbourne", "address": {"streetAddress": ["131 Russell Street"], "suburb": "Melbourne", "state": "Victoria", "postcode": "3000", "country": "Australia", "countryCode": "AU"}, "checkInInstructions": "", "rating": "5.0", "ratingType": "SELF_RATED", "mainImage": {"urlSmall": "https://staging-images-cdn.qantashotels.com/insecure/thumb/plain/k/images/293/113/115/original.jpg", "urlMedium": "https://staging-images-cdn.qantashotels.com/insecure/desktop_search/plain/k/images/293/113/115/original.jpg", "urlLarge": "https://staging-images-cdn.qantashotels.com/insecure/xlarge/plain/k/images/293/113/115/original.jpg", "urlOriginal": "https://staging-images-cdn.qantashotels.com/insecure/large/plain/k/images/293/113/115/original.jpg", "caption": "QT Melbourne "}, "roomInformation": null, "description": "QT Melbourne hotel is an artisan playground where lovers of the high life come to satisfy their thirst for all things eccentric & innovative.\r\n\r\nStep off Collins Street into a world of highly curated style along the shimmer of minimalist industrial polished concrete floors inspired by the 1920’s rag trade in the local ‘Paris end’ of Melbourne. High-tech video art shines as you head upwards to your Guest Suite where timber floorboards host a slumber inducing bed, free WIFI, free in room movies, glamorous en suite, & bespoke designer furnishings.\r\n\r\nOur Restaurant, Cafe, & Bars: \r\n\r\nPascale Bar & Grill - Euro bistro influences pepper a broad menu. An open kitchen offers glimpses into our dish creation & a glass case plays host to the Madame of all patisseries. Great adjoining bar.  \r\n\r\n\r\nThe Cake Shop - Decadent Cakery for modern day Marie Antoinette’s. \r\n\r\nRooftop at QT- Designer decor, cutting edge sound & music. The place to see & be seen in Melbourne. Incredible view over the city. \r\n\r\nOur retail offering: \r\n\r\nTanto - High-end Japanese knife lovers paradise for chefs & foodies\r\n", "latitude": -37.813308, "longitude": 144.968248, "propertyFacilities": ["Restaurant", "Bar / Lounge", "Reception (24 hour)", "Room service (24 hours)", "Concierge", "Non-smoking property", "Patio / Terrace", "<PERSON><PERSON><PERSON><PERSON> (free)", "Gym / Fitness Facilities", "Valet parking (surcharge)", "Meeting rooms"]}, "leadGuest": {"name": {"title": "Mr", "given": "<PERSON>", "family": "Citizen"}, "qffNumber": "**********"}, "offer": {"id": "59131", "type": "standard", "name": "Best Available Rate", "description": "Laneway views and sophisticated industrial chic with all the mod cons.  Free Wi-Fi, In Room Movies included on every stay ", "charges": {"total": {"amount": "348.00", "currency": "AUD"}, "payableAtBooking": {"total": {"amount": "348.00", "currency": "AUD"}, "baseRate": {"amount": "348.00", "currency": "AUD"}, "extraOccupantCharge": {"amount": "0.00", "currency": "AUD"}, "tax": {"amount": "31.64", "currency": "AUD"}, "taxRecovery": {"amount": "0.00", "currency": "AUD"}, "taxDisplayable": null}, "payableAtProperty": {"total": {"amount": "0.00", "currency": "AUD"}, "breakdown": []}}, "promotion": null, "inclusions": [{"name": "WiFi Included"}], "cancellationPolicy": {"isNonRefundable": false, "description": "We understand that sometimes your travel plans change. We do not charge a change or cancel fee. However, this property imposes the following penalties to its customers that we are required to pass on: Cancellations or changes made after 14:00 (GMT+10:00) on May 12, 2020 are subject to a 1 night room & tax penalty. If you fail to check-in for this reservation, or if you cancel or change this reservation after check-in, you may incur penalty charges at the discretion of the property of up to 100% of the booking value.", "cancelTime": "14:00", "cancelTimeTimezone": "Australia/Melbourne", "cancellationWindows": []}, "pointsEarned": {"qffPoints": {"base": 1044, "bonus": 0, "qffPointsClub": 0, "total": 1044, "qffPointsClubTier": null}, "qbrPoints": {"total": 348}}}, "roomType": {"id": "1186370", "name": "QT King", "description": "Sophisticated industrial chic with high end services & amenities.", "roomTypeFacilities": ["Ensuite bathroom", "Bathrobes", "Movies", "Air conditioning", "Heating", "Internet"]}, "checkIn": "2020-05-13", "checkInDateTime": "2020-05-13T14:00:00.000+10:00", "checkOut": "2020-05-14", "checkOutDateTime": "2020-05-14T11:00:00.000+10:00", "specialRequests": "", "providerSpecific": {"bedGroupDescription": null}, "adults": 2, "children": 0, "infants": 0, "permittedActions": {"amendDates": true, "amendTravelArrangerDetails": true, "amendLeadGuestDetails": true, "amendQffNumber": true, "amendGuestNumbers": true, "amendSpecialRequests": true, "offlineDateChangeRequest": true}, "permittedOfflineRequests": ["amend_dates"]}, "payableAtProperty": {"total": {"amount": "0.00", "currency": "AUD"}, "breakdown": []}, "errors": [{"system": "customer_payments", "errorCode": "payment_failed", "code": "payment_failed", "message": "Payment failed.", "agentResolvable": false}]}