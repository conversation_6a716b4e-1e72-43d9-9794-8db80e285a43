env:
  REPO_URL: 730011650125.dkr.ecr.ap-southeast-2.amazonaws.com
  REPO_NAME: application/jetstar-hotels-ui
  APP_NAME: jetstar-hotels-ui
  DOCKER_BUILDKIT: 1
  COMPOSE_DOCKER_CLI_BUILD: 1
  BUILDKIT_PROGRESS: plain

.defaults: &defaults
  agents:
    ci-env: production

.ecr-plugin: &ecr-plugin
  ecr#v2.9.0:
    login: true
    no-include-email: true

.ecs-deploy: &ecs-deploy
  <<: *defaults
  command: hooroo-ci/scripts/ecs-deploy
  branches: master
  timeout: 20
  plugins:
    docker-compose#v4.16.0:
      run: deployment
      pull: terraform
      env:
        - APPLICATION=jetstar-hotels-ui
        - ENVIRONMENT

.deploy-s3: &deploy-s3
  command: hooroo-ci/scripts/deploy-s3
  branches: master
  plugins:
    docker-compose#v4.16.0:
      pull: terraform
      run: terraform

.tag: &tag
  command: hooroo-ci/scripts/image-tag
  branches: master
  plugins:
    docker-compose#v4.16.0:
      run: deployment

.docker-base: &docker-base
  docker-compose#v4.16.0:
    run: base
    volumes:
      - ./builds:/application/builds
    env:
      - APP_NAME=jetstar-hotels-ui
      - ENVIRONMENT
      - SENTRY_AUTH_TOKEN
      - SENTRY_ORG=qantashotels

.create-build: &create-build
  <<: *defaults
  command: hooroo-ci/scripts/create-build
  artifact_paths:
    - builds/*.tar.gz
  plugins:
    <<: *docker-base

.run-base: &run-base
  <<: *defaults
  plugins:
    docker-compose#v4.16.0:
      run: base

notify:
  - slack: '#team-bellhops'
    if: build.state == "failed" && build.branch == "master"

steps:
  - label: ':docker: Build Base Image (Without Artifacts)'
    <<: *defaults
    plugins:
      <<: *ecr-plugin
      docker-compose#v4.16.0:
        build: base
        image-repository: ${REPO_URL}/${REPO_NAME}
        image-name: ${BUILDKITE_BUILD_NUMBER}-base
        cache-from: base:${REPO_URL}/${REPO_NAME}:base
        args:
          - BUILD_NUMBER=${BUILDKITE_BUILD_NUMBER}
          - BUILDKIT_INLINE_CACHE=1
    artifact_paths:
      - package.json

  - wait

  - label: ':docker: Cache Base Image'
    <<: *defaults
    plugins:
      <<: *ecr-plugin
      docker-compose#v4.16.0:
        push: base:${REPO_URL}/${REPO_NAME}:base

  - label: ':eslint: Lint'
    <<: *run-base
    command: yarn run lint:ci
    cancel_on_build_failing: true

  - label: ':jest: Test Shard 1/4'
    <<: *run-base
    command: SHARD_INDEX=0 TOTAL_SHARDS=4 yarn run test:shard
    concurrency_group: ${APP_NAME}-testgroup
    concurrency: 4
    retry:
      automatic:
        limit: 2

  - label: ':jest: Test Shard 2/4'
    <<: *run-base
    command: SHARD_INDEX=1 TOTAL_SHARDS=4 yarn run test:shard
    concurrency_group: ${APP_NAME}-testgroup
    concurrency: 4
    retry:
      automatic:
        limit: 2

  - label: ':jest: Test Shard 3/4'
    <<: *run-base
    command: SHARD_INDEX=2 TOTAL_SHARDS=4 yarn run test:shard
    concurrency_group: ${APP_NAME}-testgroup
    concurrency: 4
    retry:
      automatic:
        limit: 2

  - label: ':jest: Test Shard 4/4'
    <<: *run-base
    command: SHARD_INDEX=3 TOTAL_SHARDS=4 yarn run test:shard
    concurrency_group: ${APP_NAME}-testgroup
    concurrency: 4
    retry:
      automatic:
        limit: 2

  - label: ':jest: Test Server Shard 1/2'
    <<: *run-base
    command: SHARD_INDEX=0 TOTAL_SHARDS=2 yarn run test:server:shard
    concurrency_group: ${APP_NAME}-server-testgroup
    concurrency: 2
    retry:
      automatic:
        limit: 2

  - label: ':jest: Test Server Shard 2/2'
    <<: *run-base
    command: SHARD_INDEX=1 TOTAL_SHARDS=2 yarn run test:server:shard
    concurrency_group: ${APP_NAME}-server-testgroup
    concurrency: 2
    retry:
      automatic:
        limit: 2

  - label: ':hammer: Build Production'
    <<: *create-build
    branches: master
    env:
      ENVIRONMENT: production

  - label: ':hammer: Build Staging'
    <<: *create-build
    branches: master pr/* test/*
    env:
      ENVIRONMENT: staging

  - label: ':hammer: Build SIT'
    <<: *create-build
    branches: master pr/*
    env:
      ENVIRONMENT: sit

  - wait

  - label: ':docker: Build Image (With Artifacts)'
    command: hooroo-ci/scripts/image-build
    branches: master test/* pr/*
    env:
      BUILDKITE_ARTIFACT_DOWNLOAD_PATHS: 'builds/*.tar.gz'

  - wait

  - label: ':docker: Tag Master'
    <<: *tag
    env:
      IMAGE_TAGS: latest,master

  - label: ':s3: Deploy Staging to S3'
    <<: *deploy-s3
    branches: master pr/* test/*
    env:
      BUILDKITE_ARTIFACT_DOWNLOAD_PATHS: builds/staging.tar.gz
      DEPLOY_TO_ENV: staging
      DEPLOY_TYPE: s3
      DEPLOY_BUCKET: s3://staging-jetstar-hotels-ui

  - label: ':s3: Deploy SIT to S3'
    <<: *deploy-s3
    branches: master pr/*
    env:
      BUILDKITE_ARTIFACT_DOWNLOAD_PATHS: builds/sit.tar.gz
      DEPLOY_TO_ENV: sit
      DEPLOY_TYPE: s3
      DEPLOY_BUCKET: s3://sit-jetstar-hotels-ui

  - wait

  - label: ':ship: Deploy Staging to ECS'
    <<: *ecs-deploy
    branches: master pr/* test/*
    env:
      APPLICATION: jetstar-hotels-ui
      ENVIRONMENT: staging

  - label: ':ship: Deploy SIT to ECS'
    <<: *ecs-deploy
    env:
      APPLICATION: jetstar-hotels-ui
      ENVIRONMENT: sit

  - wait

  - label: ':docker: Tag Staging'
    <<: *tag
    branches: master pr/* test/*
    env:
      IMAGE_TAGS: staging,staging.$BUILDKITE_BUILD_NUMBER

  - label: ':docker: Tag SIT'
    <<: *tag
    branches: master pr/*
    env:
      IMAGE_TAGS: sit,sit.$BUILDKITE_BUILD_NUMBER

  - trigger: jetstar-hotels-ui-smoke-tests
    label: ':chrome: Smoke Tests'
    branches: master pr/* test/*
    build:
      message: '${BUILDKITE_MESSAGE}'
      commit: '${BUILDKITE_COMMIT}'
      branch: '${BUILDKITE_BRANCH}'

  # - label: ':ie: Browser Testing'
  #   <<: *run-base
  #   command: hooroo-ci/scripts/browsers-test
  #   branches: master
  #   env:
  #     ENVIRONMENT: staging
  #   timeout_in_minutes: 5
  #   retry:
  #     automatic:
  #       limit: 2

  - label: ':rocket: Deploy Production to S3'
    <<: *deploy-s3
    env:
      BUILDKITE_ARTIFACT_DOWNLOAD_PATHS: builds/production.tar.gz
      DEPLOY_TO_ENV: production
      DEPLOY_TYPE: s3
      DEPLOY_BUCKET: s3://production-jetstar-hotels-ui

  - wait

  - label: ':rocket: Deploy Production to ECS'
    <<: *ecs-deploy
    env:
      ENVIRONMENT: production

  - wait

  - label: ':docker: Tag Production'
    <<: *tag
    env:
      IMAGE_TAGS: production,production.$BUILDKITE_BUILD_NUMBER
