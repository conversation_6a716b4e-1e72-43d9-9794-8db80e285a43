set -eu


REPO_URL="730011650125.dkr.ecr.ap-southeast-2.amazonaws.com"
REPO_NAME="application/jetstar-hotels-ui-cypress"

echo "
steps:
  - label: ':docker: Build Cypress Image'
    plugins:
      ecr#v2.9.0:
        login: true
        no-include-email: true
      docker-compose#v4.16.0:
        build: cypress
        image-repository: ${REPO_URL}/${REPO_NAME}
        image-name: ${BUILDKITE_BUILD_NUMBER}
        cache-from: cypress:${REPO_URL}/${REPO_NAME}:latest

  - wait

  - label: ':docker: Push image tagged with branch'
    plugins:
      ecr#v2.9.0:
        login: true
        no-include-email: true
      docker-compose#v4.16.0:
        push: cypress:${REPO_URL}/${REPO_NAME}:latest

  - wait
"

readonly spec_dir="cypress/e2e/smoke"

for spec in $(find ${spec_dir} -type f -name '*.spec.js'); do
    echo "
  - label: ':smoking: ${spec/${spec_dir}\//}'
    command: 'yarn run test:e2e --spec $spec'
    artifact_paths:
      - cypress/videos/**/*
      - cypress/screenshots/**/*
    concurrency: 8
    concurrency_group: 'jetstar_hotels_ui/$BUILDKITE_BUILD_NUMBER/smoke-tests'
    retry:
      automatic:
        limit: 2

    plugins:
      docker-compose#v4.16.0:
        run: cypress
    agents:
      ci-env: production
    "
done
