import React from 'react';
import { mountUtils } from 'test-utils';
import FAQsLayout from './FAQsLayout';
import { BRAND_CODE, HOTELS_BRAND_NAME } from 'config';
import { getFaqsData } from 'store/faqs/faqSelectors';

jest.mock('store/faqs/faqSelectors');
mountUtils.mockComponent('FaqCategory');
mountUtils.mockComponent('MoreInfo');
mountUtils.mockComponent('FaqNavBar');

const mockFaqs = {
  categories: [
    {
      category: { name: 'Booking Questions', slug: 'booking-questions' },
      faqs: [
        {
          _type: `${BRAND_CODE}.hotels.questionsAndAnswers`,
          answer: [
            {
              _type: 'block',
              children: [{ _type: 'span', marks: [], text: 'Hotel bookings can be made online through ' }],
              markDefs: [],
              style: 'normal',
            },
          ],
          question: {
            name: 'Can I make a hotel booking via Qantas Hotels Customer Support line?',
            slug: 'can-i-make-a-hotel-booking-via-qantas-hotels-customer-support-line',
          },
        },
        {
          _type: 'subSection',
          subTitle: { name: 'Cancellation and Amendments', slug: 'cancellation-and-amendments' },
          subFaqs: [
            {
              _type: `${BRAND_CODE}.hotels.questionsAndAnswers`,
              answer: [
                {
                  _type: 'block',
                  children: [{ _type: 'span', marks: [], text: 'Yes, you can cancel your booking ' }],
                  markDefs: [],
                  style: 'normal',
                },
              ],
              question: {
                name: 'Can I cancel my Deposit Pay booking',
                slug: 'can-i-cancel-my-deposit-pay-booking',
              },
            },
          ],
        },
      ],
    },
    {
      category: { name: 'Qantas Frequent Flyer Customers', slug: 'qantas-frequent-flyer-customers' },
      faqs: [
        {
          _type: `${BRAND_CODE}.hotels.questionsAndAnswers`,
          answer: [
            {
              _type: 'block',
              children: [{ _type: 'span', marks: [], text: 'We recommend you take your Booking Confirmation email...' }],
              markDefs: [],
              style: 'normal',
            },
          ],
          question: {
            name: 'What booking documentation do I need to check-in to the hotel?',
            slug: 'what-booking-documentation-do-i-need-to-check-in-to-the-hotel?',
          },
        },
      ],
    },
  ],
  moreInfo: {
    ctas: [
      {
        description: [
          {
            children: [{ marks: [], text: 'Need help with your Qantas Hotels booking. Check out how you can get in touch' }],
            markDefs: [],
            style: 'normal',
          },
        ],
        icon: {
          asset: {
            _ref: 'image-074db8b0dbe0d468374ae3d3e0c99e2ddb803313-48x48-svg',
            _type: 'reference',
          },
          caption: 'Support',
        },
        name: 'Something urgent?',
      },
    ],
  },
};

const render = () => mountUtils(<FAQsLayout />, { decorators: { store: true, helmet: true, router: true, theme: true } });

describe('<FAQsLayout />', () => {
  beforeAll(() => {
    jest.clearAllMocks();
    getFaqsData.mockReturnValue(mockFaqs);
  });

  it('uses helmet to set document title', () => {
    const { find } = render();

    expect(find('title')).toHaveText(`FAQs | ${HOTELS_BRAND_NAME} Australia`);
    expect(find('script')).toHaveProp({ type: 'application/ld+json' });
    expect(find('script').html()).toMatch(
      JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'FAQPage',
        mainEntity: [
          {
            '@type': 'Question',
            name: 'Can I make a hotel booking via Qantas Hotels Customer Support line?',
            acceptedAnswer: { '@type': 'Answer', text: '<span>Hotel bookings can be made online through</span>' },
          },
          {
            '@type': 'Question',
            name: 'Can I cancel my Deposit Pay booking',
            acceptedAnswer: { '@type': 'Answer', text: '<span>Yes, you can cancel your booking</span>' },
          },
          {
            '@type': 'Question',
            name: 'What booking documentation do I need to check-in to the hotel?',
            acceptedAnswer: { '@type': 'Answer', text: '<span>We recommend you take your Booking Confirmation email...</span>' },
          },
        ],
      }),
    );
  });

  it('renders the FaqNavBar component', () => {
    const { wrapper } = render();
    expect(wrapper.find('FaqNavBar')).toExist();
  });

  it('renders all the questions and answers from sanity', () => {
    const { wrapper } = render();
    expect(wrapper.find('FaqCategory')).toExist();
    expect(wrapper.find('FaqCategory')).toHaveLength(2);
  });
});
