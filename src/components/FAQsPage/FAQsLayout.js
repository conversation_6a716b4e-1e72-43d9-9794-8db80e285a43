import React, { useMemo } from 'react';
import ReactDOMServer from 'react-dom/server';
import { useSelector, useDispatch } from 'react-redux';
import Head from 'next/head';
import MarkdownToJsx from 'markdown-to-jsx';
import { Text, Heading, Box, Container } from '@qga/roo-ui/components';
import { HOTELS_BRAND_NAME, HOTELS_URL } from 'config';
import FaqNavBar from './FaqNavBar';
import { Wrapper } from './primitives';
import { getFaqsData } from 'store/faqs/faqSelectors';
import FaqCategory from './FaqCategory';
import FaqMoreInfo from './FaqMoreInfo';
import getAllQuestionsAndAnswers from './utils/getAllFaqs';
import getMarkdownForBlockContent from 'lib/sanity/getMarkdownForBlockContent';
import { useMount } from 'react-use';
import { fetchFaqs } from 'store/faqs/faqActions';

const faqSchemaMainEntity = (faqs) =>
  faqs.map((faq) => {
    const mdContent = getMarkdownForBlockContent(faq.answer);
    return {
      '@type': 'Question',
      name: faq.question.name,
      acceptedAnswer: {
        '@type': 'Answer',
        text: ReactDOMServer.renderToStaticMarkup(<MarkdownToJsx>{mdContent}</MarkdownToJsx>),
      },
    };
  });

const FAQsLayout = () => {
  const faqs = useSelector(getFaqsData);
  const allFAQs = useMemo(() => getAllQuestionsAndAnswers(faqs?.categories), [faqs]);
  const moreInfo = faqs?.moreInfo;
  const dispatch = useDispatch();

  useMount(() => {
    dispatch(fetchFaqs());
  });

  return (
    <Wrapper>
      <Head>
        <title>FAQs | {HOTELS_BRAND_NAME} Australia</title>
        <link rel="canonical" href={`${HOTELS_URL}/faqs`} />
        {faqs ? (
          <script type="application/ld+json">
            {JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'FAQPage',
              mainEntity: faqSchemaMainEntity(allFAQs),
            })}
          </script>
        ) : null}
      </Head>

      <Box bg="white">
        <Container py={[4, 8]}>
          <Heading.h1 mt={11}>Frequently Asked Questions</Heading.h1>
          <Text mt={5} fontSize="md">
            Can&apos;t find the information you&apos;re after? Browse our {HOTELS_BRAND_NAME} FAQs for more help.
          </Text>
        </Container>
      </Box>
      <Container py={[8, 10]}>
        <Text pl="0" mb={[2, 0]} fontSize="lg" fontWeight="bold">
          Popular topics
        </Text>
        <FaqNavBar />
      </Container>
      <Box bg="white">
        <Container py={[4, 8]} px={[4, 8, 0]} isLast bg="white">
          <Box width={['100%', '100%', '70%']}>
            {faqs?.categories?.map((item, index) => {
              const { category, faqs } = item;
              return <FaqCategory key={category.name} category={category} faqs={faqs} isFirst={index === 0} />;
            })}
          </Box>
          {moreInfo && (
            <Box width={['100%', '100%', '70%']}>
              <FaqMoreInfo heading={moreInfo?.heading} callToActions={moreInfo?.callToActions} />
            </Box>
          )}
        </Container>
      </Box>
    </Wrapper>
  );
};

export default FAQsLayout;
