import React from 'react';
import BookingConfirmationLayout from './BookingConfirmationLayout';
import { getBookingReference, getProperty, getIsLoading, getLoadingError } from 'store/booking/bookingSelectors';
import { mountUtils } from 'test-utils';
import clearFormDataFromSessionStorage from 'lib/checkout/clearFormDataFromSessionStorage';
import { getIsAuthenticated } from 'store/user/userSelectors';
import * as config from 'config';

jest.mock('config');
jest.mock('store/booking/bookingSelectors');
jest.mock('store/user/userSelectors');
jest.mock('lib/checkout/clearFormDataFromSessionStorage');
jest.mock('store/ui/uiSelectors');
jest.mock('lib/qffAuth');
jest.mock('next/router', () => {
  return {
    __esModule: true,
    useRouter: () => ({
      query: {
        bookingid: 'abc-123-000-111',
      },
    }),
  };
});

mountUtils.mockComponent('BookingSummary');
mountUtils.mockComponent('HotelInformation');
mountUtils.mockComponent('ReservationDates');
mountUtils.mockComponent('GuestDetails');
mountUtils.mockComponent('RoomDetails');
mountUtils.mockComponent('CancellationPolicy');
mountUtils.mockComponent('PropertyDescription');
mountUtils.mockComponent('CheckInInformation');
mountUtils.mockComponent('PropertyPolicies');
mountUtils.mockComponent('NpsSurveyDialog');
mountUtils.mockComponent('QHContactDetails');
mountUtils.mockComponent('PaymentDetails');
mountUtils.mockComponent('PointsEarnSummary');
mountUtils.mockComponent('BookingConfirmationFaqsLinks');
mountUtils.mockComponent('BookingConfirmationContactUsLinks');
mountUtils.mockComponent('MobileAppCloseRefreshIcon');
mountUtils.mockComponent('TravelInsuranceCrossSellBanner');

const decorators = { helmet: true, router: true, store: true };

const defaultProps = {
  match: {
    params: {
      bookingId: 'bookingId',
    },
  },
  pageTitle: 'Booking Confirmation | Jetstar hotels',
  knowBeforeYouGoRef: {
    current: {
      offsetTop: 123,
    },
  },
};

const render = () => mountUtils(<BookingConfirmationLayout {...defaultProps} />, { decorators });

describe('<BookingConfirmationLayout />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    getBookingReference.mockReturnValue('QA123456');
    getProperty.mockReturnValue({ roomInformation: 'Property room information' });
    getLoadingError.mockReturnValue(null);
    getIsLoading.mockReturnValue(false);
    getIsAuthenticated.mockReturnValue(false);
  });

  it('renders the MobileAppCloseRefreshIcon', () => {
    const { find } = render();

    expect(find('MobileAppCloseRefreshIcon')).toExist();
  });

  it('renders the booking confirmation header', () => {
    const { findByTestId } = render();

    expect(findByTestId('confirmation-header')).toExist();
  });

  it('renders the <BookingSummary /> component', () => {
    const { find } = render();

    expect(find('BookingSummary')).toHaveProp({ bookingId: 'abc-123-000-111' });
  });

  it('renders the <GuestDetails /> component', () => {
    const { find } = render();

    expect(find('GuestDetails')).toExist();
  });

  it('renders the <ReservationDates /> component', () => {
    const { find } = render();

    expect(find('ReservationDates')).toExist();
  });

  it('renders the <HotelInformation /> component', () => {
    const { find } = render();

    expect(find('HotelInformation')).toExist();
  });

  it('renders the <PropertyDescription /> component', () => {
    const { find } = render();

    expect(find('PropertyDescription')).toExist();
  });

  it('renders the <CheckInInformation /> component', () => {
    const { find } = render();

    expect(find('CheckInInformation')).toExist();
  });

  it('renders the <RoomDetails /> component', () => {
    const { find } = render();

    expect(find('RoomDetails')).toExist();
  });

  it('renders the <PaymentDetails /> component', () => {
    const { find } = render();

    expect(find('PaymentDetails')).toExist();
  });

  it('renders the <CancellationPolicy /> component', () => {
    const { find } = render();

    expect(find('CancellationPolicy')).toExist();
  });

  it('renders the <QHContactDetails /> component', () => {
    const { find } = render();

    expect(find('QHContactDetails')).toExist();
  });

  it('renders the <BookingConfirmationFaqsLinks /> component', () => {
    const { find } = render();

    expect(find('BookingConfirmationFaqsLinks')).toExist();
  });

  it('renders the <BookingConfirmationContactUsLinks /> component', () => {
    const { find } = render();

    expect(find('BookingConfirmationContactUsLinks')).toExist();
  });

  describe('<PropertyPolicies />', () => {
    describe('when the propertyRoomInfo are present', () => {
      it('renders the component ', () => {
        const { find } = render();

        expect(find('PropertyPolicies')).toExist();
      });
    });

    describe('when the propertyRoomInfo are not present', () => {
      beforeEach(() => {
        getProperty.mockReturnValue({ roomInformation: '' });
      });

      it('does not render the component ', () => {
        const { find } = render();

        expect(find('PropertyPolicies')).not.toExist();
      });
    });
  });

  it('uses helmet to set document title', () => {
    const { find } = render();

    expect(find('title')).toHaveText('Booking Confirmation | Jetstar hotels');
  });

  describe('<NpsSurveyDialog />', () => {
    describe('when the booking has loaded', () => {
      it('is rendered', () => {
        const { find } = render();

        expect(find('NpsSurveyDialog')).toExist();
      });
    });

    describe('when the booking has not loaded', () => {
      beforeEach(() => {
        getBookingReference.mockReturnValue('');
      });

      it('is not rendered', () => {
        const { find } = render();

        expect(find('NpsSurveyDialog')).not.toExist();
      });
    });
  });

  describe('<LoadingIndicator />', () => {
    describe('when loading error', () => {
      it('is rendered', () => {
        getIsLoading.mockReturnValue(true);

        const { find } = render();

        expect(find('LoadingIndicator')).toExist();
      });
    });

    describe('when not loading', () => {
      it('is not rendered', () => {
        const { find } = render();

        expect(find('LoadingIndicator')).not.toExist();
      });
    });
  });

  describe('<LoadingError />', () => {
    describe('when there is a loading error', () => {
      it('is rendered', () => {
        const loadingError = {};
        getLoadingError.mockReturnValue(loadingError);

        const { find } = render();

        expect(find('LoadingError')).toHaveProp({ error: loadingError });
      });
    });

    describe('when there is no loading error', () => {
      it('is not rendered', () => {
        const { find } = render();

        expect(find('LoadingError')).not.toExist();
      });
    });
  });

  describe('on mount', () => {
    it('calls clearFormDataFromSessionStorage once', () => {
      render();

      expect(clearFormDataFromSessionStorage).toHaveBeenCalledTimes(1);
    });
  });

  describe('TRAVEL_INSURANCE_CROSS_SELL_ENABLED true', () => {
    beforeEach(() => {
      config.TRAVEL_INSURANCE_CROSS_SELL_ENABLED = true;
    });
    it('renders <TravelInsuranceCrossSellBanner />', () => {
      const { find } = render();

      expect(find('TravelInsuranceCrossSellBanner')).toExist();
    });
  });

  describe('TRAVEL_INSURANCE_CROSS_SELL_ENABLED false', () => {
    beforeEach(() => {
      config.TRAVEL_INSURANCE_CROSS_SELL_ENABLED = false;
    });
    it('does NOT render <TravelInsuranceDisclaimer />', () => {
      const { find } = render();

      expect(find('TravelInsuranceCrossSellBanner')).not.toExist();
    });
  });
});
