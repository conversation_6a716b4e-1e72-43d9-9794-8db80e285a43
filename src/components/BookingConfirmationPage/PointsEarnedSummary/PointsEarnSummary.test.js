import React from 'react';
import { mountUtils } from 'test-utils';
import PointsEarnSummary from './PointsEarnSummary';

mountUtils.mockComponent('PointsEarnDisplay');
mountUtils.mockComponent('PointsPerDollar');

const pointsEarned = {
  qffPoints: { total: 1234 },
  qbrPoints: { total: 234 },
  maxQffEarnPpd: 3,
};

const noPointsEarned = {
  qffPoints: { total: 0 },
  qbrPoints: { total: 0 },
  maxQffEarnPpd: 0,
};

const decorators = { helmet: true, router: true, store: true };
const render = (props) =>
  mountUtils(<PointsEarnSummary pointsEarned={props} />, {
    decorators,
  });

describe('<PointsEarnSummary />', () => {
  it('renders PointsEarnDisplay', () => {
    expect(render(pointsEarned).find('PointsEarnDisplay')).toExist();
  });

  it('renders PointsPerDollar', () => {
    expect(render(pointsEarned).find('PointsPerDollar')).toExist();
  });
});

describe('when user is using points or offer is classic', () => {
  it('renders the appropriate message', () => {
    const { findByTestId } = render(noPointsEarned);
    expect(findByTestId('confirmation-no-points-earned')).toHaveText('You will not earn Qantas Points when using points');
  });
});
