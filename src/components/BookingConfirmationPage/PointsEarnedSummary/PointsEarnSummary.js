import React from 'react';
import PropTypes from 'prop-types';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { mediaQuery } from 'lib/styledSystem';
import { qantasQff } from '@qga/roo-ui/logos';
import { Box, Flex, Text, Image } from '@qga/roo-ui/components';
import PointsEarnDisplay from 'components/PointsEarnDisplay';
import PointsPerDollar from 'components/PointsPerDollar';

const TotalPointsContainer = styled(Flex)`
  border: ${themeGet('borders.1')} ${themeGet('colors.greys.alto')};
  text-align: center;
  color: ${themeGet('colors.greys.charcoal')};
  flex-direction: column;
  border-radius: ${themeGet('radii.default')};
  padding: ${themeGet('space.3')} ${themeGet('space.5')};
  margin-bottom: ${themeGet('space.4')};
  max-width: 700px;
  ${mediaQuery.minWidth.sm} {
    flex-direction: row;
  }
`;

const PointsEarnSummary = ({ pointsEarned }) => {
  if (!pointsEarned) return null;
  const { qffPoints, maxQffEarnPpd } = pointsEarned;
  const totalPointsEarned = qffPoints?.total;

  return (
    <Box mt={12}>
      <TotalPointsContainer color="greys.charcoal">
        <Flex
          flexWrap={['wrap', 'wrap', 'nowrap']}
          justifyContent={['center', 'center', 'normal']}
          alignItems="center"
          lineHeight="16px"
          py={1}
        >
          <Image
            src={qantasQff}
            alt="Qantas Frequent Flyer Logo"
            mb={[4, 4, 0]}
            height={['32px', 'initial']}
            width={['125px', 'initial']}
            mr={4}
          />
          {totalPointsEarned > 0 && (
            <Flex flexDirection="row" flexWrap="wrap" justifyContent={['center', 'normal']} alignItems="center">
              You&apos;ll earn&nbsp;
              <PointsEarnDisplay
                total={qffPoints.total}
                base={qffPoints.base}
                fontSize={['sm', 'base']}
                data-testid="total-qff-points"
              />{' '}
              <Text fontWeight="bold" fontSize={['sm', 'base']}>
                &nbsp;PTS&nbsp;•&nbsp;
              </Text>
              <PointsPerDollar pointsPerDollar={maxQffEarnPpd} fontSize={['sm', 'base']} />
              &nbsp;on your booking plus&nbsp;
              <PointsEarnDisplay
                total={pointsEarned.qbrPoints.total}
                fontSize={['sm', 'base']}
                data-testid="total-qbr-points"
                fontWeight="normal"
              />
              &nbsp;PTS for your business.
            </Flex>
          )}
          {totalPointsEarned === 0 && (
            <Flex
              flexDirection="row"
              flexWrap="wrap"
              justifyContent={['center', 'normal']}
              alignItems="center"
              data-testid="confirmation-no-points-earned"
            >
              You will not earn Qantas Points when using points
            </Flex>
          )}
        </Flex>
      </TotalPointsContainer>
      <Box maxWidth="700px">
        <Text color="greys.steel" fontSize="sm" lineHeight="loose">
          Qantas Points will automatically be credited within 8 weeks of checkout if the name and number match the details in the Frequent
          Flyer account.
        </Text>
      </Box>
    </Box>
  );
};

PointsEarnSummary.propTypes = {
  pointsEarned: PropTypes.object.isRequired,
};

export default PointsEarnSummary;
