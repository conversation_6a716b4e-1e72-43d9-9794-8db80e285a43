import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router';
import loadable from '@loadable/component';
import isEmpty from 'lodash/isEmpty';
import { fetchBooking, trackBooking, clearBooking, showNpsSurveyAfterBooking } from 'store/booking/bookingActions';
import { getBooking } from 'store/booking/bookingSelectors';
import { useIsAuthenticated, useLogout } from 'lib/oauth';
import { QFF_ACCOUNT_MANAGEMENT, ACCOUNT_MANAGEMENT_TYPES } from 'config';
import { fetchFaqs } from 'store/faqs/faqActions';
import { getQuote } from 'store/quote/quoteSelectors';
import { useRouter } from 'next/router';

export const AsyncBookingConfirmationLayout = loadable(() => import(/* webpackPrefetch: true */ './BookingConfirmationLayout'));

const BookingConfirmationPage = (props) => {
  const router = useRouter();
  const { bookingId } = useParams();
  const { logout } = useLogout();
  const dispatch = useDispatch();
  const booking = useSelector(getBooking);
  const quote = useSelector(getQuote);
  const isAuthenticated = useIsAuthenticated();
  const isRebooked = router.query.ss_action === 'rebook';

  useEffect(() => {
    dispatch(fetchFaqs());

    // We need this logic to run once.
    // If the booking exists, this means the customer is coming from the checkout page,
    // so we need to track this booking.
    // If the booking doesn't exist, this means the customer has refreshed the page, or clicked on a link and
    // clicked the back button, we don't need to track this booking, but we need to fetch the booking
    if (isEmpty(booking)) {
      dispatch(fetchBooking(bookingId));
    } else {
      dispatch(
        trackBooking({
          isRebooked,
          booking,
          quote,
        }),
      );
      dispatch(showNpsSurveyAfterBooking());
    }
    if (QFF_ACCOUNT_MANAGEMENT === ACCOUNT_MANAGEMENT_TYPES.CHECKOUT_ONLY && isAuthenticated) {
      logout();
    }
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  return <AsyncBookingConfirmationLayout {...props} />;
};

// Once the user leaves this page, we need to clear the booking,
// so when the customer returns back, we shouldn't track the booking twice.
BookingConfirmationPage.onExitPage = ({ store }) => {
  store.dispatch(clearBooking());
};

export default BookingConfirmationPage;
