import React from 'react';
import { Text } from '@qga/roo-ui/components';
import { useSelector } from 'react-redux';
import { getIsNonRefundableCancellation } from 'store/booking/bookingSelectors';

const CancellationPolicy = () => {
  const isNotRefundable = useSelector(getIsNonRefundableCancellation);

  return (
    <Text lineHeight="loose" fontWeight="bold" color={isNotRefundable ? 'greys.steel' : 'green'}>
      {isNotRefundable ? 'Non-refundable' : 'Flexible cancellation'}
    </Text>
  );
};

export default CancellationPolicy;
