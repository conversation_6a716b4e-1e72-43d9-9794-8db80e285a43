import React, { Fragment } from 'react';
import { useSelector } from 'react-redux';
import { format as formatDate } from 'date-fns';
import { themeGet } from 'styled-system';
import styled from '@emotion/styled';
import { Box, Text, Heading } from '@qga/roo-ui/components';
import ContentRow from 'components/BookingConfirmationPage/ContentRow';
import ContentWrapper from 'components/BookingConfirmationPage/ContentWrapper';
import { DISPLAY_DATE_FORMAT } from 'config';
import { getStayDates, getProperty } from 'store/booking/bookingSelectors';

const StyledText = styled(Text)`
  color: ${themeGet('greys.charcoal')};
  display: block;
`;

const ReservationDates = () => {
  const stayDates = useSelector(getStayDates);
  const property = useSelector(getProperty);
  const { checkOutBefore, checkInAfter } = property;

  const formattedCheckIn = formatDate(new Date(stayDates.checkIn), DISPLAY_DATE_FORMAT);
  const formattedCheckOut = formatDate(new Date(stayDates.checkOut), DISPLAY_DATE_FORMAT);

  return (
    <Fragment>
      <Heading.h3 fontSize={['base', 'md']} color="greys.charcoal" mb={4} data-print-style="h3">
        Reservation dates
      </Heading.h3>
      <ContentWrapper>
        <ContentRow mb={6} data-print-style="content-row">
          <Box width={[0.5, 0.2]} data-print-style="content-row-heading">
            <StyledText fontSize={['sm', 'base']} data-testid="check-in-label">
              Check-in date
            </StyledText>
          </Box>
          <Box>
            <StyledText fontSize={['sm', 'base']} data-testid="check-in-date">
              {formattedCheckIn}
            </StyledText>
            <StyledText fontSize={['sm', 'base']} data-testid="check-in-time">
              From {checkInAfter}
            </StyledText>
          </Box>
        </ContentRow>

        <ContentRow>
          <Box width={[0.5, 0.2]} data-print-style="content-row-heading">
            <StyledText fontSize={['sm', 'base']} data-testid="check-out-label">
              Check-out date
            </StyledText>
          </Box>
          <Box>
            <StyledText fontSize={['sm', 'base']} data-testid="check-out-date">
              {formattedCheckOut}
            </StyledText>

            <StyledText fontSize={['sm', 'base']} data-testid="check-out-time">
              Until {checkOutBefore}
            </StyledText>
          </Box>
        </ContentRow>
      </ContentWrapper>
    </Fragment>
  );
};

export default ReservationDates;
