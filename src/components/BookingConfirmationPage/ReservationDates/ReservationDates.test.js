import React from 'react';
import { mountUtils } from 'test-utils';
import ReservationDates from './ReservationDates';
import { getStayDates, getProperty } from 'store/booking/bookingSelectors';

jest.mock('store/booking/bookingSelectors');

const decorators = { theme: true, store: true };
const render = () => mountUtils(<ReservationDates />, { decorators });

const stayDates = { checkIn: new Date(2020, 9, 20), checkOut: new Date(2020, 9, 21) };
const property = { checkOutBefore: '11:00', checkInAfter: '14:00' };

beforeEach(() => {
  getStayDates.mockReturnValue(stayDates);
  getProperty.mockReturnValue(property);
});

describe('<ReservationDates />', () => {
  describe('checkin and checkout dates and times', () => {
    it('displays a formatted checkin date and label', () => {
      const { findByTestId } = render();

      expect(findByTestId('check-in-label')).toHaveText('Check-in date');
      expect(findByTestId('check-in-date')).toHaveText('Tue 20 Oct, 2020');
    });

    it('displays a formatted checkout date and label', () => {
      const { findByTestId } = render();

      expect(findByTestId('check-out-label')).toHaveText('Check-out date');
      expect(findByTestId('check-out-date')).toHaveText('Wed 21 Oct, 2020');
    });

    it('displays checkin time', () => {
      const { findByTestId } = render();
      expect(findByTestId('check-in-time')).toHaveText('From 14:00');
    });

    it('displays checkout time', () => {
      const { findByTestId } = render();
      expect(findByTestId('check-out-time')).toHaveText('Until 11:00');
    });
  });
});
