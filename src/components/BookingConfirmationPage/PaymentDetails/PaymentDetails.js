import React from 'react';
import { useSelector } from 'react-redux';
import { Heading, Box, Flex } from '@qga/roo-ui/components';
import get from 'lodash/get';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import { differenceInCalendarDays } from 'date-fns';
import PaymentBreakdown from 'components/PaymentBreakdown';
import Currency from 'components/Currency';
import {
  getStayDates,
  getBooking,
  getVoucherAmount,
  getTravelPassAmount,
  getPointsAmount,
  getPayableNowCashAmount,
  getPayableLaterCashAmount,
  getPayableLaterDueDate,
} from 'store/booking/bookingSelectors';

const priceStyling = { flex: ['1 1 auto', '0 0 200px'], fontSize: ['sm', 'base'] };
const priceFontSize = ['sm', 'base'];

const createLineItemCharge = (charge, apiPath) => {
  const lineItemAmount = new Decimal(get(charge, apiPath, 0));
  return { amount: lineItemAmount.toNumber(), currency: 'AUD' };
};

const PaymentDetails = () => {
  const { checkIn, checkOut } = useSelector(getStayDates);
  const stayDuration = `${differenceInCalendarDays(new Date(checkOut), new Date(checkIn))} night stay`;

  const { reservation } = useSelector(getBooking);
  const voucherAmount = useSelector(getVoucherAmount);
  const travelPassAmount = useSelector(getTravelPassAmount);
  const pointsAmount = useSelector(getPointsAmount);
  const payableNowCashAmount = useSelector(getPayableNowCashAmount);
  const payableLaterCashAmount = useSelector(getPayableLaterCashAmount);
  const payableLaterDueDate = useSelector(getPayableLaterDueDate);
  const baseRateAmount = get(reservation, 'offer.charges.payableAtBooking.baseRate.amount');
  const payableAtProperty = get(reservation, 'offer.charges.payableAtProperty.total', {});

  const taxDisplayableCharge = createLineItemCharge(reservation, 'offer.charges.payableAtBooking.taxDisplayable.amount');
  const extraOccupantCharge = createLineItemCharge(reservation, 'offer.charges.payableAtBooking.extraOccupantCharge.amount');

  const hasTaxDisplayableCharge = taxDisplayableCharge.amount > 0;
  const hasExtraOccupantCharge = extraOccupantCharge.amount > 0;

  const isClassic = get(reservation, 'offer.type') === 'classic';

  const baseRate = { amount: isClassic ? pointsAmount : baseRateAmount, currency: isClassic ? 'PTS' : 'AUD' };
  const totalVoucher = { amount: voucherAmount.negated().toNumber(), currency: 'AUD' };

  return (
    <Box width={['auto', '300px']}>
      <Heading.h3 fontSize={['base', 'md']} color="greys.charcoal" mb={4}>
        Price breakdown
      </Heading.h3>
      <Flex data-testid="payment-base-rate" justifyContent="space-between">
        <Box {...priceStyling}>{stayDuration}</Box>
        <Currency amount={baseRate.amount} currency={baseRate.currency} fontSize={priceFontSize} />
      </Flex>
      {hasExtraOccupantCharge && (
        <Flex data-testid="extra-occupant" justifyContent="space-between">
          <Box {...priceStyling}>Extra occupant charge</Box>
          <Currency amount={extraOccupantCharge.amount} currency={extraOccupantCharge.currency} fontSize={priceFontSize} />
        </Flex>
      )}
      {hasTaxDisplayableCharge && (
        <Flex data-testid="payment-tax" justifyContent="space-between">
          <Box {...priceStyling}>Tax and service fee</Box>
          <Currency amount={taxDisplayableCharge.amount} currency={taxDisplayableCharge.currency} fontSize={priceFontSize} />
        </Flex>
      )}
      {!voucherAmount.isZero() && (
        <Flex data-testid="payment-voucher" justifyContent="space-between">
          <Box {...priceStyling}>Voucher</Box>
          <Currency amount={totalVoucher.amount} currency={totalVoucher.currency} fontSize={priceFontSize} />
        </Flex>
      )}
      <Flex justifyContent="space-between">
        <Box {...priceStyling}>Processing fee</Box>
        <Currency amount={0} currency="AUD" fontSize={priceFontSize} />
      </Flex>
      <Box borderTop={1} borderColor="greys.alto" pt={3} mt={[2, 0]}>
        <PaymentBreakdown
          {...priceStyling}
          headingText="Total paid"
          payableNowCashAmount={payableNowCashAmount}
          payableLaterCashAmount={payableLaterCashAmount}
          payableLaterDueDate={payableLaterDueDate}
          pointsAmount={pointsAmount}
          travelPassAmount={travelPassAmount}
          payableAtProperty={payableAtProperty}
        />
      </Box>
    </Box>
  );
};

export default PaymentDetails;
