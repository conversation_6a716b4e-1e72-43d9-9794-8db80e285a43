import React from 'react';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import { mountUtils } from 'test-utils';
import PaymentDetails from './PaymentDetails';
import {
  getStayDates,
  getBooking,
  getVoucherAmount,
  getPointsAmount,
  getTravelPassAmount,
  getPayableNowCashAmount,
  getPayableLaterCashAmount,
  getPayableLaterDueDate,
} from 'store/booking/bookingSelectors';

jest.mock('store/booking/bookingSelectors');
jest.mock('store/ui/uiSelectors');

mountUtils.mockComponent('PaymentBreakdown');

const render = () => mountUtils(<PaymentDetails />, { decorators: { store: true } });

const stayDates = {
  checkIn: '2020-02-05',
  checkOut: '2020-02-06',
};

const bookingTotal = {
  voucher: { total: 50 },
  points: { totalPoints: 12345 },
  creditCard: { total: 532.24 },
};

const voucherAmont = new Decimal(50);
const travelPassAmount = new Decimal(70);
const pointsAmount = new Decimal(12345);
const payableNowCashAmount = new Decimal(532.24);

const reservation = {
  offer: {
    charges: {
      payableAtBooking: {
        baseRate: { amount: 356.92, currency: 'AUD' },
        tax: { amount: 19.45, currency: 'AUD' },
        taxDisplayable: { amount: 7.31, currency: 'AUD' },
        extraOccupantCharge: { amount: 10.91, currency: 'AUD' },
      },
      payableAtProperty: {
        total: { amount: 12.34, currency: 'AUD' },
      },
    },
  },
};

const payableLaterCashAmount = new Decimal(100);
const payableLaterDueDate = new Date(2020, 10, 1);

beforeEach(() => {
  getStayDates.mockReturnValue(stayDates);
  getBooking.mockReturnValue({ reservation });
  getVoucherAmount.mockReturnValue(voucherAmont);
  getPointsAmount.mockReturnValue(pointsAmount);
  getTravelPassAmount.mockReturnValue(travelPassAmount);
  getPayableNowCashAmount.mockReturnValue(payableNowCashAmount);
  getPayableLaterCashAmount.mockReturnValue(payableLaterCashAmount);
  getPayableLaterDueDate.mockReturnValue(payableLaterDueDate);
});

it('renders the static price breakdown rows', () => {
  const { find } = render();
  expect(find('Flex[data-testid="payment-base-rate"]')).toHaveText('1 night stay$356.92AUD');
});

it('renders the tax displayable fee', () => {
  const { find } = render();
  expect(find('Flex[data-testid="payment-tax"]')).toHaveText('Tax and service fee$7.31AUD');
});

it('does NOT render the tax displayable fee', () => {
  const noTaxReservation = {
    offer: {
      charges: {
        payableAtBooking: {
          baseRate: { amount: 356.92, currency: 'AUD' },
          tax: { amount: 19.45, currency: 'AUD' },
          taxDisplayable: null,
          extraOccupantCharge: { amount: 10.91, currency: 'AUD' },
        },
        payableAtProperty: {
          total: { amount: 0, currency: 'AUD' },
        },
      },
    },
  };
  getBooking.mockReturnValue({ bookingTotal, reservation: noTaxReservation });
  const { find } = render();
  expect(find('Flex[data-testid="payment-tax"]')).not.toExist();
});

it('renders the extra occupant charge', () => {
  const { find } = render();
  expect(find('Flex[data-testid="extra-occupant"]')).toHaveText('Extra occupant charge$10.91AUD');
});

it('does NOT render the extra occupant charge', () => {
  const noOccupantReservation = {
    offer: {
      charges: {
        payableAtBooking: {
          baseRate: { amount: 356.92, currency: 'AUD' },
          tax: { amount: 19.45, currency: 'AUD' },
          taxDisplayable: { amount: 7.31, currency: 'AUD' },
          extraOccupantCharge: null,
        },
      },
    },
  };
  getBooking.mockReturnValue({ bookingTotal, reservation: noOccupantReservation });
  const { find } = render();
  expect(find('Flex[data-testid="extra-occupant"]')).not.toExist();
});

it('renders the base rate in points for classic offers', () => {
  const newReservation = {
    offer: {
      ...reservation.offer,
      type: 'classic',
    },
  };
  getBooking.mockReturnValue({ bookingTotal, reservation: newReservation });
  const { find } = render();
  expect(find('Flex[data-testid="payment-base-rate"]')).toHaveText('1 night stay12,345PTS');
  expect(find('Flex[data-testid="payment-tax"]')).toHaveText('Tax and service fee$7.31AUD');
});

it('does render the voucher row if there is a voucher', () => {
  const { find } = render();
  expect(find('Flex[data-testid="payment-voucher"]')).toHaveText('Voucher$-50.00AUD');
});

it('does NOT render the voucher row if there is no voucher', () => {
  getVoucherAmount.mockReturnValue(new Decimal(0));
  const { find } = render();
  expect(find('Flex[data-testid="payment-voucher"]')).not.toExist();
});

it('renders the PaymentBreakdown', () => {
  const { find } = render();
  expect(find('PaymentBreakdown')).toHaveProp({
    headingText: 'Total paid',
    travelPassAmount,
    pointsAmount,
    payableNowCashAmount,
    payableLaterCashAmount,
    payableLaterDueDate,
    payableAtProperty: reservation.offer.charges.payableAtProperty.total,
  });
});
