import React from 'react';
import PropTypes from 'prop-types';
import { Text, Link, Box } from '@qga/roo-ui/components';
import ErrorUI from 'components/errors/ErrorUI';
import PageError from 'components/errors/PageError';
import SupportContactDetails from 'components/SupportContactDetails';
import { BRAND_SELF_SERVICE_URL } from 'config';

const LoadingError = ({ error }) => {
  if ([401, 404].includes(error.httpStatus)) {
    return (
      <ErrorUI heading="Sorry, the page you're looking for is not available">
        <Text>
          To view and manage your hotel bookings, please go to the{' '}
          <Link href={`${BRAND_SELF_SERVICE_URL}?source=hotels-booking-confirmation`}>Manage Bookings</Link> page.
          <Box mt={4}>
            <Text>If you need further assistance, contact us on&nbsp;</Text>
            <SupportContactDetails />
          </Box>
        </Text>
      </ErrorUI>
    );
  }

  return <PageError />;
};

LoadingError.propTypes = {
  error: PropTypes.shape({
    httpStatus: PropTypes.number,
  }),
};

LoadingError.defaultProps = {
  error: {
    httpStatus: null,
  },
};

export default LoadingError;
