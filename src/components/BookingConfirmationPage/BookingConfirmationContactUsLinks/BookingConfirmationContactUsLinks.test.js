import React from 'react';
import { mountUtils } from 'test-utils';
import { Heading } from '@qga/roo-ui/components';
import BookingConfirmationContactUsLinks from './BookingConfirmationContactUsLinks';

const render = () => mountUtils(<BookingConfirmationContactUsLinks />, { decorators: { store: true } });

describe('<BookingConfirmationContactUsLinks />', () => {
  it('has the correct title', () => {
    const { find } = render();
    const title = find(Heading.h2);

    expect(title.text()).toEqual('Need help with your booking?');
  });

  it('has the correct description', () => {
    const { findByTestId } = render();
    const description = findByTestId('description');

    expect(description.text()).toEqual('Get in touch with our customer service team.');
  });

  it('renders the Live Chat card', () => {
    const { find } = render();
    const liveChatCard = find('LiveChatCard');

    expect(liveChatCard).toExist();
  });

  it('renders the Call Us card', () => {
    const { find } = render();
    const callUsCard = find('CallUsCard');

    expect(callUsCard).toExist();
  });
});
