import React, { Fragment } from 'react';
import { Flex, Text, Heading, Box } from '@qga/roo-ui/components';
import LiveChatCard from 'components/ContactUsPage/RecommendedLinks/LiveChatCard';
import CallUsCard from 'components/ContactUsPage/RecommendedLinks/CallUsCard';

const BookingConfirmationContactUsLinks = () => {
  return (
    <Fragment>
      <Box mb={10}>
        <Heading.h2 fontWeight="bold" mb={1}>
          Need help with your booking?
        </Heading.h2>
        <Text data-testid="description" fontSize="md">
          Get in touch with our customer service team.
        </Text>
      </Box>
      <Flex justifyContent="space-between">
        <LiveChatCard />
        <CallUsCard />
      </Flex>
    </Fragment>
  );
};

export default BookingConfirmationContactUsLinks;
