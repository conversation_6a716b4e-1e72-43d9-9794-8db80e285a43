import React from 'react';
import { act } from 'react-dom/test-utils';
import * as config from 'config';
import NpsSurveyDialog from './NpsSurveyDialog';
import { mountUtils, resetConfigModule } from 'test-utils';
import { useDialog } from 'components/Dialog';
import { useDataLayer } from 'hooks/useDataLayer';
import { getBooking } from 'store/booking/bookingSelectors';

jest.mock('config');
jest.mock('store/booking/bookingSelectors');
jest.mock('hooks/useDataLayer');
jest.mock('components/Dialog');

jest.useFakeTimers();

const closeDialog = jest.fn();
const openDialog = jest.fn();
const emitInteractionEvent = jest.fn();
jest.spyOn(window.navigator, 'userAgent', 'get').mockReturnValue('__userAgent__');
jest.spyOn(window, 'open').mockImplementation();

const props = { bookingReference: 'reference123' };
const decorators = { store: true, theme: true };
const render = () => mountUtils(<NpsSurveyDialog {...props} />, { decorators });

describe('<NpsSurveyDialog />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    useDataLayer.mockReturnValue({ emitInteractionEvent });
    useDialog.mockReturnValue({
      Dialog: ({ children }) => <div>{children}</div>,
      openDialog,
      closeDialog,
    });
    resetConfigModule(config);
    config.BOOKING_CONFIRMATION_NPS_SURVEY_URL = 'http://surveymockey.com/';
  });

  describe('showNpsSurveyAfterBooking', () => {
    describe('when is false', () => {
      beforeEach(() => {
        getBooking.mockReturnValue({
          bookingId: 'booking-1234',
        });
      });

      it('does not return the Survey dialog', () => {
        render();
        expect(openDialog).not.toHaveBeenCalled();
      });
    });

    describe('when is true', () => {
      beforeEach(() => {
        getBooking.mockReturnValue({
          showNpsSurveyAfterBooking: true,
        });
      });

      it('returns the Survey dialog', () => {
        const { wrapper } = render();
        act(() => {
          jest.runOnlyPendingTimers();
        });
        wrapper.update();
        expect(openDialog).toHaveBeenCalled();
      });

      describe('when the "open survey" button is clicked', () => {
        const openSurvey = () => {
          const { wrapper, findByTestId } = render();
          act(() => {
            jest.runOnlyPendingTimers();
          });
          wrapper.update();

          findByTestId('open-survey').simulate('click');
        };

        it('closes the dialog window', () => {
          openSurvey();

          expect(closeDialog).toHaveBeenCalled();
        });

        it('dispatches an event to the data layer', () => {
          openSurvey();

          expect(emitInteractionEvent).toHaveBeenCalledWith({
            type: 'NPS Survey Pop Up',
            value: 'Take A Survey Selected',
          });
        });

        describe('opening the survey in a new window', () => {
          describe('when the URL is without any parameters', () => {
            it('renders the right survey URL to be opened in a new page', () => {
              config.BOOKING_CONFIRMATION_NPS_SURVEY_URL = 'https://www.surveyurl.com';

              openSurvey();

              expect(window.open.mock.calls[0]).toMatchInlineSnapshot(`
                [
                  "https://www.surveyurl.com/?user_agent=__userAgent__&ref=reference123",
                ]
              `);
            });
          });

          describe('when the URL has parameters', () => {
            it('renders the right survey URL to be opened in a new page', () => {
              config.BOOKING_CONFIRMATION_NPS_SURVEY_URL = 'https://www.surveyurl.com/other/value?user=Me';

              openSurvey();

              expect(window.open.mock.calls[0]).toMatchInlineSnapshot(`
                [
                  "https://www.surveyurl.com/other/value?user=Me&user_agent=__userAgent__&ref=reference123",
                ]
              `);
            });
          });
        });
      });

      describe('when the "not now" button is clicked', () => {
        beforeEach(() => {
          const { wrapper, findByTestId } = render();
          act(() => {
            jest.runOnlyPendingTimers();
          });
          wrapper.update();
          const closeDialogBtn = findByTestId('close-dialog');

          closeDialogBtn.simulate('click');
        });

        it('closes the dialog window', () => {
          expect(closeDialog).toHaveBeenCalled();
        });

        it('dispatches an event to the data layer', () => {
          expect(emitInteractionEvent).toHaveBeenCalledWith({
            type: 'NPS Survey Pop Up',
            value: 'Not Now Selected',
          });
        });
      });
    });
  });
});
