import React, { Fragment } from 'react';
import { Text, Heading, Flex, Box } from '@qga/roo-ui/components';
import { useSelector } from 'react-redux';
import { themeGet } from 'styled-system';
import { getIsNonRefundableCancellation, getCancellationDescription } from 'store/booking/bookingSelectors';
import TruncateText from 'components/TruncateText';
import Markup from 'components/Markup';

const FlexibleInfoBlock = () => {
  const cancellationPolicyDescription = useSelector(getCancellationDescription);

  return (
    <Flex flexDirection="column" data-testid="cancellation-policy-flexible">
      <Text fontSize={['sm', 'base']} color="green" fontWeight="bold" mb={2}>
        This booking has a flexible cancellation policy
      </Text>
      <Box display={['none', 'block']}>
        <Markup content={cancellationPolicyDescription} fontSize={['sm', 'base']} />
      </Box>
      <TruncateText
        data-testid="cancellation-policy-text-flexible"
        display={['block', 'none']}
        text={cancellationPolicyDescription}
        isTruncated
        background={themeGet('colors.gradients.transparentToGrey')}
        modalTitle="Cancellation Policy"
      />
    </Flex>
  );
};

const NonFlexibleInfoBlock = () => {
  const cancellationPolicyDescription = useSelector(getCancellationDescription);

  return (
    <Flex flexDirection="column">
      <Text fontSize={['sm', 'base']} mb={2} data-testid="cancellation-policy-title-not-flexible">
        This booking is not refundable.
      </Text>
      <Box display={['none', 'block']}>
        <Markup content={cancellationPolicyDescription} fontSize={['sm', 'base']} />
      </Box>
      <TruncateText
        data-testid="cancellation-policy-text-not-flexible"
        display={['block', 'none']}
        text={cancellationPolicyDescription}
        isTruncated
        background={themeGet('colors.gradients.transparentToGrey')}
        modalTitle="Cancellation Policy"
      />
    </Flex>
  );
};

const CancellationPolicy = () => {
  const isNotRefundable = useSelector(getIsNonRefundableCancellation);

  return (
    <Fragment>
      <Heading.h3 fontSize={['base', 'md']} mb={4}>
        Cancellation policy
      </Heading.h3>
      {isNotRefundable ? <NonFlexibleInfoBlock /> : <FlexibleInfoBlock />}
    </Fragment>
  );
};

export default CancellationPolicy;
