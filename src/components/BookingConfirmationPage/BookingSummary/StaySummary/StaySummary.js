import { format as formatDate } from 'date-fns';
import React from 'react';
import { useSelector } from 'react-redux';
import { Box, Text } from '@qga/roo-ui/components';
import ContentRow from 'components/BookingConfirmationPage/ContentRow';
import ContentWrapper from 'components/BookingConfirmationPage/ContentWrapper';
import { DISPLAY_DATE_FORMAT } from 'config';
import { getBookingReference, getStayDates } from 'store/booking/bookingSelectors';

const StaySummary = () => {
  const { checkIn, checkOut } = useSelector(getStayDates) || {};
  const bookingReference = useSelector(getBookingReference);

  const formattedCheckIn = formatDate(new Date(checkIn), DISPLAY_DATE_FORMAT);
  const formattedCheckOut = formatDate(new Date(checkOut), DISPLAY_DATE_FORMAT);

  return (
    <ContentWrapper pt={[6, 0]} mb={6}>
      <ContentRow>
        <Box width={[0.5, 0.2]} data-print-style="content-row-heading">
          <Text fontSize={['sm', 'base']}>Check-in date</Text>
        </Box>
        <Box>
          <Text fontSize={['sm', 'base']} data-testid="check-in-date" fontWeight="bold">
            {formattedCheckIn}
          </Text>
        </Box>
      </ContentRow>

      <ContentRow>
        <Box width={[0.5, 0.2]} data-print-style="content-row-heading">
          <Text fontSize={['sm', 'base']}>Check-out date</Text>
        </Box>
        <Box>
          <Text fontSize={['sm', 'base']} data-testid="check-out-date" fontWeight="bold">
            {formattedCheckOut}
          </Text>
        </Box>
      </ContentRow>

      <ContentRow>
        <Box width={[0.5, 0.2]} data-print-style="content-row-heading">
          <Text fontSize={['sm', 'base']}>Booking reference</Text>
        </Box>
        <Box>
          <Text fontSize={['sm', 'base']} data-testid="booking-reference" data-hj-suppress>
            {bookingReference}
          </Text>
        </Box>
      </ContentRow>
    </ContentWrapper>
  );
};

StaySummary.displayName = 'StaySummary';

export default StaySummary;
