import React from 'react';
import StaySummary from './StaySummary';
import { parse } from 'lib/date';
import { getBookingReference, getStayDates } from 'store/booking/bookingSelectors';
import { mountUtils } from 'test-utils';

jest.mock('store/booking/bookingSelectors');

describe('<StaySummary />', () => {
  const decorators = { store: true, theme: true };
  const render = (props) => mountUtils(<StaySummary {...props} />, { decorators });

  const bookingReference = 'ABC-123-DEF-456';

  const checkIn = parse('2019-12-12');
  const checkOut = parse('2019-12-24');

  beforeEach(() => {
    getBookingReference.mockReturnValue(bookingReference);
    getStayDates.mockReturnValue({ checkIn, checkOut });
  });

  it('renders the booking check-in date', () => {
    const { findByTestId } = render();

    expect(findByTestId('check-in-date').text()).toEqual('Thu 12 Dec, 2019');
  });

  it('renders the booking check-out date', () => {
    const { findByTestId } = render();

    expect(findByTestId('check-out-date').text()).toEqual('Tue 24 Dec, 2019');
  });

  it('renders the booking reference', () => {
    const { findByTestId } = render();

    expect(findByTestId('booking-reference').text()).toEqual(bookingReference);
  });
});
