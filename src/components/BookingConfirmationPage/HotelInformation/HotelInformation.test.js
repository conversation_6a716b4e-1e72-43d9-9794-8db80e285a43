import React from 'react';
import { mountUtils } from 'test-utils';
import HotelInformation from './HotelInformation';
import { useDataLayer } from 'hooks/useDataLayer';

window.open = jest.fn();

jest.mock('hooks/useDataLayer');
jest.mock('store/ui/uiSelectors');

mountUtils.mockComponent('Image');

const emitInteractionEvent = jest.fn();

const defaultProps = {
  property: {
    name: 'Grand Hotel',
    rating: 5,
    ratingType: 'SELF_RATED',
    latitude: 35.0,
    longitude: -45.333,
    mainImage: {
      urlMedium: 'www.imageMedium.com',
      urlLarge: 'www.imageLarge.com',
      caption: 'image description',
    },
    address: {
      streetAddress: ['Main St.'],
      suburb: 'Melbourne',
      state: 'Vic',
      country: 'Au',
    },
  },
};

const decorators = { theme: true, store: true };
const render = () => mountUtils(<HotelInformation {...defaultProps} />, { decorators });

describe('<HotelInformation />', () => {
  const { property } = defaultProps;

  beforeEach(() => {
    jest.clearAllMocks();
    useDataLayer.mockReturnValue({ emitInteractionEvent });
  });

  it('returns the property image with the correct props', () => {
    const { find } = render();
    expect(find('Image')).toHaveProp({
      src: property.mainImage.urlMedium,
      alt: property.mainImage.caption,
    });
  });

  it('returns the property name', () => {
    const { findByText } = render();
    const propertyName = property.name;

    expect(findByText(propertyName)).toExist();
  });

  it('returns the <Address /> component with the correct props', () => {
    const { find } = render();
    expect(find('Address')).toHaveProp({
      address: property.address,
    });
  });

  it('returns the star ratings with the correct props', () => {
    const { find } = render();
    expect(find('StarRating')).toHaveProp({
      rating: property.rating,
      ratingType: property.ratingType,
    });
  });
});
