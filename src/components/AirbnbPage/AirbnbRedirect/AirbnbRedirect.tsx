import React, { ReactElement, useEffect, useState } from 'react';
import { iconAirbnbLogo } from '@qga/roo-ui/assets';
import { Box, Heading, Icon, Paragraph, Image } from '@qga/roo-ui/components';
import { Wrapper, LogoWrapper, RedirectLink, RedirectText } from './AirbnbRedirect.style';
import { useRouter } from 'next/router';
import { searchAirbnb } from 'lib/clients/searchAirbnb';
import { getMemberId } from 'store/user/userSelectors';
import { useSelector } from 'react-redux';
import { NextPageWithLayout } from 'pages/_app.page';
import MinimalLayout from 'layouts/MinimalLayout';

const REDIRECT_DELAY = 5000;

const AirbnbRedirect: NextPageWithLayout = () => {
  const router = useRouter();
  const memberNumber = useSelector(getMemberId);
  const [url, setUrl] = useState('');

  useEffect(() => {
    async function getUrl() {
      if (memberNumber) {
        const data = await searchAirbnb({
          memberNumber,
          checkIn: router.query.checkin as string,
          checkOut: router.query.checkout as string,
          location: router.query.location as string,
          guests: Number(router.query.guests),
        });
        setUrl(data.url);
        setTimeout(() => {
          router.push(data.url);
        }, REDIRECT_DELAY);
      }
    }

    getUrl();
  }, [memberNumber, router, router.query]);

  return (
    <Wrapper>
      <LogoWrapper>
        <Icon name="roo" color="#E40000" size={50} />
        <Icon name="chevronRight" color="greys.steel" size={24} />
        <Image title="airbnb" width="31px" height="34px" src={iconAirbnbLogo} alt="" />
      </LogoWrapper>
      <Box mt={4}>
        <Heading.h4>Redirecting you to Airbnb</Heading.h4>
        <Paragraph fontSize="base">Complete your booking on Airbnb during this session to earn Qantas Points</Paragraph>
      </Box>
      <RedirectText isVisible={!!url}>
        If you are not redirected after a few seconds, please <RedirectLink href={url}>continue to Airbnb</RedirectLink>
      </RedirectText>
    </Wrapper>
  );
};

AirbnbRedirect.getLayout = function getLayout(page: ReactElement) {
  return <MinimalLayout>{page}</MinimalLayout>;
};

export default AirbnbRedirect;
