import { act } from '@testing-library/react-hooks';
import useAirbnbRedirect from './useAirbnbRedirect';
import { mocked, renderHook } from 'test-utils';
import { useRouter } from 'next/router';
import { useLoginUrl } from 'lib/qffAuth';
import { useIsAuthenticated } from 'lib/oauth';

const mockLoginUrl = {
  loginUrl: 'mock-login-url',
};

jest.mock('lib/qffAuth');
jest.mock('lib/oauth');
const mockRouter = {
  push: jest.fn(),
};

jest.mock('next/router', () => ({
  useRouter: jest.fn(() => mockRouter),
}));

const render = () => renderHook(() => useAirbnbRedirect(), { store: true });

describe('useAirbnbRedirect', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mocked(useRouter).mockReturnValue(mockRouter);
    mocked(useLoginUrl).mockReturnValue(mockLoginUrl);
  });

  it('logged out redirects to login url', () => {
    mocked(useIsAuthenticated).mockReturnValue(false);
    const { result } = render();
    act(() => {
      result.current.handleAirbnbRedirect('guests=2');
    });
    expect(mockRouter.push).toHaveBeenCalledWith('mock-login-url');
  });

  it('logged in redirects to airbnb interstitial page', () => {
    mocked(useIsAuthenticated).mockReturnValue(true);
    const { result } = render();
    act(() => {
      result.current.handleAirbnbRedirect('guests=2');
    });
    expect(mockRouter.push).toHaveBeenCalledWith('http://test/au/en/hotels/airbnb/redirect?guests=2');
  });
});
