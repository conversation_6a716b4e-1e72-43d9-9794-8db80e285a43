import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { Range, createSliderWithTooltip } from 'rc-slider';
import { Box } from '@qga/roo-ui/components';
import { hashObject } from 'lib/hash';
import { Global, css } from '@emotion/core';
import debounce from 'lodash/debounce';

const SliderWithTooltip = createSliderWithTooltip(Range);

const handleMouseDown = (event) => {
  event.nativeEvent.stopImmediatePropagation();
  event.nativeEvent.preventDefault();
};

/* If the tooltip wraps onto 2 lines it can create a "ResizeObserver loop limit 
exceeded" error. This error is hidden by browsers (except safari) but shows up 
in sentry. Not sure why the wrapping causes the problem, but this will prevent it */
const globalStyleOverride = css`
  .rc-slider-tooltip-inner {
    white-space: nowrap;
  }
`;

const RangeSlider = ({
  min,
  max,
  selectedMin,
  selectedMax,
  onChange,
  onAfterChange,
  step,
  pushable,
  tipFormatter,
  railStyle,
  trackStyle,
  handleStyle,
}) => {
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedAfterChange = useCallback(debounce(onAfterChange, 500), [onAfterChange]);
  return (
    <Box data-testid="mousedown-swallower" onMouseDown={handleMouseDown}>
      <Global styles={globalStyleOverride} />
      <SliderWithTooltip
        key={hashObject({ min, max, selectedMin, selectedMax })}
        min={min}
        max={max}
        defaultValue={[selectedMin, selectedMax]}
        allowCross={false}
        pushable={pushable}
        onChange={onChange}
        onAfterChange={debouncedAfterChange}
        step={step}
        tipFormatter={tipFormatter}
        railStyle={railStyle}
        trackStyle={[trackStyle]}
        handleStyle={[handleStyle]}
        ariaLabelForHandle="Price per night"
      />
    </Box>
  );
};

RangeSlider.propTypes = {
  min: PropTypes.number,
  max: PropTypes.number,
  selectedMin: PropTypes.number,
  selectedMax: PropTypes.number,
  onAfterChange: PropTypes.func,
  onChange: PropTypes.func,
  step: PropTypes.number,
  pushable: PropTypes.number,
  tipFormatter: PropTypes.func,
  railStyle: PropTypes.object,
  trackStyle: PropTypes.object,
  handleStyle: PropTypes.object,
};

RangeSlider.defaultProps = {
  min: 0,
  max: 0,
  selectedMin: 0,
  selectedMax: 0,
  step: 1,
  pushable: 5,
  tipFormatter: (value) => value,
  railStyle: {},
  trackStyle: {},
  handleStyle: {},
  onChange: () => {},
  onAfterChange: () => {},
};

export default RangeSlider;
