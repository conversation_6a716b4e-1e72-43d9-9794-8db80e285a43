import React from 'react';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import { mountUtils } from 'test-utils';
import PaymentBreakdown from './PaymentBreakdown';

const pointsAmount = new Decimal(0);
const travelPassAmount = new Decimal(0);
const payableNowCashAmount = new Decimal(0);
const payableLaterCashAmount = new Decimal(0);
const payableLaterDueDate = new Date(2020, 9, 9);
const payableAtProperty = { amount: '0', currency: 'AUD' };

const defaultProps = {
  pointsAmount,
  travelPassAmount,
  payableNowCashAmount,
  payableLaterCashAmount,
  payableLaterDueDate,
  payableAtProperty,
};

const render = (props) => mountUtils(<PaymentBreakdown {...defaultProps} {...props} />, { theme: true, store: true });

describe('with only payableNowCashAmount', () => {
  it('renders a single cash amount with appropriate label', () => {
    const { findByTestId } = render({ payableNowCashAmount: new Decimal(100) });
    expect(findByTestId('cash-payable-now-amount')).toHaveText('Total due now$100.00AUD');
    expect(findByTestId('points-amount')).not.toExist();
    expect(findByTestId('travel-pass-amount')).not.toExist();
    expect(findByTestId('cash-payable-later-amount')).not.toExist();
    expect(findByTestId('payable-at-property-amount')).not.toExist();
  });
});

describe('with only pointsAmount', () => {
  it('renders a single points amount with appropriate label', () => {
    const { findByTestId } = render({ pointsAmount: new Decimal(100) });
    expect(findByTestId('points-amount')).toHaveText('Total due now100PTS*');
    expect(findByTestId('travel-pass-amount')).not.toExist();
    expect(findByTestId('cash-payable-now-amount')).not.toExist();
    expect(findByTestId('cash-payable-later-amount')).not.toExist();
    expect(findByTestId('payable-at-property-amount')).not.toExist();
  });
});

describe('with only travelPassAmount', () => {
  it('renders a single travel pass amount with appropriate label', () => {
    const { findByTestId } = render({ travelPassAmount: new Decimal(100) });
    expect(findByTestId('total-to-pay-now-label')).toExist();
    expect(findByTestId('travel-pass-amount')).toHaveText('Qantas TravelPass$100.00AUD');
    expect(findByTestId('cash-payable-now-amount')).not.toExist();
    expect(findByTestId('cash-payable-later-amount')).not.toExist();
    expect(findByTestId('payable-at-property-amount')).not.toExist();
  });
});

describe('when pointsAmount, travelPassAmount and payableNowCashAmount are zero (due to full voucher payment)', () => {
  it('renders total to pay now of zero dollars', () => {
    const { findByTestId } = render();
    expect(findByTestId('zero-payable-amount')).toHaveText('Total due now$0.00AUD');
    expect(findByTestId('travel-pass-amount')).not.toExist();
    expect(findByTestId('points-amount')).not.toExist();
    expect(findByTestId('cash-payable-now-amount')).not.toExist();
    expect(findByTestId('cash-payable-later-amount')).not.toExist();
    expect(findByTestId('payable-at-property-amount')).not.toExist();
  });
});

describe('with a payableNowCashAmount, payableLaterCashAmount, pointsAmount and payableAtProperty', () => {
  it('renders a single cash amount', () => {
    const { findByTestId } = render({
      payableNowCashAmount: new Decimal(100),
      payableLaterCashAmount: new Decimal(200),
      pointsAmount: new Decimal(100),
      travelPassAmount: new Decimal(100),
      payableAtProperty: { amount: '10', currency: 'AUD' },
    });
    expect(findByTestId('points-amount')).toHaveText('Qantas Points100PTS*');
    expect(findByTestId('travel-pass-amount')).toHaveText('Qantas TravelPass$100.00AUD');
    expect(findByTestId('cash-payable-now-amount')).toHaveText('Cash$100.00AUD');
    expect(findByTestId('cash-payable-later-amount')).toHaveText('Pay later$200.00AUD');
    expect(findByTestId('payable-at-property-amount')).toHaveText('Pay later at property$10.00AUD');
  });
});

describe('with a payableLaterCashAmount and no due date', () => {
  it('does not render cash-payable-later-amount', () => {
    const { findByTestId } = render({
      payableLaterCashAmount: new Decimal(200),
      payableLaterDueDate: null,
    });
    expect(findByTestId('cash-payable-later-amount')).not.toExist();
    expect(findByTestId('payable-later-due-date-message')).not.toExist();
  });
});
