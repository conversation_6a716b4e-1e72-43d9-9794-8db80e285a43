import React from 'react';
import PropTypes from 'prop-types';
import { Flex, Text, Image } from '@qga/roo-ui/components';
import pluralize from 'pluralize';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import formatNumber from 'lib/formatters/formatNumber';

const RATING_URL = 'https://www.tripadvisor.com/img/cdsi/img2/ratings/traveler/';

const roundHalf = (number) => Math.floor(Number(number) * 2) / 2;

const ratingUrl = (rating) => {
  const ratingString = formatNumber({ number: roundHalf(rating), decimalPlaces: 1 });
  return `${RATING_URL}${ratingString}-15969-4.svg`;
};

const ReviewsText = styled(Text)`
  color: ${themeGet('colors.greys.dusty')};
  text-decoration: ${(props) => (props.underlined ? 'underline' : 'none')};
  font-size: ${themeGet('fontSizes.xs')};
  margin-left: ${themeGet('space.1')};
`;

const TripAdvisorRating = ({ rating, small, displayReviews, underlined, ...rest }) => {
  if (!rating || !rating.reviewCount) return null;

  const ratingLogoSize = small ? '80px' : '110px';
  const averageRating = rating.averageRating || null;
  const reviewCount = rating.reviewCount;
  const altText = `Trip-Advisor ${averageRating} Rating`;

  return (
    <Flex alignItems="center" data-testid="rating" flexWrap="wrap" {...rest}>
      <Image src={ratingUrl(averageRating)} width={ratingLogoSize} alt={altText} role="img" aria-label={altText} />
      {displayReviews && (
        <ReviewsText data-testid="reviews-count" underlined={underlined}>
          {pluralize('reviews', reviewCount, true)}
        </ReviewsText>
      )}
    </Flex>
  );
};

TripAdvisorRating.propTypes = {
  rating: PropTypes.object,
  small: PropTypes.bool,
  displayReviews: PropTypes.bool,
  underlined: PropTypes.bool,
};

TripAdvisorRating.defaultProps = {
  rating: null,
  small: false,
  displayReviews: false,
  underlined: false,
};

export default TripAdvisorRating;
