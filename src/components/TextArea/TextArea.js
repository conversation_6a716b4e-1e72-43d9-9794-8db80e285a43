import React, { Fragment, useCallback, useState } from 'react';
import PropTypes from 'prop-types';
import styled from '@emotion/styled';
import noop from 'lodash/noop';
import { Text, Textarea as RooTextArea } from '@qga/roo-ui/components';

const StyledTextarea = styled(RooTextArea)`
  resize: vertical;
`;

const TextArea = React.forwardRef(({ onChange, maxLength, ...rest }, ref) => {
  const [remainingCharacters, setRemainingCharacters] = useState(maxLength);

  const manageLength = useCallback(
    (e) => {
      onChange(e);
      if (maxLength) {
        const value = e.target.value;
        const remainingLength = maxLength - value.length;
        setRemainingCharacters(Math.max(0, remainingLength));
      }
    },
    [onChange, setRemainingCharacters, maxLength],
  );

  return (
    <Fragment>
      <StyledTextarea {...rest} ref={ref} onChange={manageLength} maxLength={maxLength} />
      {maxLength && (
        <Text data-testid="remaining-characters" color="greys.dusty">
          {remainingCharacters} characters remaining
        </Text>
      )}
    </Fragment>
  );
});

TextArea.propTypes = {
  onChange: PropTypes.func,
  maxLength: PropTypes.number,
};

TextArea.defaultProps = {
  onChange: noop,
  maxLength: null,
};

TextArea.displayName = 'TextArea';

export default TextArea;
