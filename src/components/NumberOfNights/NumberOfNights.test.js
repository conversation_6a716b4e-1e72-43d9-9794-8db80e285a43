import React from 'react';
import { shallow } from 'enzyme';
import NumberOfNights from './NumberOfNights';

let props;

const setup = (options = {}) => {
  props = {
    checkIn: options.checkIn || new Date('2018-07-28'),
    checkOut: options.checkOut || new Date('2018-07-29'),
  };
};

const render = () => shallow(<NumberOfNights {...props} />);

describe('when the stay is 1 night', () => {
  it('return 1 night', () => {
    setup();

    expect(render().find('[data-testid="numberOfNights"]').children().at(0).text()).toEqual('1 night');
  });
});

describe('when the stay is 2 nights', () => {
  it('return 2 nights', () => {
    setup({
      checkIn: new Date('2018-07-28'),
      checkOut: new Date('2018-07-30'),
    });

    expect(render().find('[data-testid="numberOfNights"]').children().at(0).text()).toEqual('2 nights');
  });
});
