import React from 'react';
import PropTypes from 'prop-types';
import { Text } from '@qga/roo-ui/components';
import { differenceInCalendarDays } from 'lib/date';
import pluralize from 'pluralize';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';

const OfferBreakdownText = styled(Text)`
  ${themeGet('card.propertyCard.offerBreakdown')}
`;

const NumberOfNights = ({ checkIn, checkOut, ...rest }) => {
  const totalDays = differenceInCalendarDays(checkOut, checkIn);
  return (
    <OfferBreakdownText data-testid="numberOfNights" {...rest}>
      {pluralize('night', totalDays, true)}
    </OfferBreakdownText>
  );
};

NumberOfNights.propTypes = {
  checkIn: PropTypes.instanceOf(Date).isRequired,
  checkOut: PropTypes.instanceOf(Date).isRequired,
};

export default NumberOfNights;
