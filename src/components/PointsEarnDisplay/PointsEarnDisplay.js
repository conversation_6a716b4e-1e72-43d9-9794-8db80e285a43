import React, { Fragment } from 'react';
import PropTypes from 'prop-types';
import styled from '@emotion/styled';
import { style } from 'styled-system';
import { Text } from '@qga/roo-ui/components';
import formatNumber from 'lib/formatters/formatNumber';

const qffFormatter = (points) => formatNumber({ number: points, decimalPlaces: 0 });

const textDecoration = style({
  prop: 'textDecoration',
  cssProperty: 'textDecoration',
});

const Points = styled(Text)`
  ${textDecoration}
`;

const PointsEarnDisplay = ({ total, base, fontWeight, ...rest }) => {
  const formattedTotal = qffFormatter(total);
  const formattedBase = qffFormatter(base);
  const hasBonus = base > 0 && base < total;

  return (
    <Fragment>
      {hasBonus && (
        <Fragment>
          <Points {...rest} textDecoration="line-through">
            {formattedBase}
          </Points>
          &nbsp;
        </Fragment>
      )}
      <Points fontWeight={fontWeight} {...rest} data-testid="total-points-displayed">
        {formattedTotal}
      </Points>
    </Fragment>
  );
};

PointsEarnDisplay.propTypes = {
  base: PropTypes.number,
  fontWeight: PropTypes.string,
  total: PropTypes.number.isRequired,
};

PointsEarnDisplay.defaultProps = {
  base: undefined,
  fontWeight: 'bold',
};

export default PointsEarnDisplay;
