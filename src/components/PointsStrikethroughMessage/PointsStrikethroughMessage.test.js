import React from 'react';
import { mountUtils } from 'test-utils';
import { getPointsStrikethroughMessage } from 'store/campaign/campaignSelectors';
import renderPointsStrikethroughMessage from 'lib/search/renderPointsStrikethroughMessage';
import PointsStrikethroughMessage from './';

jest.mock('store/campaign/campaignSelectors');
jest.mock('lib/search/renderPointsStrikethroughMessage');

describe('<PointsStrikethroughMessage />', () => {
  const props = {
    total: {
      amount: '42000',
      currency: 'PTS',
    },
    offerType: 'not-classic',
  };

  const render = (extraProps) => mountUtils(<PointsStrikethroughMessage {...props} {...extraProps} />, { decorators: { store: true } });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('when strikethrough message is not available', () => {
    beforeEach(() => {
      getPointsStrikethroughMessage.mockReturnValue(undefined);
    });

    it('does not render', () => {
      expect(render().find('Text')).not.toExist();
    });
  });

  describe('when the render check is not satisfied', () => {
    beforeEach(() => {
      renderPointsStrikethroughMessage.mockReturnValue(false);
    });

    it('does not render', () => {
      expect(render().find('Text')).not.toExist();
    });
  });

  describe('when message is available and the render check is satisfied', () => {
    beforeEach(() => {
      getPointsStrikethroughMessage.mockReturnValue('henlo fren');
      renderPointsStrikethroughMessage.mockReturnValue(true);
    });

    it('renders the strikethrough message with T&Cs delimiter', () => {
      expect(render().find('Text')).toIncludeText('henlo fren±');
    });
  });
});
