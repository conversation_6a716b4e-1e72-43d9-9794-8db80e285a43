import React from 'react';
import PropTypes from 'prop-types';
import styled from '@emotion/styled';
import { useSelector } from 'react-redux';
import { Text as RooText } from '@qga/roo-ui/components';
import renderPointsStrikethroughMessage from 'lib/search/renderPointsStrikethroughMessage';
import { getPointsStrikethroughMessage } from 'store/campaign/campaignSelectors';

const Text = styled(RooText)`
  white-space: nowrap;
`;

const PointsStrikethroughMessage = ({ total = {}, offerType = null, ...rest }) => {
  const message = useSelector(getPointsStrikethroughMessage);
  const shouldRenderPointsStrikethroughMessage = renderPointsStrikethroughMessage({ total, offerType });

  if (!message || !shouldRenderPointsStrikethroughMessage) return null;

  return (
    <Text {...rest} data-testid="points-strikethrough">
      &nbsp;
      {message}
      <sup>±</sup>
    </Text>
  );
};

PointsStrikethroughMessage.propTypes = {
  offerType: PropTypes.string,
  total: PropTypes.object,
};

export default PointsStrikethroughMessage;
