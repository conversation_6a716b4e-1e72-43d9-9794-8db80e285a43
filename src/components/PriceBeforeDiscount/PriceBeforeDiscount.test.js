import React from 'react';
import { mountUtils } from 'test-utils';
import PriceBeforeDiscount from './PriceBeforeDiscount';
import Currency from 'components/Currency';

jest.mock('store/campaign/campaignSelectors');

mountUtils.mockComponent('PointsStrikethroughMessage');

describe('<PriceBeforeDiscount />', () => {
  let props, total;
  beforeEach(() => {
    total = {
      amount: '1234.56',
      currency: 'AUD',
    };

    props = {
      total: total,
      roundToCeiling: true,
      hideCurrency: true,
      size: 'xl',
      offerType: 'NON classic',
    };

    jest.clearAllMocks();
  });

  const render = (extraProps) => mountUtils(<PriceBeforeDiscount {...props} {...extraProps} />, { decorators: { store: true } });

  describe('when a total amount exists', () => {
    it('renders the Currency component with the total price', () => {
      expect(render().find(Currency)).toHaveProp(total);
    });
  });

  describe('when total amount is 0', () => {
    beforeEach(() => {
      props.total = {
        amount: 0,
        currency: 'AUD',
      };
    });

    it('does not render the Currency component', () => {
      expect(render().find(Currency).exists()).toEqual(false);
    });
  });

  describe('when total amount is null', () => {
    beforeEach(() => {
      props.total = {
        amount: null,
        currency: 'AUD',
      };
    });
    it('does not render the Currency component', () => {
      expect(render().find(Currency).exists()).toEqual(false);
    });
  });

  describe('points strike-through', () => {
    it('renders the strike through message', () => {
      const { find } = render();

      expect(find('PointsStrikethroughMessage')).toHaveProp({
        offerType: props.offerType,
        total: props.total,
      });
    });
  });
});
