import React from 'react';
import PropTypes from 'prop-types';
import { Flex } from '@qga/roo-ui/components';
import Currency from 'components/Currency';
import PointsStrikethroughMessage from 'components/PointsStrikethroughMessage';

const PriceBeforeDiscount = ({ total, hideCurrency, roundToCeiling, lineHeight, offerType, ...rest }) => {
  const { amount, currency } = total;

  if (!amount) {
    return null;
  }

  const textProps = {
    color: 'greys.steel',
    fontSize: 'sm',
    lineHeight,
  };

  return (
    <Flex mt={2} mb={1} lineHeight={lineHeight} data-testid="price-before-discount">
      <Currency
        amount={amount}
        currency={currency}
        roundToCeiling={roundToCeiling}
        hideCurrency={hideCurrency}
        textDecoration="line-through"
        {...textProps}
        {...rest}
        data-testid="currency-before-discount"
      />
      <PointsStrikethroughMessage {...textProps} offerType={offerType} total={total} />
    </Flex>
  );
};

PriceBeforeDiscount.propTypes = {
  total: PropTypes.object.isRequired,
  hideCurrency: PropTypes.bool,
  roundToCeiling: PropTypes.bool,
  lineHeight: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  offerType: PropTypes.string,
};

PriceBeforeDiscount.defaultProps = {
  hideCurrency: false,
  lineHeight: 1,
  roundToCeiling: false,
};

export default PriceBeforeDiscount;
