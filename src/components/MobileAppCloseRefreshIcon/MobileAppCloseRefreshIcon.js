import { useEffect } from 'react';
import { getIsMobileApp } from 'store/ui/uiSelectors';
import { useSelector } from 'react-redux';
import { closeAndRefreshIcon } from 'lib/qta/qta';

const MobileAppCloseRefreshIcon = () => {
  const isMobileApp = useSelector(getIsMobileApp);

  useEffect(() => {
    if (isMobileApp) {
      closeAndRefreshIcon();
    }
  });

  return null;
};

export default MobileAppCloseRefreshIcon;
