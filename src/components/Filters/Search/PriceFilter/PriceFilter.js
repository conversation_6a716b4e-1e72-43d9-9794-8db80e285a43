import React from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import { PAYMENT_METHODS } from 'lib/enums/payment';
import { getQueryPayWith } from 'store/router/routerSelectors';
import CashPriceRangeFilter from './CashPriceRangeFilter';
import PointsPriceRangeFilter from './PointsPriceRangeFilter';

const PriceFilter = ({ onChange }) => {
  const payWith = useSelector(getQueryPayWith);
  if (payWith === PAYMENT_METHODS.CASH) return <CashPriceRangeFilter onChange={onChange} />;
  if (payWith === PAYMENT_METHODS.POINTS) return <PointsPriceRangeFilter onChange={onChange} />;
};

PriceFilter.propTypes = {
  onChange: PropTypes.func.isRequired,
};

export default PriceFilter;
