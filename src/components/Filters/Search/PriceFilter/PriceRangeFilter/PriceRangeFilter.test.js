import React from 'react';
import { mountUtils } from 'test-utils';
import PriceRangeFilter from './PriceRangeFilter';

mountUtils.mockComponent('RangeSlider');

const defaultProps = {
  formatPrice: (price) => price,
  onChange: jest.fn(),
  updatePriceRangeLabels: jest.fn(),
  upperPriceLimit: 800,
  stepIncrement: 10,
};

const decorators = { store: true };
const render = (props) => mountUtils(<PriceRangeFilter {...defaultProps} {...props} />, { decorators });

afterEach(() => {
  defaultProps.onChange.mockClear();
  defaultProps.updatePriceRangeLabels.mockClear();
});

describe('min/max price labels', () => {
  let minPriceProps;
  beforeEach(() => {
    minPriceProps = {
      minPrice: 10,
      maxPrice: 20,
      minAvailablePrice: 10,
      maxAvailablePrice: 20,
      minPriceLabelValue: 10,
      maxPriceLabelValue: 20,
    };
  });

  it('renders the minimum price', () => {
    const { find } = render(minPriceProps);
    expect(find('Text[data-testid="min-price"]').text()).toEqual('10');
  });

  it('renders the maximum price', () => {
    const { find } = render(minPriceProps);
    expect(find('Text[data-testid="max-price"]').text()).toEqual('20');
  });

  describe('with a max available price == upperPriceLimit', () => {
    it('renders the maximum price with a "+" suffix', () => {
      const { findByTestId } = render({
        minPrice: 10,
        maxPrice: 800,
        minAvailablePrice: 10,
        maxAvailablePrice: 800,
        minPriceLabelValue: 10,
        maxPriceLabelValue: 800,
      });
      expect(findByTestId('max-price').text()).toEqual('800+');
    });
  });

  describe('with a max available price > upperPriceLimit', () => {
    it('renders the maximum price limited to {upperPriceLimit}+', () => {
      const { findByTestId } = render({
        minPrice: 10,
        maxPrice: 900,
        minAvailablePrice: 10,
        maxAvailablePrice: 900,
        minPriceLabelValue: 10,
        maxPriceLabelValue: 900,
      });
      expect(findByTestId('max-price').text()).toEqual('800+');
    });
  });

  describe('with a min available price > upperPriceLimit', () => {
    it('renders the {upperPriceLimit}+ only', () => {
      const { findByTestId } = render({
        minPrice: defaultProps.upperPriceLimit + 1,
        maxPrice: defaultProps.upperPriceLimit + 1,
        minAvailablePrice: 10,
        maxAvailablePrice: 900,
        minPriceLabelValue: defaultProps.upperPriceLimit + 1,
        maxPriceLabelValue: defaultProps.upperPriceLimit + 1,
      });
      expect(findByTestId('max-price').text()).toEqual('800+');
      expect(findByTestId('min-price')).not.toExist();
    });
  });

  describe('with a selectedMax > maxAvailable', () => {
    it('renders the maximum price as the max available', () => {
      const { findByTestId } = render({
        minPrice: 10,
        maxPrice: 200,
        minAvailablePrice: 10,
        maxAvailablePrice: 100,
        minPriceLabelValue: 10,
        maxPriceLabelValue: 200,
      });
      expect(findByTestId('max-price').text()).toEqual('100');
    });
  });

  describe('with a price label that is not on a step interval', () => {
    it('renders the exact amount passed', () => {
      const { findByTestId } = render({
        minPrice: 10,
        maxPrice: 20,
        minAvailablePrice: 10,
        maxAvailablePrice: 20,
        minPriceLabelValue: 5,
        maxPriceLabelValue: 6,
      });
      expect(findByTestId('min-price').text()).toEqual('5');
      expect(findByTestId('max-price').text()).toEqual('6');
    });
  });
});

describe('rendering the RangeSlider', () => {
  it('renders with expected props', () => {
    const { find } = render({
      minPrice: 10,
      maxPrice: 20,
      minAvailablePrice: 10,
      maxAvailablePrice: 20,
      minPriceLabelValue: 10,
      maxPriceLabelValue: 20,
    });
    expect(find('RangeSlider')).toHaveProp({
      selectedMin: 10,
      selectedMax: 20,
      min: 10,
      max: 20,
      step: 10,
    });
  });

  describe('with values that are not increments of the step increment', () => {
    let incrementProps;
    beforeEach(() => {
      incrementProps = {
        minPrice: 11,
        maxPrice: 22,
        minAvailablePrice: 5,
        maxAvailablePrice: 33,
        minPriceLabelValue: 11,
        maxPriceLabelValue: 22,
      };
    });

    it('steps the selectedMin value down based on the minPrice', () => {
      const { find } = render(incrementProps);
      expect(find('RangeSlider')).toHaveProp({ selectedMin: 10 });
    });

    it('steps the min value down based on the min minAvailablePrice', () => {
      const { find } = render(incrementProps);
      expect(find('RangeSlider')).toHaveProp({ min: 0 });
    });

    it('steps the selectedMax value up based on the maxPrice', () => {
      const { find } = render(incrementProps);
      expect(find('RangeSlider')).toHaveProp({ selectedMax: 30 });
    });

    it('steps the max value down based on max maxAvailablePrice', () => {
      const { find } = render(incrementProps);
      expect(find('RangeSlider')).toHaveProp({ max: 40 });
    });
  });
});

describe('sliding the RangeSlider', () => {
  it('calls updatePriceRangeLabels with expected args', () => {
    const { find } = render({
      minPrice: 20,
      maxPrice: 80,
      minAvailablePrice: 10,
      maxAvailablePrice: 100,
      minPriceLabelValue: 20,
      maxPriceLabelValue: 80,
    });
    find('RangeSlider').props().onChange([30, 60]);
    expect(defaultProps.updatePriceRangeLabels).toHaveBeenCalledWith({ minPriceLabelValue: 30, maxPriceLabelValue: 60 });
  });
});

describe('changing the RangeSlider', () => {
  it('calls onChange with expected args', () => {
    const { find } = render({
      minPrice: 20,
      maxPrice: 80,
      minAvailablePrice: 10,
      maxAvailablePrice: 100,
      minPriceLabelValue: 20,
      maxPriceLabelValue: 80,
    });
    find('RangeSlider').props().onAfterChange([30, 60]);
    expect(defaultProps.onChange).toHaveBeenCalledWith({ minPrice: 30, maxPrice: 60 });
  });

  describe('when the changed minPrice is equal to the minAvailablePrice and maxPrice is equal to the maxAvailablePrice', () => {
    beforeEach(() => {
      const { find } = render({
        minPrice: 20,
        maxPrice: 80,
        minAvailablePrice: 10,
        maxAvailablePrice: 100,
        minPriceLabelValue: 20,
        maxPriceLabelValue: 80,
      });
      find('RangeSlider').props().onAfterChange([10, 100]);
    });

    it('calls onChange with a minPrice of undefined', () => {
      expect(defaultProps.onChange).toHaveBeenCalledWith({ minPrice: undefined });
    });

    it('calls onChange with a maxPrice of undefined', () => {
      expect(defaultProps.onChange).toHaveBeenCalledWith({ maxPrice: undefined });
    });
  });

  describe('with currency as AUD', () => {
    it('renders the correct price label', () => {
      const { findByTestId } = render({
        minPrice: 10,
        maxPrice: 200,
        minAvailablePrice: 10,
        maxAvailablePrice: 100,
        minPriceLabelValue: 10,
        maxPriceLabelValue: 200,
        currency: 'AUD',
      });

      expect(findByTestId('currency-label')).toHaveText('Price per night (AUD)');
    });
  });

  describe('with currency asPTS', () => {
    it('renders the correct price label', () => {
      const { findByTestId } = render({
        minPrice: 10,
        maxPrice: 200,
        minAvailablePrice: 10,
        maxAvailablePrice: 100,
        minPriceLabelValue: 10,
        maxPriceLabelValue: 200,
        currency: 'PTS',
      });

      expect(findByTestId('currency-label')).toHaveText('Points per night (PTS)');
    });
  });
});
