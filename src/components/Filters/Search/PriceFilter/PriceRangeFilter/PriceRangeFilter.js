import React, { Fragment, useCallback } from 'react';
import PropTypes from 'prop-types';
import noop from 'lodash/noop';
import { Heading, Flex, Box, Text } from '@qga/roo-ui/components';
import theme from 'lib/theme';
import FilterContainer from 'components/Filters/FilterContainer';
import RangeSlider from 'components/RangeSlider';

const stepDown = (price, stepIncrement) => price - (price % stepIncrement);
const stepUp = (price, stepIncrement) => (price % stepIncrement === 0 ? price : stepDown(price, stepIncrement) + stepIncrement);

const calculateMinPrice = (price, stepIncrement) => stepDown(Math.ceil(price), stepIncrement);
const calculateMaxPrice = (price, upperPriceLimit, stepIncrement) => Math.min(stepUp(Math.ceil(price), stepIncrement), upperPriceLimit);
const maxPriceLabelSuffix = (price, UPPER_PRICE_LIMIT) => (price === UPPER_PRICE_LIMIT ? '+' : '');
const createPriceFormatterWithLimit = (formatPrice, upperPriceLimit) => (price) =>
  `${formatPrice(price)}${maxPriceLabelSuffix(price, upperPriceLimit)}`;

const PriceRangeFilter = ({
  currency,
  minPrice,
  maxPrice,
  minAvailablePrice,
  maxAvailablePrice,
  minPriceLabelValue,
  maxPriceLabelValue,
  onChange,
  updatePriceRangeLabels,
  formatPrice,
  upperPriceLimit,
  stepIncrement,
}) => {
  const onChangeHandler = useCallback(
    ([minPrice, maxPrice]) => {
      if (!onChange) return;

      if (minPrice === calculateMinPrice(minAvailablePrice, stepIncrement)) {
        minPrice = undefined;
      }
      if (maxPrice === calculateMaxPrice(maxAvailablePrice, upperPriceLimit, stepIncrement)) {
        maxPrice = undefined;
      }

      updatePriceRangeLabels && updatePriceRangeLabels({ minPriceLabelValue: minPrice, maxPriceLabelValue: maxPrice });
      onChange({ minPrice, maxPrice });
    },
    [maxAvailablePrice, minAvailablePrice, onChange, stepIncrement, updatePriceRangeLabels, upperPriceLimit],
  );

  const updatePriceRangeLabelsHandler = useCallback(
    ([minPrice, maxPrice]) => {
      updatePriceRangeLabels && updatePriceRangeLabels({ minPriceLabelValue: minPrice, maxPriceLabelValue: maxPrice });
    },
    [updatePriceRangeLabels],
  );

  const steppedMinAvailablePrice = calculateMinPrice(minAvailablePrice, stepIncrement);
  const steppedMaxAvailablePrice = calculateMaxPrice(maxAvailablePrice, upperPriceLimit, stepIncrement);
  const steppedSelectedMinPrice = calculateMinPrice(minPrice || steppedMinAvailablePrice, stepIncrement);
  const steppedSelectedMaxPrice = calculateMaxPrice(maxPrice || steppedMaxAvailablePrice, upperPriceLimit, stepIncrement);

  const steppedSelectedMinPriceLabel = calculateMinPrice(minPriceLabelValue || steppedMinAvailablePrice, 1);
  const steppedSelectedMaxPriceLabel = calculateMaxPrice(
    maxPriceLabelValue || steppedMaxAvailablePrice,
    Math.min(upperPriceLimit, steppedMaxAvailablePrice),
    1,
  );

  const formatPriceWithLimit = createPriceFormatterWithLimit(formatPrice, upperPriceLimit);
  const showLowerPriceLimit = steppedSelectedMinPriceLabel < upperPriceLimit;
  const label = currency === 'PTS' ? 'Points' : 'Price';

  return (
    <Box>
      <Heading.h2 fontSize="sm" data-testid="currency-label">
        {label} per night ({currency})
      </Heading.h2>
      <FilterContainer p={[4, 4, 0]}>
        <Flex justifyContent="space-between">
          <Box>
            {showLowerPriceLimit && (
              <Fragment>
                <Text data-testid="min-price" fontSize="sm">
                  {formatPrice(steppedSelectedMinPriceLabel)}
                </Text>

                {' - '}
              </Fragment>
            )}
            <Text data-testid="max-price" fontSize="sm">
              {formatPriceWithLimit(steppedSelectedMaxPriceLabel)}
            </Text>
          </Box>
        </Flex>
        <Box p={4}>
          <RangeSlider
            selectedMin={steppedSelectedMinPrice}
            selectedMax={steppedSelectedMaxPrice}
            min={steppedMinAvailablePrice}
            max={steppedMaxAvailablePrice}
            step={stepIncrement}
            onAfterChange={onChangeHandler}
            onChange={updatePriceRangeLabelsHandler}
            tipFormatter={formatPriceWithLimit}
            railStyle={theme.slider.rail}
            trackStyle={theme.slider.track}
            handleStyle={theme.slider.handle}
          />
        </Box>
      </FilterContainer>
    </Box>
  );
};

PriceRangeFilter.propTypes = {
  currency: PropTypes.oneOf(['AUD', 'PTS']),
  minAvailablePrice: PropTypes.number,
  maxAvailablePrice: PropTypes.number,
  minPrice: PropTypes.number,
  maxPrice: PropTypes.number,
  minPriceLabelValue: PropTypes.number,
  maxPriceLabelValue: PropTypes.number,
  onChange: PropTypes.func,
  updatePriceRangeLabels: PropTypes.func,
  formatPrice: PropTypes.func,
  upperPriceLimit: PropTypes.number,
  stepIncrement: PropTypes.number,
};

PriceRangeFilter.defaultProps = {
  currency: 'AUD',
  minAvailablePrice: undefined,
  maxAvailablePrice: undefined,
  minPrice: undefined,
  maxPrice: undefined,
  minPriceLabelValue: undefined,
  maxPriceLabelValue: undefined,
  onChange: noop,
  updatePriceRangeLabels: noop,
  formatPrice: noop,
  upperPriceLimit: undefined,
  stepIncrement: undefined,
};

export default PriceRangeFilter;
