import React, { Fragment, useCallback } from 'react';
import { useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import { Box, Heading } from '@qga/roo-ui/components';
import FilterContainer from 'components/Filters/FilterContainer';
import { CLASSIC_OFFERS_ENABLED, DEPOSIT_PAY_ENABLED } from 'config';
import MultiSelectFilterItem from 'components/Filters/MultiSelectFilter/MultiSelectFilterItem';
import {
  getAvailableDepositPayCount,
  getAvailableFreeCancellationCount,
  getAvailableClassicOffersCount,
  getDepositPayAvailabilityMessage,
  getClassicRewardAvailabilityMessage,
} from 'store/search/searchSelectors';
import { getQueryClassicRewards, getQueryDepositPay, getQueryFreeCancellation } from 'store/router/routerSelectors';

const PaymentPolicyFilter = ({ onChange }) => {
  const isClassicRewardsSelected = useSelector(getQueryClassicRewards);
  const isDepositPaySelected = useSelector(getQueryDepositPay);
  const isFreeCancellationSelected = useSelector(getQueryFreeCancellation);
  const depositPayCount = useSelector(getAvailableDepositPayCount);
  const classicRewardsAvailabilityMessage = useSelector(getClassicRewardAvailabilityMessage);
  const depositPayAvailabilityMessage = useSelector(getDepositPayAvailabilityMessage);
  const freeCancellationCount = useSelector(getAvailableFreeCancellationCount);
  const classicRewardsCount = useSelector(getAvailableClassicOffersCount);

  const handleChange = useCallback(
    (event) => {
      const { name, checked } = event.target;

      onChange({
        [name]: checked ? true : undefined,
      });
    },
    [onChange],
  );

  return (
    <Fragment>
      <Heading.h2 display="block" fontSize="sm" mb={2}>
        Payment Options
      </Heading.h2>
      <Box data-testid="payment-policy-filter" mb={[0, 6]}>
        <FilterContainer>
          <MultiSelectFilterItem
            name="Free cancellation"
            type="freeCancellation"
            checked={isFreeCancellationSelected}
            onChange={handleChange}
            count={freeCancellationCount}
            iconName="freeCancellation"
          />
          {CLASSIC_OFFERS_ENABLED && (
            <MultiSelectFilterItem
              name="Classic Rewards"
              type="classicRewards"
              checked={isClassicRewardsSelected}
              onChange={handleChange}
              count={classicRewardsCount}
              description={classicRewardsAvailabilityMessage}
              iconName="ribbon"
            />
          )}
          {DEPOSIT_PAY_ENABLED && (
            <MultiSelectFilterItem
              name="Deposit Pay"
              type="depositPay"
              checked={isDepositPaySelected}
              onChange={handleChange}
              count={depositPayCount}
              description={depositPayAvailabilityMessage}
              iconName="depositPay"
            />
          )}
        </FilterContainer>
      </Box>
    </Fragment>
  );
};

PaymentPolicyFilter.propTypes = {
  onChange: PropTypes.func.isRequired,
};

export default PaymentPolicyFilter;
