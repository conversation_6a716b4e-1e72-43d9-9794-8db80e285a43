import React from 'react';
import { mountUtils } from 'test-utils';
import PaymentPolicyFilter from './PaymentPolicyFilter';
import {
  getAvailableDepositPayCount,
  getAvailableFreeCancellationCount,
  getAvailableClassicOffersCount,
  getDepositPayAvailabilityMessage,
  getClassicRewardAvailabilityMessage,
} from 'store/search/searchSelectors';
import { getQueryClassicRewards, getQueryDepositPay, getQueryFreeCancellation } from 'store/router/routerSelectors';
import * as config from 'config';

jest.mock('store/search/searchSelectors');
jest.mock('store/router/routerSelectors');
jest.mock('config');

mountUtils.mockComponent('MultiSelectFilterItem');

const onChange = jest.fn();
const depositPayCount = 100;
const depositPayAvailabilityMessage = 'deposit pay availability message';
const classicRewardAvailabilityMessage = 'classic reward availability message';
const freeCancellationCount = 200;
const classicRewardsCount = 9;

const render = () => mountUtils(<PaymentPolicyFilter onChange={onChange} />, { decorators: { store: true, theme: true } });

beforeEach(() => {
  config.CLASSIC_OFFERS_ENABLED = true;
  config.DEPOSIT_PAY_ENABLED = true;
  getAvailableDepositPayCount.mockReturnValue(depositPayCount);
  getAvailableFreeCancellationCount.mockReturnValue(freeCancellationCount);
  getAvailableClassicOffersCount.mockReturnValue(classicRewardsCount);
  getDepositPayAvailabilityMessage.mockReturnValue(depositPayAvailabilityMessage);
  getClassicRewardAvailabilityMessage.mockReturnValue(classicRewardAvailabilityMessage);
  getQueryClassicRewards.mockReturnValue(true);
  getQueryDepositPay.mockReturnValue(true);

  onChange.mockClear();
});

describe('Free cancellation filter', () => {
  describe('with free cancellation true', () => {
    beforeEach(() => {
      getQueryFreeCancellation.mockReturnValue(true);
    });

    it('renders the freeCancellation MultiSelectFilterItem', () => {
      const { find } = render();
      expect(find('MultiSelectFilterItem[type="freeCancellation"]')).toHaveProp({
        name: 'Free cancellation',
        type: 'freeCancellation',
        checked: true,
        count: freeCancellationCount,
        iconName: 'freeCancellation',
      });
    });
  });

  describe('with free cancellation false', () => {
    beforeEach(() => {
      getQueryFreeCancellation.mockReturnValue(false);
    });
    it('renders the freeCancellation MultiSelectFilterItem', () => {
      const { find } = render();
      expect(find('MultiSelectFilterItem[type="freeCancellation"]')).toHaveProp({
        name: 'Free cancellation',
        type: 'freeCancellation',
        checked: false,
        count: freeCancellationCount,
        iconName: 'freeCancellation',
      });
    });
  });

  describe('triggering onChange', () => {
    it('calls on change with the free cancellation state', () => {
      const { find } = render();
      find('MultiSelectFilterItem[type="freeCancellation"]')
        .props()
        .onChange({ target: { name: 'freeCancellation', checked: true } });

      expect(onChange).toHaveBeenCalledWith({
        freeCancellation: true,
      });
    });
  });
});

describe('Deposit pay filter', () => {
  describe('with DEPOSIT_PAY_ENABLED = true', () => {
    beforeEach(() => {
      config.DEPOSIT_PAY_ENABLED = true;
    });

    describe('when the filter is true', () => {
      beforeEach(() => {
        getQueryDepositPay.mockReturnValue(true);
      });

      it('renders the deposit pay MultiSelectFilterItem', () => {
        const { find } = render();
        expect(find('MultiSelectFilterItem[type="depositPay"]')).toHaveProp({
          name: 'Deposit Pay',
          type: 'depositPay',
          checked: true,
          count: depositPayCount,
          description: depositPayAvailabilityMessage,
          iconName: 'depositPay',
        });
      });
    });

    describe('when the filter is false', () => {
      beforeEach(() => {
        getQueryDepositPay.mockReturnValue(false);
      });

      it('renders the deposit pay MultiSelectFilterItem', () => {
        const { find } = render();
        expect(find('MultiSelectFilterItem[type="depositPay"]')).toHaveProp({
          name: 'Deposit Pay',
          type: 'depositPay',
          checked: false,
          count: depositPayCount,
          iconName: 'depositPay',
        });
      });
    });

    describe('triggering onChange', () => {
      it('calls on change with the state', () => {
        const { find } = render();
        find('MultiSelectFilterItem[type="depositPay"]')
          .props()
          .onChange({ target: { name: 'depositPay', checked: true } });

        expect(onChange).toHaveBeenCalledWith({
          depositPay: true,
        });
      });
    });

    describe('with config.DEPOSIT_PAY_ENABLED = false', () => {
      beforeEach(() => {
        config.DEPOSIT_PAY_ENABLED = false;
      });

      it('does not render the deposit pay filter', () => {
        const { find } = render();
        expect(find('MultiSelectFilterItem[type="depositPay"]')).not.toExist();
      });
    });
  });

  describe('Classic rewards filter', () => {
    describe('when the filter is true', () => {
      beforeEach(() => {
        getQueryClassicRewards.mockReturnValue(true);
      });

      it('renders the classic rewards MultiSelectFilterItem', () => {
        const { find } = render();
        expect(find('MultiSelectFilterItem[type="classicRewards"]')).toHaveProp({
          name: 'Classic Rewards',
          type: 'classicRewards',
          checked: true,
          count: classicRewardsCount,
          description: classicRewardAvailabilityMessage,
          iconName: 'ribbon',
        });
      });
    });

    describe('when the filter is false', () => {
      beforeEach(() => {
        getQueryClassicRewards.mockReturnValue(false);
      });

      it('renders the classic rewards MultiSelectFilterItem', () => {
        const { find } = render();
        expect(find('MultiSelectFilterItem[type="classicRewards"]')).toHaveProp({
          name: 'Classic Rewards',
          type: 'classicRewards',
          checked: false,
          count: classicRewardsCount,
          description: classicRewardAvailabilityMessage,
          iconName: 'ribbon',
        });
      });
    });

    describe('triggering onChange', () => {
      it('calls on change with the state', () => {
        const { find } = render();
        find('MultiSelectFilterItem[type="classicRewards"]')
          .props()
          .onChange({ target: { name: 'classicRewards', checked: true } });

        expect(onChange).toHaveBeenCalledWith({ classicRewards: true });
      });
    });

    describe('when `CLASSIC_OFFERS_ENABLED` is false', () => {
      beforeEach(() => {
        config.CLASSIC_OFFERS_ENABLED = false;
        getQueryClassicRewards.mockReturnValue(true);
      });

      it('does NOT render the classic rewards MultiSelectFilterItem', () => {
        const { find } = render();
        expect(find('MultiSelectFilterItem[type="classicRewards"]')).not.toExist();
      });
    });
  });
});
