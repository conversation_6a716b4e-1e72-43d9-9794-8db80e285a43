import React from 'react';
import RatingFilterElement from './RatingFilterElement';
import { Radio, StarRating } from '@qga/roo-ui/components';
import { mountUtils } from 'test-utils';
import { default as TripAdvisorRating } from 'components/TripAdvisorRating/RatingCircles';

const onChange = jest.fn();

const decorators = { theme: true };
const render = (props = {}) =>
  mountUtils(
    <RatingFilterElement
      ratingType="minStarRating"
      text="My star rating"
      currentRating="5"
      rating="5"
      showRatingStars
      onChange={onChange}
      {...props}
    />,
    { decorators },
  );

afterEach(() => {
  onChange.mockClear();
});

describe('<RatingFilterElement />', () => {
  describe('<Label />', () => {
    describe('When passed the text prop', () => {
      it('returns the correct text', () => {
        const { find } = render();
        expect(find('label').text()).toEqual('My star rating');
      });
    });
  });
  describe('<Radio />', () => {
    it('renders the radio button with expected props', () => {
      const { find } = render();
      expect(find(Radio)).toHaveProp({
        name: 'minStarRating',
        value: '5',
      });
    });

    describe('when currentRating is equal to the rating', () => {
      it('renders the radio button with checked=true', () => {
        const { find } = render({ currentRating: '5', rating: '5' });
        expect(find(Radio).props().checked).toBeTruthy();
      });
    });

    describe('when currentRating is not equal to the rating', () => {
      it('renders the radio button with checked=true', () => {
        const { find } = render({ currentRating: '4', rating: '5' });
        expect(find(Radio).props().checked).toBeFalsy();
      });
    });
  });

  describe('<StarRating />', () => {
    describe('When the showRatingStars prop is false', () => {
      it('does not display the minStarRating element', () => {
        const { findByTestId } = render({ showRatingStars: false });
        expect(findByTestId('star-rating')).not.toExist();
      });
    });
  });

  describe('selecting a star rating', () => {
    it('calls onChange with the star rating', () => {
      const { find } = render({ rating: '3' });
      find('input[type="radio"]').simulate('change', { target: { checked: true, value: '3' } });
      expect(onChange).toHaveBeenCalledWith({ minStarRating: '3' });
    });
  });

  describe('when ratingType of minStarRating', () => {
    const { find } = render({ ratingType: 'minStarRating' });
    expect(find(StarRating)).toHaveLength(1);
    expect(find(TripAdvisorRating)).toHaveLength(0);
  });

  describe('when ratingType of minTripadvisorRating', () => {
    const { find } = render({ ratingType: 'minTripadvisorRating' });
    expect(find(StarRating)).toHaveLength(0);
    expect(find(TripAdvisorRating)).toHaveLength(1);
  });
});
