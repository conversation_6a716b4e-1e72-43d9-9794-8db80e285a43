import React, { Fragment, useCallback, useMemo } from 'react';
import styled from '@emotion/styled';
import PropTypes from 'prop-types';
import kebabCase from 'lodash/kebabCase';
import { Box, Radio, StarRating, Label, Text } from '@qga/roo-ui/components';
import FilterLabel from 'components/Filters/FilterLabel';
import { mediaQuery } from 'lib/styledSystem';
import { default as TripAdvisorRating } from 'components/TripAdvisorRating/RatingCircles';

const RatingWrapper = styled(FilterLabel)`
  display: flex;
  justify-content: space-between;
  align-items: center;

  ${mediaQuery.minWidth.md} {
    &:first-of-type {
      &:not(:last-of-type) {
        padding-top: 0;
      }
    }
  }

  &:last-of-type {
    border: 0;
    margin: 0;
  }
`;

const RatingLabel = styled(Label)`
  margin-bottom: 0;
  text-align: left;
`;

const RatingFilterElement = ({ onChange, ratingType, text, rating, showRatingStars, currentRating }) => {
  const isChecked = useMemo(() => rating === currentRating || (!rating && !currentRating), [rating, currentRating]);

  const radioCheckedHandler = useCallback(
    (event) => {
      if (event.target.checked) {
        onChange({ [ratingType]: event.target.value });
      }
    },
    [onChange, ratingType],
  );

  return (
    <Fragment>
      <RatingWrapper isChecked={isChecked} as="form">
        <RatingLabel id={`${ratingType}-${kebabCase(text)}`}>
          <Radio
            name={ratingType}
            value={rating}
            onChange={radioCheckedHandler}
            checked={isChecked}
            aria-labelledby={`${ratingType}-${kebabCase(text)}`}
          />
          <Text fontWeight={['bold', 'normal']} fontSize="sm">
            {text}
          </Text>
        </RatingLabel>
      </RatingWrapper>
      {showRatingStars && (
        <Box data-testid="star-rating">
          {ratingType === 'minStarRating' && <StarRating ratingType="SELF_RATED" rating={rating} size="16" />}
          {ratingType === 'minTripadvisorRating' && <TripAdvisorRating rating={rating} size="16" />}
        </Box>
      )}
    </Fragment>
  );
};

RatingFilterElement.propTypes = {
  ratingType: PropTypes.string.isRequired,
  text: PropTypes.string.isRequired,
  rating: PropTypes.oneOf(['5', '4', '3']),
  showRatingStars: PropTypes.bool,
  currentRating: PropTypes.string,
  onChange: PropTypes.func.isRequired,
};

RatingFilterElement.defaultProps = {
  showRatingStars: true,
  currentRating: undefined,
  rating: undefined,
};

export default RatingFilterElement;
