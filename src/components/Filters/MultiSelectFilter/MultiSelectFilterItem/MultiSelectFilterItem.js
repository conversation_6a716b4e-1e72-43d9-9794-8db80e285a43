import React from 'react';
import PropTypes from 'prop-types';
import styled from '@emotion/styled';
import FilterLabel from 'components/Filters/FilterLabel';
import { mediaQuery } from 'lib/styledSystem';
import { Checkbox, Box, Text, Flex, Icon } from '@qga/roo-ui/components';

const BlockText = styled(Text)`
  font-weight: inherit;
  display: block;
  :first-letter {
    text-transform: capitalize;
  }
`;

const OpacityLabelWrapper = styled(FilterLabel)`
  opacity: 1;
  transition: opacity 0.25s ease-in-out;
  display: flex;

  &[data-disabled='true'] {
    opacity: 0.5;
  }

  ${mediaQuery.minWidth.md} {
    &:first-of-type {
      &:not(:last-of-type) {
        padding-top: 0;
      }
    }
  }

  &:last-of-type {
    border: 0;
    margin: 0;
  }
`;

const MultiSelectFilterItem = ({ checked, type, onChange, name, count, displayPlusIcon, hideCount, description, iconName }) => {
  const isDisabled = count === 0 && !checked;
  return (
    <OpacityLabelWrapper key={type} name={type} isChecked={checked} data-disabled={isDisabled}>
      <Checkbox name={type} checked={checked} onChange={onChange} disabled={isDisabled} />
      <Flex alignItems="center" justifyContent="space-between" width="100%">
        <Box>
          <Box>
            {iconName && <Icon name={iconName} size={[0, 22]} />}
            <Text fontSize="sm" ml={2}>
              {name}
            </Text>
          </Box>
          {description && (
            <BlockText fontSize="xs" data-testid="description">
              ({description})
            </BlockText>
          )}
        </Box>
        <Box textAlign="right" width="30px">
          {(!checked || isDisabled) && !hideCount && (
            <Text color="greys.dusty" fontWeight="inherit" fontSize="sm">
              {displayPlusIcon && '+'}
              {count}
            </Text>
          )}
        </Box>
      </Flex>
    </OpacityLabelWrapper>
  );
};

MultiSelectFilterItem.propTypes = {
  checked: PropTypes.bool,
  type: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  name: PropTypes.string.isRequired,
  count: PropTypes.number,
  displayPlusIcon: PropTypes.bool,
  hideCount: PropTypes.bool,
  description: PropTypes.string,
  iconName: PropTypes.string,
};

MultiSelectFilterItem.defaultProps = {
  checked: false,
  displayPlusIcon: false,
  hideCount: false,
  count: null,
  description: null,
  iconName: null,
};

export default MultiSelectFilterItem;
