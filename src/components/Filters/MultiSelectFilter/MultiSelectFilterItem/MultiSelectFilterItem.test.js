import React from 'react';
import { mountUtils } from 'test-utils';
import MultiSelectFilterItem from './MultiSelectFilterItem';

const props = {
  checked: false,
  type: 'motels',
  onChange: jest.fn(),
  name: 'Motels',
  count: 20,
};

const decorators = { theme: true };
const render = (newProps) => mountUtils(<MultiSelectFilterItem {...props} {...newProps} />, { decorators });

describe('label & count', () => {
  it('renders label with property type and counts', () => {
    const { find } = render();
    expect(find(MultiSelectFilterItem).text()).toEqual('Motels20');
  });

  it('renders label with property type and without counts', () => {
    const { find } = render({ hideCount: true });
    expect(find(MultiSelectFilterItem).text()).toEqual('Motels');
  });

  it('renders FilterLabel with correct props', () => {
    const { find } = render();
    expect(find('OpacityLabelWrapper')).toHaveProp({
      isChecked: props.checked,
    });
  });
});

describe('description', () => {
  it('does NOT render a description', () => {
    const { findByTestId } = render();

    expect(findByTestId('description')).not.toExist();
  });

  describe('with a description prop', () => {
    it('renders a description', () => {
      const description = 'test description';
      const { findByTestId } = render({ description });

      expect(findByTestId('description')).toHaveText('(test description)');
    });
  });
});

describe('icon', () => {
  it('does NOT render an icon', () => {
    const { find } = render();

    expect(find('Icon')).not.toExist();
  });

  describe('with a iconName prop', () => {
    it('renders an icon', () => {
      const iconName = 'freeCancellation';
      const { find } = render({ iconName });

      expect(find('Icon')).toHaveProp({
        name: iconName,
      });
    });
  });
});

describe('checkbox', () => {
  it('renders Checkbox with correct props', () => {
    const { find } = render();
    expect(find('Checkbox')).toHaveProp({
      name: props.type,
      checked: props.checked,
      onChange: props.onChange,
    });
  });

  describe('when count = 0', () => {
    const count = 0;

    describe('when not checked', () => {
      const checked = false;

      it('disabled = true', () => {
        const { find } = render({ count, checked });
        expect(find('Checkbox')).toHaveProp({
          disabled: true,
        });
      });
    });

    describe('when checked', () => {
      const checked = true;

      it('disabled = false', () => {
        const { find } = render({ count, checked });
        expect(find('Checkbox')).toHaveProp({
          disabled: false,
        });
      });
    });
  });

  describe('when count > 0', () => {
    const count = 8;

    describe('when not checked', () => {
      const checked = false;

      it('disabled = false', () => {
        const { find } = render({ count, checked });
        expect(find('Checkbox')).toHaveProp({
          disabled: false,
        });
      });
    });

    describe('when checked', () => {
      const checked = true;

      it('disabled = false', () => {
        const { find } = render({ count, checked });
        expect(find('Checkbox')).toHaveProp({
          disabled: false,
        });
      });
    });
  });
});
