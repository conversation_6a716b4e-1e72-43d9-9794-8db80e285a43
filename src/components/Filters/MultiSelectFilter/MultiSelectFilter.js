import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import without from 'lodash/without';
import noop from 'lodash/noop';
import { Heading, Box, Flex } from '@qga/roo-ui/components';
import FilterContainer from 'components/Filters/FilterContainer';
import MultiSelectFilterItem from './MultiSelectFilterItem';
import TextButton from 'components/TextButton';

const MultiSelectFilter = ({
  onChange,
  hideCount,
  displayClearFilter,
  onClearFilter,
  filterKey,
  availableItems,
  selectedItems,
  displayPlusIcon,
  title,
  ...rest
}) => {
  const handleChange = useCallback(
    (event) => {
      const { name: type, checked } = event.target;
      onChange({
        [filterKey]: checked ? [...selectedItems, type] : without(selectedItems, type),
      });
    },
    [filterKey, onChange, selectedItems],
  );

  const isChecked = useCallback((type) => (selectedItems || []).includes(type), [selectedItems]);

  return (
    <Box {...rest}>
      <Flex justifyContent="space-between">
        <Heading.h2 fontSize="sm">{title}</Heading.h2>
        {displayClearFilter && (
          <Box>
            <TextButton onClick={onClearFilter} fontSize="sm" data-testid="clear-filters-button">
              Clear
            </TextButton>
          </Box>
        )}
      </Flex>
      <Box mb={[0, displayClearFilter ? 0 : 6]}>
        <FilterContainer>
          {availableItems.map((filter) => (
            <MultiSelectFilterItem
              key={filter.name}
              name={filter.name}
              count={filter.count}
              type={filter.type}
              checked={isChecked(filter.type)}
              onChange={handleChange}
              displayPlusIcon={displayPlusIcon && selectedItems.length > 0}
              hideCount={hideCount}
            />
          ))}
        </FilterContainer>
      </Box>
    </Box>
  );
};

MultiSelectFilter.propTypes = {
  onChange: PropTypes.func.isRequired,
  hideCount: PropTypes.bool,
  displayClearFilter: PropTypes.bool,
  onClearFilter: PropTypes.func,
  availableItems: PropTypes.arrayOf(String).isRequired,
  selectedItems: PropTypes.arrayOf(String).isRequired,
  filterKey: PropTypes.string.isRequired,
  displayPlusIcon: PropTypes.bool,
  title: PropTypes.string.isRequired,
};

MultiSelectFilter.defaultProps = {
  hideCount: false,
  displayClearFilter: false,
  onClearFilter: noop,
  displayPlusIcon: false,
};

export default MultiSelectFilter;
