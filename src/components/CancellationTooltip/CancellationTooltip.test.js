import React from 'react';
import { mountUtils } from 'test-utils';
import CancellationTooltip from './CancellationTooltip';
import { addDays, format as formatDate } from 'lib/date';
import { DISPLAY_DATE_FORMAT } from 'config';

jest.mock('react-popper', () => ({
  usePopper: () => ({
    styles: {},
    attributes: {},
  }),
}));

jest.mock('lodash/debounce', () => (fn) => {
  const debounced = fn;
  debounced.cancel = jest.fn();
  return debounced;
});

const nonRefundableCancellationPolicy = {
  isNonrefundable: true,
  description: 'Non Refundable description',
  cancellationWindows: [],
};

const nextWeek = addDays(new Date(), 7);

const refundableCancellationPolicy = {
  isNonrefundable: false,
  description: 'Free Cancellation description',
  cancellationWindows: [
    {
      startTime: nextWeek.toString(),
      formattedBeforeDate: formatDate(nextWeek, DISPLAY_DATE_FORMAT),
      nights: '1',
    },
  ],
};

const partiallyRefundableCancellationPolicy = {
  isNonrefundable: false,
  description: 'Partially Refundable description',
  cancellationWindows: [
    {
      startTime: new Date('2020/01/01').toString(),
      formattedBeforeDate: 'Wed 1 Jan, 2020',
      nights: '1',
    },
  ],
};

const decorators = { theme: true };
const render = (props) => mountUtils(<CancellationTooltip {...props} />, { decorators });

describe('<CancellationTooltip />', () => {
  describe('when tooltip is closed', () => {
    it('renders the non-refundable CTA', () => {
      const { find } = render({ cancellationPolicy: nonRefundableCancellationPolicy });
      expect(find('NakedButton')).toHaveText('Non-refundable');
    });

    it('renders the refundable CTA', () => {
      const { find } = render({ cancellationPolicy: refundableCancellationPolicy });
      expect(find('NakedButton')).toHaveText(`Free cancellationbefore ${formatDate(nextWeek, DISPLAY_DATE_FORMAT)}`);
      expect(find('Icon')).toHaveProp({ name: 'freeCancellation' });
    });

    it('renders the partially refundable CTA', () => {
      const { find } = render({ cancellationPolicy: partiallyRefundableCancellationPolicy });
      expect(find('NakedButton')).toHaveText('Cancellation Policy');
    });
  });

  describe('when tooltip is open', () => {
    it('renders the non-refundable description', () => {
      const { find, findByTestId } = render({ cancellationPolicy: nonRefundableCancellationPolicy });
      find('NakedButton').simulate('mouseEnter');
      expect(findByTestId('cancellation-type-header')).toHaveText('Non-refundable');
      expect(find('Markup')).toHaveText('Non Refundable description');
      find('button').simulate('mouseLeave');
      expect(find('Markup')).not.toExist();
    });

    it('renders the refundable description', () => {
      const { find, findByTestId } = render({ cancellationPolicy: refundableCancellationPolicy });
      find('NakedButton').simulate('mouseEnter');
      expect(findByTestId('cancellation-type-header')).toHaveText(`Free cancellationbefore ${formatDate(nextWeek, DISPLAY_DATE_FORMAT)}`);
      expect(find('Markup')).toHaveText('Free Cancellation description');
      find('button').simulate('mouseLeave');
      expect(find('Markup')).not.toExist();
    });

    it('renders the partially refundable description', () => {
      const { find, findByTestId } = render({ cancellationPolicy: partiallyRefundableCancellationPolicy });
      find('NakedButton').simulate('mouseEnter');
      expect(findByTestId('cancellation-type-header')).toHaveText('Cancellation Policy');
      expect(find('Markup')).toHaveText('Partially Refundable description');
      find('NakedButton').simulate('mouseLeave');
      expect(find('Markup')).not.toExist();
    });
  });
});
