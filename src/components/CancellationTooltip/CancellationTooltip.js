import React from 'react';
import PropTypes from 'prop-types';
import Markup from 'components/Markup';
import { Box, NakedButton } from '@qga/roo-ui/components';
import CancellationRefundSummary from 'components/CancellationRefundSummary';
import { useTooltip } from 'lib/hooks';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';

const TooltipWrapper = styled(Box)`
  box-shadow: ${themeGet('shadows.tooltip')};
  color: ${themeGet('colors.greys.steel')};
  background-color: ${themeGet('colors.white')};
  padding: ${themeGet('space.6')} ${themeGet('space.8')};
  border-radius: ${themeGet('radii.default')};
  width: ${themeGet('uiStructure.tooltipWidth')};
  text-align: left;
`;

const CancellationTooltip = ({ cancellationPolicy }) => {
  const { description } = cancellationPolicy;
  const { triggerProps, tooltipProps, isOpen } = useTooltip('cancellation-policy-button');

  return (
    <NakedButton {...triggerProps} pb={2}>
      <CancellationRefundSummary cancellationPolicy={cancellationPolicy} />
      {isOpen && (
        <Box {...tooltipProps}>
          <TooltipWrapper>
            <CancellationRefundSummary mb={4} data-testid="cancellation-type-header" cancellationPolicy={cancellationPolicy} />
            <Markup content={description} mt={4} fontSize="base" color="greys.steel" />
          </TooltipWrapper>
        </Box>
      )}
    </NakedButton>
  );
};

CancellationTooltip.propTypes = {
  cancellationPolicy: PropTypes.object.isRequired,
};

export default CancellationTooltip;
