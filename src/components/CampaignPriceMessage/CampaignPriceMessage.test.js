import React from 'react';
import { mountUtils } from 'test-utils';
import CampaignPriceMessage from './CampaignPriceMessage';
import { getCampaignPriceMessages, getCampaignCountries } from 'store/campaign/campaignSelectors';

jest.mock('store/campaign/campaignSelectors');

const campaignMessages = {
  cash: 'Save 10% when you use cash',
  points: 'Save 20% when you use points',
};

const defaultProps = {
  currency: 'PTS',
  offerType: 'NON classic',
  country: 'Australia',
};

const decorators = { store: true };
const render = (props) => mountUtils(<CampaignPriceMessage {...defaultProps} {...props} />, { decorators });

beforeEach(() => {
  getCampaignPriceMessages.mockReturnValue(campaignMessages);
});

describe('CampaignPriceMessage', () => {
  describe('when the offer is classic', () => {
    it('does not render the message', () => {
      const { findByTestId } = render({ offerType: 'classic' });

      expect(findByTestId('campaign-price-message')).not.toExist();
    });
  });

  describe('when the offer has not got a campaign message', () => {
    beforeEach(() => {
      getCampaignPriceMessages.mockReturnValue({});
    });

    it('does not render the message', () => {
      const { findByTestId } = render();

      expect(findByTestId('campaign-price-message')).not.toExist();
    });
  });

  describe('when is not classic, has a campaign message and it is in POINTS mode', () => {
    it('renders the correct message', () => {
      const { findByTestId } = render();

      expect(findByTestId('campaign-price-message')).toHaveText('Save 20% when you use points');
    });
  });

  describe('when is not classic, has a campaign message and it is in CASH mode', () => {
    it('renders the correct message', () => {
      const { findByTestId } = render({ currency: 'cash' });

      expect(findByTestId('campaign-price-message')).toHaveText('Save 10% when you use cash');
    });
  });

  describe('when campaign includes the current country (case insensitive)', () => {
    it('displays the price message', () => {
      getCampaignCountries.mockReturnValue({ include: ['australia'] });
      const { findByTestId } = render({ currency: 'cash' });

      expect(findByTestId('campaign-price-message')).toHaveText('Save 10% when you use cash');
    });
  });

  describe('when campaign does not include the current country', () => {
    it('does not display the price message', () => {
      getCampaignCountries.mockReturnValue({ include: ['New Zealand'] });
      const { findByTestId } = render({ currency: 'cash' });

      expect(findByTestId('campaign-price-message')).not.toExist();
    });
  });

  describe('when campaign excludes the current country (case insensitive)', () => {
    it('does not display the price message', () => {
      getCampaignCountries.mockReturnValue({ exclude: ['Australia'] });
      const { findByTestId } = render({ currency: 'cash' });

      expect(findByTestId('campaign-price-message')).not.toExist();
    });
  });

  describe('when campaign does not exclude the current country', () => {
    it('displays the price message', () => {
      getCampaignCountries.mockReturnValue({ exclude: ['New Zealand'] });
      const { findByTestId } = render({ currency: 'cash' });

      expect(findByTestId('campaign-price-message')).toHaveText('Save 10% when you use cash');
    });
  });
});
