import React from 'react';
import get from 'lodash/get';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import { Text } from '@qga/roo-ui/components';
import { getCampaignCountries, getCampaignPriceMessages } from 'store/campaign/campaignSelectors';

const caseInsenstiveIncludes = (array, string) => !!array.find((item) => (item || '').toLowerCase() === (string || '').toLowerCase());

const isValidCountry = ({ country, includedCountries, excludedCountries }) =>
  (includedCountries.length === 0 || caseInsenstiveIncludes(includedCountries, country)) &&
  (excludedCountries.length === 0 || !caseInsenstiveIncludes(excludedCountries, country));

const CampaignPriceMessage = ({ currency, offerType, country }) => {
  const isCurrencyPoints = currency === 'PTS';
  const isClassic = offerType === 'classic';
  const campaignPriceMessages = useSelector(getCampaignPriceMessages);
  const { include: includedCountries = [], exclude: excludedCountries = [] } = useSelector(getCampaignCountries) || {};

  const getCampaignPriceMessage = ({ isCurrencyPoints, campaignPriceMessages }) =>
    isCurrencyPoints ? get(campaignPriceMessages, 'points') : get(campaignPriceMessages, 'cash');
  const campaignPriceMessage = getCampaignPriceMessage({ isCurrencyPoints, campaignPriceMessages });

  const showCampaignPriceMessage = !isClassic && campaignPriceMessage && isValidCountry({ country, includedCountries, excludedCountries });

  if (!showCampaignPriceMessage) return null;

  return (
    <Text color="brand.primary" fontSize="sm" textAlign="left" mb={1} data-testid="campaign-price-message">
      {campaignPriceMessage}
    </Text>
  );
};

CampaignPriceMessage.propTypes = {
  currency: PropTypes.string.isRequired,
  offerType: PropTypes.string,
  country: PropTypes.string,
};

CampaignPriceMessage.defaultProps = {
  offerType: undefined,
};

export default CampaignPriceMessage;
