import React, { createRef } from 'react';
import { act } from 'react-dom/test-utils';
import { useInView } from 'react-intersection-observer';
import { mountUtils } from 'test-utils';
import InPageNavBar from './InPageNavBar';
import { useBreakpoints } from 'hooks/useBreakpoints';
import { useDataLayer } from 'hooks/useDataLayer';
import { isLegacyBrowser, scrollTo } from 'lib/browser';
import { getBrowser } from 'store/userEnvironment/userEnvironmentSelectors';
import { calculateMenuOffset } from './calculateMenuOffset';

jest.mock('react', () => {
  const originReact = jest.requireActual('react');
  const mockCreateRef = jest.fn();
  return {
    ...originReact,
    createRef: mockCreateRef,
  };
});
jest.mock('./calculateMenuOffset');
jest.mock('hooks/useBreakpoints');
jest.mock('hooks/useDataLayer');
jest.mock('react-intersection-observer');
jest.mock('lib/browser');
jest.mock('store/userEnvironment/userEnvironmentSelectors');

const emitInteractionEvent = jest.fn();
const isLessThanBreakpoint = jest.fn();

document.body.innerHTML = '<div id="mock"/>';
const mockedElement = {
  ...document.getElementById('mock'),
  offsetTop: 50,
};
mockedElement.getBoundingClientRect = jest.fn().mockReturnValue({ top: 200 });

document.body.innerHTML = '<div id="location-mock"/>';
const locationMockedElement = {
  ...document.getElementById('location-mock'),
  offsetTop: 100,
};
locationMockedElement.getBoundingClientRect = jest.fn().mockReturnValue({ top: 300 });

let intersection;
useInView.mockImplementation(() => [React.createRef(), intersection]);
IntersectionObserver.prototype.observe = jest.fn();

const menuRef = {
  current: {
    scrollLeft: 0,
  },
};
const linkRef = {
  current: {
    scrollWidth: 50,
  },
};

const menuHeight = 10;
const menuItems = [
  { name: 'photos', text: 'Photos', linkRef, ref: { current: mockedElement } },
  { name: 'rooms', text: 'Rooms', linkRef, ref: { current: mockedElement } },
  { name: 'about', text: 'About this property', linkRef, ref: { current: mockedElement } },
  { name: 'location', text: 'Location', linkRef, ref: { current: locationMockedElement } },
  { name: 'policies', text: 'Property policies', linkRef, ref: { current: mockedElement } },
];

const render = (props) => mountUtils(<InPageNavBar {...props} />, { decorators: { store: true } });

beforeEach(() => {
  jest.clearAllMocks();
  useBreakpoints.mockReturnValue({ isLessThanBreakpoint });
  calculateMenuOffset.mockReturnValue(50);
  createRef.mockReturnValue(menuRef);
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  isLegacyBrowser.mockReturnValue(false);
  getBrowser.mockReturnValue({ name: 'browser name' });
});

describe('<InPageNavBar />', () => {
  describe('with a legacy browser', () => {
    beforeEach(() => {
      isLegacyBrowser.mockReturnValue(true);
    });

    it('does not render', () => {
      const { find } = render({ menuItems, menuHeight });
      expect(find('Wrapper')).not.toExist();
    });
  });

  it('adds all menu items to the intersection observer', () => {
    render({ menuItems, menuHeight });
    menuItems.forEach((item) => {
      expect(IntersectionObserver.prototype.observe).toHaveBeenCalledWith(item.ref.current);
    });
  });

  it('shows the nav links', () => {
    const { find } = render({ menuItems });

    expect(find('NavLink')).toHaveLength(5);
  });

  it('renders <Wrapper /> with the correct styles', () => {
    const { find } = render();

    expect(find('Wrapper')).toHaveProp({
      position: 'sticky',
      zIndex: 'stickyNavigation',
    });
  });
  describe('menuRefPositionOffset prop', () => {
    it('accepts a custom value', () => {
      const { find } = render({ menuRefPositionOffset: 80 });
      expect(find('InPageNavBar')).toHaveProp({ menuRefPositionOffset: 80 });
    });

    it('has a default value of 120', () => {
      const { find } = render();
      expect(find('InPageNavBar')).toHaveProp({ menuRefPositionOffset: 120 });
    });
  });

  describe('when clicking a link', () => {
    beforeEach(() => {
      const { wrapper, findByText } = render({ menuItems });
      const aboutButton = findByText('About this property');

      act(() => {
        aboutButton.simulate('click');
      });
      wrapper.update();
    });

    it('emits an event to the data layer', () => {
      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'In Page Navigation',
        value: 'About This Property Selected',
      });
    });

    it('calls scrollTo with the correct options', () => {
      expect(scrollTo).toHaveBeenCalledWith({
        behavior: 'instant',
        top: 170,
      });
    });
  });

  describe('when the active section is updated', () => {
    describe('and with a mobile viewport', () => {
      beforeEach(() => {
        isLessThanBreakpoint.mockReturnValue(true);

        const { wrapper, findByText } = render({ menuItems });
        const aboutButton = findByText('About this property');

        act(() => {
          aboutButton.simulate('click');
        });

        wrapper.update();
      });

      it('calls calculateMenuOffset', () => {
        expect(calculateMenuOffset).toHaveBeenCalledWith(menuItems, 'about');
      });

      it('scrolls into view', () => {
        expect(menuRef.current.scrollLeft).toEqual(50);
      });
    });

    describe('and with a desktop viewport', () => {
      beforeEach(() => {
        isLessThanBreakpoint.mockReturnValue(false);

        const { wrapper, findByText } = render({ menuItems });
        const aboutButton = findByText('About this property');

        act(() => {
          aboutButton.simulate('click');
        });

        wrapper.update();
      });

      it('calls calculateMenuOffset', () => {
        expect(calculateMenuOffset).not.toHaveBeenCalledWith();
      });

      it('does NOT scroll into view', () => {
        expect(menuRef.current.scrollLeft).toEqual(0);
      });
    });
  });
});
