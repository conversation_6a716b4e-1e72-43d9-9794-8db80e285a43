import React from 'react';
import { mountUtils } from 'test-utils';
import Dropdown from './Dropdown';

const defaultDimensions = { width: 100, height: 100, top: 0, left: 0 };

Element.prototype.getBoundingClientRect = jest.fn().mockReturnValue(defaultDimensions);

const render = (props) => mountUtils(<Dropdown viewThreshold={0.5} {...props}></Dropdown>);

it('renders children', () => {
  const { findByTestId } = render({ children: <div data-testid="dropdown-content" /> });
  expect(findByTestId('dropdown-content')).toExist();
});

it('does NOT render the skip to content button', () => {
  const { find } = render({ children: <div data-testid="dropdown-content" /> });
  expect(find('SkipToContentButton')).not.toExist();
});

it('renders the skip to content button', () => {
  const { find } = render({ skipToContent: '#content' });
  expect(find('SkipToContentButton')).toExist();
});

it('renders below the parent by default', () => {
  const { find, wrapper } = render();
  wrapper.update();

  expect(find('DropdownBase')).toHaveProp({ top: '100%' });
  expect(find('DropdownBase')).not.toHaveProp({ bottom: '100%' });
});

describe('when the viewThreshold is set to 1', () => {
  const props = { viewThreshold: 1 };

  it('renders above when there is not enough room to show the whole dropdown', () => {
    window.innerHeight = 1000;
    Element.prototype.getBoundingClientRect.mockReturnValue({ height: 100, top: 900 });

    const { find, wrapper } = render(props);
    wrapper.update();

    expect(find('DropdownBase')).toHaveProp({ bottom: '100%' });
    expect(find('DropdownBase')).not.toHaveProp({ top: '100%' });
  });

  it('renders below when there is enough room to show the whole dropdown', () => {
    window.innerHeight = 1000;
    Element.prototype.getBoundingClientRect.mockReturnValue({ height: 100, top: 899 });

    const { find, wrapper } = render(props);
    wrapper.update();

    expect(find('DropdownBase')).toHaveProp({ top: '100%' });
    expect(find('DropdownBase')).not.toHaveProp({ bottom: '100%' });
  });
});

describe('when the viewThreshold is set to 0.5', () => {
  const props = { viewThreshold: 0.5 };

  it('renders above when there is not enough room to show half the dropdown', () => {
    window.innerHeight = 1000;
    Element.prototype.getBoundingClientRect.mockReturnValue({ height: 500, top: 750 });

    const { find, wrapper } = render(props);
    wrapper.update();

    expect(find('DropdownBase')).toHaveProp({ bottom: '100%' });
    expect(find('DropdownBase')).not.toHaveProp({ top: '100%' });
  });

  it('renders below when there is enough room to show half the dropdown', () => {
    window.innerHeight = 1000;
    Element.prototype.getBoundingClientRect.mockReturnValue({ height: 500, top: 749 });

    const { find, wrapper } = render(props);
    wrapper.update();

    expect(find('DropdownBase')).toHaveProp({ top: '100%' });
    expect(find('DropdownBase')).not.toHaveProp({ bottom: '100%' });
  });
});
