import React, { useMemo, useCallback } from 'react';
import PropTypes from 'prop-types';
import { themeGet } from 'styled-system';
import styled from '@emotion/styled';
import { Wrapper, Flex, Icon, omitProps, Text } from '@qga/roo-ui/components';
import { mediaQuery } from 'lib/styledSystem';
import Modal from 'components/Modal';
import FacilitiesList from 'components/FacilitiesList';
import mapFacilities from './lib/mapFacilities';
import { useModal } from 'lib/hooks';
import { useDataLayer } from 'hooks/useDataLayer';
import TextButton from 'components/TextButton';

const facilitiesPerRow = 3;
const facilitiesPerRowSummary = 2;

const FacilitiesWrapper = styled(Flex, omitProps(['summary']))`
  flex-flow: row wrap;
  justify-content: left;
  align-items: ${(props) => (props.summary ? 'center' : 'flex-start')};
  flex-direction: ${(props) => (props.summary ? 'row' : 'column')};

  ${mediaQuery.minWidth.sm} {
    flex-direction: row;
  }
`;

const Facility = styled(Flex, omitProps(['summary']))`
  margin-bottom: ${themeGet('space.4')};
  justify-content: center;
  align-items: left;
  font-size: ${(props) => (props.summary ? themeGet('fontSizes.sm')(props) : themeGet('fontSizes.md')(props))};
  padding-right: 0 ${(props) => (props.summary ? themeGet('space.2') : 0)};
  min-width: ${(props) => (props.summary ? `calc(100% / ${facilitiesPerRowSummary} - 8px)` : `initial`)};

  ${mediaQuery.minWidth.sm} {
    flex-basis: calc(100% / ${facilitiesPerRow});
  }

  ${mediaQuery.minWidth.md} {
    max-width: ${(props) => (props.summary ? `calc(100% / ${facilitiesPerRowSummary} - 16px)` : `calc(100% / ${facilitiesPerRow})`)};
  }
`;

Facility.displayName = 'Facility';

Facility.propTypes = {
  summary: PropTypes.bool,
};

const PropertyFacilities = React.memo(({ facilities, summary, interactionEventValue, ...rest }) => {
  const { openModal, modalProps } = useModal();
  const { emitInteractionEvent } = useDataLayer();
  const maxFacilities = summary ? facilitiesPerRowSummary * 2 : facilitiesPerRow * 3;
  const normalisedFacilities = useMemo(() => mapFacilities(facilities, maxFacilities), [facilities, maxFacilities]);

  const handleOnClick = useCallback(() => {
    openModal();
    emitInteractionEvent({ type: 'Features Pop Up', value: interactionEventValue });
  }, [interactionEventValue, emitInteractionEvent, openModal]);

  if (!facilities || facilities.length === 0) return null;

  return (
    <Wrapper {...rest}>
      <FacilitiesWrapper summary={summary}>
        {normalisedFacilities.map((facility) => (
          <Facility key={facility.originalLabel} summary={summary}>
            <Flex width="100%" alignItems="center">
              <Flex
                pr={3}
                justifyContent="left"
                alignItems="center"
                mr={summary ? 0 : 3}
                mb={0}
                textAlign="left"
                data-testid="facility-icon"
              >
                <Icon name={facility.icon} size={24} color="greys.charcoal" />
              </Flex>
              <Text textAlign="left" data-testid="facility-text" fontSize="base">
                {summary ? facility.normalisedLabel : facility.originalLabel}
              </Text>
            </Flex>
          </Facility>
        ))}
      </FacilitiesWrapper>
      <Flex>
        <TextButton onClick={handleOnClick} fontSize="base" py="2" data-testid="view-all">
          View all property facilities
        </TextButton>
      </Flex>
      <Modal {...modalProps} title="Property features">
        <FacilitiesList facilities={facilities} />
      </Modal>
    </Wrapper>
  );
});

PropertyFacilities.displayName = 'PropertyFacilities';

PropertyFacilities.propTypes = {
  facilities: PropTypes.arrayOf(PropTypes.string),
  summary: PropTypes.bool,
  interactionEventValue: PropTypes.string.isRequired,
};

PropertyFacilities.defaultProps = {
  facilities: [],
  summary: false,
};

export default PropertyFacilities;
