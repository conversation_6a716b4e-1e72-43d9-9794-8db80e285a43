import sortBy from 'lodash/sortBy';
import compact from 'lodash/compact';
import uniqBy from 'lodash/uniqBy';

const FACILITY_ICON_MAPPING = {
  'Wi-Fi': { icon: 'wifi', order: 1 },
  'Swimming Pool': { icon: 'pool', order: 2 },
  Restaurant: { icon: 'restaurant', order: 3 },
  'Luggage Storage': { icon: 'work', order: 4 },
  Parking: { icon: 'localParking', order: 5 },
  'Smoke Free': { icon: 'smokeFree', order: 6 },
  'Fitness Centre': { icon: 'fitnessCenter', order: 7 },
  '24/7 Desk': { icon: 'roomService', order: 8 },
  'Laundry Services': { icon: 'localLaundryService', order: 9 },
  'Business Services': { icon: 'businessCenter', order: 10 },
  'Bar/Lounge': { icon: 'localBar', order: 11 },
  'Airport Shuttle': { icon: 'directionsBus', order: 12 },
  'Day Spa': { icon: 'spa', order: 13 },
  Accessible: { icon: 'accessible', order: 14 },
  'Breakfast Available': { icon: 'freeBreakfast', order: 15 },
  'Room Service': { icon: 'roomService', order: 16 },
};

const FACILITY_KEYWORD_MAPPING = {
  wifi: 'Wi-Fi',
  'wi fi': 'Wi-Fi',
  'wireless internet': 'Wi-Fi',
  'swimming pool': 'Swimming Pool',
  'lap pool': 'Swimming Pool',
  'outdoor pool': 'Swimming Pool',
  'indoor pool': 'Swimming Pool',
  restaurant: 'Restaurant',
  luggage: 'Luggage Storage',
  parking: 'Parking',
  'smoke free': 'Smoke Free',
  'non smoking': 'Smoke Free',
  gym: 'Fitness Centre',
  fitness: 'Fitness Centre',
  'health club': 'Fitness Centre',
  laundry: 'Laundry Services',
  business: 'Business Services',
  bar: 'Bar/Lounge',
  lounge: 'Bar/Lounge',
  lounges: 'Bar/Lounge',
  'airport transportation': 'Airport Shuttle',
  'airport shuttle': 'Airport Shuttle',
  'day spa': 'Day Spa',
  'health spa': 'Day Spa',
  'spa services': 'Day Spa',
  'spa treatment': 'Day Spa',
  'full service spa': 'Day Spa',
  'beauty services': 'Day Spa',
  massage: 'Day Spa',
  '24 hour reception': '24/7 Desk',
  'reception 24 hour': '24/7 Desk',
  '24 hour front desk': '24/7 Desk',
  'disabled access': 'Accessible',
  accessible: 'Accessible',
  accessibility: 'Accessible',
  'wheel chair access': 'Accessible',
  'roll in shower': 'Accessible',
  breakfast: 'Breakfast Available',
  'room service': 'Room Service',
};

const FACILITY_SEARCH_TERMS = Object.keys(FACILITY_KEYWORD_MAPPING);

const nonWordRegExp = /\s*\W\s*/g; //remove non-word chars
const multiWhitespaceRegExp = /\s+/g; // compress multiple spaces to 1
const trimWhitespaceRegExp = /\b.*\b/; //trim elading/trailing white-space

/*
  Takes an array of facilities as strings and returns a normalised array sorted by the order 
  defined in FACILITY_ICON_MAPPING of facility objects that match FACILITY_KEYWORD_MAPPING in 
  the following format:

  { order, icon, originalLabel, normalisedLabel }

  Matching rules are basic... 
  1. replace non word characters with spaces and trim leading/trailing white space to reduce the 
     double up of strings to match against when hyphens or brackets etc are used.
  2. search for any key from FACILITY_KEYWORD_MAPPING that matches any simplified facility passed on word  
     boundaries so we don't find things like 'bar' within larger words
*/
const mapFacilities = (facilityNames, max) => {
  const normalisedFacilities = facilityNames.map((facilityName) => {
    const simplifiedFacilityName = facilityName
      .toLowerCase()
      .replace(nonWordRegExp, ' ')
      .replace(multiWhitespaceRegExp, ' ')
      .match(trimWhitespaceRegExp)[0];

    const match = FACILITY_SEARCH_TERMS.find((term) => new RegExp(`${term}`).test(simplifiedFacilityName));

    if (match) {
      const normalisedLabel = FACILITY_KEYWORD_MAPPING[match];
      return { ...FACILITY_ICON_MAPPING[normalisedLabel], normalisedLabel, originalLabel: facilityName };
    }

    return null;
  });

  const uniqFacilities = uniqBy(compact(normalisedFacilities), (facility) => facility.icon);
  const sortedFacilities = sortBy(uniqFacilities, (facility) => facility.order);

  return sortedFacilities.slice(0, max);
};

export default mapFacilities;
