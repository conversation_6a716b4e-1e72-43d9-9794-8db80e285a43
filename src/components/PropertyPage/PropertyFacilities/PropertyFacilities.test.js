import React from 'react';
import PropertyFacilities from './PropertyFacilities';
import { mountUtils } from 'test-utils';
import { emitUserInteraction } from 'store/userEnvironment/userEnvironmentActions';

jest.mock('components/FacilitiesList', () => 'facilities-mock');
jest.mock('store/ui/uiSelectors');

const facilities = [
  'WiFi (free)',
  'Reception (24 hour)',
  'Outdoor pool (heated)',
  'Parking',
  'Non-smoking property',
  'Restaurant',
  'Bar / Lounge',
  'Luggage',
  'Business Services',
  'Day Spa',
];

const propertyName = 'Property Name';
const decorators = { theme: true, store: true };
const interactionEventValue = 'Top Link Selected';

let wrapper;
let facility;

describe('when in  summary mode', () => {
  beforeEach(() => {
    wrapper = mountUtils(
      <PropertyFacilities facilities={facilities} propertyName={propertyName} interactionEventValue={interactionEventValue} summary />,
      { decorators },
    );
    facility = wrapper.find('Facility').at(0);
  });

  it('renders 5 facilities with normalised labels', () => {
    const { findByTestId, find } = wrapper;
    expect(find('Facility')).toHaveLength(4);
    expect(findByTestId('facility-text').map((node) => node.text())).toEqual(['Wi-Fi', 'Swimming Pool', 'Restaurant', 'Luggage Storage']);
  });

  it('has a facility icon wrapper with expected props', () => {
    expect(facility.find('Flex[data-testid="facility-icon"]')).toHaveProp({ mr: 0 });
  });

  it('has facility text with expected props', () => {
    expect(facility.find('Text[data-testid="facility-text"]')).toHaveProp({ textAlign: 'left' });
  });

  it('has a view-all link with correct text', () => {
    expect(wrapper.find('button[data-testid="view-all"]')).toHaveText('View all property facilities');
  });

  describe('clicking on view more', () => {
    beforeEach(() => {
      wrapper.find('button[data-testid="view-all"]').simulate('click');
    });

    it('renders facilities in a modal', () => {
      expect(wrapper.find('Modal').find('facilities-mock')).toHaveProp({ facilities });
    });
  });
});

describe('when not in summary mode', () => {
  beforeEach(() => {
    wrapper = mountUtils(
      <PropertyFacilities facilities={facilities} propertyName={propertyName} interactionEventValue={interactionEventValue} />,
      { decorators },
    );
    facility = wrapper.find('Facility').at(0);
  });

  it('renders 9 facilities with their original labels', () => {
    const { find, findByTestId } = wrapper;
    expect(find('Facility')).toHaveLength(9);
    expect(findByTestId('facility-text').map((node) => node.text())).toEqual([
      'WiFi (free)',
      'Outdoor pool (heated)',
      'Restaurant',
      'Luggage',
      'Parking',
      'Non-smoking property',
      'Reception (24 hour)',
      'Business Services',
      'Bar / Lounge',
    ]);
  });

  it('has a facility icon wrapper with expected props', () => {
    expect(facility.find('Flex[data-testid="facility-icon"]')).toHaveProp({
      mr: 3,
      mb: 0,
      textAlign: 'left',
    });
  });

  it('has facility text with expected props', () => {
    expect(facility.find('Text[data-testid="facility-text"]')).toHaveProp({
      textAlign: 'left',
    });
  });

  it('has a view-all link with correct text', () => {
    const { findByTestId } = wrapper;
    expect(findByTestId('view-all')).toHaveText('View all property facilities');
  });

  describe('clicking on view more', () => {
    beforeEach(() => {
      const { findByTestId } = wrapper;
      findByTestId('view-all').simulate('click');
    });

    it('renders facilities in a modal', () => {
      const { find } = wrapper;
      expect(find('Modal').find('facilities-mock')).toHaveProp({ facilities });
    });

    it('emits a gtm event when clicked', () => {
      const { dispatch } = wrapper.decorators.store;
      expect(dispatch).toHaveBeenCalledWith(expect.objectContaining({ type: emitUserInteraction.type }));
    });
  });
});
