import React, { createRef } from 'react';
import { mountUtils } from 'test-utils';
import WithInPageNavBar from './WithInPageNavBar';
import { useBreakpoints } from 'hooks/useBreakpoints';
import { getBrowser } from 'store/userEnvironment/userEnvironmentSelectors';

jest.mock('components/InPageNavBar', () =>
  jest.fn(({ children, 'data-testid': dataTestId }) => <div data-testid={dataTestId}>{children}</div>),
);
jest.mock('hooks/useBreakpoints');
jest.mock('store/userEnvironment/userEnvironmentSelectors');

const menuItems = [
  { name: 'photos', text: 'Photos', linkId: 'photos-link', ref: { current: createRef() } },
  { name: 'rooms', text: 'Rooms', linkId: 'rooms-link', ref: { current: createRef() } },
  { name: 'about', text: 'About this property', linkId: 'about-property-link', ref: { current: createRef() } },
  { name: 'location', text: 'Location', linkId: 'location-link', ref: { current: createRef() } },
  { name: 'policies', text: 'Property policies', linkId: 'property-policies-link', ref: { current: createRef() } },
];

const desktopNavMenu = 'NavMenu[data-testid="desktop-nav-menu"]';
const mobileNavMenu = 'NavMenu[data-testid="mobile-nav-menu"]';

const isLessThanBreakpoint = jest.fn();
const render = () =>
  mountUtils(<WithInPageNavBar menuItems={menuItems}>children</WithInPageNavBar>, { decorators: { store: true, theme: true } });

beforeEach(() => {
  jest.clearAllMocks();
});

describe('when using a modern browser', () => {
  beforeEach(() => {
    getBrowser.mockReturnValue({ name: 'Chrome' });
  });

  describe('mobile/tablet viewport', () => {
    beforeEach(() => {
      useBreakpoints.mockReturnValue({ isLessThanBreakpoint });
      isLessThanBreakpoint.mockReturnValue(true);
    });

    it('does NOT render the desktop InPageNavBar', () => {
      const { find, findByTestId } = render();

      expect(findByTestId('desktop-nav-menu')).not.toExist();
      expect(find(desktopNavMenu)).not.toExist();
    });

    it('renders the mobile InPageNavBar', () => {
      const { find, findByTestId } = render();

      expect(findByTestId('mobile-nav-menu')).toExist();
      expect(find(mobileNavMenu)).toHaveProp({ menuItems });
    });

    it('renders the children', () => {
      const { wrapper } = render();

      expect(wrapper).toHaveText('children');
    });
  });

  describe('desktop viewport', () => {
    beforeEach(() => {
      useBreakpoints.mockReturnValue({ isLessThanBreakpoint });
      isLessThanBreakpoint.mockReturnValue(false);
    });

    it('renders the desktop InPageNavBar', () => {
      const { find } = render();

      expect(find(desktopNavMenu)).toHaveProp({ menuItems });
    });

    it('does NOT render the mobile InPageNavBar', () => {
      const { find, findByTestId } = render();

      expect(findByTestId('mobile-nav-menu')).not.toExist();
      expect(find(mobileNavMenu)).not.toExist();
    });

    it('renders the children', () => {
      const { wrapper } = render();

      expect(wrapper).toHaveText('children');
    });
  });
});

describe('when using a legacy browser', () => {
  beforeEach(() => {
    getBrowser.mockReturnValue({ name: 'Internet Explorer' });
  });

  describe('mobile/tablet viewport', () => {
    beforeEach(() => {
      useBreakpoints.mockReturnValue({ isLessThanBreakpoint });
      isLessThanBreakpoint.mockReturnValue(true);
    });

    it('does NOT render the desktop InPageNavBar', () => {
      const { findByTestId } = render();

      expect(findByTestId('desktop-nav-menu')).not.toExist();
    });

    it('does NOT render the mobile InPageNavBar', () => {
      const { findByTestId } = render();

      expect(findByTestId('mobile-nav-menu')).not.toExist();
    });

    it('renders the children', () => {
      const { wrapper } = render();

      expect(wrapper).toHaveText('children');
    });
  });

  describe('desktop viewport', () => {
    beforeEach(() => {
      useBreakpoints.mockReturnValue({ isLessThanBreakpoint });
      isLessThanBreakpoint.mockReturnValue(false);
    });

    it('does NOT render the desktop InPageNavBar', () => {
      const { findByTestId } = render();

      expect(findByTestId('desktop-nav-menu')).not.toExist();
    });

    it('does NOT render the mobile InPageNavBar', () => {
      const { findByTestId } = render();

      expect(findByTestId('mobile-nav-menu')).not.toExist();
    });

    it('renders the children', () => {
      const { wrapper } = render();

      expect(wrapper).toHaveText('children');
    });
  });
});
