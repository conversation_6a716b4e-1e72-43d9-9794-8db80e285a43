import PropTypes from 'prop-types';
import React from 'react';
import InPageNavBar from 'components/InPageNavBar';
import LegacyBrowserBoundary from 'components/LegacyBrowserBoundary';
import { useBreakpoints } from 'hooks/useBreakpoints';

const NavMenu = ({ ...rest }) => (
  <LegacyBrowserBoundary>
    <InPageNavBar {...rest} />
  </LegacyBrowserBoundary>
);

const WithInPageNavBar = ({ children, ...rest }) => {
  const { isLessThanBreakpoint } = useBreakpoints();

  const isMobileOrTablet = isLessThanBreakpoint(1);

  return isMobileOrTablet ? (
    <>
      <NavMenu {...rest} data-testid="mobile-nav-menu" />
      {children}
    </>
  ) : (
    <>
      {children}
      <NavMenu {...rest} data-testid="desktop-nav-menu" />
    </>
  );
};

WithInPageNavBar.propTypes = {
  children: PropTypes.node,
  menuItems: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string.isRequired,
      linkId: PropTypes.string,
      ref: PropTypes.oneOfType([
        PropTypes.func,
        PropTypes.shape({
          current: PropTypes.oneOfType([PropTypes.object, PropTypes.element]),
        }),
      ]).isRequired,
      text: PropTypes.string.isRequired,
    }),
  ),
  menuRefPositionOffset: PropTypes.number,
};

export default WithInPageNavBar;
