import React from 'react';
import { Flex, Text, Link, Icon, NakedButton } from '@qga/roo-ui/components';
import { useDataLayer } from 'hooks/useDataLayer';
import { useDispatch } from 'react-redux';
import { setRequestCallbackModalOpen } from 'store/property/propertyActions';
import { REQUEST_CALLBACK_PHONE_NUMBER } from 'config';

const ExclusiveContactDetails = () => {
  const dispatch = useDispatch();

  const { emitInteractionEvent } = useDataLayer();

  const handleOnClick = () => {
    dispatch(setRequestCallbackModalOpen(true));
    emitInteractionEvent({
      type: `Request Callback Modal`,
      value: `Request Form Opened`,
      customAttributes: {
        user_event_value: `Exclusive Offer`,
      },
    });
  };

  return (
    <Flex
      flexDirection={['column', 'row']}
      alignItems={['flex-start', 'center']}
      justifyContent={['flex-start', 'flex-start', 'space-between']}
      borderBottom={[1, 1, 0]}
      borderColor="greys.alto"
      mb={[4, 4, 0]}
      pb={[6, 6, 0]}
      pt={[0, 0, 6]}
    >
      <NakedButton onClick={handleOnClick} data-testid="request-callback-cta">
        <>
          <Icon name="phoneCallback" color="brand.primary" size={24} mr={2} />
          <Text fontSize="sm" color="greys.steel" fontWeight="bold" mr={10}>
            Request callback
          </Text>
        </>
      </NakedButton>
      <Link
        href={`tel:${REQUEST_CALLBACK_PHONE_NUMBER}`}
        aria-label={`Contact Goulburn call center on ${REQUEST_CALLBACK_PHONE_NUMBER}`}
        data-testid="contact-call-center-cta"
        mt={[4, 0]}
        mr={[10, 0, 0]}
      >
        <Icon name="phone" color="brand.primary" size={24} mr={2} />
        <Text fontSize="sm" color="greys.steel" fontWeight="bold">
          {REQUEST_CALLBACK_PHONE_NUMBER}
        </Text>
      </Link>
    </Flex>
  );
};

export default ExclusiveContactDetails;
