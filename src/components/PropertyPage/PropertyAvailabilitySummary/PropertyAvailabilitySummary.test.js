import React from 'react';
import { mountUtils } from 'test-utils';
import PropertyAvailabilitySummary from './PropertyAvailabilitySummary';
import { Heading } from '@qga/roo-ui/components';
import {
  getHasValidQuery,
  getIsLoading as getIsLoadingStandard,
  getTotalRoomTypeCount,
  getAvailableRoomTypeCount,
} from 'store/propertyAvailability/propertyAvailabilitySelectors';
import { getIsExclusive } from 'store/property/propertySelectors';
import {
  getIsLoading as getIsLoadingExclusive,
  getExclusiveOfferAvailabilityCount,
  getExclusiveOfferRoomTypeCount,
} from 'store/exclusiveOffer/exclusiveOfferSelectors';

jest.mock('store/propertyAvailability/propertyAvailabilitySelectors');
jest.mock('store/property/propertySelectors');
jest.mock('store/exclusiveOffer/exclusiveOfferSelectors');

const propertyName = '1831 Boutique Hotel';
const decorators = { store: true, theme: true };
const render = () => mountUtils(<PropertyAvailabilitySummary propertyName={propertyName} />, { decorators });

describe('when loading and no available room types', () => {
  beforeEach(() => {
    getHasValidQuery.mockReturnValue(true);
    getIsLoadingStandard.mockReturnValue(true);
    getAvailableRoomTypeCount.mockReturnValue(0);
    getTotalRoomTypeCount.mockReturnValue(0);
  });

  it('does not render the available room type count', () => {
    const { find } = render();
    expect(find('SummaryResults')).toHaveProp({ isLoading: true });
  });
});

describe('when not loading and with results', () => {
  beforeEach(() => {
    getIsLoadingStandard.mockReturnValue(false);
    getAvailableRoomTypeCount.mockReturnValue(10);
    getTotalRoomTypeCount.mockReturnValue(20);
  });

  it('renders the available room-type count and the total room-type count', () => {
    const { findByTestId } = render();
    expect(findByTestId('available-room-type-count').at(0)).toHaveText('Showing 10 available out of 20 rooms');
  });
});

describe('when not loading and no results', () => {
  beforeEach(() => {
    getIsLoadingStandard.mockReturnValue(false);
    getAvailableRoomTypeCount.mockReturnValue(0);
    getTotalRoomTypeCount.mockReturnValue(0);
  });

  it('renders the no results message', () => {
    const { find } = render();
    expect(find(Heading.h2)).toHaveText(`We couldn't find any available rooms`);
  });
});

describe('when there is no query', () => {
  beforeEach(() => {
    getHasValidQuery.mockReturnValue(false);
  });

  it('renders nothing', () => {
    const { find } = render();
    expect(find('SummaryContainer')).not.toExist();
    expect(find('PhoneSummaryContainer')).not.toExist();
  });
});

describe('when there is an exclusive offer', () => {
  beforeEach(() => {
    getHasValidQuery.mockReturnValue(true);
    getIsExclusive.mockReturnValue(true);
    getIsLoadingExclusive.mockReturnValue(false);
    getExclusiveOfferAvailabilityCount.mockReturnValue(1);
    getExclusiveOfferRoomTypeCount.mockReturnValue(2);
  });

  it('renders the available room-type count and the total room-type count', () => {
    const { findByTestId } = render();
    expect(findByTestId('available-room-type-count').at(0)).toHaveText('Showing 1 available out of 2 rooms');
  });
});
