import React, { Fragment } from 'react';
import { themeGet } from 'styled-system';
import PropTypes from 'prop-types';
import { Flex, Box, Heading } from '@qga/roo-ui/components';
import styled from '@emotion/styled';
import PulsingLoadingBlock from 'components/PulsingLoadingBlock';
import { mediaQuery } from 'lib/styledSystem';
import { useSelector } from 'react-redux';
import {
  getHasValidQuery,
  getIsLoading as getIsLoadingStandard,
  getTotalRoomTypeCount,
  getAvailableRoomTypeCount,
} from 'store/propertyAvailability/propertyAvailabilitySelectors';
import { getIsExclusive } from 'store/property/propertySelectors';
import {
  getIsLoading as getIsLoadingExclusive,
  getExclusiveOfferAvailabilityCount,
  getExclusiveOfferRoomTypeCount,
} from 'store/exclusiveOffer/exclusiveOfferSelectors';

const SummaryLoader = styled(Flex)`
  height: ${themeGet('space.6')};
  transition: all 0.2s ease-in-out 0.2s;
  opacity: ${(props) => (props.isLoading ? '1' : '0')};
  align-items: center;
  margin-top: -24px;
`;

const SummaryResults = styled(Flex)`
  height: ${themeGet('space.6')};
  transition: all 0.2s ease-in-out 0.4s;
  opacity: ${(props) => (props.isLoading ? '0' : '1')};
  align-items: center;
  font-size: ${themeGet('fontSizes.sm')};
`;

const Summary = ({ isLoading, availableRoomTypeCount, totalRoomTypeCount }) => {
  const hasRooms = availableRoomTypeCount > 0;
  const showingWithResults = hasRooms ? 'Showing ' : '';
  const roomsShownCopy = `${showingWithResults}${availableRoomTypeCount} available out of ${totalRoomTypeCount} rooms`;
  return (
    <Fragment>
      <Flex flexDirection="column" alignItems="center" mb={8} mt={0}>
        <SummaryResults isLoading={isLoading}>
          <Box data-testid="available-room-type-count">{roomsShownCopy}</Box>
          <Box display={['none', 'block']}>&nbsp;for your stay</Box>
        </SummaryResults>
        <SummaryLoader isLoading={isLoading}>
          <PulsingLoadingBlock height={14} width={50} mr={1} mb={0} />
          <PulsingLoadingBlock height={14} width={20} mr={1} mb={0} />
          <PulsingLoadingBlock height={14} width={100} mr={1} mb={0} />
          <PulsingLoadingBlock height={14} width={20} mr={1} mb={0} />
          <PulsingLoadingBlock height={14} width={100} mb={0} />
        </SummaryLoader>
      </Flex>
    </Fragment>
  );
};

Summary.propTypes = {
  availableRoomTypeCount: PropTypes.number,
  totalRoomTypeCount: PropTypes.number,
  isLoading: PropTypes.bool,
};

Summary.defaultProps = {
  availableRoomTypeCount: 0,
  totalRoomTypeCount: 0,
  isLoading: false,
};

const SummaryContainer = styled(Box)`
  min-height: 20px;
  text-align: center;
  padding-top: ${themeGet('space.6')};

  ${mediaQuery.minWidth.sm} {
    padding-top: 0px;
    min-height: 60px;
  }
`;

SummaryContainer.displayName = 'SummaryContainer';

const PropertyAvailabilitySummary = () => {
  const hasQuery = useSelector(getHasValidQuery);
  const isExclusive = useSelector(getIsExclusive);
  const isLoadingStandard = useSelector(getIsLoadingStandard);
  const isLoadingExclusive = useSelector(getIsLoadingExclusive);
  const totalRoomTypeCountStandard = useSelector(getTotalRoomTypeCount);
  const totalRoomTypeCountExclusive = useSelector(getExclusiveOfferRoomTypeCount);
  const availableRoomTypeCountStandard = useSelector(getAvailableRoomTypeCount);
  const availableRoomTypeCountExclusive = useSelector(getExclusiveOfferAvailabilityCount);

  if (!hasQuery) return null;

  const isLoading = isExclusive ? isLoadingExclusive : isLoadingStandard;
  const totalRoomsCount = isExclusive ? totalRoomTypeCountExclusive : totalRoomTypeCountStandard;
  const availableRoomsCount = isExclusive ? availableRoomTypeCountExclusive : availableRoomTypeCountStandard;

  return (
    <Fragment>
      <SummaryContainer>
        {isLoading || availableRoomsCount > 0 ? (
          <Heading.h2 data-testid="property-availability-header" mb={3} fontSize="xl">
            Choose your room
          </Heading.h2>
        ) : (
          <Heading.h2 data-testid="property-availability-header" fontSize="xl">
            We couldn&apos;t find any available rooms
          </Heading.h2>
        )}
        <Summary isLoading={isLoading} availableRoomTypeCount={availableRoomsCount} totalRoomTypeCount={totalRoomsCount} />
      </SummaryContainer>
    </Fragment>
  );
};

export default PropertyAvailabilitySummary;
