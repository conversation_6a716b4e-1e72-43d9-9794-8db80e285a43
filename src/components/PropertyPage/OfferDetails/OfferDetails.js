import React from 'react';
import { Flex, Text, Button, Icon, Link } from '@qga/roo-ui/components';
import styled from '@emotion/styled';
import Currency from 'components/Currency';
import OfferGuestText from 'components/OfferGuestsText';
import { useDataLayer } from 'hooks/useDataLayer';
import { format } from 'lib/date';
import { useSelector } from 'react-redux';
import {
  getExclusiveOffer,
  getIsLoading as getIsLoadingExclusiveOffers,
  getLeadInOffer,
} from 'store/exclusiveOffer/exclusiveOfferSelectors';

const DEFAULT_DATE_FORMAT = 'd MMM, yyyy';

const UppercaseText = styled(Text)`
  text-transform: uppercase;
`;

const Wrapper = styled(Flex)`
  pointer-events: auto;
`;

const OfferDetails = () => {
  const { saleDates, id } = useSelector(getExclusiveOffer);
  const isLoading = useSelector(getIsLoadingExclusiveOffers);
  const leadInOffer = useSelector(getLeadInOffer);

  const { emitInteractionEvent } = useDataLayer();

  if (!leadInOffer) return null;

  const { hasValuedAtPrice, valuedAtTotal, offerTotal, title, description, minNumberOfNights, adults } = leadInOffer;
  const offerExpiry = format(new Date(saleDates.end), DEFAULT_DATE_FORMAT);

  const handleOnClick = () => {
    emitInteractionEvent({ type: 'Exclusive Offer', value: `View Room Selected for offer ${id}` });
  };

  return (
    <>
      {!isLoading && (
        <Wrapper flexDirection="column" bg="white" p={[3, 3, 8]} pt={[0, 0, 6]} width={['initial', 'initial', 490]}>
          <Flex borderRadius="default" bg="bayBlue30" px={4} py={1} mb={4} width={230} justifyContent="center" data-testid="offer-expiry">
            <Icon name="schedule" pr={1} />
            <UppercaseText fontSize="xs" fontWeight="bold">
              Offer ends {offerExpiry}
            </UppercaseText>
          </Flex>
          <Text fontSize="lg" mb={2} data-testid="offer-title">
            {title}
          </Text>
          <Text fontSize="base" color="greys.steel" mb={4} data-testid="offer-description">
            {description}
          </Text>
          <OfferGuestText guests={adults} numberOfNights={minNumberOfNights} fontSize="sm" data-testid="offer-guests" />
          <Flex alignItems="center" mb={2} data-testid="offer-price">
            <Currency {...offerTotal} roundToCeiling fontSize={36} fontWeight="bold" hideCurrency color="greys.charcoal" />
            <Text pl={1} pt={1} fontSize="sm">
              {offerTotal.currency}
            </Text>
          </Flex>
          {hasValuedAtPrice && (
            <Flex alignItems="center" mb={4} data-testid="offer-valued-at">
              <Text pr={1} fontSize="sm" color="greys.steel">
                Valued at{' '}
              </Text>
              <Currency {...valuedAtTotal} roundToCeiling fontSize="sm" hideCurrency color="greys.steel" />
            </Flex>
          )}
          <Button width={['100%', 220]} variant="primary" as={Link} href="#rooms" onClick={handleOnClick}>
            <Text fontSize="base" fontWeight="bold">
              View options
            </Text>
            <Icon name="arrowDownward" pl={1} />
          </Button>
        </Wrapper>
      )}
    </>
  );
};

export default OfferDetails;
