import React from 'react';
import { mountUtils } from 'test-utils';
import OfferDetails from './OfferDetails';
import { useDataLayer } from 'hooks/useDataLayer';
import { getExclusiveOffer, getIsLoading, getLeadInOffer } from 'store/exclusiveOffer/exclusiveOfferSelectors';

jest.mock('hooks/useDataLayer');
jest.mock('store/exclusiveOffer/exclusiveOfferSelectors');

const getOfferData = (overrides = {}) => ({
  id: '5f1a4e5a-1cf3-4138-940b-b8532356756c',
  rooms: [
    {
      offers: [
        {
          adults: 2,
          description:
            'Complimentary upgrade to Deluxe Sea View room from Deluxe room. Full Buffet Breakfast daily at Backyard Restaurant. Self Parking for one car. Daily free Wifi.',
          minNumberOfNights: 3,
          offerTotal: {
            amount: '499',
            currency: 'AUD',
          },
          title: 'Stay 3 nights and receive super luxe treatment!',
          hasValuedAtPrice: true,
          valuedAtTotal: {
            amount: '899',
            currency: 'AUD',
          },
          ...overrides,
        },
      ],
    },
  ],
  saleDates: {
    end: '2021-11-30',
  },
});

const emitInteractionEvent = jest.fn();

const decorators = { store: true, theme: true };
const render = () => mountUtils(<OfferDetails />, { decorators });

describe('<OfferDetails />', () => {
  beforeEach(() => {
    const offerData = getOfferData();
    jest.clearAllMocks();
    useDataLayer.mockReturnValue({ emitInteractionEvent });
    getExclusiveOffer.mockReturnValue(offerData);
    getLeadInOffer.mockReturnValue(offerData.rooms[0].offers[0]);
  });

  describe('when loading', () => {
    beforeEach(() => {
      getIsLoading.mockReturnValue(true);
    });

    it('does not render the offer expiry', () => {
      const { findByTestId } = render();
      expect(findByTestId('offer-expiry')).not.toExist();
    });

    it('does not render the offer title', () => {
      const { findByTestId } = render();
      expect(findByTestId('offer-title')).not.toExist();
    });

    it('does not render the offer description', () => {
      const { findByTestId } = render();
      expect(findByTestId('offer-description')).not.toExist();
    });

    it('does not render the offer guests and accommodation duration', () => {
      const { findByTestId } = render();
      expect(findByTestId('offer-guests')).not.toExist();
    });

    it('does not render the offer price', () => {
      const { findByTestId } = render();
      expect(findByTestId('offer-price')).not.toExist();
    });

    it('does not render the valued at price', () => {
      const { findByTestId } = render();
      expect(findByTestId('offer-valued-at')).not.toExist();
    });
  });

  describe('when not loading', () => {
    beforeEach(() => {
      getIsLoading.mockReturnValue(false);
    });
    it('renders the offer expiry', () => {
      const { findByTestId } = render();
      expect(findByTestId('offer-expiry')).toHaveText('Offer ends 30 Nov, 2021');
    });

    it('renders the offer title', () => {
      const { findByTestId } = render();
      expect(findByTestId('offer-title')).toHaveText('Stay 3 nights and receive super luxe treatment!');
    });

    it('renders the offer description', () => {
      const { findByTestId } = render();
      expect(findByTestId('offer-description')).toHaveText(
        'Complimentary upgrade to Deluxe Sea View room from Deluxe room. Full Buffet Breakfast daily at Backyard Restaurant. Self Parking for one car. Daily free Wifi.',
      );
    });

    it('renders the offer guests and accommodation duration', () => {
      const { findByTestId } = render();
      expect(findByTestId('offer-guests')).toHaveText('2 guests • 3 nights from');
    });

    it('renders the offer price', () => {
      const { findByTestId } = render();
      expect(findByTestId('offer-price')).toHaveText('$499AUD');
    });

    it('renders the valued at price', () => {
      const { findByTestId } = render();
      expect(findByTestId('offer-valued-at')).toHaveText('Valued at $899');
    });

    it('does NOT render the valued at price', () => {
      const offerDataNoValuedAt = getOfferData({ hasValuedAtPrice: false });
      getExclusiveOffer.mockReturnValue(offerDataNoValuedAt);
      getLeadInOffer.mockReturnValue(offerDataNoValuedAt.rooms[0].offers[0]);
      const { findByTestId } = render();
      expect(findByTestId('offer-valued-at')).not.toExist();
    });

    it('emits an event to the data layer when clicked', () => {
      const { find } = render();
      find('Button').simulate('click');

      expect(find('Button')).toHaveText('View options');
      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Exclusive Offer',
        value: 'View Room Selected for offer 5f1a4e5a-1cf3-4138-940b-b8532356756c',
      });
    });
  });
});
