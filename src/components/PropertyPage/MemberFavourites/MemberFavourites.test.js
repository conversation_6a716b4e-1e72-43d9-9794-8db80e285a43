import React from 'react';
import { mountUtils } from 'test-utils';
import MemberFavourites from './MemberFavourites';
import * as config from 'config';

jest.mock('config');

mountUtils.mockComponent('MemberFavouritesFetcher');
mountUtils.mockComponent('MemberFavouritesButton');

describe('<MemberFavourites />', () => {
  let props;

  const render = () => mountUtils(<MemberFavourites {...props} />);

  beforeEach(() => {
    props = {};

    Object.assign(config, jest.requireActual('config'));
  });

  describe('with DREAM_PLANER_ENABLED = true', () => {
    beforeEach(() => {
      config.DREAM_PLANER_ENABLED = true;
    });

    it('renders <MemberFavouritesFetcher />', () => {
      expect(render().find('MemberFavouritesFetcher')).toExist();
    });

    it('renders <MemberFavouritesButton />', () => {
      expect(render().find('MemberFavouritesButton')).toExist();
    });
  });

  describe('with DREAM_PLANER_ENABLED = false', () => {
    beforeEach(() => {
      config.DREAM_PLANER_ENABLED = false;
    });

    it('renders <MemberFavouritesFetcher />', () => {
      expect(render().find('MemberFavouritesFetcher')).not.toExist();
    });

    it('renders <MemberFavouritesButton />', () => {
      expect(render().find('MemberFavouritesButton')).not.toExist();
    });
  });
});
