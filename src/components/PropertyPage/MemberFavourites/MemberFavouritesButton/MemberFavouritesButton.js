import has from 'lodash/has';
import PropTypes from 'prop-types';
import React, { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';
import { Flex } from '@qga/roo-ui/components';
import ButtonLabel from './ButtonLabel';
import { useDataLayer } from 'hooks/useDataLayer';
import { useLoginUrl } from 'lib/qffAuth';
import { addMemberFavourite, removeMemberFavourite } from 'store/memberFavourites/memberFavouritesActions';
import { getMemberFavourites, getMemberFavouritesStatus, getMemberFavouritesError } from 'store/memberFavourites/memberFavouritesSelectors';
import { getProperty } from 'store/property/propertySelectors';
import { useIsAuthenticated } from 'lib/oauth';
import { FavouriteButton, FavouriteIcon, NonFavouriteIcon } from './primitives';
import { status } from 'store/memberFavourites/memberFavouritesReducer';
import { registerTempFavouritePropertyId, getTempFavouritePropertyId, clearTempFavouritePropertyId } from 'lib/favourites/sessionStorage';

const MemberFavouritesButton = () => {
  const dispatch = useDispatch();
  const { emitInteractionEvent } = useDataLayer();

  const router = useRouter();
  const isAuthenticated = useIsAuthenticated();
  const memberFavourites = useSelector(getMemberFavourites);
  const memberFavouritesStatus = useSelector(getMemberFavouritesStatus);
  const memberFavouritesError = useSelector(getMemberFavouritesError);
  const property = useSelector(getProperty);
  const { loginUrl } = useLoginUrl();

  const propertyId = property?.id;
  const isFavourite = propertyId && has(memberFavourites, propertyId);
  const isDisabled = memberFavouritesStatus === status.PENDING;

  const handleAddFavourite = () => {
    emitInteractionEvent({ type: 'Member Favourites Button', value: 'Add Favourite Selected' });

    if (isAuthenticated) {
      dispatch(addMemberFavourite({ propertyId }));
    } else {
      registerTempFavouritePropertyId(propertyId);
      router.push(loginUrl);
    }
  };

  const handleRemoveFavourite = useCallback(() => {
    emitInteractionEvent({ type: 'Member Favourites Button', value: 'Remove Favourite Selected' });
    dispatch(removeMemberFavourite(propertyId));
  }, [dispatch, emitInteractionEvent, propertyId]);

  useEffect(() => {
    const tempFavouritePropertyId = getTempFavouritePropertyId();
    if (isAuthenticated && !!memberFavourites && tempFavouritePropertyId) {
      if (!isFavourite) {
        dispatch(addMemberFavourite({ propertyId: tempFavouritePropertyId }));
      }
      clearTempFavouritePropertyId();
    }
  }, [dispatch, isAuthenticated, isFavourite, memberFavourites]);

  return (
    <UnconnectedMemberFavouritesButton
      isAuthenticated={isAuthenticated}
      isDisabled={isDisabled}
      hasError={!!memberFavouritesError}
      isFavourite={isFavourite}
      onClick={isFavourite ? handleRemoveFavourite : handleAddFavourite}
    />
  );
};

export const UnconnectedMemberFavouritesButton = ({ isAuthenticated, isDisabled, hasError, isFavourite, onClick }) => (
  <Flex alignItems="center" justifyContent={['space-between', 'flex-end']} flex={1}>
    <ButtonLabel isAuthenticated={isAuthenticated} isFavourite={isFavourite} hasError={hasError} />
    <FavouriteButton disabled={isDisabled} onClick={onClick}>
      {hasError && <NonFavouriteIcon name="favoriteStrike" size={[24, 32]} title="Error adding to Dream planner icon" />}
      {!hasError && isFavourite && <FavouriteIcon name="favorite" size={[24, 32]} title="Remove from Dream Planner Icon" />}
      {!hasError && !isFavourite && <NonFavouriteIcon name="favoriteBorder" size={[24, 32]} title="Add to Dream Planner Icon" />}
    </FavouriteButton>
  </Flex>
);

UnconnectedMemberFavouritesButton.propTypes = {
  isAuthenticated: PropTypes.bool,
  isDisabled: PropTypes.bool,
  hasError: PropTypes.bool,
  isFavourite: PropTypes.bool,
  onClick: PropTypes.func.isRequired,
};

UnconnectedMemberFavouritesButton.defaultProps = {
  isAuthenticated: false,
  isDisabled: false,
  hasError: false,
  isFavourite: false,
};

export default MemberFavouritesButton;
