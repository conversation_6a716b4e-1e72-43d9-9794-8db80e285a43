import React from 'react';
import { act } from 'react-dom/test-utils';
import { mountUtils } from 'test-utils';
import { useDataLayer } from 'hooks/useDataLayer';
import { useLoginUrl } from 'lib/qffAuth';
import { addMemberFavourite, removeMemberFavourite } from 'store/memberFavourites/memberFavouritesActions';
import { status } from 'store/memberFavourites/memberFavouritesReducer';
import { getMemberFavourites, getMemberFavouritesStatus, getMemberFavouritesError } from 'store/memberFavourites/memberFavouritesSelectors';
import { getProperty } from 'store/property/propertySelectors';
import { useIsAuthenticated } from 'lib/oauth';
import MemberFavouritesButton from './MemberFavouritesButton';

jest.mock('hooks/useDataLayer');
jest.mock('lib/qffAuth');
jest.mock('store/memberFavourites/memberFavouritesSelectors');
jest.mock('store/property/propertySelectors');
jest.mock('lib/oauth');
const mockRouter = {
  push: jest.fn(),
};

jest.mock('next/router', () => ({
  useRouter: jest.fn(() => mockRouter),
}));

global.window.location = {
  href: '/mock-url',
  toString: () => {
    return global.window.location.href;
  },
};

mountUtils.mockComponent('ButtonLabel');

const mockDataLayer = { emitInteractionEvent: jest.fn() };
const mockFavouriteProperty = { id: 'property1' };
const mockNonFavouriteProperty = { id: 'property2' };
const mockMemberFavourites = { property1: mockFavouriteProperty };

const render = (props = {}) => mountUtils(<MemberFavouritesButton {...props} />, { decorators: { store: true, theme: true } });

beforeEach(() => {
  jest.clearAllMocks();
  getMemberFavourites.mockReturnValue(mockMemberFavourites);
  useDataLayer.mockReturnValue(mockDataLayer);
  getMemberFavouritesStatus.mockReturnValue(status.RESOLVED);
  useLoginUrl.mockReturnValue({ loginUrl: '/hotels/auth/callback?state=/foo' });
});

describe('when the user is not authenticated', () => {
  beforeEach(() => {
    useIsAuthenticated.mockReturnValue(false);
    getProperty.mockReturnValue(mockNonFavouriteProperty);
  });

  it('shows the button', () => {
    const { find } = render();

    expect(find('FavouriteButton')).toHaveProp({
      disabled: false,
      onClick: expect.any(Function),
    });
  });

  it('shows the button label', () => {
    const { find } = render();

    expect(find('ButtonLabel')).toHaveProp({
      isAuthenticated: false,
      isFavourite: false,
      hasError: false,
    });
  });

  it('shows the non favourite icon', () => {
    const { find } = render();

    expect(find('NonFavouriteIcon')).toHaveProp({
      name: 'favoriteBorder',
      title: 'Add to Dream Planner Icon',
    });
  });

  describe('when clicking the button', () => {
    it('sends an event to the data layer', () => {
      const { find } = render();

      act(() => {
        find('FavouriteButton').simulate('click');
      });

      expect(mockDataLayer.emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Member Favourites Button',
        value: 'Add Favourite Selected',
      });
    });

    describe('when the user signs in', () => {
      it('dispatches the addMemberFavourite action', async () => {
        useIsAuthenticated.mockReturnValue(true);
        const { decorators, find, wrapper } = render();

        act(() => {
          find('FavouriteButton').simulate('click');
        });

        wrapper.update();

        expect(decorators.store.dispatch).toHaveBeenCalledWith(addMemberFavourite({ propertyId: mockNonFavouriteProperty.id }));
      });
    });
  });

  describe('and the member favourites status is pending', () => {
    beforeEach(() => {
      getMemberFavouritesStatus.mockReturnValue(status.PENDING);
    });

    it('disables the Member Favourite Button', () => {
      const { find } = render();

      expect(find('FavouriteButton')).toHaveProp({
        disabled: true,
      });
    });
  });
});

describe('when the user is authenticated', () => {
  beforeEach(() => {
    useIsAuthenticated.mockReturnValue(true);
  });

  it('shows the button', () => {
    const { find } = render();

    expect(find('FavouriteButton')).toHaveProp({
      disabled: false,
      onClick: expect.any(Function),
    });
  });

  it('shows the button label', () => {
    const { find } = render();

    expect(find('ButtonLabel')).toHaveProp({
      isAuthenticated: true,
      isFavourite: false,
      hasError: false,
    });
  });

  describe('and there is an error', () => {
    beforeEach(() => {
      getMemberFavouritesStatus.mockReturnValue(status.ERROR);
      getMemberFavouritesError.mockReturnValue('error');
      getProperty.mockReturnValue(mockNonFavouriteProperty);
    });

    it('shows the favoriteStrike icon', () => {
      const { find } = render();

      expect(find('NonFavouriteIcon')).toHaveProp({
        name: 'favoriteStrike',
        title: 'Error adding to Dream planner icon',
      });
    });

    describe('when clicking the icon', () => {
      it('dispatches the addMemberFavourite action', () => {
        const { decorators, find, wrapper } = render();

        act(() => {
          find('NonFavouriteIcon').simulate('click');
        });

        wrapper.update();

        expect(decorators.store.dispatch).toHaveBeenCalledWith(addMemberFavourite({ propertyId: mockNonFavouriteProperty.id }));
      });

      it('emits an event to the data layer', () => {
        const { find } = render();

        act(() => {
          find('NonFavouriteIcon').simulate('click');
        });

        expect(mockDataLayer.emitInteractionEvent).toHaveBeenCalledWith({
          type: 'Member Favourites Button',
          value: 'Add Favourite Selected',
        });
      });
    });
  });

  describe('and the property is not a favourite', () => {
    beforeEach(() => {
      getMemberFavouritesError.mockReturnValue(null);
      getProperty.mockReturnValue(mockNonFavouriteProperty);
    });

    it('shows the non favourite icon', () => {
      const { find } = render();

      expect(find('NonFavouriteIcon')).toHaveProp({
        name: 'favoriteBorder',
        title: 'Add to Dream Planner Icon',
      });
    });

    describe('when clicking the icon', () => {
      it('dispatches the addMemberFavourite action', () => {
        const { decorators, find, wrapper } = render();

        act(() => {
          find('NonFavouriteIcon').simulate('click');
        });

        wrapper.update();

        expect(decorators.store.dispatch).toHaveBeenCalledWith(addMemberFavourite({ propertyId: mockNonFavouriteProperty.id }));
      });

      it('emits an event to the data layer', () => {
        const { find } = render();

        act(() => {
          find('NonFavouriteIcon').simulate('click');
        });

        expect(mockDataLayer.emitInteractionEvent).toHaveBeenCalledWith({
          type: 'Member Favourites Button',
          value: 'Add Favourite Selected',
        });
      });
    });
  });

  describe('and the property is a favourite', () => {
    beforeEach(() => {
      getProperty.mockReturnValue(mockFavouriteProperty);
    });

    it('shows the favourite icon', () => {
      const { find } = render();

      expect(find('FavouriteIcon')).toHaveProp({
        name: 'favorite',
        title: 'Remove from Dream Planner Icon',
      });
    });

    describe('when clicking the icon', () => {
      it('dispatches the removeMemberFavourite action', () => {
        const { decorators, find, wrapper } = render();

        act(() => {
          find('FavouriteIcon').simulate('click');
        });

        wrapper.update();

        expect(decorators.store.dispatch).toHaveBeenCalledWith(removeMemberFavourite(mockFavouriteProperty.id));
      });

      it('emits an event to the data layer', () => {
        const { find } = render();

        act(() => {
          find('FavouriteIcon').simulate('click');
        });

        expect(mockDataLayer.emitInteractionEvent).toHaveBeenCalledWith({
          type: 'Member Favourites Button',
          value: 'Remove Favourite Selected',
        });
      });
    });
  });

  describe('and the member favourites status is pending', () => {
    beforeEach(() => {
      getMemberFavouritesStatus.mockReturnValue(status.PENDING);
    });

    it('disables the Member Favourite Button', () => {
      const { find } = render();

      expect(find('FavouriteButton')).toHaveProp({
        disabled: true,
      });
    });
  });

  describe('and favourite is being removed from the dream planner widget', () => {
    beforeEach(() => {
      getMemberFavouritesStatus.mockReturnValue(status.ERROR);
      getMemberFavouritesError.mockReturnValue(null);
      getProperty.mockReturnValue(mockNonFavouriteProperty);
    });

    it('shows the favoriteBorder icon', () => {
      const { find } = render();

      expect(find('NonFavouriteIcon')).toHaveProp({
        name: 'favoriteBorder',
        title: 'Add to Dream Planner Icon',
      });
    });
  });
});
