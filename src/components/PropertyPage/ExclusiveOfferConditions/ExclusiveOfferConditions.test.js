import React from 'react';
import { mountUtils } from 'test-utils';
import ExclusiveOfferConditions from './ExclusiveOfferConditions';
import { getExclusiveOffer, getLeadInOffer } from 'store/exclusiveOffer/exclusiveOfferSelectors';

jest.mock('store/exclusiveOffer/exclusiveOfferSelectors');

const exclusiveOffer = {
  travelDates: {
    start: '2021-11-01',
    end: '2021-11-30',
  },
  rooms: [
    {
      offers: [{ minNumberOfNights: 2 }],
    },
  ],
};

const decorators = { theme: true, store: true };
const render = () => mountUtils(<ExclusiveOfferConditions />, { decorators });

describe('Exclusive Offer Conditions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    getExclusiveOffer.mockReturnValue(exclusiveOffer);
    getLeadInOffer.mockReturnValue(exclusiveOffer.rooms[0].offers[0]);
  });

  it('renders the travel period', () => {
    const { findByTestId } = render();
    expect(findByTestId('travel-period')).toHaveText('Travel period: 01 Nov 2021 - 30 Nov 2021. Subject to availability.');
  });

  it('renders the minimum stay', () => {
    const { findByTestId } = render();
    expect(findByTestId('min-number-nights')).toHaveText('Min stay: 2 nights.');
  });
});
