import React from 'react';
import { useSelector } from 'react-redux';
import { getExclusiveOffer, getLeadInOffer } from 'store/exclusiveOffer/exclusiveOfferSelectors';
import { Text, Box, Heading } from '@qga/roo-ui/components';
import { format } from 'date-fns';
import pluralize from 'pluralize';

const ExclusiveOfferConditions = () => {
  const { travelDates } = useSelector(getExclusiveOffer);
  const { minNumberOfNights } = useSelector(getLeadInOffer);
  const startDate = format(new Date(travelDates.start), 'dd MMM yyyy');
  const endDate = format(new Date(travelDates.end), 'dd MMM yyyy');
  const minNumberOfNightsText = pluralize('night', minNumberOfNights, true);

  return (
    <Box mb={[0, 0, 12]} pb={[6, 6, 0]}>
      <Heading.h3 fontSize={['md', 'lg']}>
        Hurry! Exclusive offers have special inclusions with great savings for a limited time!
      </Heading.h3>
      <Text display="block" fontSize="base" color="greys.steel" data-testid="travel-period">
        <strong>Travel period:</strong> {startDate} - {endDate}. Subject to availability.
      </Text>
      <Text display="block" fontSize="base" color="greys.steel" data-testid="min-number-nights">
        <strong>Min stay:</strong> {minNumberOfNightsText}.
      </Text>
    </Box>
  );
};

export default ExclusiveOfferConditions;
