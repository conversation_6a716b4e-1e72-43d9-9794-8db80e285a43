import React from 'react';
import PropTypes from 'prop-types';
import { Box, Text } from '@qga/roo-ui/components';
import AppLink from 'components/AppLink';
import { useSelector } from 'react-redux';
import { getQueryString } from 'store/router/routerSelectors';

const StandardOfferMessage = ({ propertyId }) => {
  const propertyQueryString = useSelector(getQueryString);
  const propertyUrl = `/properties/${propertyId}/${propertyQueryString}`;

  return (
    <Box mb={4} data-testid="standard-offer-message">
      <Text>
        Can’t find availability or have specific days and want more room offers?
        <AppLink ml={1} textDecoration="underline" to={propertyUrl}>
          See all offers
        </AppLink>
      </Text>
    </Box>
  );
};

StandardOfferMessage.propTypes = {
  propertyId: PropTypes.string.isRequired,
};

export default StandardOfferMessage;
