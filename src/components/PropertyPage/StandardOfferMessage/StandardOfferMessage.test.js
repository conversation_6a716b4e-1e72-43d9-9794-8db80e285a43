import React from 'react';
import { mountUtils } from 'test-utils';
import StandardOfferMessage from './StandardOfferMessage';
import { getQueryString } from 'store/router/routerSelectors';

jest.mock('store/router/routerSelectors');

const propertyId = '42';
const query = 'adults=2&checkIn=2021-12-24&checkOut=2021-12-25&children=0&infants=0';

const decorators = { theme: true, store: true, router: true };

const render = (props = { propertyId }) => mountUtils(<StandardOfferMessage {...props} />, { decorators });

describe('<StandardOfferMessage>', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    getQueryString.mockReturnValue(query);
  });

  it('renders text message', () => {
    expect(render().findByTestId('standard-offer-message')).toHaveText(
      'Can’t find availability or have specific days and want more room offers?See all offers',
    );
  });

  it('renders the link with proper props', () => {
    expect(render().find('AppLink')).toHaveProp({ to: `/properties/${propertyId}/${query}` });
  });
});
