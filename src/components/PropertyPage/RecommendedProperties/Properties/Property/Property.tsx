import React, { useCallback } from 'react';
import { Box } from '@qga/roo-ui/components';
import PropertyCard from 'components/PropertyCard';
import { useDataLayer } from 'hooks/useDataLayer';
import { registerInteraction } from 'lib/analytics/recommendations';
import { usePersonalisation } from 'hooks/usePersonalisation';
import { PropertyCardProps } from 'types/property';
import { useFeature } from '@optimizely/react-sdk';

interface Props extends PropertyCardProps {
  id: string;
  index: number;
}

const Property = ({ id: propertyId, index, ...rest }: Props) => {
  const { emitInteractionEvent } = useDataLayer();
  const [isEnabled, variables] = useFeature('jetstar-hotels-property-page-recommendations', { autoUpdate: true });
  const treatment = isEnabled ? variables.recommendation : 'off';
  const personalisation = usePersonalisation();

  const handlePropertyLinkOnClick = useCallback(() => {
    const cardNumber = index + 1;
    const customAttributes = { user_event_value: propertyId };
    emitInteractionEvent({ type: 'Recommended Properties', value: `Card ${cardNumber} Selected`, customAttributes });

    registerInteraction({
      propertyId,
      treatment,
      placement: 'property_page',
    });

    personalisation.trackRecommendationClick(propertyId);
  }, [personalisation, emitInteractionEvent, index, propertyId, treatment]);

  return (
    <Box mr={[4, 0, 4]} my={[2, 2, 0]} width={[1, 1, 0.32]}>
      <PropertyCard
        {...rest}
        id={propertyId}
        featuredOfferId={undefined}
        recommended={true}
        showCampaignMessage
        inline
        onClick={handlePropertyLinkOnClick}
      />
    </Box>
  );
};

export default Property;
