import React, { useEffect } from 'react';
import { useInView } from 'react-intersection-observer';
import { useDispatch, useSelector } from 'react-redux';
import { isEmpty } from 'lodash';
import { Box } from '@qga/roo-ui/components';
import { getIsDomestic, getPropertyLocation } from 'store/property/propertySelectors';
import ResultsFetcher from './ResultsFetcher';
import Properties from './Properties/Properties';
import { OptimizelyFeature } from '@optimizely/react-sdk';
import { emitRecommendedPropertiesResult } from 'store/recommendedProperty/recommendedPropertyActions';
import { getGa4Properties } from 'store/recommendedProperty/recommendedPropertySelectors';
import { getSearchQuery } from 'store/search/searchSelectors';
import { getPointsConversion } from 'store/pointsBurnTiers/pointsBurnSelectors';
import { fetchPointsBurnTiers } from 'store/pointsBurnTiers/pointsBurnActions';

const RecommendedProperties = React.memo(() => {
  const isDomestic = useSelector(getIsDomestic);
  const [ref, inView] = useInView({ triggerOnce: true });
  const dispatch = useDispatch();
  const propertyResults = useSelector(getGa4Properties);
  const regionName = useSelector(getPropertyLocation);
  const searchQuery = useSelector(getSearchQuery);
  const payWith = searchQuery?.payWith;
  const pointsConversion = useSelector(getPointsConversion);
  const hasLevels = !!pointsConversion?.levels?.length;

  useEffect(() => {
    if (!hasLevels) {
      dispatch(fetchPointsBurnTiers());
    }
  }, [dispatch, hasLevels]);

  useEffect(() => {
    const query = { location: regionName, payWith: payWith };
    if (!isEmpty(propertyResults) && hasLevels) {
      dispatch(
        emitRecommendedPropertiesResult({
          results: propertyResults,
          query: query,
          listName: 'Property Page Recommended Properties',
          category: 'jetstar',
          currency: 'AUD',
          type: 'list',
          pointsConversion: pointsConversion,
        }),
      );
    }
  }, [dispatch, hasLevels, payWith, pointsConversion, pointsConversion.levels.length, propertyResults, regionName]);

  if (!isDomestic) return null;

  return (
    <Box ref={ref}>
      {inView && (
        <OptimizelyFeature feature="jetstar-hotels-property-page-recommendations">
          {(isEnabled) =>
            isEnabled && (
              <>
                <ResultsFetcher />
                <Properties />
              </>
            )
          }
        </OptimizelyFeature>
      )}
    </Box>
  );
});

RecommendedProperties.displayName = 'RecommendedProperties';

export default RecommendedProperties;
