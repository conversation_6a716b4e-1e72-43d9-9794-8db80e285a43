import React from 'react';
import { useInView } from 'react-intersection-observer';
import { mountUtils } from 'test-utils';
import { useDispatch } from 'react-redux';
import RecommendedProperties from './RecommendedProperties';
import { getIsDomestic, getPropertyLocation } from 'store/property/propertySelectors';
import { OptimizelyFeature } from '@optimizely/react-sdk';
import { getGa4Properties } from 'store/recommendedProperty/recommendedPropertySelectors';
import { getSearchQuery } from 'store/search/searchSelectors';
import { emitRecommendedPropertiesResult } from 'store/recommendedProperty/recommendedPropertyActions';
import { getPointsConversion } from 'store/pointsBurnTiers/pointsBurnSelectors';

jest.mock('react-intersection-observer');
jest.mock('store/property/propertySelectors');
jest.mock('store/search/searchSelectors');
jest.mock('store/recommendedProperty/recommendedPropertySelectors');
jest.mock('store/pointsBurnTiers/pointsBurnSelectors');
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: jest.fn(),
}));
jest.mock('@optimizely/react-sdk', () => ({
  OptimizelyFeature: jest.fn(),
}));

const dispatch = jest.fn();

mountUtils.mockComponent('Properties');
mountUtils.mockComponent('ResultsFetcher');

const mockRef = React.createRef();

const regionName = 'Melbourne, VIC, Australia';
const payWith = { payWith: 'cash' };
const query = { location: regionName, payWith: 'cash' };

const propertyResults = {
  property: { id: '124', name: 'hotel' },
  roomType: { name: 'room' },
  offer: {
    charges: {
      total: {
        amount: '300.00',
        currency: 'AUD',
      },
      totalCash: {
        amount: '0',
        currency: 'AUD',
      },
    },
  },
};

const pointsConversion = {
  levels: [
    { min: 0, max: 150, rate: 0.00824 },
    { min: 150, max: 400, rate: 0.00834 },
    { min: 400, max: 650, rate: 0.00848 },
    { min: 650, max: 900, rate: 0.00875 },
    { min: 900, max: null, rate: 0.00931 },
  ],
  name: 'VERSION11',
};

const payload = {
  results: propertyResults,
  query: query,
  listName: 'Property Page Recommended Properties',
  category: 'jetstar',
  type: 'list',
  currency: 'AUD',
  pointsConversion: pointsConversion,
};

const decorators = { theme: true, store: true };
const render = () => mountUtils(<RecommendedProperties />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
  getGa4Properties.mockReturnValue(propertyResults);
  getSearchQuery.mockReturnValue(payWith);
  getPropertyLocation.mockReturnValue(regionName);
  useDispatch.mockReturnValue(dispatch);
  getPointsConversion.mockReturnValue(pointsConversion);
});

describe('when the jetstar-hotels-property-page-recommendations variant is popular_properties/related_properties', () => {
  beforeEach(() => {
    OptimizelyFeature.mockImplementation(({ children }) => children(true, { recommendation: 'popular_properties' }));
  });

  describe('when the property is domestic', () => {
    beforeEach(() => {
      getIsDomestic.mockReturnValue(true);
    });

    describe('and the component is in view', () => {
      beforeEach(() => {
        useInView.mockReturnValue([mockRef, true]);
      });

      it('renders <ResultsFetcher />', () => {
        const { find } = render();

        expect(find('ResultsFetcher')).toExist();
      });

      it('renders <Properties />', () => {
        const { find } = render();

        expect(find('ResultsFetcher')).toExist();
      });

      it('emits emitRecommendedPropertiesResult', () => {
        render();
        expect(dispatch).toHaveBeenCalledWith(emitRecommendedPropertiesResult(payload));
      });
    });

    describe('and the component is out of view', () => {
      beforeEach(() => {
        useInView.mockReturnValue([mockRef, false]);
      });

      it('does NOT render <ResultsFetcher />', () => {
        const { find } = render();

        expect(find('ResultsFetcher')).not.toExist();
      });

      it('does not render <Properties />', () => {
        const { find } = render();

        expect(find('ResultsFetcher')).not.toExist();
      });
    });
  });

  describe('when the property is international', () => {
    beforeEach(() => {
      getIsDomestic.mockReturnValue(false);
    });

    describe('and the component is in view', () => {
      beforeEach(() => {
        useInView.mockReturnValue([mockRef, true]);
      });

      it('does NOT render <ResultsFetcher />', () => {
        const { find } = render();

        expect(find('ResultsFetcher')).not.toExist();
      });

      it('does not render <Properties />', () => {
        const { find } = render();

        expect(find('ResultsFetcher')).not.toExist();
      });
    });

    describe('and the component is out of view', () => {
      beforeEach(() => {
        useInView.mockReturnValue([mockRef, false]);
      });

      it('does NOT render <ResultsFetcher />', () => {
        const { find } = render();

        expect(find('ResultsFetcher')).not.toExist();
      });

      it('does not render <Properties />', () => {
        const { find } = render();

        expect(find('ResultsFetcher')).not.toExist();
      });
    });
  });
});

describe('when the jetstar-hotels-property-page-recommendations variant is off', () => {
  beforeEach(() => {
    getIsDomestic.mockReturnValue(true);
    useInView.mockReturnValue([mockRef, true]);
    OptimizelyFeature.mockImplementation(({ children }) => children(false, { recommendation: 'popular_properties' }));
  });

  it('does NOT render <ResultsFetcher />', () => {
    const { find } = render();

    expect(find('ResultsFetcher')).not.toExist();
  });

  it('does not render <Properties />', () => {
    const { find } = render();

    expect(find('ResultsFetcher')).not.toExist();
  });
});
