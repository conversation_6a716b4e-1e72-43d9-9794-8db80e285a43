import React from 'react';
import { mountUtils } from 'test-utils';
import NoQueryMessage from './NoQueryMessage';

const render = (props = {}) => mountUtils(<NoQueryMessage {...props} />);

describe('with a query', () => {
  it('renders nothing', () => {
    expect(render({ hasQuery: true }).findByTestId('no-query-message')).not.toExist();
  });
});

describe('without a query', () => {
  it('renders renders the correct heading', () => {
    const { find } = render({ hasQuery: false, propertyName: 'The Hilton' });
    expect(find('h2').text()).toEqual('Check availability at The Hilton');
  });

  it('renders renders the correct message', () => {
    const { findByText } = render({ hasQuery: false });
    expect(findByText('To view room prices at this property, please enter your stay dates.')).toExist();
  });
});
