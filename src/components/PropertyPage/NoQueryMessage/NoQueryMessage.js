import React from 'react';
import PropTypes from 'prop-types';
import { Box, Heading, Text } from '@qga/roo-ui/components';

const NoQueryMessage = ({ hasQuery, propertyName }) => {
  if (hasQuery) return null;

  return (
    <Box mb={8} data-testid="no-query-message">
      <Heading.h2 fontSize="md" pt={4}>
        Check availability at {propertyName}
      </Heading.h2>
      <Box bg="lightOrange" p={2} mt={4} textAlign="center">
        <Text fontWeight="bold">To view room prices at this property, please enter your stay dates.</Text>
      </Box>
    </Box>
  );
};

NoQueryMessage.propTypes = {
  hasQuery: PropTypes.bool,
  propertyName: PropTypes.string,
};

NoQueryMessage.defaultProps = {
  hasQuery: false,
  propertyName: '',
};

export default NoQueryMessage;
