import styled from '@emotion/styled';
import PropTypes from 'prop-types';
import React from 'react';
import { Box, Flex } from '@qga/roo-ui/components';
import PageBlock from 'components/PageBlock';
import theme from 'lib/theme';
import PayWith from 'components/PropertyPage/PayWith';

const AnimatedPageBlock = styled(PageBlock)`
  opacity: ${(props) => (props.visible ? '1' : '0')};
  transition: opacity 150ms ease-in-out;
`;

const PayWithStickyNav = React.memo(({ visible }) => {
  return (
    <>
      <AnimatedPageBlock
        backgroundColor="greys.porcelain"
        boxShadow={[0, theme.shadows.hard]}
        position={['none', 'sticky']}
        printable={false}
        py={[0, 2]}
        top={57}
        visible={visible}
        zIndex={visible ? theme.zIndices.stickyPaymentOptions : 0}
        minHeight={visible ? 60 : 0}
      >
        <Flex justifyContent="flex-end">{visible && <PayWith name="points-plus-pay-sticky" pointsAndPayEnabled size="xs" />}</Flex>
      </AnimatedPageBlock>
      <PageBlock display={['none', 'block']} pt={[0, 6]} backgroundColor="greys.porcelain">
        <Box borderBottom={1} borderColor="greys.alto" />
      </PageBlock>
    </>
  );
});

PayWithStickyNav.displayName = 'PayWithStickyNav';

PayWithStickyNav.propTypes = {
  visible: PropTypes.bool,
};

export default PayWithStickyNav;
