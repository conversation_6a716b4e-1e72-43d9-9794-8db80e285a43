import React, { createRef } from 'react';
import { useInView } from 'react-intersection-observer';
import { mountUtils } from 'test-utils';
import { getFullKnownQuery, getQueryOccupants } from 'store/router/routerSelectors';
import { updateQuery } from 'store/propertyAvailability/propertyAvailabilityActions';
import SearchControls from './SearchControls';
import * as config from 'config';
import { getBrowser } from 'store/userEnvironment/userEnvironmentSelectors';

jest.mock('config');
jest.mock('store/router/routerSelectors');
jest.mock('store/userEnvironment/userEnvironmentSelectors');

jest.mock('react-intersection-observer', () => ({
  useInView: jest.fn(),
}));

mountUtils.mockComponent('PayWith');
mountUtils.mockComponent('PayWithStickyNav');
mountUtils.mockComponent('PayWithToggleMessage');
mountUtils.mockComponent('AvailabilityDatePicker');
mountUtils.mockComponent('OccupantPicker');

describe('<SearchControls />', () => {
  let intersection = true;
  const render = () => mountUtils(<SearchControls />, { decorators: { store: true } });

  const occupants = {
    adults: 1,
    children: 2,
    infants: 3,
  };

  const query = {
    adults: 1,
    children: 2,
    infants: 3,
    checkIn: '2020-10-10',
    checkOut: '2020-10-11',
  };

  useInView.mockImplementation(() => [createRef(), intersection]);

  beforeEach(() => {
    getBrowser.mockReturnValue('Browser');
    getFullKnownQuery.mockReturnValue(query);
    getQueryOccupants.mockReturnValue(occupants);
  });

  it('renders the OccupantPicker', () => {
    const { find } = render();
    expect(find('OccupantPicker')).toHaveProp({ occupants });
  });

  it('passes updateQuery wrapped in dispatch to OccupantPicker', () => {
    const { find, decorators } = render();
    find('OccupantPicker').prop('updateQuery')(query);
    expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery(query));
  });

  it('renders the AvailabilityDatePicker', () => {
    const { find } = render();
    expect(find('AvailabilityDatePicker')).toHaveProp({
      anchorX: 'left',
      selectedDates: {
        startDate: query.checkIn,
        endDate: query.checkOut,
      },
    });
  });

  it('passes updateQuery wrapped in dispatch to AvailabilityDatePicker', () => {
    const { find, decorators } = render();
    find('AvailabilityDatePicker').prop('updateQuery')(query);
    expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery(query));
  });

  describe('when pay-with toggle enabled', () => {
    beforeEach(() => {
      config.PAYWITH_TOGGLE_ENABLED = true;
    });

    describe('and intersection is in view', () => {
      beforeEach(() => (intersection = true));

      it('renders PayWithToggleMessage', () => {
        const { find } = render();

        expect(find('PayWithToggleMessage')).toExist();
      });

      it('renders PayWith', () => {
        const { find } = render();

        expect(find('PayWith')).toExist();
      });

      it('renders PayWithStickyNav with inverse inView state', () => {
        const { find } = render();

        expect(find('PayWithStickyNav').prop('visible')).toEqual(false);
      });
    });

    describe('and intersection is out of view', () => {
      beforeEach(() => (intersection = false));

      it('does not render PayWithToggleMessage', () => {
        const { find } = render();

        expect(find('PayWithToggleMessage')).not.toExist();
      });

      it('does not render PayWith', () => {
        const { find } = render();

        expect(find('PayWith')).not.toExist();
      });

      it('renders PayWithStickyNav with inverse inView state', () => {
        const { find } = render();

        expect(find('PayWithStickyNav').prop('visible')).toEqual(true);
      });
    });
  });

  describe('when pay-with toggle disabled', () => {
    beforeEach(() => {
      config.PAYWITH_TOGGLE_ENABLED = false;
      intersection = true;
    });

    it('does not render PayWithToggleMessage', () => {
      const { find } = render();

      expect(find('PayWithToggleMessage')).not.toExist();
    });

    it('does not render PayWith', () => {
      const { find } = render();

      expect(find('PayWith')).not.toExist();
    });

    it('does not render PayWithStickyNav', () => {
      const { find } = render();

      expect(find('PayWithStickyNav')).not.toExist();
    });
  });

  describe('when the browser is IE or Safari', () => {
    beforeEach(() => {
      getBrowser.mockReturnValue('Safari');
    });

    it('does not render PayWithStickyNav', () => {
      const { find } = render();

      expect(find('PayWithStickyNav')).not.toExist();
    });
  });
});
