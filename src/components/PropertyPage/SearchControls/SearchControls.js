import React, { useState, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useInView } from 'react-intersection-observer';
import { Flex, Box } from '@qga/roo-ui/components';
import OccupantPicker from 'components/OccupantPicker';
import { hashObject } from 'lib/hash';
import { updateQuery as updateQueryAction } from 'store/propertyAvailability/propertyAvailabilityActions';
import { getFullKnownQuery, getQueryOccupants } from 'store/router/routerSelectors';
import PageBlock from 'components/PageBlock';
import PayWith from '../PayWith';
import PayWithToggleMessage from 'components/PayWithToggleMessage';
import { PAYWITH_TOGGLE_ENABLED } from 'config';
import PayWithStickyNav from './PayWithStickyNav';
import { isSafariOrIE } from 'lib/browser/isSafariOrIE';
import { getBrowser } from 'store/userEnvironment/userEnvironmentSelectors';
import some from 'lodash/some';
import pick from 'lodash/pick';
import AvailabilityDatePicker from 'components/AvailabilityDatePicker';
// import { getIsExclusive } from 'store/property/propertySelectors';
// import { subDays } from 'date-fns';
// import { getExclusiveOffer } from 'store/exclusiveOffer/exclusiveOfferSelectors';

const labelOptions = {};

const withKey = (props) => ({ key: hashObject(props), ...props });

const SearchControls = () => {
  const query = useSelector(getFullKnownQuery);
  const occupants = useSelector(getQueryOccupants);
  const dispatch = useDispatch();

  const browser = useSelector(getBrowser);
  const safariOrIE = isSafariOrIE(browser);

  const [ref, inView] = useInView({
    delay: 100,
    initialInView: true,
    rootMargin: '10px 0px 500px 0px',
  });

  const [selectedDates, setselectedDates] = useState({ startDate: query.checkIn, endDate: query.checkOut });

  const occupantQueryProps = withKey({ occupants });

  const updateQuery = (query) => {
    const queryOccupants = pick(query, ['adults', 'children', 'infants']);
    const hasOccupants = some(occupants) || some(queryOccupants);
    const defaultOccupants = hasOccupants ? {} : { adults: 2, children: 0, infants: 0 };
    dispatch(updateQueryAction({ ...defaultOccupants, ...query }));
  };

  const onClickOutside = useCallback(() => {
    setselectedDates({ startDate: undefined, endDate: undefined });
  }, [setselectedDates]);

  // const isExclusive = useSelector(getIsExclusive);
  // const offer = useSelector(getExclusiveOffer);
  // const { start: startDate, end: endDate } = offer?.travelDates || {};
  // const dateQueryProps = React.useMemo(() => withKey({ selectedDates: { startDate: query.checkIn, endDate: query.checkOut } }), [
  //   query.checkIn,
  //   query.checkOut,
  // ]);

  // if (isExclusive && startDate && endDate) {
  //   dateQueryProps.initialDisplayDate = new Date(startDate);
  //   dateQueryProps.boundaries = { start: subDays(new Date(startDate), 1), end: new Date(endDate) };
  // }

  return (
    <>
      <PageBlock>
        <Flex
          alignItems={['center', 'center', 'flex-end']}
          flexDirection={['column', 'column', 'row']}
          justifyContent={['center', 'center', 'space-between']}
          width="100%"
        >
          <Flex justifyContent={['center', 'center', 'flex-start']} width={[1, 1, 'inherit']} pb={[4, 6, 0]} mb={[0, 0, 8]}>
            <Box mr={4} data-testid="stay-date-picker" flexBasis={['200px', '436px']}>
              <AvailabilityDatePicker
                selectedDates={selectedDates}
                labelOptions={labelOptions}
                clearSelectedDates={onClickOutside}
                updateQuery={updateQuery}
                anchorX="left"
                backgroundColor="transparent"
                isLimited={true}
              />
            </Box>

            <Box flexBasis={['85px', '150px']}>
              <OccupantPicker {...occupantQueryProps} labelOptions={labelOptions} updateQuery={updateQuery} verboseInMobile={false} />
            </Box>
          </Flex>

          {PAYWITH_TOGGLE_ENABLED && inView && (
            <Flex flexDirection="column">
              <PayWithToggleMessage pointerDirection="bottom" pointerPosition="50%" mb={2} />
              <PayWith size="sm" />
            </Flex>
          )}
        </Flex>
      </PageBlock>

      <Box data-testid="ref-box" ref={ref} />

      {PAYWITH_TOGGLE_ENABLED && !safariOrIE && <PayWithStickyNav visible={!inView} />}
    </>
  );
};

export default SearchControls;
