/* eslint-disable prettier/prettier */

import React from 'react';
import { mountUtils } from 'test-utils';
import PropertyHelmet from './PropertyHelmet';
import * as config from 'config';

jest.mock('config');
jest.mock('store/search/searchSelectors');
jest.mock('store/propertyAvailability/propertyAvailabilitySelectors');

const property = {
  id: '1',
  name: 'The Grand Budapest',
  description: 'This is a great property',
  rating: 4,
  ratingType: 'AAA',
  latitude: 'latitude',
  longitude: 'longitude',
  mainImage: {
    urlOriginal: 'urlOriginal',
  },
  address: {
    streetAddress: ['address line 1', 'address line 2'],
    suburb: 'suburb',
    state: 'state',
    country: 'country',
  },
  images: [
    { urlOriginal: 'urlOriginal', caption: 'nice place' },
    { urlOriginal: 'urlOriginal', caption: 'entry' },
  ],
  customerRatings: [{ averageRating: 4, reviewCount: 100 }],
  propertyFacilities: ['wifi', 'sauna', 'indoor pool'],
};

const decorators = { helmet: true, store: true };
const render = (props) => mountUtils(<PropertyHelmet property={property} {...props} />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();

  config.HOTELS_BRAND_NAME = jest.requireActual('config').HOTELS_BRAND_NAME;
  config.HOTELS_URL = jest.requireActual('config').HOTELS_URL;
});

it('sets the correct page title with helmet', () => {
  const { find } = render();

  expect(find('title')).toHaveText(`The Grand Budapest | ${config.HOTELS_BRAND_NAME}`);
});

it('sets the correct canonical link on helmet', () => {
  const { find } = render();
  const canonicalLink = `${config.HOTELS_URL}/properties/1-the-grand-budapest`;

  expect(find('link')).toHaveProp({ href: canonicalLink });
});

test.each`
  name                      | content
  ${'hotels-booking-stage'} | ${'property-details'}
  ${'og:site_name'}         | ${config.HOTELS_BRAND_NAME}
  ${'og:title'}             | ${property.name}
  ${'og:url'}               | ${`${config.HOTELS_URL}/properties/1-the-grand-budapest`}
  ${'og:image'}             | ${property.mainImage.urlLarge}
  ${'og:latitude'}          | ${property.latitude}
  ${'og:longitude'}         | ${property.longitude}
  ${'og:street_address'}    | ${property.address.streetAddress.join(', ')}
  ${'og:locality'}          | ${property.address.suburb}
  ${'og:region'}            | ${property.address.state}
  ${'og:country_name'}      | ${property.address.country}
`('sets the meta for $name', ({ name, content }) => {
  const { find } = render({ property });
  expect(find(`meta[name="${name}"]`)).toHaveProp({ content });
});

describe('property structured schema', () => {
  it('sets the correct page markup for SEO', () => {
    const { find } = render();

    expect(find('script')).toHaveText(
      JSON.stringify({
        "@context": "https://schema.org",
        "@type": "Hotel",
        "name": "The Grand Budapest",
        "url": `${config.HOTELS_URL}/properties/1-the-grand-budapest`,
        "description": 'This is a great property',
        "starRating": {
          '@type': 'Rating',
          "ratingValue": 4,
          "bestRating": 4
        },
        "address": {
          "@type": "PostalAddress",
          "streetAddress": "address line 1, address line 2",
          "addressLocality": "suburb",
          "addressRegion": "state",
          "addressCountry": "country"
        },
        "image": "urlOriginal",
        "photo": [
          { "url": "urlOriginal", "caption": "nice place", "@type": "ImageObject" },
          { "url": "urlOriginal", "caption": "entry", "@type": "ImageObject" }
        ],
        "amenityFeature": ["wifi", "sauna", "indoor pool"],
        "aggregateRating": {
          "@type": "AggregateRating",
          "itemReviewed": {
            "@type": "Thing",
            "name": "The Grand Budapest"
          },
          "ratingValue": 4,
          "reviewCount": 100
        },
      }),
    );
  });

  it('only includes positive TripAdvisor review counts', () => {
    const selfRatedProperty = { ...property, customerRatings: [{ averageRating: 4, reviewCount: -10 }] };

    const { find } = render({ property: selfRatedProperty });

    expect(find('script')).toHaveText(
      JSON.stringify({
        "@context": "https://schema.org",
        "@type": "Hotel",
        "name": "The Grand Budapest",
        "url": `${config.HOTELS_URL}/properties/1-the-grand-budapest`,
        "description": "This is a great property",
        "starRating": {
          "@type": "Rating",
          "ratingValue": 4,
          "bestRating": 4
        },
        "address": {
          "@type": "PostalAddress",
          "streetAddress": "address line 1, address line 2",
          "addressLocality": "suburb",
          "addressRegion": "state",
          "addressCountry": "country"
        },
        "image": "urlOriginal",
        "photo": [
          { "url": "urlOriginal", "caption": "nice place", "@type": "ImageObject" },
          { "url": "urlOriginal", "caption": "entry", "@type": "ImageObject" }
        ],
        "amenityFeature": ["wifi", "sauna", "indoor pool"],
        "aggregateRating": {
          "@type": "AggregateRating",
          "itemReviewed": {
            "@type": "Thing",
            "name": "The Grand Budapest"
          },
          "ratingValue": 4,
          "reviewCount": null
        },
      }),
    );
  });

  it('does not include aggregateRating when there are no customer ratings', () => {
    const selfRatedProperty = { ...property, customerRatings: [] };

    const { find } = render({ property: selfRatedProperty });

    expect(find('script')).toHaveText(
      JSON.stringify({
        "@context": "https://schema.org",
        "@type": "Hotel",
        "name": "The Grand Budapest",
        "url": `${config.HOTELS_URL}/properties/1-the-grand-budapest`,
        "description": "This is a great property",
        "starRating": {
          "@type": "Rating",
          "ratingValue": 4,
          "bestRating": 4
        },
        "address": {
          "@type": "PostalAddress",
          "streetAddress": "address line 1, address line 2",
          "addressLocality": "suburb",
          "addressRegion": "state",
          "addressCountry": "country"
        },
        "image": "urlOriginal",
        "photo": [
          { "url": "urlOriginal", "caption": "nice place", "@type": "ImageObject" },
          { "url": "urlOriginal", "caption": "entry", "@type": "ImageObject" }
        ],
        "amenityFeature": ["wifi", "sauna", "indoor pool"]
      }),
    );
  });

  it('does not include image when there is no main image', () => {
    const selfRatedProperty = { ...property, mainImage: null };

    const { find } = render({ property: selfRatedProperty });

    expect(find('script')).toHaveText(
      JSON.stringify({
        "@context": "https://schema.org",
        "@type": "Hotel",
        "name": "The Grand Budapest",
        "url": `${config.HOTELS_URL}/properties/1-the-grand-budapest`,
        "description": "This is a great property",
        "starRating": {
          "@type": "Rating",
          "ratingValue": 4,
          "bestRating": 4
        },
        "address": {
          "@type": "PostalAddress",
          "streetAddress": "address line 1, address line 2",
          "addressLocality": "suburb",
          "addressRegion": "state",
          "addressCountry": "country"
        },
        "photo": [
          { "url": "urlOriginal", "caption": "nice place", "@type": "ImageObject" },
          { "url": "urlOriginal", "caption": "entry", "@type": "ImageObject" }
        ],
        "amenityFeature": ["wifi", "sauna", "indoor pool"],
        "aggregateRating": {
          "@type": "AggregateRating",
          "itemReviewed": {
            "@type": "Thing",
            "name": "The Grand Budapest"
          },
          "ratingValue": 4,
          "reviewCount": 100
        },
      }),
    );
  });

  it('does not include photos when there are no images', () => {
    const selfRatedProperty = { ...property, images: null };

    const { find } = render({ property: selfRatedProperty });

    expect(find('script')).toHaveText(
      JSON.stringify({
        "@context": "https://schema.org",
        "@type": "Hotel",
        "name": "The Grand Budapest",
        "url": `${config.HOTELS_URL}/properties/1-the-grand-budapest`,
        "description": "This is a great property",
        "starRating": {
          "@type": "Rating",
          "ratingValue": 4,
          "bestRating": 4
        },
        "address": {
          "@type": "PostalAddress",
          "streetAddress": "address line 1, address line 2",
          "addressLocality": "suburb",
          "addressRegion": "state",
          "addressCountry": "country"
        },
        "image": "urlOriginal",
        "amenityFeature": ["wifi", "sauna", "indoor pool"],
        "aggregateRating": {
          "@type": "AggregateRating",
          "itemReviewed": {
            "@type": "Thing",
            "name": "The Grand Budapest"
          },
          "ratingValue": 4,
          "reviewCount": 100
        },
      }),
    );
  });

  it('does not return starRating in the markup when the ratingType is not "AAA"', () => {
    const selfRatedProperty = { ...property, ratingType: 'Self Rated' };

    const { find } = render({ property: selfRatedProperty });

    expect(find('script')).toHaveText(
      JSON.stringify({
        "@context": "https://schema.org",
        "@type": "Hotel",
        "name": "The Grand Budapest",
        "url": `${config.HOTELS_URL}/properties/1-the-grand-budapest`,
        "description": "This is a great property",
        "starRating": null,
        "address": {
          "@type": "PostalAddress",
          "streetAddress": "address line 1, address line 2",
          "addressLocality": "suburb",
          "addressRegion": "state",
          "addressCountry": "country"
        },
        "image": "urlOriginal",
        "photo": [
          { "url": "urlOriginal", "caption": "nice place", "@type": "ImageObject" },
          { "url": "urlOriginal", "caption": "entry", "@type": "ImageObject" }
        ],
        "amenityFeature": ["wifi", "sauna", "indoor pool"],
        "aggregateRating": {
          "@type": "AggregateRating",
          "itemReviewed": {
            "@type": "Thing",
            "name": "The Grand Budapest"
          },
          "ratingValue": 4,
          "reviewCount": 100
        },
      }),
    );
  });
});

describe('when POINTS_EARN_ENABLED is true', () => {
  beforeEach(() => {
    config.POINTS_EARN_ENABLED = true;
  });

  it('renders the correct description', () => {
    const { find } = render();

    expect(find(`meta[name="description"]`)).toHaveProp({ content: `Book Now & Earn Qantas Points on ${property.name}` });
  });
});
describe('when POINTS_EARN_ENABLED is false', () => {
  beforeEach(() => {
    config.POINTS_EARN_ENABLED = false;
  });

  it('renders the correct description', () => {
    const { find } = render();

    expect(find(`meta[name="description"]`)).toHaveProp({ content: `Book Your Stay at ${property.name} with ${config.HOTELS_BRAND_NAME}` });
  });
});
