/* eslint-disable prettier/prettier */
import React from 'react';
import PropTypes from 'prop-types';
import Head from 'next/head';
import kebabCase from 'lodash/kebabCase';
import get from 'lodash/get';
import { HOTELS_BRAND_NAME, HOTELS_URL, POINTS_EARN_ENABLED } from 'config';

const pageTitle = (property) => `${property.name} | ${HOTELS_BRAND_NAME}`;

const canonicalLink = (property) => `${HOTELS_URL}/properties/${property.id}-${kebabCase(property.name)}`;

const meta = (property) => [
  { name: 'hotels-booking-stage', content: 'property-details' }, //used by qantas app
  {
    name: 'description',
    content: POINTS_EARN_ENABLED
      ? `Book Now & Earn Qantas Points on ${property.name}`
      : `Book Your Stay at ${property.name} with ${HOTELS_BRAND_NAME}`,
  },
  { name: 'og:site_name', content: HOTELS_BRAND_NAME },
  { name: 'og:title', content: property.name },
  { name: 'og:url', content: canonicalLink(property) },
  { name: 'og:image', content: get(property, 'mainImage.urlLarge') },
  { name: 'og:latitude', content: property.latitude },
  { name: 'og:longitude', content: property.longitude },
  { name: 'og:street_address', content: get(property, 'address.streetAddress', []).join(', ') },
  { name: 'og:locality', content: get(property, 'address.suburb') },
  { name: 'og:region', content: get(property, 'address.state') },
  { name: 'og:country_name', content: get(property, 'address.country') },
];

const PropertyHelmet = ({ property }) => {
  const { images, name, description, address, mainImage, customerRatings, propertyFacilities } = property;
  const starRating =
    property.ratingType === 'AAA'
      ? {
          '@type': 'Rating',
          ratingValue: property.rating,
          bestRating: property.rating,
        }
      : null;
  const schema = {
    "@context": "https://schema.org",
    "@type": "Hotel",
    "name": name,
    "url": canonicalLink(property),
    "description": description,
    "starRating": starRating,
    "address": {
      "@type": "PostalAddress",
      "streetAddress": address?.streetAddress.join(", "),
      "addressLocality": address?.suburb,
      "addressRegion": address?.state,
      "addressCountry": address?.country
    },
    "image": mainImage?.urlOriginal,
    "photo": images?.map(({ urlOriginal, caption }) => ({ url: urlOriginal, caption, "@type": "ImageObject" })),
    "amenityFeature": propertyFacilities ? propertyFacilities : null
  };

  if (customerRatings.length > 0) {
    schema["aggregateRating"] = {
      "@type": "AggregateRating",
      "itemReviewed": {
        "@type": "Thing",
        "name": name
      },
      "ratingValue": customerRatings[0]?.averageRating,
      "reviewCount": customerRatings[0]?.reviewCount > 0 ? customerRatings[0].reviewCount : null
    };
  }

  return (
    <Head>
      <title>{pageTitle(property)}</title>
      <link rel="canonical" href={canonicalLink(property)} />
      {meta(property).map(({ name, content }) => (
        <meta key={name} name={name} content={content} />
      ))}
      <style type="text/css">{`
        html {
            scroll-padding-top: 50px;
        }
    `}</style>
      <script type="application/ld+json">{JSON.stringify(schema)}</script>
    </Head>
  );
};

PropertyHelmet.propTypes = {
  property: PropTypes.shape({
    id: PropTypes.string,
    name: PropTypes.string,
    rating: PropTypes.number,
    ratingType: PropTypes.string,
    propertyId: PropTypes.string,
    description: PropTypes.string,
    address: PropTypes.object,
    mainImage: PropTypes.object,
    images: PropTypes.array,
    propertyFacilities: PropTypes.array,
    customerRatings: PropTypes.array,
  }).isRequired,
};

export default PropertyHelmet;
