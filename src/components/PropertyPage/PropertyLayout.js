import React, { useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import isEmpty from 'lodash/isEmpty';
import { Box, Heading, Hide } from '@qga/roo-ui/components';
import { useSelector, useDispatch } from 'react-redux';
import { useMount, useUnmount } from 'react-use';
import PageBlock from 'components/PageBlock';
import styled from '@emotion/styled';
import PropertyHelmet from './PropertyHelmet';
import PropertySummary from './PropertySummary';
import PropertyDescription from './PropertyDescription';
import PropertyFacilities from './PropertyFacilities';
import StandardOfferMessage from './StandardOfferMessage';
import PropertyKnowBeforeYouGo from './PropertyKnowBeforeYouGo';
import SearchControls from './SearchControls';
import PropertyAvailability from './PropertyAvailability';
import PropertyAvailabilitySummary from './PropertyAvailabilitySummary';
import BackToSearchLink from './BackToSearchLink';
import NoQueryMessage from './NoQueryMessage';
import { getProperty, getTripAdvisorRating } from 'store/property/propertySelectors';
import { getExclusiveOffer } from 'store/exclusiveOffer/exclusiveOfferSelectors';
import { getHasValidQuery, getPropertySearchQuery } from 'store/propertyAvailability/propertyAvailabilitySelectors';
import { getFullscreenGalleryContent, getIsMobileApp } from 'store/ui/uiSelectors';
import { getAppNavigationIcon, getQueryParams } from 'store/router/routerSelectors';
import MobileAppBoundary from 'components/MobileAppBoundary';
import PropertyMap from 'components/PropertyMap';
import PropertyHeader from './PropertyHeader';
import { useModal } from 'lib/hooks';
import WelcomeMessage from 'components/WelcomeMessage';
import DynamicMessageBox from 'components/ListSearchPage/Messaging/DynamicMessageBox';
import Waypoints from 'components/Waypoints';
import FullscreenImageGallery from 'components/FullscreenImageGallery';
import RecommendedProperties from './RecommendedProperties';
import { useDataLayer } from 'hooks/useDataLayer';
import CampaignMessaging from 'components/CampaignMessaging';
import StandardImageGallery from 'components/StandardImageGallery';
import OfferDetails from './OfferDetails';
import ExclusiveOffersFetcher from './ExclusiveOffersFetcher';
import ExclusiveOfferCrossSellBanner from './ExclusiveOfferCrossSellBanner';
import ExclusiveOfferUnavailableDialog from './ExclusiveOfferUnavailableDialog';
import Markdown from 'components/Markdown';
import DisclosureSection from './DisclosureSection';
import { EXCLUSIVE_OFFERS_ENABLED } from 'config';
import WithInPageNavBar from './WithInPageNavBar';
import MobileAppLeftNavigationIcon from 'components/MobileAppLeftNavigationIcon';
import { useFeaturedPropertyRedirector } from './hooks/useFeaturedPropertyRedirector';
import { useTrackPropertyPageView } from './hooks/useTrackPropertyPageView';
import { fetchProperty, clearProperty } from 'store/property/propertyActions';
import { clearExclusiveOffer, fetchExclusiveOffer } from 'store/exclusiveOffer/exclusiveOfferActions';
import { setIsPointsPay } from 'store/ui/uiActions';
import { useRouter } from 'next/router';
import RequestCallbackModal from '../RequestCallbackModal';
import BackToDealsLink from './BackToDealsLink';
import usePropertyPageStandardGa4Event from 'hooks/usePropertyPageStandardGa4Event';
import usePropertyPageExclusiveGa4Event from 'hooks/usePropertyPageExclusiveGa4Event';

const collapsibleProps = {
  expand: false,
  variant: 'large',
};

const ClickThroughWrapper = styled(Box)`
  pointer-events: none;
`;

const PropertyLayout = ({ isExclusiveOffer = false, previewToken }) => {
  const { emitInteractionEvent } = useDataLayer();
  const { openModal, modalProps } = useModal();
  const dispatch = useDispatch();
  const router = useRouter();

  const isMobileApp = useSelector(getIsMobileApp);
  const query = useSelector(getPropertySearchQuery);

  const { dealsRegion, dealType } = useSelector(getQueryParams) || {};
  const isFromDeals = dealsRegion ? true : false;

  const hasQuery = useSelector(getHasValidQuery);
  const property = useSelector(getProperty);
  const appNavigationIcon = useSelector(getAppNavigationIcon);
  const tripAdvisorRating = useSelector(getTripAdvisorRating);
  const { images, startIndex } = useSelector(getFullscreenGalleryContent);
  const exclusiveOffer = useSelector(getExclusiveOffer);

  const hasPropertyFacilities = property?.propertyFacilities && property?.propertyFacilities?.length > 0;

  useTrackPropertyPageView();
  useFeaturedPropertyRedirector();

  useMount(() => {
    dispatch(fetchProperty());

    if (isExclusiveOffer) {
      let payload = {};
      if (previewToken) {
        const type = 'property';
        const { propertyid: propertyId } = router.query;
        payload = { type, previewToken, propertyId };
      }
      dispatch(fetchExclusiveOffer({ ...payload }));
    }
  });

  useUnmount(() => {
    dispatch(setIsPointsPay(false));
    dispatch(clearProperty());
    dispatch(clearExclusiveOffer());
  });

  useEffect(() => {
    dispatch(fetchProperty());
  }, [query, dispatch]);

  useEffect(() => {
    if (!isEmpty(images)) {
      openModal();
    }
  }, [openModal, images]);

  const menuItems = [
    {
      name: 'photos',
      text: 'Photos',
      id: 'photos',
      justifyContent: 'flex-start',
      ref: useRef(),
      linkRef: useRef(),
    },
    {
      name: 'rooms',
      text: 'Rooms',
      id: 'rooms',
      justifyContent: 'flex-start',
      ref: useRef(),
      linkRef: useRef(),
    },
    {
      name: 'highlights',
      text: 'Highlights',
      id: 'highlights',
      justifyContent: 'flex-start',
      ref: useRef(),
      linkRef: useRef(),
      isExclusive: true,
    },
    {
      name: 'offer-terms',
      text: 'Offer Terms',
      id: 'offer-terms',
      justifyContent: 'center',
      ref: useRef(),
      linkRef: useRef(),
      isExclusive: true,
    },
    {
      name: 'about-property',
      text: 'About this property',
      id: 'about-property',
      justifyContent: 'center',
      ref: useRef(),
      linkRef: useRef(),
    },
    {
      name: 'location',
      text: 'Location',
      id: 'location',
      justifyContent: 'flex-end',
      ref: useRef(),
      linkRef: useRef(),
    },
    {
      name: 'property-policies',
      text: 'Property policies',
      id: 'property-policies',
      justifyContent: 'flex-end',
      ref: useRef(),
      linkRef: useRef(),
    },
  ];

  const getMenuRef = (name) => menuItems.find((item) => item.name === name).ref;

  const handleGalleryOpened = () => {
    emitInteractionEvent({ type: 'Property Summary Gallery', value: 'Gallery Opened' });
  };

  const handleGalleryImageChanged = (id) => {
    emitInteractionEvent({ type: 'Property Summary Gallery', value: `Gallery Scrolled to ${id}` });
  };

  const { standardViewItemEvent } = usePropertyPageStandardGa4Event();
  const { exclusiveViewItemEvent } = usePropertyPageExclusiveGa4Event();

  useEffect(() => {
    isExclusiveOffer ? exclusiveViewItemEvent() : standardViewItemEvent();
  }, [exclusiveViewItemEvent, isExclusiveOffer, standardViewItemEvent]);

  if (!property) return null;

  return (
    <Box position="relative" bg="white">
      <RequestCallbackModal interactionType="Exclusive Offers" />
      <ExclusiveOffersFetcher />
      <Waypoints />
      <PropertyHelmet property={property} />
      <MobileAppLeftNavigationIcon iconName={appNavigationIcon || 'back'} />

      <CampaignMessaging showDefaultMessage={false} />

      <MobileAppBoundary>
        <PageBlock bg="white" py={4} borderBottom={1} borderColor="greys.porcelain" printable={false}>
          {!isFromDeals && <BackToSearchLink />}
          {isFromDeals && <BackToDealsLink dealsRegion={dealsRegion} dealType={dealType} />}
        </PageBlock>
      </MobileAppBoundary>

      <PageBlock bg="white" gutter={[0, 3]}>
        <PropertyHeader property={property} tripAdvisorRating={tripAdvisorRating} />
      </PageBlock>

      <WithInPageNavBar menuItems={menuItems} menuRefPositionOffset={isMobileApp ? 40 : 120}>
        <Box position="relative" ref={getMenuRef('photos')}>
          <div id="photos"></div>
          <StandardImageGallery images={property.images} onClick={handleGalleryOpened} onImageChanged={handleGalleryImageChanged} />
          {exclusiveOffer && (
            <Hide xs sm>
              <ClickThroughWrapper position="absolute" top="0" left="0" right="0">
                <PageBlock gutter={[0, 3]}>
                  <OfferDetails />
                </PageBlock>
              </ClickThroughWrapper>
            </Hide>
          )}
        </Box>
      </WithInPageNavBar>

      <PageBlock bg="white" gutter={[0, 3]} mt={[3, 3, 0]}>
        <Hide md lg xl>
          {exclusiveOffer && <OfferDetails />}
        </Hide>
      </PageBlock>

      <PageBlock bg="white" gutter={[0, 3]} mt={[3, 3, 0]}>
        <PropertySummary property={property} hasPropertyFacilities={hasPropertyFacilities} menuItems={menuItems} />
      </PageBlock>

      <PageBlock pt={[0, 5]} bg="white" display="flex">
        <DynamicMessageBox />
      </PageBlock>

      <PageBlock bg="white" gutter={0}>
        <WelcomeMessage />
      </PageBlock>

      <Box backgroundColor={['white', 'greys.porcelain']} ref={getMenuRef('rooms')}>
        <div id="rooms"></div>
        <PageBlock pt={[0, 6, 6]} printable={false}>
          <PropertyAvailabilitySummary propertyName={property.name} />
          <NoQueryMessage hasQuery={hasQuery} propertyName={property.name} />
        </PageBlock>

        <div id="main-content"></div>
        <SearchControls />

        {EXCLUSIVE_OFFERS_ENABLED && <ExclusiveOfferCrossSellBanner />}

        <PageBlock py={[7, 2]} overflowY={['hidden', 'inherit']}>
          {exclusiveOffer && <StandardOfferMessage propertyId={property.id} />}
          <PropertyAvailability />
        </PageBlock>
      </Box>

      {exclusiveOffer?.highlights?.description && (
        <DisclosureSection id="highlights" title="Highlights" ref={getMenuRef('highlights')}>
          {exclusiveOffer.highlights?.title && (
            <Heading.h4 fontSize="md" fontWeight="normal">
              {exclusiveOffer.highlights?.title}
            </Heading.h4>
          )}
          <Markdown content={exclusiveOffer.highlights.description} disableParsingRawHTML={false} />
        </DisclosureSection>
      )}

      {exclusiveOffer?.terms?.hotelTerms && (
        <DisclosureSection id="offer-terms" title="Offer Terms" ref={getMenuRef('offer-terms')} {...collapsibleProps}>
          <Markdown content={exclusiveOffer.terms.hotelTerms} disableParsingRawHTML={false} />
        </DisclosureSection>
      )}

      <DisclosureSection id="about-property" title="About this property" ref={getMenuRef('about-property')}>
        <PropertyDescription property={property} />
      </DisclosureSection>

      <DisclosureSection id="facilities" title="Facilities" ref={React.createRef()} {...collapsibleProps}>
        <PropertyFacilities facilities={property.propertyFacilities} interactionEventValue="Bottom Link Selected" />
      </DisclosureSection>

      <DisclosureSection id="location" title="Location" ref={getMenuRef('location')}>
        <PropertyMap property={property} />
      </DisclosureSection>

      <DisclosureSection id="property-policies" title="Property policies" ref={getMenuRef('property-policies')}>
        <PropertyKnowBeforeYouGo
          checkInInstructions={property.checkInInstructions}
          knowBeforeYouGoDescription={property.knowBeforeYouGoDescription}
          mandatoryFeesDescription={property.mandatoryFeesDescription}
        />
      </DisclosureSection>

      <PageBlock bg="snow">
        <RecommendedProperties />
      </PageBlock>

      <FullscreenImageGallery {...modalProps} images={images} startIndex={startIndex} />
      <ExclusiveOfferUnavailableDialog />
    </Box>
  );
};

PropertyLayout.propTypes = {
  isExclusiveOffer: PropTypes.bool,
  previewToken: PropTypes.string,
};

export default PropertyLayout;
