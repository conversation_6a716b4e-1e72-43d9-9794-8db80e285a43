import React from 'react';
import PropTypes from 'prop-types';
import styled from '@emotion/styled';
import TruncateText from 'components/TruncateText';
import { themeGet } from 'styled-system';
import { Icon, Flex } from '@qga/roo-ui/components';

const RenovationsWrapper = styled(Flex)`
  background-color: ${themeGet('colors.blue')};
  width: 100%;
  margin: 0;
  padding: ${themeGet('space.4')} ${themeGet('space.8')};
  border-bottom: 0;
`;

const Renovations = ({ renovations }) => {
  return (
    <RenovationsWrapper data-testid="renovations-container">
      <Icon name="renovations" size={[18, 22]} mr={4} />
      <TruncateText data-testid="renavations-truncate-text" text={renovations} background={'colors.blue'} modalTitle="Renovations" />
    </RenovationsWrapper>
  );
};

Renovations.propTypes = {
  renovations: PropTypes.string.isRequired,
};

export default Renovations;
