import React from 'react';
import BackLink from 'components/BackLink';
import capitalize from 'lodash/capitalize';
import { useDataLayer } from 'hooks/useDataLayer';
import includes from 'lodash/includes';
import split from 'lodash/split';
import join from 'lodash/join';

type Props = {
  dealsRegion: string;
  dealType: string;
};

const BackToDealsLink = ({ dealsRegion, dealType }: Props) => {
  const { emitInteractionEvent } = useDataLayer();

  const capitalizedDealtype = split(dealType, '-').map((part) => capitalize(part));
  const regionDealtype = join(capitalizedDealtype, ' ');
  const capitalizedDealsRegion = capitalize(dealsRegion);
  const labelText = includes(['best-deals', 'chain-deals', 'flyer-deals'], dealType)
    ? `Back to ${regionDealtype} in ${capitalizedDealsRegion}`
    : `Back to ${regionDealtype} deals in ${capitalizedDealsRegion}`;
  const phoneLabel = 'Back to deals';
  const dealsUrl = `/deals/${dealsRegion}/${dealType}`;

  const handleOnClick = () => {
    emitInteractionEvent({ type: 'Return Deals Page', value: `Link Selected` });
  };

  return <BackLink to={dealsUrl} phoneLabel={phoneLabel} label={labelText} onClick={handleOnClick} />;
};

export default BackToDealsLink;
