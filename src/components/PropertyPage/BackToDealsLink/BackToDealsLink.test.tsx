import React from 'react';
import { mocked, mountUtils } from 'test-utils';
import BackToDealsLink from './BackToDealsLink';
import { useDataLayer } from 'hooks/useDataLayer';

jest.mock('hooks/useDataLayer');

const emitInteractionEvent = jest.fn();

const decorators = { store: true, router: true };
const render = () => mountUtils(<BackToDealsLink dealsRegion="sydney" dealType="classic-rewards" />, { decorators });

describe('BackToDealsLink', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
  });

  it('renders <BackLink />', () => {
    const { find } = render();

    expect(find('BackLink')).toExist();
    expect(find('BackLink')).toHaveProp({
      label: 'Back to Classic Rewards deals in Sydney',
      phoneLabel: 'Back to deals',
      to: '/deals/sydney/classic-rewards',
    });
  });

  it('emits a data interaction for GA tracking', () => {
    const { find } = render();
    find('BackLink').simulate('click');

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Return Deals Page',
      value: 'Link Selected',
    });
  });
});
