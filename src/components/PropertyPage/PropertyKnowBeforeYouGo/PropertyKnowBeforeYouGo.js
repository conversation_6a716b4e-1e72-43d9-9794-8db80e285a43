import React from 'react';
import { Text } from '@qga/roo-ui/components';
import Markup from 'components/Markup';
import PropTypes from 'prop-types';

const PropertyKnowBeforeYouGo = ({ checkInInstructions, knowBeforeYouGoDescription, mandatoryFeesDescription }) => (
  <>
    {checkInInstructions && (
      <Text fontSize="base" data-testid="check-in-instructions">
        <Markup content={checkInInstructions} />
      </Text>
    )}

    {knowBeforeYouGoDescription && (
      <Text fontSize="base" data-testid="know-before-you-go-description">
        <Markup content={knowBeforeYouGoDescription} />
      </Text>
    )}

    {mandatoryFeesDescription && (
      <Text fontSize="base" data-testid="mandatory-fees-description">
        <Markup content={mandatoryFeesDescription} />
      </Text>
    )}
    <Text fontSize="base" data-testid="default-policy">
      Bed Types are requests only and may not be honoured at the hotel if availability does not permit.
    </Text>
  </>
);

PropertyKnowBeforeYouGo.propTypes = {
  checkInInstructions: PropTypes.string,
  knowBeforeYouGoDescription: PropTypes.string,
  mandatoryFeesDescription: PropTypes.string,
};

PropertyKnowBeforeYouGo.defaultProps = {
  checkInInstructions: '',
  knowBeforeYouGoDescription: '',
  mandatoryFeesDescription: '',
};

PropertyKnowBeforeYouGo.displayName = 'PropertyKnowBeforeYouGo';

export default PropertyKnowBeforeYouGo;
