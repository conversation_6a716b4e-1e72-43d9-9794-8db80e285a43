import React from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import RoomTypeList from './RoomTypeList';
import UnavailableRoomTypeList from './UnavailableRoomTypeList';
import ResultLoader from 'components/Loader/ResultLoader';
import LoaderSkeletonCard from './LoaderSkeletonCard';
import { getIsExclusive, getRoomTypesWithoutOffers } from 'store/property/propertySelectors';
import {
  getHasValidQuery,
  getIsLoading,
  getUnavailableRoomTypes,
  getSortedAvailableRoomTypes,
} from 'store/propertyAvailability/propertyAvailabilitySelectors';
import { getIsLoading as getIsLoadingExclusive } from 'store/exclusiveOffer/exclusiveOfferSelectors';

//memoised to prevent unnecessary renders when ResultLoader state changes
const LoaderResults = React.memo(({ availableRoomTypes, hasQuery }) => {
  const unavailableRoomTypes = useSelector(getUnavailableRoomTypes);

  return (
    <>
      <RoomTypeList roomTypes={availableRoomTypes} hasQuery={hasQuery} showPhoneFilters />
      {unavailableRoomTypes.length > 0 && <UnavailableRoomTypeList roomTypes={unavailableRoomTypes} hasQuery={hasQuery} />}
    </>
  );
});

LoaderResults.displayName = 'LoaderResults';

LoaderResults.propTypes = {
  availableRoomTypes: PropTypes.array.isRequired,
  hasQuery: PropTypes.bool.isRequired,
};

const RoomTypes = React.memo(() => {
  const hasQuery = useSelector(getHasValidQuery);
  const isLoading = useSelector(getIsLoading);
  const isExclusive = useSelector(getIsExclusive);
  const isLoadingExclusive = useSelector(getIsLoadingExclusive);

  const availableRoomTypes = useSelector(getSortedAvailableRoomTypes) || [];
  const roomTypesWithoutOffers = useSelector(getRoomTypesWithoutOffers) || [];
  const skeletonLoaderSize = availableRoomTypes.length || 5;

  return hasQuery ? (
    <ResultLoader isLoading={isLoading} skeletonResultCount={skeletonLoaderSize} skeletonCardComponent={LoaderSkeletonCard}>
      <LoaderResults availableRoomTypes={availableRoomTypes} hasQuery={hasQuery} />
    </ResultLoader>
  ) : !isExclusive ? (
    <RoomTypeList roomTypes={roomTypesWithoutOffers} hasQuery={hasQuery} />
  ) : (
    <ResultLoader isLoading={isLoadingExclusive} skeletonResultCount={skeletonLoaderSize} skeletonCardComponent={LoaderSkeletonCard}>
      <RoomTypeList roomTypes={roomTypesWithoutOffers} hasQuery={false} />
    </ResultLoader>
  );
});

RoomTypes.displayName = 'RoomTypes';

export default RoomTypes;
