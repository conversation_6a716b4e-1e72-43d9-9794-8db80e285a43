import React from 'react';
import PropTypes from 'prop-types';
import styled from '@emotion/styled';
import { Flex, Box, Icon } from '@qga/roo-ui/components';

const FlagChildren = styled(Box)`
  flex: 1 1 auto;
  align-self: center;
`;

export const Flag = ({ icon, children, ...rest }) => {
  return (
    <Flex {...rest}>
      <Box flex="0 1 32px">{icon && <Icon name={icon} mr="2" />}</Box>
      <FlagChildren>{children}</FlagChildren>
    </Flex>
  );
};

Flag.propTypes = {
  icon: PropTypes.string,
  children: PropTypes.node.isRequired,
};

Flag.defaultProps = {
  icon: '',
};
