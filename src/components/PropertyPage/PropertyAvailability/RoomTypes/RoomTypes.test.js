import React from 'react';
import { mountUtils } from 'test-utils';
import RoomTypes from './RoomTypes';
import ResultLoader from 'components/Loader/ResultLoader';
import { getRoomTypesWithoutOffers, getIsExclusive } from 'store/property/propertySelectors';
import RoomTypeList from './RoomTypeList';
import UnavailableRoomTypeList from './UnavailableRoomTypeList';
import LoaderSkeletonCard from './LoaderSkeletonCard';
import {
  getHasValidQuery,
  getIsLoading,
  getUnavailableRoomTypes,
  getSortedAvailableRoomTypes,
} from 'store/propertyAvailability/propertyAvailabilitySelectors';
import { getExclusiveOfferRoomTypes, getIsLoading as getIsLoadingExclusive } from 'store/exclusiveOffer/exclusiveOfferSelectors';

jest.mock('store/propertyAvailability/propertyAvailabilitySelectors');
jest.mock('store/exclusiveOffer/exclusiveOfferSelectors');
jest.mock('store/property/propertySelectors');
jest.mock('./RoomTypeList', () => jest.fn().mockReturnValue(null));
jest.mock('./UnavailableRoomTypeList', () => jest.fn().mockReturnValue(null));
jest.mock('components/Loader/ResultLoader', () => jest.fn(({ children }) => children));

const render = () => mountUtils(<RoomTypes />, { decorators: { store: true } });

const unavailableRoomTypes = [{ id: 1 }];
const availableRoomTypes = [{ id: 2 }];
const roomTypesWithoutOffers = [{ id: 3, description: 'extranet desc', roomFacilities: ['air conditioning'] }];
const exclusiveOfferCardsData = [
  {
    id: 3,
    offerId: 'cmsOffer1',
    roomFacilities: ['air conditioning'],
    extranet: { id: 3 },
    description: 'cms desc',
    children: 0,
    adults: 1,
    maxOccupantCount: 1,
  },
];

beforeEach(() => {
  getUnavailableRoomTypes.mockReturnValue(unavailableRoomTypes);
  getSortedAvailableRoomTypes.mockReturnValue(availableRoomTypes);
  getRoomTypesWithoutOffers.mockReturnValue(roomTypesWithoutOffers);
  getExclusiveOfferRoomTypes.mockReturnValue(exclusiveOfferCardsData);
  getIsExclusive.mockReturnValue(false);
});

describe('when loading', () => {
  beforeEach(() => {
    getIsLoading.mockReturnValue(true);
  });

  describe('and there is a query', () => {
    beforeEach(() => {
      getHasValidQuery.mockReturnValue(true);
    });

    it('renders the ResultLoader', () => {
      const { find } = render();

      expect(find(ResultLoader)).toHaveProp({
        isLoading: true,
        skeletonResultCount: availableRoomTypes.length,
        skeletonCardComponent: LoaderSkeletonCard,
      });
    });
  });
});

describe('when not loading', () => {
  beforeEach(() => {
    getIsLoading.mockReturnValue(false);
  });

  describe('and there is a query', () => {
    it('renders the available RoomTypeList', () => {
      const { find } = render();
      expect(find(RoomTypeList)).toHaveProp({ hasQuery: true, roomTypes: availableRoomTypes, showPhoneFilters: true });
    });

    it('renders the UnavailableRoomTypeList', () => {
      const { find } = render();
      expect(find(UnavailableRoomTypeList)).toHaveProp({ hasQuery: true, roomTypes: unavailableRoomTypes });
    });

    describe('when there are no unavailable room types', () => {
      beforeEach(() => {
        getUnavailableRoomTypes.mockReturnValue([]);
      });

      it('does not render the unavailable room types', () => {
        const { find } = render();
        expect(find(UnavailableRoomTypeList)).not.toExist();
      });
    });
  });
});

describe('and there is no query', () => {
  beforeEach(() => {
    getHasValidQuery.mockReturnValue(false);
  });

  it('renders the RoomTypeList with roomTypes without offers', () => {
    const { find } = render();

    expect(find(RoomTypeList)).toHaveLength(1);
    expect(find(RoomTypeList)).toHaveProp({ hasQuery: false, roomTypes: roomTypesWithoutOffers });
  });
});

describe('when the offer is exclusive', () => {
  beforeEach(() => {
    getIsExclusive.mockReturnValue(true);
  });

  describe('and it is loading', () => {
    beforeEach(() => {
      getIsLoadingExclusive.mockReturnValue(true);
    });

    it('renders the ResultLoader', () => {
      const { find } = render();

      expect(find(ResultLoader)).toHaveProp({
        isLoading: true,
        skeletonResultCount: availableRoomTypes.length,
        skeletonCardComponent: LoaderSkeletonCard,
      });
    });
  });

  describe('when not loading', () => {
    beforeEach(() => {
      getIsLoadingExclusive.mockReturnValue(false);
    });

    it('renders the available RoomTypeList with the correct props', () => {
      const { find } = render();
      expect(find(RoomTypeList)).toHaveProp({ hasQuery: false, roomTypes: roomTypesWithoutOffers });
    });

    it('does not render the unavailable room types', () => {
      const { find } = render();
      expect(find(UnavailableRoomTypeList)).not.toExist();
    });

    it('does not merge exclusive offers with room type details', () => {
      const { find } = render();
      expect(find(RoomTypeList)).not.toHaveProp({
        hasQuery: false,
        roomTypes: exclusiveOfferCardsData,
      });
    });

    describe('when in pre-search state (no offers)', () => {
      beforeEach(() => {
        getExclusiveOfferRoomTypes.mockReturnValue(null);
      });

      it('returns an empty list if no offers are available', () => {
        const { find } = render();

        expect(find(RoomTypeList)).toHaveProp({
          hasQuery: false,
          roomTypes: roomTypesWithoutOffers,
        });
      });
    });
  });
});
