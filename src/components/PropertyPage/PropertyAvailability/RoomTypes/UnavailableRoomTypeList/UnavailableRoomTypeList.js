import React, { Fragment, useState, useRef, useCallback } from 'react';
import PropTypes from 'prop-types';
import { Text, Icon, Flex, Box } from '@qga/roo-ui/components';
import TextButton from 'components/TextButton';
import RoomTypeList from 'components/PropertyPage/PropertyAvailability/RoomTypes/RoomTypeList';
import pluralize from 'pluralize';
import { useDataLayer } from 'hooks/useDataLayer';

const DEFAULT_VISIBLE_COUNT = 2;

const RoomToggle = ({ onClick, children, testId }) => (
  <Flex justifyContent="center" pt={6} borderTop={1} borderColor="greys.alto">
    <TextButton fontSize="lg" onClick={onClick} textDecoration="none" data-testid={testId}>
      {children}
    </TextButton>
  </Flex>
);

RoomToggle.propTypes = {
  testId: PropTypes.string.isRequired,
  onClick: PropTypes.func.isRequired,
  children: PropTypes.node.isRequired,
};

const UnavailableRoomTypeList = ({ roomTypes, hasQuery }) => {
  const [expanded, setExpanded] = useState(false);
  const expandCount = roomTypes.length - DEFAULT_VISIBLE_COUNT;
  const shouldDisplayShowMoreButton = roomTypes.length > DEFAULT_VISIBLE_COUNT && !expanded;
  const roomTypesToDisplay = expanded ? roomTypes : roomTypes.slice(0, DEFAULT_VISIBLE_COUNT);
  const summaryMessageElement = useRef();
  const unavailableMessage = roomTypes.length === 1 ? 'There is 1 room ' : `There are ${roomTypes.length} rooms `;
  const { emitInteractionEvent } = useDataLayer();

  const collapse = useCallback(() => {
    setExpanded(false);
    summaryMessageElement.current.scrollIntoView();
    emitInteractionEvent({ type: 'Room Details', value: 'Hide unavailable rooms selected' });
  }, [setExpanded, summaryMessageElement, emitInteractionEvent]);

  const expand = useCallback(() => {
    setExpanded(true);
    emitInteractionEvent({ type: 'Room Details', value: 'View more unavailable rooms selected' });
  }, [setExpanded, emitInteractionEvent]);

  return (
    <Fragment>
      <Box mb={4} ml={[2, 0]} textAlign="start">
        <Text fontSize={['base', 'lg']} ref={summaryMessageElement} data-testid="summary-message">
          {unavailableMessage}
          <Text fontSize={['base', 'lg']} fontWeight="bold">
            unavailable{' '}
          </Text>
          for your stay
        </Text>
      </Box>
      <RoomTypeList roomTypes={roomTypesToDisplay} hasQuery={hasQuery} />
      {shouldDisplayShowMoreButton && (
        <RoomToggle onClick={expand} testId="expand-unavailable-rooms">
          <Text fontSize="base">
            Show {expandCount} more unavailable {pluralize('room', expandCount, false)}
          </Text>
          <Icon name="expandMore" />
        </RoomToggle>
      )}
      {expanded && (
        <RoomToggle onClick={collapse} testId="collapse-unavailable-rooms">
          <Text>Hide unavailable rooms</Text>
          <Icon name="expandLess" />
        </RoomToggle>
      )}
    </Fragment>
  );
};

UnavailableRoomTypeList.propTypes = {
  roomTypes: PropTypes.array,
  hasQuery: PropTypes.bool,
};

UnavailableRoomTypeList.defaultProps = {
  roomTypes: [],
  hasQuery: false,
};

export default UnavailableRoomTypeList;
