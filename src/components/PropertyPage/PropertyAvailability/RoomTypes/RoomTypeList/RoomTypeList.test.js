import React from 'react';
import { mountUtils } from 'test-utils';
import RoomType from './RoomType';
import RoomTypeList from './RoomTypeList';

jest.useFakeTimers();
jest.mock('./RoomType', () => jest.fn().mockReturnValue(null));

const roomTypes = [{ id: '1' }, { id: '2' }];
const decorators = { store: true, theme: true };
const render = (props) => mountUtils(<RoomTypeList {...props} roomTypes={roomTypes} hasQuery={true} />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
});

it('renders the correct number of roomTypes', () => {
  const { find } = render();
  const roomTypeNodes = find(RoomType);

  expect(roomTypeNodes).toHaveLength(roomTypes.length);
});

it('renders the roomTypes with the expected key', () => {
  const { find } = render();
  const roomTypeNodes = find(RoomType);

  expect(roomTypeNodes.at(0).key()).toEqual(roomTypes[0].id);
  expect(roomTypeNodes.at(1).key()).toEqual(roomTypes[1].id);
});

it('renders the roomTypes with the expected props', () => {
  const { find } = render();
  const roomTypeNodes = find(RoomType);

  expect(roomTypeNodes.at(0)).toHaveProp({ hasQuery: true, roomType: roomTypes[0] });
  expect(roomTypeNodes.at(1)).toHaveProp({ hasQuery: true, roomType: roomTypes[1] });
});
