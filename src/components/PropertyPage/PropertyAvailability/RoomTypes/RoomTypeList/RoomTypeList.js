import PropTypes from 'prop-types';
import React, { Fragment } from 'react';
import compact from 'lodash/compact';
import RoomType from './RoomType';
import { Box } from '@qga/roo-ui/components';

const RoomTypeList = ({ ...rest }) => {
  return (
    <Fragment>
      <Box overflow={['hidden', 'inherit']}>
        <RoomTypes {...rest} />
      </Box>
    </Fragment>
  );
};

const RoomTypes = ({ roomTypes = [], hasQuery }) =>
  compact(roomTypes.map((roomType) => <RoomType roomType={roomType} hasQuery={hasQuery} key={roomType.id} />));

RoomTypes.defaultProps = {
  roomTypes: [],
};
RoomTypes.displayName = 'RoomTypes';
RoomTypes.propTypes = {
  hasQuery: PropTypes.bool.isRequired,
  roomTypes: PropTypes.array,
};

export default RoomTypeList;
