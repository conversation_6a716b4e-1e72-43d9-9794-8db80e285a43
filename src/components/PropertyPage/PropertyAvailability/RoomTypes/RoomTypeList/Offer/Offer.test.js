import React from 'react';
import { act } from 'react-dom/test-utils';
import { mountUtils } from 'test-utils';
import Offer from './Offer';
import { useBreakpoints } from 'hooks/useBreakpoints';
import { getQueryFeaturedOfferId } from 'store/router/routerSelectors';

jest.mock('hooks/useBreakpoints');
jest.mock('store/router/routerSelectors');

mountUtils.mockComponent('OfferSummary');
mountUtils.mockComponent('ExpandedOffer');

const pointsEarned = {
  qffPoints: {
    base: 891,
    total: 891,
  },
  qbrPoints: {
    total: 298,
  },
};

const offer = {
  type: 'standard',
  description: 'Junior Suite',
  cancellationPolicy: {
    isNonrefundable: true,
    description: 'Non-refundable unless you are entitled to a refund or other remedy under the Australian Consumer Law.',
  },
  name: 'Breakfast Included - Non-refundable',
  charges: {
    total: {
      amount: '297.99',
      currency: 'AUD',
    },
    totalBeforeDiscount: {
      amount: '297.99',
      currency: 'AUD',
    },
    totalDiscount: {
      amount: '0.00',
      currency: 'AUD',
    },
    payableAtProperty: {
      total: {
        amount: 0,
        currency: 'AUD',
      },
    },
  },
  inclusions: [
    {
      code: 'breakfast',
      name: 'Breakfast Included',
      description: 'Breakfast Buffet',
    },
  ],
  valueAdds: ['Breakfast for 2'],
  id: '202017398_230005304_493003',
  pointsEarned,
  promotion: null,
  depositPay: {
    depositPayable: true,
  },
};

const query = {
  checkIn: new Date(2019, 3, 30),
  checkOut: new Date(2019, 4, 1),
};

const checkoutQueryString = 'checkIn=2019-04-30&checkOut=2019-05-01&adults=2&children=0&infants=0&payWith=cash';

const defaultProps = {
  offer,
  query,
  checkoutQueryString,
  propertyId: '1',
  roomTypeId: '2',
  counter: 1,
  isPointsPay: false,
  roomName: 'Standard King',
  isLoading: false,
  totalOffers: 5,
};

const render = (props) => mountUtils(<Offer {...defaultProps} {...props} />, { decorators: { store: true } });

beforeEach(() => {
  jest.resetAllMocks();
  getQueryFeaturedOfferId.mockReturnValue('not_this_offer_id');
  useBreakpoints.mockReturnValue({
    isLessThanBreakpoint: () => false,
  });
});

it('renders <ExpandedOffer /> with the correct props', () => {
  const { find } = render();
  expect(find('OfferHighlighter')).not.toExist();
  expect(find('ExpandedOffer')).toHaveProp({
    roomTypeId: defaultProps.roomTypeId,
    offer: defaultProps.offer,
    roomName: defaultProps.roomName,
  });
});

it('highlights the offer', () => {
  getQueryFeaturedOfferId.mockReturnValue('202017398_230005304_493003');
  const { find } = render();
  expect(find('OfferHighlighter')).toExist();
});

it('renders <OfferSummary /> when the <ExpandedOffer /> is clicked', () => {
  const { wrapper, find } = render();

  act(() => {
    find('ExpandedOffer').prop('toggleOfferExpanded')(false);
  });
  wrapper.update();

  expect(find('OfferSummary')).toHaveProp({ offer: defaultProps.offer });
});

it('renders <ExpandedOffer /> when the <OfferSummary /> is clicked', () => {
  const { wrapper, find } = render();

  act(() => {
    find('ExpandedOffer').prop('toggleOfferExpanded')(false);
  });
  wrapper.update();

  act(() => {
    find('OfferSummary').prop('toggleOfferExpanded')(true);
  });
  wrapper.update();

  expect(find('ExpandedOffer')).toExist();
});
