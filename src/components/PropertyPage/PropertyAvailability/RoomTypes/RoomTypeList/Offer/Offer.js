import React, { Fragment, useEffect, useState, useRef } from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import { Box } from '@qga/roo-ui/components';
import OfferHighlighter from './OfferHighlighter';
import isNil from 'lodash/isNil';
import { useBreakpoints } from 'hooks/useBreakpoints';
import ExpandedOffer from './components/ExpandedOffer';
import OfferSummary from './components/OfferSummary';
import { getQueryFeaturedOfferId } from 'store/router/routerSelectors';
import useViewPromotionEvent from 'hooks/useViewPromotionEvent';

const isHighlighted = (featuredId, offerId) => {
  if ([featuredId, offerId].some(isNil)) return false;
  return String(featuredId) === String(offerId);
};

const Offer = ({ roomTypeId, offer, counter, roomName, totalOffers, ...rest }) => {
  const ref = useRef(null);
  const { isLessThanBreakpoint } = useBreakpoints();
  const isMobile = isLessThanBreakpoint(0);
  const [expanded, setExpanded] = useState(isMobile ? false : counter <= 1);
  const featuredOfferId = useSelector(getQueryFeaturedOfferId);
  const highlighted = isHighlighted(featuredOfferId, offer.id);
  const Highlight = highlighted ? OfferHighlighter : Fragment;
  const isLastOffer = counter === totalOffers;
  const promotionName = offer?.promotion?.name;

  useEffect(() => {
    if (setExpanded) {
      setExpanded(isMobile ? false : counter <= 1);
    }
  }, [isMobile, counter]);

  useViewPromotionEvent({
    ref,
    promotion: {
      name: promotionName,
      slot: 'room_offer',
    },
  });

  return (
    <Box ref={ref} {...rest}>
      <Highlight>
        {expanded ? (
          <ExpandedOffer
            roomTypeId={roomTypeId}
            offer={offer}
            roomName={roomName}
            toggleOfferExpanded={setExpanded}
            isLastOffer={isLastOffer}
          />
        ) : (
          <OfferSummary offer={offer} toggleOfferExpanded={setExpanded} mb={0} isLastOffer={isLastOffer} />
        )}
      </Highlight>
    </Box>
  );
};

Offer.propTypes = {
  offer: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    name: PropTypes.string.isRequired,
    description: PropTypes.string,
    charges: PropTypes.object.isRequired,
    pointsEarned: PropTypes.object,
    inclusions: PropTypes.array.isRequired,
    valueAdds: PropTypes.arrayOf(String),
    cancellationPolicy: PropTypes.object.isRequired,
    payableAtProperty: PropTypes.object,
    type: PropTypes.string.isRequired,
    depositPay: PropTypes.object.isRequired,
    promotion: PropTypes.shape({
      name: PropTypes.string,
    }),
  }).isRequired,
  roomTypeId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  counter: PropTypes.number.isRequired,
  totalOffers: PropTypes.number.isRequired,
  roomName: PropTypes.string.isRequired,
};

export default Offer;
