import React from 'react';
import OfferPointsEarn from './OfferPointsEarn';
import { mountUtils } from 'test-utils';
import * as config from 'config';

jest.mock('config');

mountUtils.mockComponent('BusinessRewardsModal');
mountUtils.mockComponent('BusinessRewardsTooltip');
mountUtils.mockComponent('PointsPerDollar');

const defaultProps = {
  pointsEarned: {
    qffPoints: {
      base: 5139,
      bonus: 0,
      total: 5139,
    },
    qbrPoints: {
      total: 1714,
    },
    maxQffEarnPpd: 3,
  },
  isClassic: false,
  isCurrencyCash: true,
  isPointsPlusPay: true,
};

const decorators = { theme: true, store: true };

const render = (props) => mountUtils(<OfferPointsEarn {...defaultProps} {...props} />, { decorators });

beforeEach(() => {
  Object.assign(config, jest.requireActual('config'));
});

describe('with POINTS_EARN_ENABLED on', () => {
  beforeEach(() => {
    config.POINTS_EARN_ENABLED = true;
  });

  it('renders <PointsPerDollar>', () => {
    const { find } = render();
    expect(find('PointsPerDollar')).toExist();
  });

  describe('when the offer is not classic', () => {
    describe('points', () => {
      it('can display the business points', () => {
        const { find } = render();
        expect(find('BusinessRewardsModal')).toHaveProp({ qbrPoints: { total: 1714 } });
      });

      it('displays a QFF icon', () => {
        const { find } = render({ inclusions: [] });
        const icons = find('Icon');
        expect(icons.filter({ name: 'roo' })).toExist();
      });

      it('can display the points when the offer has a promotion', () => {
        const { find } = render({
          pointsEarned: {
            qffPoints: {
              base: 891,
              bonus: 1000,
              total: 1891,
            },
            qbrPoints: {
              total: 298,
            },
          },
        });

        expect(find('BusinessRewardsModal')).toHaveProp({ qbrPoints: { total: 298 } });
      });
    });
  });

  describe('when the offer is classic', () => {
    it('display a no points earn message', () => {
      const { findByText } = render({ isClassic: true });
      expect(findByText('You will not earn Qantas Points on Classic Rewards offers')).toExist();
    });
  });
});

describe('with POINTS_EARN_ENABLED off', () => {
  beforeEach(() => {
    config.POINTS_EARN_ENABLED = false;
  });

  it('renders nothing', () => {
    const { wrapper } = render();
    expect(wrapper.html()).toBe(null);
  });
});
