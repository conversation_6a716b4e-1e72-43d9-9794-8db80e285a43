import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import { Flex, Box, Icon, NakedButton, Heading, Text } from '@qga/roo-ui/components';
import get from 'lodash/get';
import keyBy from 'lodash/keyBy';
import { OfferWrapper, CollapseWrapper, PriceBoxWrapper, Flag, FullWidthButton } from './primitives';
import { ORDERED_INCLUSIONS_WHITELIST } from './constants';
import ExpandedClickableArea from 'components/ExpandedClickableArea';
import { getQueryCheckIn, getQueryCheckOut } from 'store/router/routerSelectors';
import { getCountry } from 'store/property/propertySelectors';
import PriceBeforeDiscount from 'components/PriceBeforeDiscount';
import Currency from 'components/Currency';
import CampaignPriceMessage from 'components/CampaignPriceMessage';
import NumberOfNights from 'components/NumberOfNights';
import { useDataLayer } from 'hooks/useDataLayer';
import PromotionalSash from 'components/PromotionalSash';
import { useFeature } from '@optimizely/react-sdk';

const ConnectedNumberOfNights = () => {
  const checkIn = useSelector(getQueryCheckIn);
  const checkOut = useSelector(getQueryCheckOut);

  return <NumberOfNights checkIn={checkIn} checkOut={checkOut} />;
};

const OfferSummary = ({ offer, toggleOfferExpanded, isLastOffer }) => {
  const { name: offerName, charges, inclusions, type: offerType } = offer;
  const promotionName = get(offer, 'promotion.name');
  const hasPromo = !!promotionName;
  const country = useSelector(getCountry);
  const { totalBeforeDiscount, total } = charges;
  const currency = get(charges, 'total.currency');
  const isCurrencyCash = currency !== 'PTS';
  const hasDiscount = totalBeforeDiscount && totalBeforeDiscount.amount !== total.amount;
  const { emitInteractionEvent } = useDataLayer();

  const inclusionsByKey = keyBy(inclusions, 'code');

  const [isGlobalInclusionsEnabled] = useFeature('jetstar_hotels_global_inclusions_ui');
  const orderedInclusions = isGlobalInclusionsEnabled
    ? inclusions
    : ORDERED_INCLUSIONS_WHITELIST.reduce((accum, { code, icon }) => {
        const inclusion = inclusionsByKey[code];
        if (!inclusion) return accum;
        return [...accum, { ...inclusion, icon }];
      }, []);

  const onExpandOffer = useCallback(() => {
    toggleOfferExpanded(true);
    emitInteractionEvent({ type: 'Room Offer Details', value: 'Offer Expanded' });
  }, [toggleOfferExpanded, emitInteractionEvent]);

  return (
    <ExpandedClickableArea>
      <OfferWrapper data-testid="offer-card" isLastOffer={isLastOffer}>
        <Flex width={['100%', '50%']} mb={[4, 0]} data-testid="extra-flex" flexDirection="column" pt={hasPromo ? 4 : 0}>
          <Flex>
            <Heading.h3 mb={0} fontSize="md" lineHeight="normal">
              {offerName}
            </Heading.h3>
            <CollapseWrapper sm md lg>
              <NakedButton onClick={onExpandOffer} aria-label="Expand offer details" data-expanded-clickable-area-target>
                <Icon name="expandMore" size={24} />
              </NakedButton>
            </CollapseWrapper>
          </Flex>
          <Box color="greys.steel">
            <Flex flexDirection="row" data-testid="inclusions-preview-wrapper">
              {orderedInclusions.map(({ code, icon }) => (
                <Flag key={code} icon={icon} />
              ))}
            </Flex>
          </Box>
        </Flex>
        <PriceBoxWrapper mt={hasPromo ? 4 : 0} data-testid="promo">
          {promotionName && (
            <Box position="absolute" left={['15px', 'initial']} top="0px">
              <PromotionalSash promotionName={promotionName} />
            </Box>
          )}
          <Flex flexDirection="row" justifyContent="flex-start" alignItems="baseline" flexWrap="wrap">
            <Flex alignItems="center" justifyContent={['flex-start', 'flex-end']}>
              <Currency
                amount={total.amount}
                currency={total.currency}
                roundToCeiling
                fontSize={32}
                hideCurrency={isCurrencyCash}
                fontWeight="bold"
                color="greys.charcoal"
                data-testid="total-to-pay"
                alignCurrency="superscript"
              />
              {!isCurrencyCash && (
                <Text pt={2} data-testid="asterisk">
                  *
                </Text>
              )}
            </Flex>
            <Box color="greys.charcoal" pl={2} top={1}>
              {isCurrencyCash && <Text> {currency}</Text>}
              <Text> / </Text>
              <ConnectedNumberOfNights />
            </Box>
            {hasDiscount && (
              <Flex pr={2} data-testid="price-before-discount-box" width="100%">
                <PriceBeforeDiscount
                  offerType={offerType}
                  size="base"
                  roundToCeiling
                  total={totalBeforeDiscount}
                  hideCurrency={isCurrencyCash}
                  lineHeight="0.6"
                />
              </Flex>
            )}
          </Flex>
          <CampaignPriceMessage currency={total.currency} offerType={offerType} country={country} />
        </PriceBoxWrapper>
        <CollapseWrapper as={Flex} xs justifyContent="flex-end">
          <FullWidthButton data-testid="expand-offer-summary" onClick={onExpandOffer} aria-label="Expand offer details">
            <Icon name="expandMore" size={24} />
          </FullWidthButton>
        </CollapseWrapper>
      </OfferWrapper>
    </ExpandedClickableArea>
  );
};

OfferSummary.propTypes = {
  offer: PropTypes.object.isRequired,
  toggleOfferExpanded: PropTypes.func.isRequired,
  isLastOffer: PropTypes.bool.isRequired,
};

export default OfferSummary;
