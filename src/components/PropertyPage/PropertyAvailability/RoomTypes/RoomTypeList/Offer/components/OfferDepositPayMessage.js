import React from 'react';
import PropTypes from 'prop-types';
import { Text } from '@qga/roo-ui/components';
import { Flag } from 'components/PropertyPage/PropertyAvailability/RoomTypes/primitives';
import { DEPOSIT_PAY_ENABLED } from 'config';

const OfferDepositPayMessage = ({ depositPay }) => {
  const isDepositPayable = DEPOSIT_PAY_ENABLED && !!depositPay?.depositPayable;

  if (!isDepositPayable) return null;

  return (
    <Flag icon="depositPay" color="green" mb={3}>
      <Text fontSize={['sm', 'base']} fontWeight="bold">
        Deposit Pay
      </Text>
    </Flag>
  );
};

OfferDepositPayMessage.propTypes = {
  depositPay: PropTypes.object,
};

OfferDepositPayMessage.defaultProps = {
  depositPay: {
    depositPayable: false,
  },
};

export default OfferDepositPayMessage;
