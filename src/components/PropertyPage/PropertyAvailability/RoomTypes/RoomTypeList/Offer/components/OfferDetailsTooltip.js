import React from 'react';
import PropTypes from 'prop-types';
import { Box, NakedButton, Heading, Text } from '@qga/roo-ui/components';
import { useTooltip } from 'lib/hooks';
import { TooltipWrapper } from './primitives';

const OfferDetailsTooltip = ({ offerDescription = null, offerName }) => {
  const { triggerProps, tooltipProps, isOpen } = useTooltip('offer-details-button');

  return (
    <NakedButton {...triggerProps} pb={2}>
      <Text color="greys.steel" fontSize="sm" textDecoration="underline">
        Offer details
      </Text>
      {isOpen && (
        <Box {...tooltipProps}>
          <TooltipWrapper>
            <Heading.h4 fontSize="sm">{offerName}</Heading.h4>
            {offerDescription && <Text>{offerDescription}</Text>}
          </TooltipWrapper>
        </Box>
      )}
    </NakedButton>
  );
};

OfferDetailsTooltip.propTypes = {
  offerDescription: PropTypes.string,
  offerName: PropTypes.string.isRequired,
};

export default OfferDetailsTooltip;
