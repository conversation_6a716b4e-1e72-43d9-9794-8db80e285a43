import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { Flex, Box, Icon, NakedButton } from '@qga/roo-ui/components';
import { useSelector } from 'react-redux';
import get from 'lodash/get';
import OfferPointsEarn from './OfferPointsEarn';
import OfferInclusions from './OfferInclusions';
import OfferValueAdds from './OfferValueAdds';
import OfferPriceBox from './OfferPriceBox';
import OfferDetails from './OfferDetails';
import usePointsEarned from 'lib/hooks/usePointsEarned/usePointsEarned';
import { OfferWrapper, CollapseWrapper, PriceBoxWrapper, FullWidthButton } from './primitives';
import { useDataLayer } from 'hooks/useDataLayer';
import PromotionalSash from 'components/PromotionalSash';
import { getIsPointsPay } from 'store/ui/uiSelectors';

const ExpandedOffer = ({ roomTypeId, offer, roomName, toggleOfferExpanded, isLastOffer }) => {
  const { id: offerId, name: offerName, charges, inclusions, cancellationPolicy, description, type, depositPay, valueAdds } = offer;
  const { pointsEarned, onCashPaymentAmountChange } = usePointsEarned({ pointsEarned: offer.pointsEarned });
  const promotionName = get(offer, 'promotion.name');
  const hasPromo = !!promotionName;
  const isClassic = offer.type === 'classic';
  const isCurrencyCash = charges.total.currency !== 'PTS';
  const { emitInteractionEvent } = useDataLayer();
  const isPointsPlusPay = useSelector(getIsPointsPay);

  const onCollapseOffer = useCallback(() => {
    toggleOfferExpanded(false);
    emitInteractionEvent({ type: 'Room Offer Details', value: 'Offer Collapsed' });
  }, [toggleOfferExpanded, emitInteractionEvent]);

  return (
    <OfferWrapper data-testid="offer-card" isLastOffer={isLastOffer}>
      <Flex width={['100%', '50%']} mb={[4, 0]} data-testid="extra-flex" flexDirection="column" pt={hasPromo ? 4 : 0}>
        <Flex>
          <OfferDetails
            cancellationPolicy={cancellationPolicy}
            offerDescription={description}
            offerName={offerName}
            depositPay={depositPay}
          />
          <CollapseWrapper sm md lg>
            <NakedButton onClick={onCollapseOffer} aria-label="Collapse offer details">
              <Icon name="expandLess" size={24} />
            </NakedButton>
          </CollapseWrapper>
        </Flex>
        <OfferInclusions inclusions={inclusions} />
        <OfferValueAdds valueAdds={valueAdds} />
        <OfferPointsEarn
          pointsEarned={pointsEarned}
          isClassic={isClassic}
          isCurrencyCash={isCurrencyCash}
          isPointsPlusPay={isPointsPlusPay}
        />
      </Flex>
      <PriceBoxWrapper mt={hasPromo ? 4 : 0}>
        {promotionName && (
          <Box position="absolute" left={['15px', 'initial']} top="0px">
            <PromotionalSash promotionName={promotionName} />
          </Box>
        )}
        <OfferPriceBox
          charges={charges}
          offerType={type}
          offerName={offerName}
          offerId={offerId}
          roomTypeId={roomTypeId}
          roomName={roomName}
          onCashPaymentAmountChange={onCashPaymentAmountChange}
        />
      </PriceBoxWrapper>
      <CollapseWrapper as={Flex} xs justifyContent="flex-end">
        <FullWidthButton data-testid="collapse-offer-summary" onClick={onCollapseOffer} aria-label="Collapse offer details">
          <Icon name="expandLess" size={24} />
        </FullWidthButton>
      </CollapseWrapper>
    </OfferWrapper>
  );
};

ExpandedOffer.propTypes = {
  offer: PropTypes.object.isRequired,
  roomTypeId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  roomName: PropTypes.string.isRequired,
  toggleOfferExpanded: PropTypes.func.isRequired,
  isLastOffer: PropTypes.bool.isRequired,
};

export default ExpandedOffer;
