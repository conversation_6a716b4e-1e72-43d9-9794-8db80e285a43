import React from 'react';
import PropTypes from 'prop-types';
import { Box, NakedButton, Text } from '@qga/roo-ui/components';
import PointsEarnDisplay from 'components/PointsEarnDisplay';
import { useTooltip } from 'lib/hooks';
import { TooltipWrapper } from './primitives';
import { HOTELS_BRAND_NAME } from 'config';

const BusinessRewardsTooltip = ({ qbrPoints }) => {
  const { triggerProps, tooltipProps, isOpen } = useTooltip('qbr-total-button');
  const { total } = qbrPoints;
  return (
    <NakedButton {...triggerProps} pb={2}>
      {total === 0 && (
        <Text display="block" fontSize="base">
          You will not earn Qantas Points when using points
        </Text>
      )}
      {total > 0 && (
        <Text display="block" fontSize="14px">
          Plus <PointsEarnDisplay fontWeight="normal" fontSize="14px" {...qbrPoints} /> PTS{' '}
          <Text color="greys.steel" fontSize="14px" textDecoration="underline">
            for your business
          </Text>
          <Text>**</Text>
        </Text>
      )}
      {isOpen && total > 0 && (
        <Box {...tooltipProps}>
          <TooltipWrapper>
            <Text>
              Travelling for business? Add your Qantas Business Rewards membership ABN number to any domestic or international{' '}
              {HOTELS_BRAND_NAME} booking and your business will earn 1 Qantas Point per A$1 spent.**
            </Text>
          </TooltipWrapper>
        </Box>
      )}
    </NakedButton>
  );
};

BusinessRewardsTooltip.propTypes = {
  qbrPoints: PropTypes.shape({ total: PropTypes.number }).isRequired,
};

export default BusinessRewardsTooltip;
