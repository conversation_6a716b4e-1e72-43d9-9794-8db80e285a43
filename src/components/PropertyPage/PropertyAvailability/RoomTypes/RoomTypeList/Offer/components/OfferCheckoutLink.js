import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import pick from 'lodash/pick';
import { useDataLayer } from 'hooks/useDataLayer';
import isNumber from 'lodash/isNumber';
import { Flex, Button } from '@qga/roo-ui/components';
import { mediaQuery } from 'lib/styledSystem';
import stringifyQueryValues from 'lib/search/stringifyQueryValues';
import { getFullKnownQuery } from 'store/router/routerSelectors';
import AppLink from 'components/AppLink';
import { getAllAvailableOffers } from 'store/exclusiveOffer/exclusiveOfferSelectors';
import { useRouter } from 'next/router';
import { addToCart } from 'store/checkout/checkoutActions';
import { getProperty } from 'store/property/propertySelectors';
import { getAvailableRoomTypes } from 'store/propertyAvailability/propertyAvailabilitySelectors';
import { getPointsConversion } from 'store/pointsBurnTiers/pointsBurnSelectors';
import useSelectPromotionEvent from 'hooks/useSelectPromotionEvent';

const ButtonLink = styled(Button)`
  flex-grow: 1;
  width: 100%;
  font-size: ${themeGet('fontSizes.base')};
  padding-top: 10px;
  padding-bottom: 10px;
  ${mediaQuery.minWidth.md} {
    padding: ${themeGet('space.2')} ${themeGet('space.6')};
    flex-grow: initial;
    max-width: 196px;
  }

  &:hover {
    color: ${themeGet('colors.white')};
  }
`;

ButtonLink.displayName = 'ButtonLink';

const checkoutQueryString = ({ query, roomTypeId, propertyId, offerId, payWith, initialCash }) => {
  const baseCheckoutQuery = {
    roomTypeId,
    propertyId,
    offerId,
    ...pick(query, ['checkIn', 'checkOut', 'adults', 'children', 'infants', 'travelType']),
  };

  return stringifyQueryValues({
    ...baseCheckoutQuery,
    initialCash: isNumber(initialCash) ? initialCash : undefined,
    payWith,
  });
};

const OfferCheckoutLink = ({
  propertyId,
  roomTypeId,
  offerId,
  offerName,
  initialCash = undefined,
  payWith = undefined,
  roomName,
  children,
  ...rest
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const property = useSelector(getProperty);
  const query = useSelector(getFullKnownQuery);
  const pointsConversion = useSelector(getPointsConversion);
  const isRebooked = router.query.ss_action === 'rebook';
  const checkoutUrl = `/checkout?${checkoutQueryString({
    query,
    initialCash,
    roomTypeId,
    propertyId,
    offerId,
    payWith: payWith || query.payWith,
    ss_action: router.query.ss_action,
  })}`;
  const { emitInteractionEvent } = useDataLayer();
  const availableOffers = useSelector(getAllAvailableOffers);
  const offer = availableOffers[offerId];
  const roomTypes = useSelector(getAvailableRoomTypes);
  const roomType = roomTypes.find((r) => r.id === roomTypeId);
  const promotion = { name: offer?.promotion?.name, slot: 'room_offer' };
  const { fireSelectPromotionEvent } = useSelectPromotionEvent({ promotion });

  const handleOnClick = useCallback(() => {
    emitInteractionEvent({ type: 'Select Offer Button', value: `${offerName}` });
    fireSelectPromotionEvent();
    dispatch(
      addToCart({
        property,
        roomType,
        offer,
        query,
        initialCash,
        pointsConversion,
        isRebooked,
      }),
    );
  }, [
    dispatch,
    emitInteractionEvent,
    fireSelectPromotionEvent,
    initialCash,
    isRebooked,
    offer,
    offerName,
    pointsConversion,
    property,
    query,
    roomType,
  ]);

  return (
    <Flex justifyContent="flex-start" mt={2} {...rest}>
      <ButtonLink
        as={AppLink}
        to={checkoutUrl}
        variant="primary"
        data-testid="offer-checkout-link"
        onClick={handleOnClick}
        aria-label={`Select ${roomName}, ${offerName}`}
      >
        {children}
      </ButtonLink>
    </Flex>
  );
};

OfferCheckoutLink.propTypes = {
  propertyId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  offerId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  roomTypeId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  offerName: PropTypes.string.isRequired,
  initialCash: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  payWith: PropTypes.string,
  roomName: PropTypes.string.isRequired,
  children: PropTypes.oneOfType([PropTypes.node, PropTypes.func, PropTypes.string]),
};

OfferCheckoutLink.defaultProps = {
  initialCash: null,
  payWith: null,
  children: 'Select',
};

export default OfferCheckoutLink;
