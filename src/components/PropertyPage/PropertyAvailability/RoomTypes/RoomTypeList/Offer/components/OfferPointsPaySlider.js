import React, { Fragment, useCallback, useState } from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import { Box, Flex, Text } from '@qga/roo-ui/components';
import OfferCheckoutLink from './OfferCheckoutLink';
import OfferPayableAtProperty from './OfferPayableAtProperty';
import { PAYMENT_METHODS } from 'lib/enums/payment';
import { getQueryPayWith } from 'store/router/routerSelectors';
import PointsPaySliderForm from 'components/PointsPaySlider/PointsPaySliderForm';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import { useMount } from 'react-use';

const OfferPointsPaySlider = ({
  totalCash,
  totalPointsAmount,
  payableAtProperty,
  propertyId,
  offerId,
  roomTypeId,
  offerName,
  offerType = null,
  roomName,
  onCashPaymentAmountChange,
  campaignPriceMessage = null,
}) => {
  const queryPayWith = useSelector(getQueryPayWith);
  const [initialCash, setInitialCash] = useState(totalCash.amount);
  const [payWith, setPayWith] = useState(queryPayWith);

  const totalCashAmount = new Decimal(totalCash.amount);

  useMount(() => {
    onCashPaymentAmountChange({ cashAmount: totalCashAmount });
  });

  const updatePointsAndCash = useCallback(
    ({ cash, points }) => {
      const cashAmount = cash.amount;
      const pointsAmount = points.amount;
      const payWith = pointsAmount.greaterThan(0) ? PAYMENT_METHODS.POINTS : PAYMENT_METHODS.CASH;

      onCashPaymentAmountChange({ cashAmount });
      setInitialCash(cashAmount.toNumber());
      setPayWith(payWith);
    },
    [onCashPaymentAmountChange],
  );

  return (
    <Fragment>
      <Box backgroundColor="greys.porcelain" my={[6, 1]} p={4} data-testid="points-pay-wrapper">
        <Flex flexDirection="column">
          {campaignPriceMessage && (
            <Text display="block" data-testid="campaign-message">
              {campaignPriceMessage}
            </Text>
          )}

          <PointsPaySliderForm
            offerType={offerType}
            showLoginBanner
            showPriceBeforeDiscount
            totalCashAmount={totalCashAmount}
            totalPointsAmount={totalPointsAmount}
            updatePointsAndCash={updatePointsAndCash}
          />
        </Flex>
      </Box>
      <OfferPayableAtProperty payableAtProperty={payableAtProperty} isIncludedInTotal={true} />
      <OfferCheckoutLink
        propertyId={propertyId}
        roomTypeId={roomTypeId}
        offerId={offerId}
        initialCash={initialCash}
        offerName={offerName}
        roomName={roomName}
        payWith={payWith}
      />
    </Fragment>
  );
};

OfferPointsPaySlider.propTypes = {
  label: PropTypes.node,
  totalCash: PropTypes.shape({
    amount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    currency: PropTypes.string.isRequired,
  }).isRequired,
  totalPointsAmount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  payableAtProperty: PropTypes.shape({
    amount: PropTypes.string.isRequired,
  }).isRequired,
  propertyId: PropTypes.string.isRequired,
  roomTypeId: PropTypes.string.isRequired,
  offerId: PropTypes.string.isRequired,
  offerName: PropTypes.string.isRequired,
  offerType: PropTypes.string,
  roomName: PropTypes.string.isRequired,
  onCashPaymentAmountChange: PropTypes.func.isRequired,
  campaignPriceMessage: PropTypes.node,
};

export default OfferPointsPaySlider;
