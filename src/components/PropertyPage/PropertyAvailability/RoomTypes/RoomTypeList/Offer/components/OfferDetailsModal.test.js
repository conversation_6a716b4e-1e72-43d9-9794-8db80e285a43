import React from 'react';
import OfferDetailsModal from './OfferDetailsModal';
import { mountUtils } from 'test-utils';
import { useDataLayer } from 'hooks/useDataLayer';

jest.mock('hooks/useDataLayer');
jest.mock('store/ui/uiSelectors');

const defaultProps = {
  offerDescription: 'Offer description',
  offerName: 'Offer name',
};

const decorators = { theme: true, store: true };
const render = (props = {}) => mountUtils(<OfferDetailsModal {...defaultProps} {...props} />, { decorators });
const emitInteractionEvent = jest.fn();

beforeEach(() => {
  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

it('renders the CTA', () => {
  const { find } = render();
  expect(find('TextButton')).toHaveText('Offer details');
});

describe('Clicking the link to trigger a modal', () => {
  it('Displays the offer description in a modal', () => {
    const { find, findByTestId } = render();
    find('button').simulate('click');
    expect(findByTestId('offer-description')).toHaveText(defaultProps.offerName + defaultProps.offerDescription);
  });

  it('dispatches an event to the data layer', () => {
    const { find } = render();
    find('button').simulate('click');
    expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Offer Details Pop Up', value: 'Link Selected' });
  });
});
