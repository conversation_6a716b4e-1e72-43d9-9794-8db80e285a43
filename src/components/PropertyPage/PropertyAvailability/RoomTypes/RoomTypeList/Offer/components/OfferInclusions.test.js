import React from 'react';
import OfferInclusions from './OfferInclusions';
import { mountUtils } from 'test-utils';
import { useFeature } from '@optimizely/react-sdk';

jest.mock('store/ui/uiSelectors');
jest.mock('@optimizely/react-sdk', () => ({
  useFeature: jest.fn(),
}));

const lessThanFourInclusions = {
  inclusions: [
    {
      code: 'upgrade',
      icon: 'incJqUpgrade',
      name: 'Guaranteed Room Upgrade',
      description: 'Guaranteed Room Upgrade',
    },
    {
      code: 'all_inclusive',
      icon: 'incJqAllinclusive',
      name: 'All Inclusive',
      description: 'All Inclusive',
    },
  ],
};

const moreThanFourInclusions = {
  inclusions: [
    {
      code: 'breakfast',
      icon: 'incJqBreakfast',
      name: 'Breakfast Included',
      description: 'Breakfast Included',
    },
    {
      code: 'all_inclusive',
      icon: 'incJqAllinclusive',
      name: 'All Inclusive',
      description: 'All Inclusive',
    },
    {
      code: 'internet',
      icon: 'incJqWifi',
      name: 'Wifi Included',
      description: 'Wifi Included',
    },
    {
      code: 'early_checkin',
      icon: 'incJqCheckin',
      name: 'Early Check-in Included',
      description: 'Early Check-in Included',
    },
    {
      code: 'late_checkout',
      icon: 'incJqCheckout',
      name: 'Late Check-out Included',
      description: 'Late Check-out Included',
    },
    {
      code: 'dinner',
      icon: 'incJqMeal',
      name: 'Dinner Included',
      description: 'Dinner Included',
    },
  ],
};

const render = (props = {}) => mountUtils(<OfferInclusions {...props} />, { decorators: { theme: true, store: true } });

describe('with less than 4 inclusions', () => {
  describe('InclusionsPreviewWrapper', () => {
    describe('when useGlobalInclusions is true', () => {
      beforeEach(() => {
        useFeature.mockReturnValue([true]);
      });
      it('displays a list of all inclusions in order', () => {
        const { findByTestId } = render(lessThanFourInclusions);
        const inclusionsPreview = findByTestId('inclusions-preview-wrapper');

        expect(findByTestId('inclusions-preview-wrapper').children()).toHaveLength(2);
        expect(inclusionsPreview.children().at(0)).toHaveText('Guaranteed Room Upgrade');
        expect(inclusionsPreview.children().at(1)).toHaveText('All Inclusive');
      });

      it('displays an icon for mapped inclusions', () => {
        const { find } = render(lessThanFourInclusions);
        const icons = find('Icon');

        expect(icons.filter({ name: 'incJqUpgrade' })).toExist();
        expect(icons.filter({ name: 'incJqAllinclusive' })).toExist();
      });

      it('does not display the view all message', () => {
        const { findByTestId } = render(lessThanFourInclusions);
        const viewAllButton = findByTestId('view-all-inclusions');

        expect(viewAllButton).not.toExist();
      });
    });

    describe('when useGlobalInclusions is false', () => {
      beforeEach(() => {
        useFeature.mockReturnValue([false]);
      });
      it('displays a list of all inclusions in order', () => {
        const { findByTestId } = render(lessThanFourInclusions);
        const inclusionsPreview = findByTestId('inclusions-preview-wrapper');

        expect(findByTestId('inclusions-preview-wrapper').children()).toHaveLength(2);
        expect(inclusionsPreview.children().at(0)).toHaveText('All Inclusive');
        expect(inclusionsPreview.children().at(1)).toHaveText('Guaranteed Room Upgrade');
      });

      it('displays an icon for mapped inclusions', () => {
        const { find } = render(lessThanFourInclusions);
        const icons = find('Icon');

        expect(icons.filter({ name: 'arrowUpward' })).toExist();
        expect(icons.filter({ name: 'allInclusive' })).toExist();
      });

      it('does not display the view all message', () => {
        const { findByTestId } = render(lessThanFourInclusions);
        const viewAllButton = findByTestId('view-all-inclusions');

        expect(viewAllButton).not.toExist();
      });
    });
  });
});

describe('with more than 4 inclusions', () => {
  describe('InclusionsPreviewWrapper', () => {
    describe('when useGlobalInclusions is true', () => {
      beforeEach(() => {
        useFeature.mockReturnValue([true]);
      });
      it('displays only the first 4 inclusions as for the ORDERED_INCLUSION_WHITELIST', () => {
        const { findByTestId } = render(moreThanFourInclusions);
        const inclusionsPreview = findByTestId('inclusions-preview-wrapper');

        expect(inclusionsPreview.children().at(0)).toHaveText('Breakfast Included');
        expect(inclusionsPreview.children().at(1)).toHaveText('All Inclusive');
        expect(inclusionsPreview.children().at(2)).toHaveText('Wifi Included');
        expect(inclusionsPreview.children().at(3)).toHaveText('Early Check-in Included');
      });

      it('displays an icon for the 4 mapped inclusions', () => {
        const { find } = render(moreThanFourInclusions);
        const icons = find('Icon');

        expect(icons.filter({ name: 'incJqAllinclusive' })).toExist();
        expect(icons.filter({ name: 'incJqBreakfast' })).toExist();
        expect(icons.filter({ name: 'incJqCheckin' })).toExist();
        expect(icons.filter({ name: 'incJqWifi' })).toExist();
        expect(icons.filter({ name: 'incJqMeal' })).not.toExist();
        expect(icons.filter({ name: 'incJqCheckout' })).not.toExist();
      });

      it('displays the view all message', () => {
        const { findByTestId } = render(moreThanFourInclusions);
        const viewAllButton = findByTestId('view-all-inclusions');

        expect(viewAllButton).toExist();
      });
    });

    describe('when useGlobalInclusions is false', () => {
      beforeEach(() => {
        useFeature.mockReturnValue([false]);
      });
      it('displays only the first 4 inclusions as for the ORDERED_INCLUSION_WHITELIST', () => {
        const { findByTestId } = render(moreThanFourInclusions);
        const inclusionsPreview = findByTestId('inclusions-preview-wrapper');

        expect(inclusionsPreview.children().at(0)).toHaveText('All Inclusive');
        expect(inclusionsPreview.children().at(1)).toHaveText('Breakfast Included');
        expect(inclusionsPreview.children().at(2)).toHaveText('Dinner Included');
        expect(inclusionsPreview.children().at(3)).toHaveText('Late Check-out Included');
      });

      it('displays an icon for the 4 mapped inclusions', () => {
        const { find } = render(moreThanFourInclusions);
        const icons = find('Icon');

        expect(icons.filter({ name: 'allInclusive' })).toExist();
        expect(icons.filter({ name: 'freeBreakfast' })).toExist();
        expect(icons.filter({ name: 'restaurant' })).toExist();
        expect(icons.filter({ name: 'accessTime' })).toExist();
        expect(icons.filter({ name: 'earlyCheckin' })).not.toExist();
        expect(icons.filter({ name: 'wifi' })).not.toExist();
      });

      it('displays the view all message', () => {
        const { findByTestId } = render(moreThanFourInclusions);
        const viewAllButton = findByTestId('view-all-inclusions');

        expect(viewAllButton).toExist();
      });
    });
  });

  it('shows the InclusionsList with all the inclusions when the view all button is clicked', () => {
    const { findByTestId, findByText } = render(moreThanFourInclusions);
    const viewAllButton = findByTestId('view-all-inclusions');

    viewAllButton.simulate('click');

    expect(findByText('Offer Inclusions')).toExist();
    expect(findByText('All Inclusive')).toExist();
    expect(findByText('Breakfast Included')).toExist();
    expect(findByText('Dinner Included')).toExist();
    expect(findByText('Late Check-out Included')).toExist();
    expect(findByText('Early Check-in Included')).toExist();
    expect(findByText('Wifi Included')).toExist();
  });
});

it('can handle empty array of inclusions', () => {
  const { find } = render({ inclusions: [] });
  const inclusionIcons = find('Icon');

  expect(inclusionIcons).toHaveLength(0);
});

it('can handle null inclusions', () => {
  const { find } = render();
  const inclusionIcons = find('Icon');

  expect(inclusionIcons).toHaveLength(0);
});
