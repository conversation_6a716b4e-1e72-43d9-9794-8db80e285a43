import React from 'react';
import { act } from 'react-dom/test-utils';
import { mountUtils } from 'test-utils';
import { useDataLayer } from 'hooks/useDataLayer';
import RoomOfferDetails from './RoomOfferDetails';
import { getProperty, getTripAdvisorRating } from 'store/property/propertySelectors';

mountUtils.mockComponent('RoomTitle');
mountUtils.mockComponent('RoomDescription');
mountUtils.mockComponent('ImageGallery', 'ImageGallery component', ['showFullscreenButton']);
mountUtils.mockComponent('RoomFacilities');
mountUtils.mockComponent('TripAdvisorReviewsModal');

jest.mock('hooks/useDataLayer');
jest.mock('store/property/propertySelectors');
jest.mock('store/ui/uiSelectors');

const emitInteractionEvent = jest.fn();
const property = {
  id: '123',
  name: 'Great Hotel',
  rating: 4,
  ratingType: 'AAA',
};

const rating = {
  averageRating: 4.5,
  source: 'trip_advisor',
  reviewCount: 1234,
};
const roomType = {
  name: 'QT King',
  maxOccupantCount: 2,
  description: 'Sophisticated industrial chic with high end services & amenities.',
  roomTypeFacilities: ['Movies', 'Air Con', 'Ensuite bathroom', 'Heating', 'Movies selection', 'A/C', 'Ensuite', 'SPA'],
  images: [
    {
      caption: 'Featured Image',
      urlSmall: 'https://i.travelapi.com/hotels/7000000/6950000/6948200/6948120/5cc9cd13_n.jpg',
      urlOriginal: 'https://i.travelapi.com/hotels/7000000/6950000/6948200/6948120/5cc9cd13_b.jpg',
      urlMedium: 'https://i.travelapi.com/hotels/7000000/6950000/6948200/6948120/5cc9cd13_b.jpg',
      urlLarge: 'https://i.travelapi.com/hotels/7000000/6950000/6948200/6948120/5cc9cd13_z.jpg',
    },
  ],
};

const mockImages = roomType.images.map(({ urlLarge, urlSmall, caption }, index) => ({
  src: urlLarge,
  alt: caption,
  thumbnail: urlSmall,
  index: index + 1,
  total: 1,
}));

const render = () => mountUtils(<RoomOfferDetails roomType={roomType} />, { decorators: { store: true, theme: true } });

const renderModal = () => {
  const { wrapper, find } = render();
  find('TextButton').first().simulate('click');
  return wrapper.update();
};

describe('<RoomOfferDetails />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    useDataLayer.mockReturnValue({ emitInteractionEvent });
    getProperty.mockReturnValue(property);
    getTripAdvisorRating.mockReturnValue(rating);
  });

  it('renders the Modal', () => {
    const { find } = render();
    expect(find('Modal')).toExist();
  });

  it('renders the Header', () => {
    const wrapper = renderModal();
    expect(wrapper.find('[data-testid="modal-property-name"]').first()).toHaveText(property.name);
    expect(wrapper.find('StarRating')).toExist();
    expect(wrapper.find('TripAdvisorRating')).toExist();
  });

  it('renders the RoomTitle with the expected props', () => {
    const wrapper = renderModal();
    expect(wrapper.find('RoomTitle')).toHaveProp({
      name: roomType.name,
      maxOccupantCount: roomType.maxOccupantCount,
    });
  });

  it('renders the RoomDescription with the expected props', () => {
    const wrapper = renderModal();
    expect(wrapper.find('RoomDescription')).toHaveProp({
      description: roomType.description,
    });
  });

  it('renders the ImageGallery with the expected props', () => {
    const wrapper = renderModal();
    expect(wrapper.find('ImageGallery').first()).toHaveProp({
      images: mockImages,
    });
  });

  it('renders the RoomFacilities with the expected props', () => {
    const wrapper = renderModal();
    expect(wrapper.find('RoomFacilities')).toHaveProp({
      facilities: roomType.roomTypeFacilities,
    });
  });

  it('emits an event to the data layer when modal is opened', () => {
    renderModal();
    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'View Room Details Pop Up',
      value: 'Link Selected',
    });
  });

  it('emits an event to the data layer when modal is closed', () => {
    const wrapper = renderModal();
    jest.clearAllMocks();

    act(() => {
      wrapper.find('Modal').first().prop('onRequestClose')();
    });

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'View Room Details Pop Up',
      value: 'Dismiss Selected',
    });
  });

  it('emits an event to the data layer when the image gallery is clicked', () => {
    const wrapper = renderModal();
    wrapper.find('ImageGallery').first().prop('onOpen')();

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Room Offer Details Gallery',
      value: 'Gallery Opened',
    });
  });

  it('emits an event to the data layer when the image gallery is navigated', () => {
    const wrapper = renderModal();
    wrapper.find('ImageGallery').first().prop('onImageChanged')(2);

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Room Offer Details Gallery',
      value: 'Gallery Scrolled to 2',
    });
  });
});
