import React from 'react';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import OfferPointsPaySlider from './OfferPointsPaySlider';
import { mountUtils } from 'test-utils';
import { getQueryPayWith } from 'store/router/routerSelectors';

jest.mock('store/router/routerSelectors');

mountUtils.mockComponent('Currency');
mountUtils.mockComponent('OfferCheckoutLink');
mountUtils.mockComponent('OfferPayableAtProperty');
mountUtils.mockComponent('PriceBeforeDiscount');
mountUtils.mockComponent('PointsPaySliderForm');

const totalPointsAmount = 1000;
const defaultProps = {
  propertyId: '1',
  roomTypeId: '2',
  offerId: '3',
  totalPointsAmount,
  payableAtBooking: { amount: '90', currency: 'AUD' },
  offerName: 'Standard King',
  offerType: 'offer type',
  roomName: 'Standard King',
  onCashPaymentAmountChange: jest.fn(),
};

const render = (props) => mountUtils(<OfferPointsPaySlider {...defaultProps} {...props} />, { decorators: { store: true } });

let totalCash = { amount: '100', currency: 'AUD' };
let payableAtProperty = { amount: '10', currency: 'AUD' };

const cashAmount = new Decimal(100);
const payWith = 'points';

beforeEach(() => {
  getQueryPayWith.mockReturnValue(payWith);
});

afterEach(() => {
  defaultProps.onCashPaymentAmountChange.mockReset();
});

it('renders the PointsPaySliderForm with the expected props', () => {
  const { find } = render({ totalCash, payableAtProperty });
  const wrapper = find('PointsPaySliderForm');

  expect(wrapper.prop('offerType')).toEqual(defaultProps.offerType);
  expect(wrapper.prop('showLoginBanner')).toBeTruthy();
  expect(wrapper.prop('showPriceBeforeDiscount')).toBeTruthy();
  expect(wrapper.prop('totalCashAmount').toNumber()).toEqual(100);
});

it('calls onCashPaymentAmountChange with the cash amount', () => {
  render({ totalCash, payableAtProperty });
  expect(defaultProps.onCashPaymentAmountChange).toHaveBeenCalledWith({ cashAmount });
});

it('renders the OfferPayableAtProperty', () => {
  const { find } = render({ totalCash, payableAtProperty });
  expect(find('OfferPayableAtProperty')).toHaveProp({
    payableAtProperty,
    isIncludedInTotal: true,
  });
});

describe('OfferCheckoutLink', () => {
  describe('with a points amount from the slider', () => {
    it('renders the OfferCheckoutLink with a payWith of points', () => {
      const { find } = render({ totalCash, payableAtProperty });
      expect(find('OfferCheckoutLink')).toHaveProp({
        propertyId: defaultProps.propertyId,
        roomTypeId: defaultProps.roomTypeId,
        offerId: defaultProps.offerId,
        offerName: defaultProps.offerName,
        payWith: 'points',
        initialCash: cashAmount.toString(),
      });
    });
  });

  describe('with a zero points amount from the slider', () => {
    it('renders the OfferCheckoutLink with a payWith based on the one supplied', () => {
      const { find } = render({ totalCash, payableAtProperty });

      expect(find('OfferCheckoutLink')).toHaveProp({
        propertyId: defaultProps.propertyId,
        roomTypeId: defaultProps.roomTypeId,
        offerId: defaultProps.offerId,
        offerName: defaultProps.offerName,
        payWith: 'points',
        initialCash: cashAmount.toString(),
      });
    });
  });
});

describe('with campaign price messaging', () => {
  it('renders the label', () => {
    const { findByTestId } = render({
      totalCash,
      payableAtProperty,
      campaignPriceMessage: 'Save 20%',
    });

    expect(findByTestId('campaign-message').first()).toHaveText('Save 20%');
  });
});

describe('without campaign price messaging', () => {
  it('does NOT render the label', () => {
    const { findByTestId } = render({
      totalCash,
      payableAtProperty,
      campaignPriceMessage: null,
    });

    expect(findByTestId('campaign-message')).not.toExist();
  });
});
