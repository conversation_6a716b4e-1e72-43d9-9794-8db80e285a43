import React from 'react';
import PropTypes from 'prop-types';
import { Box, Text } from '@qga/roo-ui/components';
import { Flag } from './primitives';

const OfferValueAdds = React.memo(({ valueAdds }) => (
  <Box color="greys.steel">
    {(valueAdds || []).map((valueAdd) => (
      <Flag key={valueAdd} icon="done">
        <Text fontSize="base">{valueAdd}</Text>
      </Flag>
    ))}
  </Box>
));

OfferValueAdds.defaultProps = {
  valueAdds: [],
};

OfferValueAdds.propTypes = {
  valueAdds: PropTypes.arrayOf(PropTypes.string),
};

OfferValueAdds.displayName = 'OfferValueAdds';

export default OfferValueAdds;
