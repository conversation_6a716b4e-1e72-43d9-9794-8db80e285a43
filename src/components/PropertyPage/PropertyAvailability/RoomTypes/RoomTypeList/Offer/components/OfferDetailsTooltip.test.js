import React from 'react';
import { mountUtils } from 'test-utils';
import OfferDetailsTooltip from './OfferDetailsTooltip';

jest.mock('react-popper', () => ({
  usePopper: () => ({
    styles: {},
    attributes: {},
  }),
}));

jest.mock('lodash/debounce', () => (fn) => {
  const debounced = fn;
  debounced.cancel = jest.fn();
  return debounced;
});

const defaultProps = {
  offerDescription: 'Offer description',
  offerName: 'Offer name',
};

const decorators = { theme: true };
const render = (props) => mountUtils(<OfferDetailsTooltip {...defaultProps} {...props} />, { decorators });

describe('<OfferDetailsTooltip />', () => {
  describe('when tooltip is closed', () => {
    it('renders the offer details CTA', () => {
      const { find } = render();
      expect(find('NakedButton')).toHaveText('Offer details');
    });
  });

  describe('when tooltip is open', () => {
    it('renders the offer description', () => {
      const { find } = render();
      find('NakedButton').simulate('mouseEnter');
      expect(find('TooltipWrapper')).toHaveText(defaultProps.offerName + defaultProps.offerDescription);
      find('NakedButton').simulate('mouseLeave');
      expect(find('TooltipWrapper')).not.toExist();
    });
  });
});
