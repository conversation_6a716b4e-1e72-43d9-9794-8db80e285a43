import React from 'react';
import PropTypes from 'prop-types';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { mediaQuery } from 'lib/styledSystem';
import { Flex, Box, Icon, NakedButton, Hide } from '@qga/roo-ui/components';

export const List = styled.ul`
  margin: 0;
  padding: 0;
  margin-top: -${themeGet('space.3')};

  ${mediaQuery.minWidth.sm} {
    display: flex;
    flex-wrap: wrap;
  }
`;

export const ListItem = styled.li`
  list-style-type: none;
  padding: ${themeGet('space.3')} 0;
  margin: 0;
  border-bottom: 1px solid ${themeGet('colors.greys.alto')};

  ${mediaQuery.minWidth.sm} {
    flex-basis: 50%;
  }
`;

export const Flag = ({ icon, children }) => (
  <Flex>
    <Box flex="0 1 32px">{icon && <Icon name={icon} mr="2" />}</Box>
    {children}
  </Flex>
);

Flag.propTypes = {
  icon: PropTypes.string,
  children: PropTypes.node,
};

Flag.defaultProps = {
  icon: '',
  children: undefined,
};

export const OfferWrapper = styled(Flex)`
  position: relative;
  background-color: ${themeGet('colors.white')};
  flex-direction: column;
  padding: ${themeGet('space.4')};
  border-bottom: ${themeGet('borders.1')} ${themeGet('colors.greys.dusty')};
  border-bottom: ${(props) => (props.isLastOffer ? 0 : `${themeGet('borders.1')(props)} ${themeGet('colors.greys.dusty')(props)}`)};

  ${mediaQuery.minWidth.sm} {
    flex-direction: row;
    border-radius: ${themeGet('radii.default')};
    padding: ${themeGet('space.4')} ${themeGet('space.8')};
    border: ${themeGet('borders.1')} ${themeGet('colors.greys.dusty')};
  }
`;

export const CollapseWrapper = styled(Hide)`
  flex-grow: 1;
  text-align: right;
`;

export const PriceBoxWrapper = styled(Flex)`
  width: 100%;
  flex-direction: column;
  align-items: flex-start;
  padding-right: 0;
  ${mediaQuery.minWidth.sm} {
    width: 45%;
    padding-right: ${themeGet('space.8')};
  }
`;

export const FullWidthButton = styled(NakedButton)`
  width: 100%;
  display: flex;
  justify-content: center;
  padding-top: ${themeGet('space.6')};
`;

export const TooltipWrapper = styled(Box)`
  box-shadow: ${themeGet('shadows.tooltip')};
  color: ${themeGet('colors.greys.steel')};
  background-color: ${themeGet('colors.white')};
  padding: ${themeGet('space.6')} ${themeGet('space.8')};
  border-radius: ${themeGet('radii.default')};
  width: ${themeGet('uiStructure.tooltipWidth')};
  text-align: left;
`;
