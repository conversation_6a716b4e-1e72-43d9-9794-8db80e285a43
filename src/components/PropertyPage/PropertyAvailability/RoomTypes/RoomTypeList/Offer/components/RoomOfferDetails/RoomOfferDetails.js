import React, { Fragment, useCallback } from 'react';
import PropTypes from 'prop-types';
import { useSelector, useDispatch } from 'react-redux';
import { useModal } from 'lib/hooks';
import { setFullscreenGalleryContent } from 'store/ui/uiActions';
import { Flex, Box, Hide, Text, StarRating, ImageGallery } from '@qga/roo-ui/components';
import { useDataLayer } from 'hooks/useDataLayer';
import Modal from 'components/Modal';
import TextButton from 'components/TextButton';
import RoomDescription from './RoomDescription';
import RoomTitle from './RoomTitle';
import RoomFacilities from './RoomFacilities';
import { getProperty, getTripAdvisorRating } from 'store/property/propertySelectors';
import TripAdvisorRating from 'components/TripAdvisorRating';

const RoomOfferDetails = ({ roomType }) => {
  const { name: roomName, maxOccupantCount, images, roomTypeFacilities, description } = roomType;
  const { name: propertyName, rating, ratingType } = useSelector(getProperty);
  const tripAdvisorRating = useSelector(getTripAdvisorRating);
  const { openModal, closeModal, modalProps } = useModal();
  const { emitInteractionEvent } = useDataLayer();
  const dispatch = useDispatch();

  const imageGalleryList = images?.map(({ urlLarge, urlSmall, caption }, index) => ({
    src: urlLarge,
    alt: caption,
    thumbnail: urlSmall,
    index: index + 1,
    total: images.length,
  }));

  const openModalHandler = useCallback(() => {
    openModal();
    emitInteractionEvent({ type: 'View Room Details Pop Up', value: 'Link Selected' });
  }, [emitInteractionEvent, openModal]);

  const closeModalHandler = useCallback(() => {
    closeModal();
    emitInteractionEvent({ type: 'View Room Details Pop Up', value: 'Dismiss Selected' });
  }, [emitInteractionEvent, closeModal]);

  const handleOpenGallery = (startIndex) => {
    dispatch(setFullscreenGalleryContent({ images: imageGalleryList, startIndex }));
    emitInteractionEvent({ type: 'Room Offer Details Gallery', value: 'Gallery Opened' });
  };

  const handleGalleryImageChanged = useCallback(
    (id) => {
      emitInteractionEvent({ type: 'Room Offer Details Gallery', value: `Gallery Scrolled to ${id}` });
    },
    [emitInteractionEvent],
  );

  return (
    <Fragment>
      <TextButton onClick={openModalHandler} fontSize="base" aria-label={`View room details for ${roomName}`}>
        View room details
      </TextButton>
      <Modal
        {...modalProps}
        width="80%"
        padding={0}
        onRequestClose={closeModalHandler}
        title={
          <Flex>
            <Text fontSize={['base', 'md']} data-testid="modal-property-name">
              {propertyName}
            </Text>
            <Hide xs sm as={Flex} alignItems="center" ml={3}>
              <StarRating rating={rating} ratingType={ratingType} size={12} />
              {tripAdvisorRating && <TripAdvisorRating ml={2} rating={tripAdvisorRating} displayReviews underlined small />}
            </Hide>
          </Flex>
        }
      >
        <Hide lg>
          <ImageGallery
            images={imageGalleryList}
            onOpen={handleOpenGallery}
            onImageChanged={handleGalleryImageChanged}
            height={['213px', '400px']}
            showFullscreenButton={false}
          />
        </Hide>
        <Flex flexDirection={['column', 'column', 'row']} justifyContent="space-between" alignItems="flex-start">
          <Box p={[5, 5, 8]} pb={[0, 0, 8]} width={['100%', '100%', '40%']}>
            <RoomTitle name={roomName} maxOccupantCount={maxOccupantCount} />
            {description && <RoomDescription description={description} />}
          </Box>
          <Flex flexDirection="column" width={['100%', '100%', '60%']} borderLeft={1} borderColor="greys.alto">
            <Hide xs sm md>
              <ImageGallery
                images={imageGalleryList}
                onImageChanged={handleGalleryImageChanged}
                height="385px"
                showThumbnails
                showFullscreenButton={false}
              />
            </Hide>
            <RoomFacilities facilities={roomTypeFacilities} />
          </Flex>
        </Flex>
      </Modal>
    </Fragment>
  );
};

RoomOfferDetails.propTypes = {
  roomType: PropTypes.shape({
    name: PropTypes.string,
    maxOccupantCount: PropTypes.number,
    images: PropTypes.arrayOf(PropTypes.object),
    roomTypeFacilities: PropTypes.arrayOf(PropTypes.string),
    description: PropTypes.string,
  }).isRequired,
};

export default RoomOfferDetails;
