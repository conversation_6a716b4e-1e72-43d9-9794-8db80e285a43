import React, { memo } from 'react';
import PropTypes from 'prop-types';
import { Flex, Box, Heading, Hide } from '@qga/roo-ui/components';
import CancellationRefundModal from 'components/CancellationRefundModal';
import OfferDepositPayMessage from './OfferDepositPayMessage';
import CancellationTooltip from 'components/CancellationTooltip';
import OfferDetailsModal from './OfferDetailsModal';
import OfferDetailsTooltip from './OfferDetailsTooltip';

const OfferDetails = memo(({ cancellationPolicy, offerDescription, offerName, depositPay }) => (
  <Flex flexDirection="column" flex="1 1 0px">
    <Box>
      <Heading.h3 display="block" mb="0" fontSize="md" lineHeight="normal">
        {offerName}
      </Heading.h3>
      <Hide md lg>
        <OfferDetailsModal offerDescription={offerDescription} offerName={offerName} />
      </Hide>
      <Hide sm xs>
        <OfferDetailsTooltip offerDescription={offerDescription} offerName={offerName} />
      </Hide>
    </Box>
    <Flex flex="1 1 auto" mt={4} flexDirection="column" alignItems="flex-start">
      <Hide md lg>
        <CancellationRefundModal cancellationPolicy={cancellationPolicy} fontSize={['sm', 'base']} mb={2} />
      </Hide>
      <Hide sm xs>
        <CancellationTooltip cancellationPolicy={cancellationPolicy} />
      </Hide>
      <OfferDepositPayMessage depositPay={depositPay} />
    </Flex>
  </Flex>
));

OfferDetails.propTypes = {
  cancellationPolicy: PropTypes.shape({
    isNonrefundable: PropTypes.bool.isRequired,
    description: PropTypes.string.isRequired,
  }).isRequired,
  offerDescription: PropTypes.string,
  offerName: PropTypes.string.isRequired,
  depositPay: PropTypes.object.isRequired,
};

OfferDetails.displayName = 'OfferDetails';

export default OfferDetails;
