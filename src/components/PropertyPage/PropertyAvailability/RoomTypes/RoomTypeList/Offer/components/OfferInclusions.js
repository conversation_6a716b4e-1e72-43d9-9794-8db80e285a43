import React from 'react';
import PropTypes from 'prop-types';
import keyBy from 'lodash/keyBy';
import { Box, Text, Flex } from '@qga/roo-ui/components';
import { useModal } from 'lib/hooks';
import Modal from 'components/Modal';
import { ORDERED_INCLUSIONS_WHITELIST } from './constants';
import { Flag } from './primitives';
import InclusionsList from './InclusionsList';
import TextButton from 'components/TextButton';
import { useFeature } from '@optimizely/react-sdk';

const INCLUSIONS_PREVIEW_AMOUNT = 4;

const OfferInclusions = React.memo(({ inclusions }) => {
  const { openModal, modalProps } = useModal();
  const totalInclusions = inclusions.length;
  const showViewAll = totalInclusions > INCLUSIONS_PREVIEW_AMOUNT;
  const [isGlobalInclusionsEnabled] = useFeature('jetstar_hotels_global_inclusions_ui');
  const inclusionsByKey = keyBy(inclusions, 'code');

  const orderedInclusions = isGlobalInclusionsEnabled
    ? inclusions
    : inclusions
      ? ORDERED_INCLUSIONS_WHITELIST.reduce((accum, { code, icon }) => {
          const inclusion = inclusionsByKey[code];
          if (!inclusion) return accum;
          return [...accum, { ...inclusion, icon }];
        }, [])
      : null;

  const orderedInclusionsPreview = orderedInclusions.slice(0, INCLUSIONS_PREVIEW_AMOUNT);

  return (
    <Box color="greys.steel">
      <Flex flexDirection="column" data-testid="inclusions-preview-wrapper">
        {orderedInclusionsPreview.map(({ code, icon, description }) => (
          <Flag key={code} icon={icon}>
            <Text fontSize="base">{description}</Text>
          </Flag>
        ))}
      </Flex>
      {showViewAll && (
        <TextButton onClick={openModal} fontSize="base" mt="3" data-testid="view-all-inclusions">
          {`View ${totalInclusions} offer inclusions`}
        </TextButton>
      )}
      <Modal {...modalProps} title="Offer Inclusions">
        <InclusionsList inclusions={orderedInclusions} />
      </Modal>
    </Box>
  );
});

OfferInclusions.defaultProps = {
  inclusions: [],
};

OfferInclusions.propTypes = {
  inclusions: PropTypes.array,
};

OfferInclusions.displayName = 'OfferInclusions';

export default OfferInclusions;
