import React from 'react';
import OfferPriceBox from './OfferPriceBox';
import { mountUtils } from 'test-utils';
import OfferPointsPaySlider from './OfferPointsPaySlider';
import { getLevels } from 'store/pointsConversion/pointsConversionSelectors';
import { TIER_9 } from 'test-utils/points/conversionTiers';
import { getQueryCheckIn, getQueryCheckOut } from 'store/router/routerSelectors';
import { getPropertyId, getCountry } from 'store/property/propertySelectors';
import { getIsPointsPay } from 'store/ui/uiSelectors';

jest.mock('./OfferPointsPaySlider', () => () => null);
jest.mock('store/pointsConversion/pointsConversionSelectors');
jest.mock('store/router/routerSelectors');
jest.mock('store/property/propertySelectors');
jest.mock('store/ui/uiSelectors');
jest.mock('store/campaign/campaignSelectors');

mountUtils.mockComponent('OfferCheckoutLink');
mountUtils.mockComponent('OfferPayableAtProperty');
mountUtils.mockComponent('CampaignPriceMessage');
mountUtils.mockComponent('OfferPointsPaySlider');
mountUtils.mockComponent('OfferGuestsText');

const defaultProps = {
  onCashPaymentAmountChange: jest.fn(),
  offerType: 'standard',
  checkoutUrl: 'http://checkoutpage.com',
  roomTypeId: '2',
  offerId: '3',
  offerName: 'Best Rate',
  roomName: 'Standard King',
  charges: {
    total: {
      amount: '159.00',
      currency: 'AUD',
    },
    totalBeforeDiscount: {
      amount: '159.00',
      currency: 'AUD',
    },
    totalDiscount: {
      amount: '0',
      currency: 'AUD',
    },
    payableAtBooking: {
      total: {
        amount: '149.00',
        currency: 'AUD',
      },
    },
    payableAtProperty: {
      total: {
        amount: '10',
        currency: 'AUD',
      },
    },
  },
};

const checkIn = new Date(2025, 3, 30);
const checkOut = new Date(2025, 4, 1);
const propertyId = '1';
const isPointPay = true;
const isNotPointPay = false;
const country = 'Australia';

const decorators = { theme: true, store: true, router: true };
const render = (props = {}) => mountUtils(<OfferPriceBox {...defaultProps} {...props} />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
  getLevels.mockReturnValue(TIER_9);
  getQueryCheckIn.mockReturnValue(checkIn);
  getQueryCheckOut.mockReturnValue(checkOut);
  getPropertyId.mockReturnValue(propertyId);
  getIsPointsPay.mockReturnValue(isNotPointPay);
  getCountry.mockReturnValue(country);
});

describe('with in cash mode', () => {
  it('shows the OfferGuestsText component', () => {
    const { find } = render();
    expect(find('OfferGuestsText')).toExist();
  });

  it('shows the currency after the number of nights', () => {
    const { findByTestId } = render();
    expect(findByTestId('currency-symbol')).toExist();
  });

  it('Shows the price', () => {
    const { wrapper } = render();
    expect(wrapper).toIncludeText('$159');
  });

  it('Shows the currency', () => {
    const { wrapper } = render();
    expect(wrapper).toIncludeText('AUD');
  });

  it('does not show the * after the currency', () => {
    const { findByTestId } = render();
    expect(findByTestId('asterisk')).not.toExist();
  });

  it('renders the OfferPayableAtProperty', () => {
    const { find } = render();
    expect(find('OfferPayableAtProperty')).toHaveProp({
      payableAtProperty: defaultProps.charges.payableAtProperty.total,
      isIncludedInTotal: true,
    });
  });

  it('renders the cash campaign price message', () => {
    const { find } = render();
    expect(find('CampaignPriceMessage')).toHaveProp({
      currency: 'AUD',
      offerType: 'standard',
      country,
    });
  });
});

describe('with in points mode', () => {
  const props = {
    ...defaultProps,
    charges: {
      total: {
        amount: '2795',
        currency: 'PTS',
      },
      totalCash: {
        amount: '100',
        currency: 'AUD',
      },
      totalBeforeDiscount: {
        amount: '2795',
        currency: 'PTS',
      },
      totalDiscount: {
        amount: '0',
        currency: 'PTS',
      },
      payableAtProperty: {
        total: {
          amount: '0',
          currency: 'AUD',
        },
      },
      payableAtBooking: {
        total: {
          amount: '2795',
          currency: 'PTS',
        },
      },
    },
  };

  it('shows the OfferGuestsText component', () => {
    const { find } = render();
    expect(find('OfferGuestsText')).toExist();
  });
  it('Shows the price', () => {
    const { wrapper } = render(props);

    expect(wrapper).toIncludeText('2,795');
  });

  it('Shows the currency', () => {
    const { wrapper } = render(props);

    expect(wrapper).toIncludeText('PTS');
  });

  it('Shows the * after the currency', () => {
    const { findByTestId } = render(props);

    expect(findByTestId('asterisk')).toExist();
  });

  it('renders the OfferPayableAtProperty', () => {
    const { find } = render(props);

    expect(find('OfferPayableAtProperty')).toHaveProp({
      payableAtProperty: props.charges.payableAtProperty.total,
      isIncludedInTotal: false,
    });
  });

  it('renders the points campaign price message with the correct props', () => {
    const { find } = render(props);

    expect(find('CampaignPriceMessage')).toHaveProp({
      currency: 'PTS',
      offerType: 'standard',
    });
  });
});

describe('OfferCheckoutLink', () => {
  it('renders the OfferCheckoutLink with the corrects props', () => {
    const { find } = render();

    expect(find('OfferCheckoutLink')).toHaveProp({
      propertyId: propertyId,
      roomTypeId: defaultProps.roomTypeId,
      offerId: defaultProps.offerId,
      offerName: defaultProps.offerName,
      roomName: defaultProps.roomName,
    });
  });
});

describe('isDiscounted', () => {
  describe('when it is false', () => {
    it('renders only the Currency components and not the PriceBeforeDiscount', () => {
      const { findByTestId } = render();

      expect(findByTestId('price-before-discount-box')).not.toExist();
      expect(findByTestId('total-to-pay')).toExist();
    });
  });

  describe('when it is true', () => {
    const props = {
      ...defaultProps,
      charges: {
        total: {
          amount: '2795',
          currency: 'PTS',
        },
        totalCash: {
          amount: '100',
          currency: 'AUD',
        },
        totalBeforeDiscount: {
          amount: '3000',
          currency: 'PTS',
        },
        totalDiscount: {
          amount: '205',
          currency: 'PTS',
        },
        payableAtProperty: {
          total: {
            amount: '0',
            currency: 'AUD',
          },
        },
        payableAtBooking: {
          total: {
            amount: '2795',
            currency: 'PTS',
          },
        },
      },
    };

    it('renders the PriceBeforeDiscount and the Currency components', () => {
      const { findByTestId } = render(props);

      expect(findByTestId('price-before-discount-box')).toExist();
      expect(findByTestId('total-to-pay')).toExist();
    });
  });
});

describe('when in points + pay mode', () => {
  beforeEach(() => {
    getIsPointsPay.mockReturnValue(isPointPay);
  });

  describe('when in cash mode', () => {
    it('renders the OfferPointsPaySlider with the correct props', () => {
      const { find } = render();

      expect(find(OfferPointsPaySlider)).toHaveProp({
        totalCash: defaultProps.charges.total,
        payableAtProperty: defaultProps.charges.payableAtProperty.total,
        propertyId: propertyId,
        roomTypeId: defaultProps.roomTypeId,
        offerId: defaultProps.offerId,
        offerType: defaultProps.offerType,
      });
    });
  });

  describe('when in points mode', () => {
    const charges = {
      total: {
        amount: '27950',
        currency: 'PTS',
      },
      totalCash: {
        amount: '100',
        currency: 'AUD',
      },
      totalBeforeDiscount: {
        amount: '30000',
        currency: 'PTS',
      },
      totalDiscount: {
        amount: '2050',
        currency: 'PTS',
      },
      payableAtProperty: {
        total: {
          amount: '0',
          currency: 'AUD',
        },
      },
      payableAtBooking: {
        total: {
          amount: '27950',
          currency: 'PTS',
        },
      },
    };

    it('renders the OfferPointsPaySlider', () => {
      const { find } = render({
        ...defaultProps,
        isPointsPay: true,
        charges,
      });

      expect(find(OfferPointsPaySlider)).toHaveProp({
        totalCash: charges.totalCash,
        payableAtProperty: charges.payableAtProperty.total,
        propertyId: propertyId,
        roomTypeId: defaultProps.roomTypeId,
        offerId: defaultProps.offerId,
        offerType: defaultProps.offerType,
      });
    });
  });

  it('does not show the currency after the number of nights', () => {
    const { findByTestId } = render({ isPointsPay: true });

    expect(findByTestId('currency-symbol')).not.toExist();
  });

  it('does not render the total price', () => {
    const { findByTestId } = render({ isPointsPay: true });

    expect(findByTestId('total-to-pay')).not.toExist();
  });

  it('does not render the price before discount', () => {
    const { findByTestId } = render({ isPointsPay: true });

    expect(findByTestId('price-before-discount-box')).not.toExist();
  });

  it('does not show the slider for classic offers', () => {
    const { find } = render({ isPointsPay: true, offerType: 'classic' });

    expect(find(OfferPointsPaySlider)).not.toExist();
  });

  it('does not show the slider for cash based payableAtBooking amounts less than 5000 points', () => {
    const { find } = render({
      isPointsPay: true,
      charges: { ...defaultProps.charges, payableAtBooking: { total: { amount: '25.00', currency: 'AUD' } } },
    });

    expect(find(OfferPointsPaySlider)).not.toExist();
  });

  it('does not show the slider for points based payableAtBooking amounts less than 5000 points', () => {
    const { find } = render({
      isPointsPay: true,
      charges: { ...defaultProps.charges, payableAtBooking: { total: { amount: '4999', currency: 'PTS' } } },
    });

    expect(find(OfferPointsPaySlider)).not.toExist();
  });
});
