import React from 'react';
import OfferDepositPayMessage from './OfferDepositPayMessage';
import { mountUtils } from 'test-utils';
import * as config from 'config';

jest.mock('config');

const render = (props = {}) => mountUtils(<OfferDepositPayMessage {...props} />, { decorators: { store: true } });

beforeEach(() => {
  config.DEPOSIT_PAY_ENABLED = true;
});

describe('Deposit Pay option', () => {
  let depositPay;

  describe('when depositPay is available', () => {
    beforeEach(() => {
      depositPay = {
        depositPayable: true,
      };
    });

    describe('when DEPOSIT_PAY_ENABLED = true', () => {
      it('shows the deposit pay message', () => {
        config.DEPOSIT_PAY_ENABLED = true;
        const { find } = render({ depositPay });
        expect(find('Text')).toHaveText('Deposit Pay');
      });
    });

    describe('when DEPOSIT_PAY_ENABLED = false', () => {
      it('does not show the deposit pay message', () => {
        config.DEPOSIT_PAY_ENABLED = false;
        const { find } = render({ depositPay });
        expect(find('Text')).not.toHaveText('Deposit Pay');
      });
    });
  });

  describe('when depositPay is not available', () => {
    it('does not show the deposit pay message', () => {
      const depositPay = {
        depositPayable: false,
      };

      const { find } = render({ depositPay });
      expect(find('Text')).not.toExist();
    });
  });

  describe('when null', () => {
    it('does not show the deposit pay message', () => {
      const depositPay = null;

      const { find } = render({ depositPay });

      expect(find('Text')).not.toExist();
    });
  });
});
