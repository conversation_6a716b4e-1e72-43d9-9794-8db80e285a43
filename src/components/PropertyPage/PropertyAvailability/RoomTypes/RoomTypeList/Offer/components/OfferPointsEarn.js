import React, { Fragment } from 'react';
import PropTypes from 'prop-types';
import { Box, Text, Hide } from '@qga/roo-ui/components';
import { Flag } from 'components/PropertyPage/PropertyAvailability/RoomTypes/primitives';
import BusinessRewardsModal from './BusinessRewardsModal';
import BusinessRewardsTooltip from './BusinessRewardsTooltip';
import { POINTS_EARN_ENABLED, POINTS_PER_DOLLAR_DEFAULT } from 'config';
import PointsPerDollar from 'components/PointsPerDollar';

const OfferPointsEarn = React.memo(({ pointsEarned, isClassic, isCurrencyCash, isPointsPlusPay }) => {
  if (!POINTS_EARN_ENABLED) return null;

  const { qbrPoints } = pointsEarned ?? {};
  const pointsPerDollar = pointsEarned?.maxQffEarnPpd ? pointsEarned.maxQffEarnPpd : POINTS_PER_DOLLAR_DEFAULT;

  return (
    <Box color="greys.steel">
      <Flag icon="roo">
        {!isClassic && (
          <Fragment>
            {(isCurrencyCash || isPointsPlusPay) && (
              <PointsPerDollar pointsPerDollar={pointsPerDollar} display="block" color="greys.steel" />
            )}

            <Hide md lg>
              <BusinessRewardsModal qbrPoints={qbrPoints} />
            </Hide>
            <Hide sm xs>
              <BusinessRewardsTooltip qbrPoints={qbrPoints} />
            </Hide>
          </Fragment>
        )}
        {isClassic && <Text display="block">You will not earn Qantas Points on Classic Rewards offers</Text>}
      </Flag>
    </Box>
  );
});

OfferPointsEarn.propTypes = {
  pointsEarned: PropTypes.object,
  isClassic: PropTypes.bool,
  inclusions: PropTypes.array,
  isCurrencyCash: PropTypes.bool.isRequired,
  isPointsPlusPay: PropTypes.bool.isRequired,
};

OfferPointsEarn.defaultProps = {
  inclusions: [],
  isClassic: false,
};

OfferPointsEarn.displayName = 'OfferPointsEarn';

export default OfferPointsEarn;
