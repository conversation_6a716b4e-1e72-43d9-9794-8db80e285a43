import React from 'react';
import OfferDetails from './OfferDetails';
import { mountUtils } from 'test-utils';

mountUtils.mockComponent('OfferDepositPayMessage');
mountUtils.mockComponent('CancellationRefundModal');
mountUtils.mockComponent('CancellationTooltip');
mountUtils.mockComponent('OfferDetailsModal');
mountUtils.mockComponent('OfferDetailsTooltip');

const defaultProps = {
  counter: 1,
  offerName: 'A great offer',
  cancellationPolicy: {
    isNonrefundable: true,
    description: 'sorry no refunds',
    cancellationWindows: [
      {
        currency: 'AUD',
        endTime: '2020-05-09T14:00:00+10:00',
        formattedBeforeDate: 'Thu 9 Apr, 2020',
        nights: '1',
        startTime: '2020-04-09T14:00:00+10:00',
      },
    ],
  },
  offerDescription: 'Standard Twin Room',
  depositPay: {
    depositPayable: true,
  },
};

const decorators = { theme: true, store: true };
const render = (props = {}) => mountUtils(<OfferDetails {...defaultProps} {...props} />, { decorators });

describe('The Offer details', () => {
  it('displays the offer name', () => {
    const { findByText } = render();
    expect(findByText(defaultProps.offerName)).toExist();
  });
});

describe('The Cancellation policy details', () => {
  it('displays the CancellationRefundModal component', () => {
    const { find } = render();
    expect(find('CancellationRefundModal').first()).toHaveProp({ cancellationPolicy: defaultProps.cancellationPolicy });
  });
});

describe('The Show Details link', () => {
  it('displays the OfferDetailsModal component', () => {
    const { find } = render();
    expect(find('OfferDetailsModal').first()).toHaveProp({
      offerDescription: defaultProps.offerDescription,
      offerName: defaultProps.offerName,
    });
  });
});

describe('deposit pay messaging', () => {
  it('shows <OfferDepositPayMessage />', () => {
    const { find } = render();
    expect(find('OfferDepositPayMessage')).toHaveProp({ depositPay: defaultProps.depositPay });
  });
});
