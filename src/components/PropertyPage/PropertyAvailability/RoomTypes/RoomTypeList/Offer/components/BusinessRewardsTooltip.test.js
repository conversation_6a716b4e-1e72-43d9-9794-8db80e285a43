import { HOTELS_BRAND_NAME } from 'config';
import React from 'react';
import { mountUtils } from 'test-utils';
import BusinessRewardsTooltip from './BusinessRewardsTooltip';

jest.mock('react-popper', () => ({
  usePopper: () => ({
    styles: {},
    attributes: {},
  }),
}));

jest.mock('lodash/debounce', () => (fn) => {
  const debounced = fn;
  debounced.cancel = jest.fn();
  return debounced;
});

const defaultProps = {
  qbrPoints: { total: 123 },
};

const decorators = { theme: true };
const render = (props) => mountUtils(<BusinessRewardsTooltip {...defaultProps} {...props} />, { decorators });

describe('<BusinessRewardsTooltip />', () => {
  describe('when tooltip is closed', () => {
    it('renders the QBR CTA', () => {
      const { find } = render();
      expect(find('NakedButton')).toHaveText('Plus 123 PTS for your business**');
    });
  });

  describe('when tooltip is open', () => {
    it('renders the QBR description', () => {
      const { find } = render();
      find('NakedButton').simulate('mouseEnter');
      expect(find('TooltipWrapper')).toHaveText(
        `Travelling for business? Add your Qantas Business Rewards membership ABN number to any domestic or international ${HOTELS_BRAND_NAME} booking and your business will earn 1 Qantas Point per A$1 spent.**`,
      );
      find('NakedButton').simulate('mouseLeave');
      expect(find('TooltipWrapper')).not.toExist();
    });
  });

  describe('when user is using points or offer is classic', () => {
    it('renders the appropriate message', () => {
      const { find } = render({
        qbrPoints: { total: 0 },
      });
      expect(find('Text')).toHaveText('You will not earn Qantas Points when using points');
    });
  });
});
