import React, { Fragment, memo } from 'react';
import PropTypes from 'prop-types';
import get from 'lodash/get';
import { useSelector } from 'react-redux';
import { Flex, Box, Text, Icon } from '@qga/roo-ui/components';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import Currency from 'components/Currency';
import { differenceInCalendarDays } from 'lib/date';
import PriceBeforeDiscount from 'components/PriceBeforeDiscount';
import OfferPointsPaySlider from './OfferPointsPaySlider';
import OfferCheckoutLink from './OfferCheckoutLink';
import OfferPayableAtProperty from './OfferPayableAtProperty';
import usePointsConverters from 'hooks/points/usePointsConverters';
import { MIN_POINTS_AMOUNT } from 'config';
import { getQueryCheckIn, getQueryCheckOut, getQueryAdults, getQueryChildren, getQueryInfants } from 'store/router/routerSelectors';
import { getPropertyId, getCountry } from 'store/property/propertySelectors';
import { getIsPointsPay } from 'store/ui/uiSelectors';
import CampaignPriceMessage from 'components/CampaignPriceMessage';
import OfferGuestsText from 'components/OfferGuestsText';

const canPayWithPoints = ({ payableAtBooking, convertCashToPoints }) => {
  const payableAtBookingInPoints =
    payableAtBooking.total.currency === 'PTS'
      ? new Decimal(payableAtBooking.total.amount)
      : convertCashToPoints({ cash: payableAtBooking.total.amount });

  return payableAtBookingInPoints.greaterThanOrEqualTo(MIN_POINTS_AMOUNT);
};

const OfferPriceBox = memo(({ charges, offerType, offerName, offerId, roomTypeId, roomName, onCashPaymentAmountChange }) => {
  const propertyId = useSelector(getPropertyId);
  const isPointsPay = useSelector(getIsPointsPay);
  const country = useSelector(getCountry);

  const checkIn = useSelector(getQueryCheckIn);
  const checkOut = useSelector(getQueryCheckOut);
  const totalNumberOfNights = differenceInCalendarDays(checkOut, checkIn);

  const adults = useSelector(getQueryAdults) || 0;
  const children = useSelector(getQueryChildren) || 0;
  const infants = useSelector(getQueryInfants) || 0;
  const totalGuests = adults + children + infants;

  const { convertCashToPoints } = usePointsConverters();
  const { totalBeforeDiscount, total, payableAtProperty, payableAtBooking, totalCash } = charges;
  const currency = get(charges, 'total.currency');
  const isCurrencyCash = currency !== 'PTS';
  const isClassic = offerType === 'classic';
  const hasDiscount = totalBeforeDiscount && totalBeforeDiscount.amount !== total.amount;
  const isShowingPointsPaySlider = isPointsPay && !isClassic && canPayWithPoints({ isClassic, payableAtBooking, convertCashToPoints });
  const isPayableAtPropertyInclusive = payableAtBooking.total.currency !== 'PTS';

  return (
    <Box width="100%" data-testid="offer-price-box">
      {isShowingPointsPaySlider && (
        <Fragment>
          <OfferGuestsText
            guests={totalGuests}
            numberOfNights={totalNumberOfNights}
            from={false}
            fontSize="sm"
            mb={2}
            fontWeight="bold"
            data-testid="offer-guests"
          />
          <OfferPointsPaySlider
            offerName={offerName}
            offerType={offerType}
            totalCash={totalCash || total} //totalCash is a temporary attribute passed by Ava in points mode due to the total in points not being the full total (payableAtBooking + payableAtProperty)
            totalPointsAmount={total.amount}
            payableAtProperty={payableAtProperty.total}
            offerId={offerId}
            roomTypeId={roomTypeId}
            propertyId={propertyId}
            onCashPaymentAmountChange={onCashPaymentAmountChange}
            roomName={roomName}
            campaignPriceMessage={<CampaignPriceMessage currency={total.currency} offerType={offerType} country={country} />}
          />
        </Fragment>
      )}
      {!isShowingPointsPaySlider && (
        <Fragment>
          <OfferGuestsText guests={totalGuests} numberOfNights={totalNumberOfNights} />
          <Flex flexDirection="row" justifyContent="flex-start" alignItems="baseline" flexWrap="wrap">
            <Flex alignItems="center" justifyContent={['flex-start', 'flex-end']}>
              {!isCurrencyCash && <Icon name="roo" mr="2" color="brand.primary" />}
              <Currency
                amount={total.amount}
                currency={total.currency}
                roundToCeiling
                fontSize={32}
                hideCurrency={isCurrencyCash}
                fontWeight="bold"
                color="greys.charcoal"
                data-testid="total-to-pay"
                alignCurrency="superscript"
              />
              {!isCurrencyCash && (
                <Text pt={2} data-testid="asterisk">
                  *
                </Text>
              )}
            </Flex>
            <Box color="greys.charcoal" pl={2} top={1}>
              {!isShowingPointsPaySlider && isCurrencyCash && <Text fontWeight="bold"> {currency}</Text>}
            </Box>
          </Flex>
          {hasDiscount && (
            <Flex pr={2} data-testid="price-before-discount-box">
              <PriceBeforeDiscount
                size="base"
                roundToCeiling
                offerType={offerType}
                total={totalBeforeDiscount}
                hideCurrency={isCurrencyCash}
                lineHeight="0.6"
              />
            </Flex>
          )}
          <CampaignPriceMessage currency={total.currency} offerType={offerType} country={country} />
          <OfferPayableAtProperty payableAtProperty={payableAtProperty.total} isIncludedInTotal={isPayableAtPropertyInclusive} />
          <OfferCheckoutLink
            propertyId={propertyId}
            roomTypeId={roomTypeId}
            offerId={offerId}
            offerName={offerName}
            roomName={roomName}
            mb={4}
          />
        </Fragment>
      )}
    </Box>
  );
});

OfferPriceBox.propTypes = {
  charges: PropTypes.object.isRequired,
  offerType: PropTypes.string.isRequired,
  roomTypeId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  offerId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  offerName: PropTypes.string.isRequired,
  roomName: PropTypes.string.isRequired,
  onCashPaymentAmountChange: PropTypes.func.isRequired,
};

OfferPriceBox.displayName = 'OfferPriceBox';

export default OfferPriceBox;
