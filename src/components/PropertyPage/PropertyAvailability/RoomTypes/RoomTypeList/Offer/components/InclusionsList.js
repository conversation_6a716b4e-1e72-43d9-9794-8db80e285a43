import React, { memo } from 'react';
import PropTypes from 'prop-types';
import { List, ListItem } from './primitives';

const InclusionsList = memo(({ inclusions }) => (
  <List>
    {inclusions.map((inclusion, index) => {
      const inclusionDescription = inclusion.description;

      return <ListItem key={index}>{inclusionDescription}</ListItem>;
    })}
  </List>
));

InclusionsList.displayName = 'InclusionsList';

InclusionsList.propTypes = {
  inclusions: PropTypes.arrayOf(PropTypes.object),
};

InclusionsList.defaultProps = {
  inclusions: [],
};

export default InclusionsList;
