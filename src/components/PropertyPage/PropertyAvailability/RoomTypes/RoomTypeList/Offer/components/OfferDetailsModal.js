import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { Box, Heading, Text } from '@qga/roo-ui/components';
import { useModal } from 'lib/hooks';
import { useDataLayer } from 'hooks/useDataLayer';
import Modal from 'components/Modal';
import TextButton from 'components/TextButton';

const OfferDetailsModal = ({ offerDescription = null, offerName }) => {
  const { openModal, modalProps } = useModal();
  const { emitInteractionEvent } = useDataLayer();

  const handleOnClick = useCallback(() => {
    openModal();
    emitInteractionEvent({ type: 'Offer Details Pop Up', value: 'Link Selected' });
  }, [emitInteractionEvent, openModal]);

  return (
    <Box>
      <TextButton onClick={handleOnClick} color="greys.steel" fontSize="sm" aria-label={`Show details of ${offerName}`}>
        Offer details
      </TextButton>
      <Modal {...modalProps} title="Offer description">
        <Box mb="5" data-testid="offer-description">
          <Heading.h4 fontSize="sm">{offerName}</Heading.h4>
          {offerDescription && <Text>{offerDescription}</Text>}
        </Box>
      </Modal>
    </Box>
  );
};

OfferDetailsModal.propTypes = {
  offerDescription: PropTypes.string,
  offerName: PropTypes.string.isRequired,
};

export default OfferDetailsModal;
