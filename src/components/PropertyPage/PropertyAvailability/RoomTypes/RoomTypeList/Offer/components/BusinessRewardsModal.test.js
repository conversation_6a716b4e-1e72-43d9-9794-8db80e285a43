import React from 'react';
import BusinessRewardsModal from './BusinessRewardsModal';
import { mountUtils } from 'test-utils';
import { useDataLayer } from 'hooks/useDataLayer';
import { HOTELS_BRAND_NAME } from 'config';

jest.mock('hooks/useDataLayer');
jest.mock('store/ui/uiSelectors');

const defaultProps = {
  qbrPoints: { total: 123 },
};

const decorators = { theme: true, store: true };
const render = (props = {}) => mountUtils(<BusinessRewardsModal {...defaultProps} {...props} />, { decorators });
const emitInteractionEvent = jest.fn();

beforeEach(() => {
  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

it('renders the CTA', () => {
  const { findByTestId } = render();
  expect(findByTestId('qbr-total-points-button')).toHaveText('Plus 123 PTS for your business**');
});

describe('Clicking the link to trigger a modal', () => {
  it('Displays the offer description in a modal', () => {
    const { find, findByTestId } = render();
    find('button').simulate('click');
    expect(findByTestId('qbr-total-points-details')).toHaveText(
      `Travelling for business? Add your Qantas Business Rewards membership ABN number to any domestic or international ${HOTELS_BRAND_NAME} booking and your business will earn 1 Qantas Point per A$1 spent.**`,
    );
  });

  it('dispatches an event to the data layer', () => {
    const { find } = render();
    find('button').simulate('click');
    expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Business Rewards Pop Up', value: 'Link Selected' });
  });
});

describe('when user is using points or offer is classic', () => {
  it('renders the appropriate message', () => {
    const { find } = render({
      qbrPoints: { total: 0 },
    });
    expect(find('Text')).toHaveText('You will not earn Qantas Points when using points');
  });
});
