import React from 'react';
import { mountUtils } from 'test-utils';
import OfferSummary from './OfferSummary';
import { getQueryCheckIn, getQueryCheckOut } from 'store/router/routerSelectors';
import { getCountry } from 'store/property/propertySelectors';
import { useDataLayer } from 'hooks/useDataLayer';
import { useFeature } from '@optimizely/react-sdk';

jest.mock('hooks/useDataLayer');
jest.mock('store/router/routerSelectors');
jest.mock('store/property/propertySelectors');
jest.mock('@optimizely/react-sdk', () => ({
  useFeature: jest.fn(),
}));

const offer = {
  type: 'standard',
  description: 'Junior Suite',
  cancellationPolicy: {
    isNonrefundable: true,
    description: 'Non-refundable unless you are entitled to a refund or other remedy under the Australian Consumer Law.',
  },
  name: 'Breakfast Included - Non-refundable',
  charges: {
    total: {
      amount: '297.99',
      currency: 'AUD',
    },
    totalBeforeDiscount: {
      amount: '297.99',
      currency: 'AUD',
    },
    totalDiscount: {
      amount: '0.00',
      currency: 'AUD',
    },
    payableAtProperty: {
      total: {
        amount: 0,
        currency: 'AUD',
      },
    },
  },
  inclusions: [
    {
      code: 'breakfast',
      icon: 'incJqBreakfast',
      name: 'Breakfast Included',
      description: 'Breakfast Buffet',
    },
  ],
  valueAdds: ['Breakfast for 2'],
  id: '202017398_230005304_493003',
  promotion: null,
  depositPay: {
    depositPayable: true,
  },
};

const checkIn = new Date(2019, 3, 30);
const checkOut = new Date(2019, 4, 1);
const country = 'Australia';
const toggleOfferExpanded = jest.fn();
const emitInteractionEvent = jest.fn();

const defaultProps = {
  offer,
  toggleOfferExpanded,
  isLastOffer: false,
};

const render = (props) => mountUtils(<OfferSummary {...defaultProps} {...props} />, { decorators: { store: true } });

beforeEach(() => {
  jest.resetAllMocks();
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  getQueryCheckIn.mockReturnValue(checkIn);
  getQueryCheckOut.mockReturnValue(checkOut);
  getCountry.mockReturnValue(country);
  useFeature.mockReturnValue([false]);
});

it('renders the offer name', () => {
  const { find } = render();
  expect(find('Heading')).toHaveText(offer.name);
});

describe('when useGlobalInclusions is true', () => {
  beforeEach(() => {
    useFeature.mockReturnValue([true]);
  });

  it('renders the inclusions', () => {
    const { find } = render();
    expect(find('Flag')).toHaveLength(1);
    expect(find('Flag').at(0)).toHaveProp({ icon: 'incJqBreakfast' });
  });
});

describe('when useGlobalInclusions is false', () => {
  beforeEach(() => {
    useFeature.mockReturnValue([false]);
  });

  it('renders the inclusions', () => {
    const { find } = render();
    expect(find('Flag')).toHaveLength(1);
    expect(find('Flag').at(0)).toHaveProp({ icon: 'freeBreakfast' });
  });
});

it('renders the total cost', () => {
  const { find } = render();
  expect(find('Currency')).toHaveProp({
    amount: offer.charges.total.amount,
    currency: offer.charges.total.currency,
  });
});

describe('when in cash mode', () => {
  it('does not render the * after the currency', () => {
    const { findByTestId } = render();
    expect(findByTestId('asterisk')).not.toExist();
  });
});

describe('when in points mode', () => {
  it('renders the * after the currency', () => {
    const total = { amount: '2000', currency: 'PTS' };
    const { findByTestId } = render({
      offer: {
        ...offer,
        charges: {
          ...offer.charges,
          total,
        },
      },
    });

    expect(findByTestId('asterisk')).toExist();
  });
});

it('renders the campaign message', () => {
  const { find } = render();
  expect(find('CampaignPriceMessage')).toHaveProp({
    country,
    offerType: offer.type,
    currency: offer.charges.total.currency,
  });
});

it('fires the toggleOfferExpanded prop when the CTA is actioned', () => {
  const { findByTestId } = render();
  findByTestId('expand-offer-summary').simulate('click');
  expect(toggleOfferExpanded).toHaveBeenCalledWith(true);
});

describe('discounts', () => {
  it('does not show a discount when there is no discount', () => {
    const { find } = render();
    expect(find('PriceBeforeDiscount')).not.toExist();
  });

  it('shows a discount when there is a discount', () => {
    const totalBeforeDiscount = { amount: '97.99', currency: 'AUD' };
    const { find } = render({
      offer: {
        ...offer,
        charges: {
          ...offer.charges,
          totalBeforeDiscount,
        },
      },
    });
    expect(find('PriceBeforeDiscount')).toHaveProp({
      total: totalBeforeDiscount,
      offerType: 'standard',
    });
  });
});

describe('sashing', () => {
  it('does not show a sash when there is no promotion', () => {
    const { find } = render();
    expect(find('BadgeFrame')).not.toExist();
  });

  it('shows a sash when there is a promotion', () => {
    const promotion = { name: 'a great deal' };
    const { find } = render({ offer: { ...offer, promotion } });
    expect(find('BadgeFrame').text()).toEqual(promotion.name);
  });
});

it('sends an event to the dataLayer', () => {
  const { find } = render();
  find('FullWidthButton').simulate('click');

  expect(emitInteractionEvent).toHaveBeenCalledWith({
    type: 'Room Offer Details',
    value: 'Offer Expanded',
  });
});
