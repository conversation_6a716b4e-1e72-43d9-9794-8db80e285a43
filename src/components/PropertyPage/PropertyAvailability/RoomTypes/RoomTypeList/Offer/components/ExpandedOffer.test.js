import React from 'react';
import { mountUtils } from 'test-utils';
import ExpandedOffer from './ExpandedOffer';
import usePointsEarned from 'lib/hooks/usePointsEarned/usePointsEarned';
import { useDataLayer } from 'hooks/useDataLayer';

jest.mock('hooks/useDataLayer');
jest.mock('store/ui/uiSelectors');
jest.mock('lib/hooks/usePointsEarned/usePointsEarned');

mountUtils.mockComponent('OfferDetails');
mountUtils.mockComponent('OfferPointsEarn');
mountUtils.mockComponent('OfferInclusions');
mountUtils.mockComponent('OfferValueAdds');
mountUtils.mockComponent('OfferPriceBox');

const pointsEarned = {
  qffPoints: {
    base: 891,
    total: 891,
  },
  qbrPoints: {
    total: 298,
  },
};

const offer = {
  type: 'standard',
  description: 'Junior Suite',
  cancellationPolicy: {
    isNonrefundable: true,
    description: 'Non-refundable unless you are entitled to a refund or other remedy under the Australian Consumer Law.',
  },
  name: 'Breakfast Included - Non-refundable',
  charges: {
    total: {
      amount: '297.99',
      currency: 'AUD',
    },
    totalBeforeDiscount: {
      amount: '297.99',
      currency: 'AUD',
    },
    totalDiscount: {
      amount: '0.00',
      currency: 'AUD',
    },
    payableAtProperty: {
      total: {
        amount: 0,
        currency: 'AUD',
      },
    },
  },
  inclusions: [
    {
      code: 'breakfast',
      name: 'Breakfast Included',
      description: 'Breakfast Buffet',
    },
  ],
  valueAdds: ['Breakfast for 2'],
  id: '202017398_230005304_493003',
  pointsEarned,
  promotion: null,
  depositPay: {
    depositPayable: true,
  },
};

const toggleOfferExpanded = jest.fn();
const emitInteractionEvent = jest.fn();
const onCashPaymentAmountChange = jest.mock();
const defaultProps = {
  offer,
  propertyId: '1',
  roomTypeId: '2',
  roomName: 'Standard King',
  toggleOfferExpanded,
  isLastOffer: false,
};

const render = (props) => mountUtils(<ExpandedOffer {...defaultProps} {...props} />, { decorators: { store: true } });

beforeEach(() => {
  jest.resetAllMocks();
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  usePointsEarned.mockReturnValue({
    pointsEarned,
    onCashPaymentAmountChange,
  });
});

it('passes the correct props to <OfferDetails>', () => {
  const { find } = render();
  expect(find('OfferDetails')).toHaveProp({
    cancellationPolicy: offer.cancellationPolicy,
    offerDescription: offer.description,
    offerName: offer.name,
    depositPay: offer.depositPay,
  });
});

it('passes the inclusions to <OfferInclusions>', () => {
  const { find } = render();
  expect(find('OfferInclusions')).toHaveProp({
    inclusions: offer.inclusions,
  });
});

it('passes the valueAdds to <OfferValueAdds>', () => {
  const { find } = render();
  expect(find('OfferValueAdds')).toHaveProp({
    valueAdds: offer.valueAdds,
  });
});

it('passes the correct details to <OfferPriceBox>', () => {
  const { find } = render();
  expect(find('OfferPriceBox')).toHaveProp({
    charges: offer.charges,
    offerType: offer.type,
    roomTypeId: defaultProps.roomTypeId,
    offerId: offer.id,
    onCashPaymentAmountChange,
  });
});

it('fires the toggleOfferExpanded prop when the CTA is actioned', () => {
  const { findByTestId } = render();
  findByTestId('collapse-offer-summary').simulate('click');
  expect(toggleOfferExpanded).toHaveBeenCalledWith(false);
});

describe('sashing', () => {
  it('does not show a sash when there is no promotion', () => {
    const { find } = render();
    expect(find('BadgeFrame')).not.toExist();
  });

  it('shows a sash when there is a promotion', () => {
    const promotion = { name: 'a great deal' };
    const { find } = render({ offer: { ...offer, promotion } });
    expect(find('BadgeFrame').text()).toEqual(promotion.name);
  });
});

describe('OfferPointsEarn', () => {
  describe('when the offer is not classic', () => {
    it('passes the points details to <OfferPointsEarn>', () => {
      const { find } = render({ offer: { ...offer, type: 'standard' } });
      expect(find('OfferPointsEarn')).toHaveProp({
        pointsEarned: offer.pointsEarned,
        isClassic: false,
      });
    });
  });

  describe('when the offer is classic', () => {
    it('passes the points details to <OfferPointsEarn>', () => {
      const { find } = render({ offer: { ...offer, type: 'classic' } });
      expect(find('OfferPointsEarn')).toHaveProp({
        pointsEarned: offer.pointsEarned,
        isClassic: true,
      });
    });
  });
});

it('sends an event to the dataLayer', () => {
  const { find } = render();
  find('FullWidthButton').simulate('click');

  expect(emitInteractionEvent).toHaveBeenCalledWith({
    type: 'Room Offer Details',
    value: 'Offer Collapsed',
  });
});
