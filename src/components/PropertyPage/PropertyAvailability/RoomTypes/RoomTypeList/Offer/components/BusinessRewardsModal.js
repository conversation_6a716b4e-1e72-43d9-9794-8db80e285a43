import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { Box, Text } from '@qga/roo-ui/components';
import { useModal } from 'lib/hooks';
import { useDataLayer } from 'hooks/useDataLayer';
import Modal from 'components/Modal';
import TextButton from 'components/TextButton';
import PointsEarnDisplay from 'components/PointsEarnDisplay';
import { HOTELS_BRAND_NAME } from 'config';

const BusinessRewardsModal = ({ qbrPoints }) => {
  const { openModal, modalProps } = useModal();
  const { emitInteractionEvent } = useDataLayer();

  const handleOnClick = useCallback(() => {
    openModal();
    emitInteractionEvent({ type: 'Business Rewards Pop Up', value: 'Link Selected' });
  }, [emitInteractionEvent, openModal]);

  const { total } = qbrPoints;

  return (
    <Box>
      {total === 0 && (
        <Text display="block" fontSize="base" data-testid="property-no-points-earned">
          You will not earn Qantas Points when using points
        </Text>
      )}
      {total > 0 && (
        <Text display="block" fontSize="base" data-testid="qbr-total-points-button">
          Plus <PointsEarnDisplay {...qbrPoints} /> PTS{' '}
          <TextButton
            onClick={handleOnClick}
            color="greys.steel"
            hoverColor="greys.charcoal"
            fontSize="14px"
            aria-label="Learn more about Qantas Business Rewards"
          >
            for your business**
          </TextButton>
        </Text>
      )}
      <Modal {...modalProps} title="Qantas Business Rewards">
        <Text data-testid="qbr-total-points-details">
          Travelling for business? Add your Qantas Business Rewards membership ABN number to any domestic or international{' '}
          {HOTELS_BRAND_NAME} booking and your business will earn 1 Qantas Point per A$1 spent.**
        </Text>
      </Modal>
    </Box>
  );
};

BusinessRewardsModal.propTypes = {
  qbrPoints: PropTypes.shape({ total: PropTypes.number }).isRequired,
};

export default BusinessRewardsModal;
