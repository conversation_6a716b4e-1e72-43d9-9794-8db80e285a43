import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import { Button, Box, Flex, Icon, NakedButton, Text, Hide } from '@qga/roo-ui/components';
import { getIsExclusive, getPropertyId, getIsLoading } from 'store/property/propertySelectors';
import Markup from 'components/Markup';
import InclusionsList from './components/InclusionsList';
import ExclusiveOffer from './components/ExclusiveOffer';
import {
  ParagraphWrapper,
  CollapsedBox,
  RoomContainer,
  OfferWrapper,
  RetrySearchLink,
  FullWidthButton,
  CollapseWrapper,
  ExclusiveHeading,
  ExtraFlex,
} from './primitives';
import OfferCheckoutLink from '../Offer/components/OfferCheckoutLink';
import { getAllAvailableOffers, getExclusiveOfferAvailabilityQuery } from 'store/exclusiveOffer/exclusiveOfferSelectors';
import isEmpty from 'lodash/isEmpty';
import { differenceInCalendarDays } from 'date-fns';
import pluralize from 'pluralize';
import { getIsPointsPay } from 'store/ui/uiSelectors';
import OfferPointsPaySlider from '../Offer/components/OfferPointsPaySlider';
import CancellationRefundModal from 'components/CancellationRefundModal';
import CancellationTooltip from 'components/CancellationTooltip';
import PromotionalSash from 'components/PromotionalSash';

const AVAILABILITY_STATES = {
  POINTS_PLUS_PAY: 'points_plus_pay',
  CHECK_AVAILABILITY: 'check_availability',
  NO_AVAILABILITY: 'no_availability',
  BOOK_NOW: 'book_now',
};

const CheckAvailability = ({ room }) => {
  const propertyId = useSelector(getPropertyId);
  const isExclusive = useSelector(getIsExclusive);
  const query = useSelector(getExclusiveOfferAvailabilityQuery);
  const [expanded, setExpanded] = useState(isExclusive);
  const RoomDescription = expanded ? ParagraphWrapper : CollapsedBox;
  const { title, description, inclusions, id: roomId, offerId } = room;
  const isShowingPointsPaySlider = useSelector(getIsPointsPay);
  const [availabilityState, setAvailabilityState] = useState(AVAILABILITY_STATES.CHECK_AVAILABILITY);
  const isLoading = useSelector(getIsLoading);

  const { [offerId]: offerAvailability } = useSelector(getAllAvailableOffers);
  const promotionName = offerAvailability?.promotion?.name;

  const hasAvailability = !isEmpty(offerAvailability);
  const hasSearchDates = query?.checkIn && query?.checkOut;
  const cancellationPolicy = offerAvailability?.cancellationPolicy;

  useEffect(() => {
    if (hasAvailability && hasSearchDates && isShowingPointsPaySlider) {
      setAvailabilityState(AVAILABILITY_STATES.POINTS_PLUS_PAY);
    } else if (hasAvailability && hasSearchDates) {
      setAvailabilityState(AVAILABILITY_STATES.BOOK_NOW);
    } else if (!hasAvailability && hasSearchDates) {
      setAvailabilityState(AVAILABILITY_STATES.NO_AVAILABILITY);
    } else {
      setAvailabilityState(AVAILABILITY_STATES.CHECK_AVAILABILITY);
    }
  }, [hasAvailability, hasSearchDates, isShowingPointsPaySlider, setAvailabilityState]);

  let offerPayload = {
    adults: room?.adults || 0,
    kids: room?.children || 0,
    infants: room?.infants || 0,
    numNights: room?.minNumberOfNights,
    totalPrice: room?.offerTotal,
    totalDiscount: room?.valuedAtTotal,
  };

  if (availabilityState === AVAILABILITY_STATES.BOOK_NOW || availabilityState === AVAILABILITY_STATES.POINTS_PLUS_PAY) {
    offerPayload = {
      adults: query?.adults,
      kids: query?.children,
      infants: query?.infants,
      numNights: differenceInCalendarDays(new Date(query?.checkOut), new Date(query?.checkIn)),
      totalPrice: offerAvailability?.charges?.total,
      totalDiscount: offerAvailability?.charges?.totalBeforeDiscount,
    };
  }

  return (
    <RoomContainer width="100%" mb={[0, 6]}>
      <OfferWrapper>
        <ExtraFlex width={['100%', '50%']} mb={[4, 0]} data-testid="extra-flex" expanded={expanded}>
          {isExclusive ? (
            <RoomDescription isExclusive={isExclusive}>
              <Flex flex="1 1 0px" justifyContent="space-between" borderBottom={1} borderColor="greys.alto" mb={[4, 6]} flexWrap="wrap">
                <ExclusiveHeading>{title}</ExclusiveHeading>
                <Hide sm md lg>
                  <NakedButton
                    onClick={() => setExpanded((prev) => !prev)}
                    aria-label={`${expanded ? 'Collapse' : 'Expand'} Offer Details`}
                  >
                    <Icon name={expanded ? 'expandLess' : 'expandMore'} size={24} />
                  </NakedButton>
                </Hide>
                {cancellationPolicy && (
                  <>
                    {expanded && (
                      <Hide md lg>
                        <CancellationRefundModal cancellationPolicy={cancellationPolicy} fontSize={['sm', 'base']} mb={2} />
                      </Hide>
                    )}
                    <Hide sm xs>
                      <CancellationTooltip cancellationPolicy={cancellationPolicy} />
                    </Hide>
                  </>
                )}
              </Flex>

              {inclusions && <InclusionsList inclusions={inclusions} />}

              {description && (
                <Text data-testid="check-availability-room-description">
                  <strong>Offers: </strong>
                  {description}
                </Text>
              )}
            </RoomDescription>
          ) : (
            description && (
              <>
                <Flex justifyContent="space-between" alignItems="center">
                  <Text as="p" fontWeight="bold" mt={1}>
                    Room information
                  </Text>
                  <Hide as={Flex} sm md lg flex={0} alignSelf="baseline" justifySelf="flex-end">
                    <NakedButton
                      onClick={() => setExpanded((prev) => !prev)}
                      aria-label={`${expanded ? 'Collapse' : 'Expand'} Offer Details`}
                    >
                      <Icon name={expanded ? 'expandLess' : 'expandMore'} size={24} />
                    </NakedButton>
                  </Hide>
                </Flex>
                <RoomDescription isExclusive={isExclusive}>
                  <Markup content={room.description} />
                </RoomDescription>
              </>
            )
          )}
        </ExtraFlex>

        <Flex width={['100%', '40%']} mx={[0, 0, 4]} pl={[0, 2, 6]} flexDirection="column" pt={0} mt={promotionName ? 10 : 0}>
          {isExclusive ? (
            <>
              {promotionName && (
                <Box position="absolute" left={['15px', 'initial']} top={20}>
                  <PromotionalSash promotionName={promotionName} />
                </Box>
              )}
              {hasAvailability && availabilityState === AVAILABILITY_STATES.BOOK_NOW && <ExclusiveOffer {...offerPayload} />}
              {!isLoading && hasAvailability && availabilityState === AVAILABILITY_STATES.POINTS_PLUS_PAY && (
                <OfferPointsPaySlider
                  offerName={offerAvailability?.name}
                  totalCash={offerAvailability?.charges?.totalCash || offerAvailability?.charges?.total} //totalCash is a temporary attribute passed by Ava in points mode due to the total in points not being the full total (payableAtBooking + payableAtProperty)
                  payableAtProperty={offerAvailability?.charges?.payableAtProperty?.total}
                  offerId={offerId}
                  roomTypeId={roomId}
                  propertyId={propertyId}
                  roomName={room?.name}
                  onCashPaymentAmountChange={() => {}}
                  label={
                    <Box as={Text} textAlign="left" width="100%">
                      Total for {pluralize('night', offerPayload.numNights, true)}
                    </Box>
                  }
                />
              )}
            </>
          ) : (
            <Text pt={1}>Enter your travel dates to get the price</Text>
          )}

          {hasAvailability && availabilityState === AVAILABILITY_STATES.BOOK_NOW && (
            <OfferCheckoutLink
              propertyId={propertyId}
              roomTypeId={room.id}
              offerId={room.offerId}
              offerName={offerAvailability.name}
              roomName={room?.name}
              mt={4}
            >
              <Text my={1} display="block" fontSize="base" fontWeight="bold">
                Book now
              </Text>
            </OfferCheckoutLink>
          )}
          {availabilityState === AVAILABILITY_STATES.NO_AVAILABILITY && (
            <>
              <Text fontSize="md" color="orange" fontWeight="bold" mb={3}>
                Not available for your current dates
              </Text>
              <RetrySearchLink href="#rooms">Please try different dates</RetrySearchLink>
            </>
          )}
          {availabilityState === AVAILABILITY_STATES.CHECK_AVAILABILITY && (
            <Flex flexDirection="column">
              {isExclusive ? <ExclusiveOffer {...offerPayload} /> : null}
              <Button
                variant="primary"
                as="a"
                mt={isExclusive ? 4 : 6}
                width={['100%', '90%', '100%', '80%']}
                href="#rooms"
                data-testid="check-availability-cta"
              >
                Check Availability
              </Button>
            </Flex>
          )}
        </Flex>

        <CollapseWrapper xs as={Flex} alignItems="flex-start">
          <FullWidthButton
            onClick={() => setExpanded((prev) => !prev)}
            aria-label={`${expanded ? 'Collapse' : 'Expand'} Offer Details`}
            data-testid="clickable-area"
          >
            <Icon name={expanded ? 'expandLess' : 'expandMore'} size={24} mt={5} mb={100} />
          </FullWidthButton>
        </CollapseWrapper>
      </OfferWrapper>
    </RoomContainer>
  );
};

CheckAvailability.propTypes = {
  room: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    offerId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    title: PropTypes.string,
    name: PropTypes.string.isRequired,
    description: PropTypes.string,
    inclusions: PropTypes.array,
    maxOccupantCount: PropTypes.number,
    minNumberOfNights: PropTypes.number,
    promotionName: PropTypes.string,
    offerTotal: PropTypes.shape({
      amount: PropTypes.string,
      currency: PropTypes.string,
    }),
    valuedAtTotal: PropTypes.shape({
      amount: PropTypes.string,
      currency: PropTypes.string,
    }),
    hasValuedAtPrice: PropTypes.bool,
    adults: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    children: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    infants: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  }).isRequired,
};

export default CheckAvailability;
