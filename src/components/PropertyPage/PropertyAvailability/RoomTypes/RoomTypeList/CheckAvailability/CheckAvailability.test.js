import React from 'react';
import { mountUtils } from 'test-utils';
import CheckAvailability from './CheckAvailability';
import { getIsExclusive, getPropertyId, getIsLoading } from 'store/property/propertySelectors';
import { getAllAvailableOffers, getExclusiveOfferAvailabilityQuery } from 'store/exclusiveOffer/exclusiveOfferSelectors';
import { getIsPointsPay } from 'store/ui/uiSelectors';

jest.mock('store/property/propertySelectors');
jest.mock('store/ui/uiSelectors');
jest.mock('store/exclusiveOffer/exclusiveOfferSelectors');

mountUtils.mockComponent('ExclusiveOffer');
mountUtils.mockComponent('InclusionsList');
mountUtils.mockComponent('OfferCheckoutLink');
mountUtils.mockComponent('OfferPointsPaySlider');
mountUtils.mockComponent('CancellationRefundModal');
mountUtils.mockComponent('CancellationTooltip');

const defaultProps = {
  room: {
    description: 'Complimentary upgrade to Deluxe Sea View room from Deluxe room.',
    title: 'Book early and save',
    inclusions: [
      { icon: { name: 'check', provider: 'mdi' }, title: 'Free parking' },
      { icon: { name: 'schedule', provider: 'mdi' }, title: 'Late checkout' },
      { icon: { name: 'place', provider: 'mdi' }, title: 'Free local attractions' },
    ],
    id: 1,
    offerId: 123,
    name: 'Queen Room',
    maxOccupantCount: 4,
    minNumberOfNights: 3,
    offerTotal: { amount: '499', currency: 'AUD' },
    valuedAtTotal: { amount: '1000', currency: 'AUD' },
    hasValuedAtPrice: true,
  },
};

const cancellationPolicy = {
  isNonrefundable: true,
  description: 'sorry no refunds',
  cancellationWindows: [
    {
      currency: 'AUD',
      endTime: '2020-05-09T14:00:00+10:00',
      formattedBeforeDate: 'Thu 9 Apr, 2020',
      nights: '1',
      startTime: '2020-04-09T14:00:00+10:00',
    },
  ],
};

const availability = {
  123: {
    id: 123,
    charges: {
      total: { amount: '499', currency: 'AUD' },
      totalBeforeDiscount: { amount: '1000', currency: 'AUD' },
    },
    cancellationPolicy,
    promotion: { name: 'Best Deal' },
  },
};

const query = {
  adults: 2,
  children: 0,
  infants: 0,
  checkOut: '2022-01-20',
  checkIn: '2022-01-16',
};

const decorators = { theme: true, store: true };
const render = (props = {}) => mountUtils(<CheckAvailability {...defaultProps} {...props} />, { decorators });

describe('CheckAvailability component', () => {
  describe('with a standard offer', () => {
    beforeAll(() => {
      jest.clearAllMocks();
      getIsExclusive.mockReturnValue(false);
      getPropertyId.mockReturnValue('1');
      getAllAvailableOffers.mockReturnValue({});
      getExclusiveOfferAvailabilityQuery.mockReturnValue({});
      getIsPointsPay.mockReturnValue(false);
      getIsLoading.mockReturnValue(false);
    });

    it('renders the room information details', () => {
      const { findByText } = render();
      expect(findByText('Room information')).toExist();
    });

    it('renders Markup with room description', () => {
      const { find } = render();
      expect(find('Markup')).toExist();
      expect(find('Markup')).toHaveProp('content', 'Complimentary upgrade to Deluxe Sea View room from Deluxe room.');
    });

    it('does not render exclusive offer title', () => {
      const { findByText } = render();
      expect(findByText('Book early and save')).not.toExist();
    });

    it('renders standard travel date text', () => {
      const { findByText } = render();
      expect(findByText('Enter your travel dates to get the price')).toExist();
    });

    it('renders the check dates button', () => {
      const { find } = render();
      expect(find('Button')).toExist();
      expect(find('Button')).toHaveProp('href', '#rooms');
    });
  });

  describe('with an exclusive offer', () => {
    beforeEach(() => {
      jest.clearAllMocks();
      getIsExclusive.mockReturnValue(true);
      getPropertyId.mockReturnValue('1');
      getAllAvailableOffers.mockReturnValue(availability);
      getExclusiveOfferAvailabilityQuery.mockReturnValue(query);
      getIsPointsPay.mockReturnValue(false);
    });

    it('renders the exclusive offer title', () => {
      const { findByText } = render();
      expect(findByText('Book early and save')).toExist();
    });

    describe('inclusions', () => {
      it('renders the InclusionsList', () => {
        const { find } = render();
        expect(find('InclusionsList')).toExist();
        expect(find('InclusionsList')).toHaveProp('inclusions', defaultProps.room.inclusions);
      });

      describe('when no inclusions are present', () => {
        it('does NOT render the InclusionsList', () => {
          const { find } = render({ room: { ...defaultProps.room, inclusions: undefined } });
          expect(find('InclusionsList')).not.toExist();
        });
      });
    });

    it('renders the room description', () => {
      const { findByTestId } = render();
      expect(findByTestId('check-availability-room-description')).toHaveText(
        'Offers: Complimentary upgrade to Deluxe Sea View room from Deluxe room.',
      );
    });

    it('renders the Promotional Sash', () => {
      const { find } = render();
      expect(find('PromotionalSash')).toHaveProp('promotionName', 'Best Deal');
    });

    it('renders the ExclusiveOffer', () => {
      const { find } = render();
      expect(find('ExclusiveOffer')).toHaveProp({
        adults: 2,
        kids: 0,
        infants: 0,
        numNights: 4,
        totalPrice: { amount: '499', currency: 'AUD' },
        totalDiscount: { amount: '1000', currency: 'AUD' },
      });
    });

    it('renders the checkout button', () => {
      const { find, findByTestId } = render();
      expect(find('OfferCheckoutLink')).toExist();
      expect(find('RetrySearchLink')).not.toExist();
      expect(findByTestId('check-availability-cta')).not.toExist();
    });

    it('renders the retry search button', () => {
      getAllAvailableOffers.mockReturnValue({});
      const { find, findByTestId } = render();
      expect(find('OfferCheckoutLink')).not.toExist();
      expect(find('RetrySearchLink')).toExist();
      expect(findByTestId('check-availability-cta')).not.toExist();
    });

    it('renders the check availability button', () => {
      getAllAvailableOffers.mockReturnValue({});
      getExclusiveOfferAvailabilityQuery.mockReturnValue({});
      const { find, findByTestId } = render();
      expect(find('OfferCheckoutLink')).not.toExist();
      expect(find('RetrySearchLink')).not.toExist();
      expect(findByTestId('check-availability-cta')).toExist();
    });

    describe('Points plus pay slider', () => {
      it('renders the points plus pay slider if the page is loaded', () => {
        getIsPointsPay.mockReturnValue(true);
        getIsLoading.mockReturnValue(false);
        const { find } = render();
        expect(find('OfferPointsPaySlider')).toExist();
      });

      it('does not render the points plus pay slider if the page is loading', () => {
        getIsPointsPay.mockReturnValue(true);
        getIsLoading.mockReturnValue(true);
        const { find } = render();
        expect(find('OfferPointsPaySlider')).not.toExist();
      });

      it('does not render the points plus pay slider if there is no availability', () => {
        getAllAvailableOffers.mockReturnValue({});
        getIsPointsPay.mockReturnValue(true);
        getIsLoading.mockReturnValue(false);
        const { find } = render();
        expect(find('OfferPointsPaySlider')).not.toExist();
      });
    });

    describe('The Cancellation policy details', () => {
      it('displays the CancellationRefundModal component', () => {
        const { find } = render();
        expect(find('CancellationRefundModal').first()).toHaveProp({ cancellationPolicy });
      });
    });
  });
});
