import styled from '@emotion/styled';
import { rem } from 'polished';
import { themeGet } from 'styled-system';
import { Box, Flex, Link, NakedButton, Hide, Heading } from '@qga/roo-ui/components';
import { mediaQuery } from 'lib/styledSystem';

export const ExclusiveHeading = styled(Heading.h4)`
  width: 80%;
  display: block;
  font-weight: normal;
  padding-bottom: ${themeGet('space.3')};
  font-size: ${themeGet('fontSizes.md')};
  line-height: normal;
`;

export const ParagraphWrapper = styled(Box)`
  p {
    margin-top: 0.25rem;
  }
`;

export const CollapseWrapper = styled(Hide)`
  flex-grow: 1;
  text-align: right;
  justify-self: flex-end;
`;

export const FullWidthButton = styled(NakedButton)`
  width: 100%;
  text-align: center;
`;

export const CollapsedBox = styled(ParagraphWrapper)`
  width: 100%;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  height: ${(props) => (props.isExclusive ? rem('165px') : rem('76px'))};

  ${mediaQuery.minWidth.sm} {
    height: ${(props) => (props.isExclusive ? rem('175px') : rem('76px'))};
  }
`;

export const RoomContainer = styled(Box)`
  flex-grow: 1;
  width: 100%;
  margin-bottom: 0;

  ${mediaQuery.minWidth.sm} {
    margin-bottom: ${themeGet('space.6')};
  }

  ${mediaQuery.minWidth.md} {
    flex-direction: row;
    margin-left: ${themeGet('space.4')};
    flex: 1;
  }
`;

export const OfferWrapper = styled(Flex)`
  position: relative;
  background-color: ${themeGet('colors.white')};
  flex-direction: column;
  padding: ${themeGet('space.4')};

  ${mediaQuery.minWidth.sm} {
    flex-direction: row;
    border-radius: ${themeGet('radii.default')};
    padding: ${themeGet('space.4')} ${themeGet('space.8')};
    border: ${themeGet('borders.1')} ${themeGet('colors.greys.dusty')};
  }
`;

export const RetrySearchLink = styled(Link)`
  text-decoration: underline;
  font-size: ${themeGet('fontSizes.sm')};
  color: ${themeGet('colors.greys.steel')};
`;

export const ExtraFlex = styled(Flex)`
  position: relative;
  flex-direction: column;

  &:after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
    height: 3em;
    height: ${(props) => (props.expanded ? '0' : '3em')};
    background: ${themeGet('colors.gradients.transparentToWhite')};
  }
`;
