import React from 'react';
import PropTypes from 'prop-types';
import camelCase from 'lodash/camelCase';
import { FeatureListItem, Box } from '@qga/roo-ui/components';

const InclusionsList = ({ inclusions }) => {
  return inclusions?.map((inclusion, index) => {
    const isLast = inclusions.length === index + 1;
    const { title, subtitle, icon } = inclusion;

    return (
      <Box pb={isLast ? 5 : 2} key={`inclusion-item-${title}`}>
        <FeatureListItem variant="sm" text={title} subText={subtitle} iconName={camelCase(icon?.name) || 'check'} mb={3} />
      </Box>
    );
  });
};

InclusionsList.propTypes = {
  inclusions: PropTypes.arrayOf(
    PropTypes.shape({
      icon: PropTypes.shape({
        name: PropTypes.string,
        provider: PropTypes.string,
      }),
      title: PropTypes.string,
      subtitle: PropTypes.string,
    }),
  ),
};

export default InclusionsList;
