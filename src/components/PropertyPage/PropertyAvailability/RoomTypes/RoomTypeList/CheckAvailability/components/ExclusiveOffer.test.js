import React from 'react';
import { mountUtils } from 'test-utils';
import ExclusiveOffer from './ExclusiveOffer';

mountUtils.mockComponent('PriceBeforeDiscount');

const defaultProps = {
  adults: 2,
  kids: 2,
  infants: 0,
  numNights: 3,
  totalPrice: { amount: '499', currency: 'AUD' },
  totalDiscount: { amount: '1000', currency: 'AUD' },
};

const render = (props = {}) => mountUtils(<ExclusiveOffer {...defaultProps} {...props} />, { theme: true });

describe('ExclusiveOffer component', () => {
  let renderedComponent;
  beforeAll(() => {
    renderedComponent = render();
  });

  it('renders the max occupants and min nights stay', () => {
    const { find } = renderedComponent;
    expect(find('UppercaseText')).toHaveText('4 guests • 3 nights from');
  });

  it('renders the Valued at price', () => {
    const { findByTestId } = renderedComponent;
    expect(findByTestId('offer-valued-at')).toHaveText('Valued at $1,000');
  });

  it('does NOT render the Valued at price if no discount is present', () => {
    const { findByTestId } = render({ totalDiscount: { currency: 'AUD' } });
    expect(findByTestId('offer-valued-at')).not.toExist();
  });

  it('renders the total amount as Currency', () => {
    const { find } = renderedComponent;
    expect(find('Currency').first()).toHaveProp({ amount: '499', currency: 'AUD' });
  });

  it('correctly renders the single max occupant and min night stay', () => {
    const { find } = render({ adults: 1, kids: 0, infants: 0, numNights: 1 });
    expect(find('UppercaseText')).toHaveText('1 guest • 1 night from');
  });
});
