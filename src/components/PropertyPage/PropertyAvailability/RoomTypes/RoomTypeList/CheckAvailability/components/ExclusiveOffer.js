import React from 'react';
import PropTypes from 'prop-types';
import { Flex, Text } from '@qga/roo-ui/components';
import Currency from 'components/Currency';
import OfferGuestsText from 'components/OfferGuestsText';

const ExclusiveOffer = ({ adults, kids, infants, numNights, totalPrice, totalDiscount }) => {
  const totalGuests = parseInt(adults || 0) + parseInt(kids || 0) + parseInt(infants || 0);
  const hasValuedAtPrice = parseInt(totalDiscount?.amount) > 0 && totalPrice.amount !== totalDiscount.amount;

  return (
    <>
      <OfferGuestsText guests={totalGuests} numberOfNights={numNights} mb={1} />
      <Flex flexDirection="row" justifyContent="flex-start" alignItems="baseline" flexWrap="wrap" mb={1}>
        <Flex alignItems="center" justifyContent={['flex-start', 'flex-end']}>
          <Currency
            {...totalPrice}
            alignItems="center"
            justifyContent="flex-start"
            roundToCeiling
            fontSize={36}
            fontWeight="bold"
            color="greys.charcoal"
            data-testid="total-to-pay"
            alignCurrency="superscript"
          />
        </Flex>
      </Flex>
      {hasValuedAtPrice && (
        <Flex alignItems="center" data-testid="offer-valued-at">
          <Text pr={1} fontSize="sm" color="greys.steel">
            Valued at{' '}
          </Text>
          <Currency {...totalDiscount} roundToCeiling fontSize="sm" hideCurrency color="greys.steel" />
        </Flex>
      )}
    </>
  );
};

ExclusiveOffer.propTypes = {
  adults: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  kids: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  infants: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  numNights: PropTypes.number,
  totalPrice: PropTypes.shape({
    amount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    currency: PropTypes.string,
  }),
  totalDiscount: PropTypes.shape({
    amount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    currency: PropTypes.string,
  }),
};

export default ExclusiveOffer;
