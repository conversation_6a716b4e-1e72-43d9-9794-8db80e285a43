import React from 'react';
import { mountUtils } from 'test-utils';
import InclusionsList from './InclusionsList';

const defaultProps = {
  inclusions: [
    { icon: { name: 'check', provider: 'mdi' }, title: 'Free parking' },
    { icon: { name: 'schedule', provider: 'mdi' }, title: 'Late checkout' },
    { icon: { name: 'place', provider: 'mdi' }, title: 'Free local attractions' },
  ],
};

const render = (props = {}) => mountUtils(<InclusionsList {...defaultProps} {...props} />, { theme: true });

describe('InclusionsList component', () => {
  it('renders all of the inclusions', () => {
    const { find } = render();
    expect(find('FeatureListItem')).toHaveLength(3);
  });

  it('does not render elements when inclusions are empty', () => {
    const { wrapper } = render({ inclusions: [] });
    expect(wrapper).toMatchInlineSnapshot(`
      <InclusionsList
        inclusions={[]}
      />
    `);
  });
});
