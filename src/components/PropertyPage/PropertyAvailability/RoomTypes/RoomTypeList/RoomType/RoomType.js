import React, { memo } from 'react';
import PropTypes from 'prop-types';
import { Box } from '@qga/roo-ui/components';
import Offer from 'components/PropertyPage/PropertyAvailability/RoomTypes/RoomTypeList/Offer';
import CheckAvailability from 'components/PropertyPage/PropertyAvailability/RoomTypes/RoomTypeList/CheckAvailability';
import TermsAndConditionsModal from '../CheckAvailability/components/TermsAndConditionsModal';
import UnavailableRoomType from 'components/PropertyPage/PropertyAvailability/RoomTypes/RoomTypeList/UnavailableRoomType';
import RoomTypeDetailsGallery from './components/RoomTypeDetailsGallery';
import { RoomTypeWrapper, OffersWrapper } from './components/primitives';

const OffersList = ({ roomName, offers, roomTypeId }) => {
  const totalOffers = offers.length;
  return (
    <OffersWrapper>
      {offers.map((offer, counter) => (
        <Offer
          roomTypeId={roomTypeId}
          offer={offer}
          counter={counter + 1}
          key={offer.id}
          roomName={roomName}
          mb={counter < totalOffers - 1 ? [0, 4] : 0}
          totalOffers={totalOffers}
        />
      ))}
    </OffersWrapper>
  );
};
OffersList.propTypes = {
  roomName: PropTypes.string.isRequired,
  offers: PropTypes.array.isRequired,
  roomTypeId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
};

const RoomType = ({ roomType, hasQuery = false }) => {
  const { name, offers, id, termsAndConditions } = roomType;
  const hasOffers = !!(offers && offers.length);
  const isUnavailable = hasQuery && !hasOffers;

  if (isUnavailable) {
    return <UnavailableRoomType roomType={roomType} hasQuery={hasQuery} />;
  }

  return (
    <Box mb={[4, 4, 8]}>
      <RoomTypeWrapper>
        <RoomTypeDetailsGallery roomType={roomType} />

        {hasOffers && <OffersList roomName={name} offers={offers} roomTypeId={id} />}
        {!hasQuery && <CheckAvailability room={roomType} />}
      </RoomTypeWrapper>

      {termsAndConditions && <TermsAndConditionsModal roomName={name} termsAndConditions={termsAndConditions} />}
    </Box>
  );
};

RoomType.propTypes = {
  roomType: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    name: PropTypes.string.isRequired,
    description: PropTypes.string,
    roomTypeFacilities: PropTypes.array.isRequired,
    offers: PropTypes.array,
    maxOccupantCount: PropTypes.number,
    images: PropTypes.array,
    mainImage: PropTypes.shape({
      urlMedium: PropTypes.string,
      caption: PropTypes.string,
    }),
    title: PropTypes.string,
    termsAndConditions: PropTypes.string,
  }).isRequired,
  hasQuery: PropTypes.bool,
};

export default memo(RoomType);
