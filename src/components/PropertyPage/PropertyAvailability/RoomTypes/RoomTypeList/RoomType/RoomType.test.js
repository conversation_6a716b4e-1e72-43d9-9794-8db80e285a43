import React from 'react';
import { mountUtils } from 'test-utils';
import RoomType from './RoomType';
import { useDataLayer } from 'hooks/useDataLayer';

jest.mock('hooks/useDataLayer');

mountUtils.mockComponent('Offer');
mountUtils.mockComponent('FacilitiesList');
mountUtils.mockComponent('Markup');
mountUtils.mockComponent('CheckAvailability');
mountUtils.mockComponent('RoomTypeDetailsGallery');
mountUtils.mockComponent('TermsAndConditionsModal');

const defaultProps = {
  hasQuery: true,
  roomType: {
    id: 333,
    name: 'A Room',
    description: 'room description',
    maxOccupantCount: 7,
    roomTypeFacilities: ['tables', 'chairs', 'a bed'],
    mainImage: {
      urlMedium: 'https://pictures.com/cat',
      caption: 'a cat',
    },
    images: [
      {
        urlLarge: 'https://pictures.com/cat/large',
        urlSmall: 'https://pictures.com/cat',
        caption: 'a cat',
      },
    ],
    offers: [{ id: 1 }, { id: 2 }, { id: 3 }],
  },
};

const propsWithoutOffers = {
  hasQuery: true,
  roomType: {
    id: 333,
    name: 'A Room',
    description: 'room description',
    maxOccupantCount: 7,
    roomTypeFacilities: ['tables', 'chairs', 'a bed'],
    mainImage: {
      urlMedium: 'https://pictures.com/cat',
      caption: 'a cat',
    },
    images: [
      {
        urlLarge: 'https://pictures.com/cat/large',
        urlSmall: 'https://pictures.com/cat',
        caption: 'a cat',
      },
    ],
  },
};

const emitInteractionEvent = jest.fn();
const decorators = { theme: true, store: true };
const render = (props = {}) => mountUtils(<RoomType {...defaultProps} {...props} />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

it('renders the Room Type Gallery', () => {
  const { find } = render();
  expect(find('RoomTypeDetailsGallery')).toHaveProp('roomType', defaultProps.roomType);
});

it('does not render the select dates message', () => {
  const { findByText } = render();
  expect(findByText('Select stay dates to show prices')).not.toExist();
});

describe('when there are offers attached to the room', () => {
  const props = {
    roomType: {
      ...defaultProps.roomType,
      offers: [{ id: 1 }, { id: 2 }, { id: 3 }],
    },
  };

  it('renders the offer components', () => {
    const { find } = render(props);
    expect(find('Offer')).toHaveLength(3);
  });
});

describe('when there are no offers attached to the room', () => {
  it('renders unavailable message', () => {
    const { findByText } = render(propsWithoutOffers);
    expect(findByText('Not available for your dates')).toExist();
  });
});

describe('when there is no query', () => {
  it('renders <CheckAvailability />', () => {
    const { find } = render({ hasQuery: false });
    expect(find('CheckAvailability')).toHaveProp('room', defaultProps.roomType);
  });
});

describe('when there is T&Cs attached to the room', () => {
  it('renders <TermsAndConditionsModal />', () => {
    const { find } = render({ roomType: { ...defaultProps.roomType, termsAndConditions: 'These are t&cs' } });
    expect(find('TermsAndConditionsModal')).toHaveProp('roomName', 'A Room');
    expect(find('TermsAndConditionsModal')).toHaveProp('termsAndConditions', 'These are t&cs');
  });
});
