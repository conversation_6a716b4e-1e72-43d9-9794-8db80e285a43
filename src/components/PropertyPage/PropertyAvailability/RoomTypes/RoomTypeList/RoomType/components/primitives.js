/* eslint-disable react/prop-types */
import React from 'react';
import { Text, Flex, Box, Hide } from '@qga/roo-ui/components';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { mediaQuery } from 'lib/styledSystem';

export const RoomTypeWrapper = styled(Flex)`
  flex-direction: column;
  flex-wrap: wrap;
  background: ${themeGet('colors.white')};
  border-radius: ${themeGet('radii.default')};
  box-shadow: ${themeGet('shadows.hard')};
  margin-bottom: ${themeGet('space.2')};

  ${mediaQuery.minWidth.sm} {
    flex-direction: row;
    padding: ${themeGet('space.4')};
  }

  ${mediaQuery.minWidth.md} {
    margin-bottom: ${themeGet('space.1')};
    padding: ${themeGet('space.4')} ${themeGet('space.6')};
  }
`;

// In tablet + desktop, apply spacing to the bottom of all offers for each room, except the last one
export const OffersWrapper = styled(Box)`
  flex-grow: 1;

  ${mediaQuery.minWidth.md} {
    flex-direction: row;
    margin-left: ${themeGet('space.4')};
    flex: 1;
  }
`;

export const FullWidthHeader = styled(Hide)`
  flex-basis: 100%;
`;

export const UnavailableMessage = ({ children }) => (
  <Flex alignItems="center" mt={[1, 0]} mb={[2, 0]}>
    <Text fontWeight="bold" fontSize="base" color="orange" mr={4}>
      {children}
    </Text>
  </Flex>
);
