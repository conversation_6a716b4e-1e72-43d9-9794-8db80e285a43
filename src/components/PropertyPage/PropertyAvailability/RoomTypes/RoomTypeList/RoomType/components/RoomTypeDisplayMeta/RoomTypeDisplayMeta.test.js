import React from 'react';
import { mountUtils } from 'test-utils';
import RoomTypeDisplayMeta from './RoomTypeDisplayMeta';

const render = (props = {}) => mountUtils(<RoomTypeDisplayMeta {...props} />);

it('displays the max room occupants if it is given', () => {
  const { find } = render({ maxOccupantCount: 3 });
  expect(find('Text')).toHaveText('Sleeps 3');
});

it('handles a singular occupant room with the correct pluralisation', () => {
  const { find } = render({ maxOccupantCount: 1 });
  expect(find('Text')).toHaveText('Sleeps 1');
});

it('displays the room size along with max room occupants', () => {
  const { find } = render({ maxOccupantCount: 1, roomSize: '30 sqm' });
  expect(find('Text')).toHaveText('Sleeps 1 • 30 sqm');
});

it('displays the bed configuration along with max room occupants', () => {
  const { find } = render({ maxOccupantCount: 1, bedConfiguration: 'King Si<PERSON>' });
  expect(find('Text')).toHaveText('Sleeps 1 • King Size');
});

it('displays max room occupants, room size, and bed configuration', () => {
  const { find } = render({ maxOccupantCount: 1, roomSize: '30 sqm', bedConfiguration: 'King Size' });
  expect(find('Text')).toHaveText('Sleeps 1 • 30 sqm • King Size');
});
