import React from 'react';
import PropTypes from 'prop-types';
import { Flex, Text } from '@qga/roo-ui/components';

const RoomTypeDisplayMeta = React.memo(({ maxOccupantCount, roomSize = '', bedConfiguration = '' }) => {
  if (!maxOccupantCount) return null;

  return (
    <Flex alignItems="center" pb={2}>
      <Text fontSize="base" color="greys.steel">
        Sleeps {maxOccupantCount}
        {roomSize && ` • ${roomSize}`}
        {bedConfiguration && ` • ${bedConfiguration}`}
      </Text>
    </Flex>
  );
});

RoomTypeDisplayMeta.displayName = 'RoomTypeDisplayMeta';

RoomTypeDisplayMeta.propTypes = {
  maxOccupantCount: PropTypes.number,
  roomSize: PropTypes.string,
  bedConfiguration: PropTypes.string,
};

export default RoomTypeDisplayMeta;
