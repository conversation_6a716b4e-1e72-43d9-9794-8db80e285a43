import React from 'react';
import { mountUtils } from 'test-utils';
import { useDataLayer } from 'hooks/useDataLayer';
import RoomTypeDetailsGallery from './RoomTypeDetailsGallery';

jest.mock('hooks/useDataLayer');

mountUtils.mockComponent('RoomTypeDisplayMeta');
mountUtils.mockComponent('ImageGallery', 'ImageGallery component', ['showFullscreenButton', 'showIndex']);
mountUtils.mockComponent('RoomOfferDetails');

const defaultProps = {
  roomType: {
    id: 333,
    name: 'A Room',
    description: 'room description',
    maxOccupantCount: 7,
    roomTypeFacilities: ['tables', 'chairs', 'a bed'],
    mainImage: {
      urlMedium: 'https://pictures.com/cat',
      caption: 'a cat',
    },
    images: [
      {
        urlLarge: 'https://pictures.com/cat/large',
        urlSmall: 'https://pictures.com/cat',
        caption: 'a cat',
      },
    ],
    offers: [{ id: 1 }, { id: 2 }, { id: 3 }],
  },
};

const mockImages = [
  {
    src: 'https://pictures.com/cat/large',
    thumbnail: 'https://pictures.com/cat',
    alt: 'a cat',
    index: 1,
    total: 1,
  },
];

const emitInteractionEvent = jest.fn();
const decorators = { theme: true, store: true };
const render = (props = {}) => mountUtils(<RoomTypeDetailsGallery {...defaultProps} {...props} />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

it('prints the title of the room', () => {
  const { findByText } = render();
  const heading = findByText('A Room');
  expect(heading).toExist();
});

it('passes the image details to the Image component', () => {
  const { find } = render();

  expect(find('ImageGallery')).toHaveProp({
    images: mockImages,
  });
});

describe('when there are no room images', () => {
  const props = {
    roomType: {
      ...defaultProps.roomType,
      images: [],
    },
  };
  it('renders an image placeholder', () => {
    const { findByText } = render(props);
    expect(findByText('Image not available')).toExist();
  });
});

describe('max occupants', () => {
  it('passes the max occupants down to <RoomTypeDisplayMeta/>', () => {
    const { find } = render();
    expect(find('RoomTypeDisplayMeta')).toHaveProp('maxOccupantCount', defaultProps.roomType.maxOccupantCount);
  });
});

describe('offer details', () => {
  it('renders <RoomOfferDetails />', () => {
    const { find } = render();
    expect(find('RoomOfferDetails')).toHaveProp('roomType', defaultProps.roomType);
  });
});

it('does not throw an error if there are no image assets', () => {
  const roomWithNoImages = {
    ...defaultProps,
    roomType: {
      ...defaultProps.roomType,
      images: null,
    },
  };
  expect(() => render(roomWithNoImages)).not.toThrowError();
});

it('emits an event to the data layer when the image gallery is clicked', () => {
  const { find } = render();
  find('ImageGallery').prop('onOpen')();

  expect(emitInteractionEvent).toHaveBeenCalledWith({
    type: 'Room Type Gallery',
    value: 'Gallery Opened',
  });
});

it('emits an event to the data layer when the image gallery is navigated', () => {
  const { find } = render();
  find('ImageGallery').prop('onImageChanged')(2);

  expect(emitInteractionEvent).toHaveBeenCalledWith({
    type: 'Room Type Gallery',
    value: 'Gallery Scrolled to 2',
  });
});
