import React from 'react';
import PropTypes from 'prop-types';
import { useDispatch } from 'react-redux';
import { Box, Heading, Hide, Flex, ImageGallery } from '@qga/roo-ui/components';
import { useDataLayer } from 'hooks/useDataLayer';
import { setFullscreenGalleryContent } from 'store/ui/uiActions';
import theme from 'lib/theme';
import RoomTypeDisplayMeta from '../RoomTypeDisplayMeta';
import RoomOfferDetails from 'components/PropertyPage/PropertyAvailability/RoomTypes/RoomTypeList/Offer/components/RoomOfferDetails';
import { FullWidthHeader } from '../primitives';

const RoomTypeDetailsGallery = ({ roomType }) => {
  const dispatch = useDispatch();
  const { emitInteractionEvent } = useDataLayer();
  const { images, name, maxOccupantCount } = roomType;

  const roomImages = images || [];
  const hasImages = roomImages.length > 0;
  const thumbnailSize = theme.imageSizes.roomTypeThumbnail;

  const imageGalleryList = roomImages.map(({ urlLarge, urlSmall, caption }, index) => ({
    src: urlLarge,
    alt: caption,
    thumbnail: urlSmall,
    index: index + 1,
    total: roomImages.length,
  }));

  const handleOpenGallery = (startIndex) => {
    dispatch(setFullscreenGalleryContent({ images: imageGalleryList, startIndex }));
    emitInteractionEvent({ type: 'Room Type Gallery', value: 'Gallery Opened' });
  };

  const handleGalleryImageChanged = (id) => {
    emitInteractionEvent({ type: 'Room Type Gallery', value: `Gallery Scrolled to ${id}` });
  };

  return (
    <>
      <FullWidthHeader xs sm>
        <Heading.h3 fontSize="lg" mb={4}>
          {name}
        </Heading.h3>
      </FullWidthHeader>
      <Flex flexDirection={['column', 'row', 'column']} pb={[5, 0]} borderBottom={[1, 0]} borderColor="greys.dusty">
        {!hasImages && (
          <Flex
            height={thumbnailSize.height}
            width={thumbnailSize.width}
            borderRadius="default"
            bg="greys.alto"
            alignItems="center"
            justifyContent="center"
            color="greys.steel"
          >
            Image not available
          </Flex>
        )}
        {hasImages && (
          <Box position="relative" height={thumbnailSize.height} width={thumbnailSize.width} mb={4}>
            <ImageGallery
              images={imageGalleryList}
              onOpen={handleOpenGallery}
              onImageChanged={handleGalleryImageChanged}
              height={thumbnailSize.height}
              showFullscreenButton={false}
              showIndex
            />
          </Box>
        )}
        <Flex flex="1 1 auto" justifyContent="space-between" px={[4, 6, 0]} py={[0, 6, 0]}>
          <Box>
            <Hide md lg>
              <Heading.h3 fontSize="lg">{name}</Heading.h3>
            </Hide>
            <RoomTypeDisplayMeta maxOccupantCount={maxOccupantCount} />
            <RoomOfferDetails roomType={roomType} />
          </Box>
        </Flex>
      </Flex>
    </>
  );
};

RoomTypeDetailsGallery.propTypes = {
  roomType: PropTypes.shape({
    name: PropTypes.string,
    maxOccupantCount: PropTypes.number,
    images: PropTypes.arrayOf(PropTypes.object),
    roomTypeFacilities: PropTypes.arrayOf(PropTypes.string),
    description: PropTypes.string,
    roomSize: PropTypes.string,
    bedConfiguration: PropTypes.string,
  }).isRequired,
};

export default RoomTypeDetailsGallery;
