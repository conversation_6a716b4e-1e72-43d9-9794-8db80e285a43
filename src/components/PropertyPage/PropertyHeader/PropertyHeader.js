import React, { memo, useCallback, useState } from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import { useFeature } from '@optimizely/react-sdk';
import { getIsExclusive, getIsLoading } from 'store/property/propertySelectors';
import { Heading, Box, Flex, StarRating, NakedButton, Hide } from '@qga/roo-ui/components';
import TripAdvisorRating from 'components/TripAdvisorRating';
import TripAdvisorReviewsModal from 'components/TripAdvisorReviewsModal';
import { useModal } from 'lib/hooks';
import { useDataLayer } from 'hooks/useDataLayer';
import FromPrice from 'components/PropertyPage/FromPrice';
import ExclusiveContactDetails from 'components/PropertyPage/ExclusiveContactDetails';
import { getExclusiveOffer, getIsLoading as getIsLoadingExclusiveOffers } from 'store/exclusiveOffer/exclusiveOfferSelectors';
import ResultLoader from 'components/Loader/ResultLoader';
import LoaderSkeletonCard from './LoaderSkeletonCard';
import useRatingTooltip from 'hooks/useRatingTooltip';

const LoadedPropertyHeader = ({ property, tripAdvisorRating, ...rest }) => {
  const isExclusive = useSelector(getIsExclusive);
  const [isRatingOpen, setIsRatingOpen] = useState(false);
  const { name, id, rating, ratingType } = property;
  const { openModal, modalProps } = useModal();
  const { emitInteractionEvent } = useDataLayer();
  const hasTripAdvisorRating = tripAdvisorRating && tripAdvisorRating.reviewCount > 0;
  const [isTripAdvisorModalEnabled] = useFeature('jetstar-hotels-tripadvisor-modal', { autoUpdate: true });

  const ratingTooltip = useRatingTooltip({ ratingType });

  const handleOnClick = useCallback(() => {
    openModal();
    emitInteractionEvent({ type: 'Tripadvisor Pop Up', value: 'Top Link Selected' });
  }, [emitInteractionEvent, openModal]);

  return (
    <Box {...rest}>
      <Flex flexDirection={['column', 'column', 'row']} justifyContent={['inherit', 'inherit', 'space-between']} mb={[0, 0, 5]}>
        <Flex flexDirection="column" mx={[3, 3, 0]} pb={[4, 4, 0]}>
          <Heading.h1 data-testid="property-name" fontSize={['lg', 'lg', '2xl']} lineHeight="tight" mb={4} fontWeight="normal">
            {name}
          </Heading.h1>
          <Flex alignItems="flex-start">
            <Box mr={3} height={isRatingOpen ? [ratingType === 'AAA' ? 130 : 120, 'auto'] : 'auto'}>
              <StarRating
                size={20}
                rating={rating}
                ratingType={ratingType}
                {...ratingTooltip}
                onTooltipChange={(isOpen) => setIsRatingOpen(isOpen)}
              />
            </Box>
            {hasTripAdvisorRating && (
              <>
                <NakedButton onClick={isTripAdvisorModalEnabled ? handleOnClick : undefined} data-testid="trip-advisor-button">
                  <TripAdvisorRating rating={tripAdvisorRating} displayReviews={isTripAdvisorModalEnabled} underlined />
                </NakedButton>
                {isTripAdvisorModalEnabled && <TripAdvisorReviewsModal {...modalProps} locationId={id} />}
              </>
            )}
          </Flex>
        </Flex>
        <Hide xs sm>
          {!isExclusive && <FromPrice />}
          {isExclusive && <ExclusiveContactDetails />}
        </Hide>
      </Flex>
    </Box>
  );
};

LoadedPropertyHeader.propTypes = {
  property: PropTypes.shape({
    name: PropTypes.string,
    rating: PropTypes.number,
    ratingType: PropTypes.string,
    latitude: PropTypes.number,
    longitude: PropTypes.number,
    id: PropTypes.string,
    address: PropTypes.shape({
      streetAddress: PropTypes.arrayOf(PropTypes.string),
      suburb: PropTypes.string,
      state: PropTypes.string,
      postcode: PropTypes.string,
      country: PropTypes.string,
    }),
  }).isRequired,
  tripAdvisorRating: PropTypes.object,
};

LoadedPropertyHeader.defaultProps = {
  tripAdvisorRating: undefined,
};

const PropertyHeader = memo(({ property, tripAdvisorRating }) => {
  const isExclusiveOffer = useSelector(getExclusiveOffer);
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const isLoading = isExclusiveOffer ? useSelector(getIsLoadingExclusiveOffers) : useSelector(getIsLoading);

  return (
    <Box bg="white" pt={[6, 10, 10]}>
      <ResultLoader isLoading={isLoading} skeletonResultCount={1} skeletonCardComponent={LoaderSkeletonCard}>
        <LoadedPropertyHeader property={property} tripAdvisorRating={tripAdvisorRating} />
      </ResultLoader>
    </Box>
  );
});

PropertyHeader.displayName = 'PropertyHeader';

PropertyHeader.propTypes = {
  property: PropTypes.shape({
    name: PropTypes.string,
    rating: PropTypes.number,
    ratingType: PropTypes.string,
    latitude: PropTypes.number,
    longitude: PropTypes.number,
    id: PropTypes.string,
    address: PropTypes.shape({
      streetAddress: PropTypes.arrayOf(PropTypes.string),
      suburb: PropTypes.string,
      state: PropTypes.string,
      postcode: PropTypes.string,
      country: PropTypes.string,
    }),
  }).isRequired,
  tripAdvisorRating: PropTypes.object,
};

export default PropertyHeader;
