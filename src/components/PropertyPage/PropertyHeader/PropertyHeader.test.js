import React from 'react';
import { mountUtils, mocked } from 'test-utils';
import { useFeature } from '@optimizely/react-sdk';
import { StarRating } from '@qga/roo-ui/components';
import PropertyHeader from './PropertyHeader';
import TripAdvisorRating from 'components/TripAdvisorRating';
import TripAdvisorReviewsModal from 'components/TripAdvisorReviewsModal';
import ResultLoader from 'components/Loader/ResultLoader';
import { getIsLoading } from 'store/property/propertySelectors';
import { getIsLoading as getIsLoadingExclusiveOffers } from 'store/exclusiveOffer/exclusiveOfferSelectors';
import LoaderSkeletonCard from './LoaderSkeletonCard';

jest.mock('components/TripAdvisorReviewsModal', () => 'trip-advisor-review-modal-mock');
jest.mock('components/PropertyPage/MapWithMarkerButton', () => jest.fn().mockImplementation(({ children }) => children));
jest.mock('components/TripAdvisorRating', () => jest.fn().mockReturnValue(null));
jest.mock('store/property/propertySelectors');
jest.mock('store/exclusiveOffer/exclusiveOfferSelectors');
jest.mock('hooks/useRatingTooltip');
jest.mock('@optimizely/react-sdk', () => ({
  useFeature: jest.fn(),
}));

mountUtils.mockComponent('FromPrice');
mountUtils.mockComponent('TripAdvisorReviewsModal');
mountUtils.mockComponent('SkeletonCard');

let property = {
  id: '1',
  name: 'The Grand Budapest',
  rating: 3.5,
  ratingType: 'AAA',
};

let tripAdvisorRating = {
  averageRating: 4,
  reviewCount: 1,
  locationId: 'locationId',
};

const decorators = { store: true, theme: true };
const render = () => mountUtils(<PropertyHeader property={property} tripAdvisorRating={tripAdvisorRating} />, { decorators });

describe('<PropertyHeader />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  describe('when loading standard offer', () => {
    beforeEach(() => {
      mocked(useFeature).mockReturnValue([false]);

      getIsLoading.mockReturnValue(true);
    });

    it('renders the ResultLoader', () => {
      const { find } = render();

      expect(find(ResultLoader)).toHaveProp({
        isLoading: true,
        skeletonResultCount: 1,
        skeletonCardComponent: LoaderSkeletonCard,
      });
    });
  });

  describe('when loading exclusive offer', () => {
    beforeEach(() => {
      getIsLoadingExclusiveOffers.mockReturnValue(true);
    });

    it('renders the ResultLoader', () => {
      const { find } = render();

      expect(find(ResultLoader)).toHaveProp({
        isLoading: true,
        skeletonResultCount: 1,
        skeletonCardComponent: LoaderSkeletonCard,
      });
    });
  });

  describe('when not loading', () => {
    beforeEach(() => {
      getIsLoading.mockReturnValue(false);
      getIsLoadingExclusiveOffers.mockReturnValue(false);
    });
    it('renders the property name', () => {
      const { findByTestId } = render();
      expect(findByTestId('property-name').text()).toEqual(property.name);
    });

    it('renders the star rating', () => {
      const { find } = render();
      expect(find(StarRating)).toHaveProp({ rating: property.rating, ratingType: property.ratingType });
    });

    it('renders <FromPrice />', () => {
      const { find } = render();
      expect(find('FromPrice')).toExist();
    });

    describe('jetstar-hotels-tripadvisor-modal feature', () => {
      describe('when is off', () => {
        it('does not render the TripAdvisorRating modal', () => {
          const { find, findByTestId } = render();
          findByTestId('trip-advisor-button').simulate('click');
          expect(find(TripAdvisorReviewsModal)).not.toExist();
        });

        it('renders the TripAdvisorRating with the displayReviews', () => {
          const { find } = render();
          expect(find(TripAdvisorRating)).toHaveProp({ rating: tripAdvisorRating, displayReviews: false });
        });
        it('should not contain onClick', () => {
          const { findByTestId } = render();
          expect(findByTestId('trip-advisor-button')).toHaveProp({ onClick: undefined });
        });
      });

      describe('when is on', () => {
        beforeEach(() => {
          mocked(useFeature).mockReturnValue([true]);
        });

        it('renders the TripAdvisorRating modal', () => {
          const { find, findByTestId } = render();
          findByTestId('trip-advisor-button').simulate('click');
          expect(find(TripAdvisorReviewsModal)).toExist();
        });

        it('renders the TripAdvisorRating with the displayReviews', () => {
          const { find } = render();
          expect(find(TripAdvisorRating)).toHaveProp({ rating: tripAdvisorRating, displayReviews: true });
        });
      });
    });
  });
});
