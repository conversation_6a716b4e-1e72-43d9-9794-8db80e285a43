import React, { useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Flex, Text } from '@qga/roo-ui/components';
import PayWithButtonGroup from 'components/PayWithButtonGroup';
import { getIsPointsPay } from 'store/ui/uiSelectors';
import { updateQuery as updateQueryAction } from 'store/propertyAvailability/propertyAvailabilityActions';
import { getQueryPayWith } from 'store/router/routerSelectors';
import { setIsPointsPay } from 'store/ui/uiActions';
import { getHasValidQuery, getIsLoading as getIsLoadingStandard } from 'store/propertyAvailability/propertyAvailabilitySelectors';
import { getIsLoading as getIsLoadingExclusive } from 'store/exclusiveOffer/exclusiveOfferSelectors';

const PayWith = ({ ...rest }) => {
  const queryPayWith = useSelector(getQueryPayWith);
  const isPointsPay = useSelector(getIsPointsPay);
  const dispatch = useDispatch();
  const validQuery = useSelector(getHasValidQuery);
  const propertyLoading = useSelector(getIsLoadingStandard);
  const exclusivePropertyLoading = useSelector(getIsLoadingExclusive);
  const isDisabled = !validQuery || propertyLoading || exclusivePropertyLoading;

  const payWith = isPointsPay ? 'pointsPlusPay' : queryPayWith;

  const updateQuery = useCallback(
    ({ payWith }) => {
      if (payWith === 'pointsPlusPay' && queryPayWith !== 'points') {
        dispatch(updateQueryAction({ payWith: 'points' }));
        dispatch(setIsPointsPay(true));
      } else if (payWith === 'pointsPlusPay') {
        dispatch(setIsPointsPay(true));
      } else {
        dispatch(setIsPointsPay(false));
        dispatch(updateQueryAction({ payWith }));
      }
    },
    [dispatch, queryPayWith],
  );

  return (
    <Flex alignItems="flex-end" flexDirection="column" ml="auto" position="relative" width={['100%', 'inherit']}>
      <PayWithButtonGroup {...rest} pointsPlusPayEnabled payWith={payWith} updateQuery={updateQuery} isDisabled={isDisabled} />
      <Text color="greys.steel" mt={2} display="block" data-testid="minimum-points-message">
        Minimum 5,000 PTS required
      </Text>
    </Flex>
  );
};

export default PayWith;
