import React from 'react';
import { mountUtils } from 'test-utils';
import { getIsPointsPay } from 'store/ui/uiSelectors';
import { getQueryPayWith } from 'store/router/routerSelectors';
import PayWith from './PayWith';
import { updateQuery } from 'store/propertyAvailability/propertyAvailabilityActions';
import { setIsPointsPay } from 'store/ui/uiActions';
import { getHasValidQuery, getIsLoading as getIsLoadingStandard } from 'store/propertyAvailability/propertyAvailabilitySelectors';
import { getIsLoading as getIsLoadingExclusive } from 'store/exclusiveOffer/exclusiveOfferSelectors';

jest.mock('store/router/routerSelectors');
jest.mock('store/ui/uiSelectors');
jest.mock('store/propertyAvailability/propertyAvailabilitySelectors');
jest.mock('store/exclusiveOffer/exclusiveOfferSelectors');

mountUtils.mockComponent('PayWithButtonGroup');

const render = () => mountUtils(<PayWith />, { decorators: { store: true } });

beforeEach(() => {
  jest.clearAllMocks();
  getHasValidQuery.mockReturnValue(true);
  getIsLoadingExclusive.mockReturnValue(false);
  getIsLoadingStandard.mockReturnValue(false);
});

it('always renders the minimum points message text', () => {
  const { findByTestId } = render();
  expect(findByTestId('minimum-points-message')).toHaveText('Minimum 5,000 PTS required');
});

describe('when starting in cash mode', () => {
  beforeEach(() => {
    getIsPointsPay.mockReturnValue(false);
    getQueryPayWith.mockReturnValue('cash');
  });

  it('renders PayWithButtonGroup with expected props', () => {
    expect(render().find('PayWithButtonGroup')).toHaveProp({
      pointsPlusPayEnabled: true,
      payWith: 'cash',
      updateQuery: expect.any(Function),
    });
  });

  describe('when clicking use points', () => {
    it('dispatches the updateQuery action', () => {
      const query = { payWith: 'points' };

      const { find, decorators } = render();

      find('PayWithButtonGroup').prop('updateQuery')(query);
      expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery(query));
    });

    it('does NOT dispatch the setIsPointsPay=true action', () => {
      const query = { payWith: 'points' };

      const { find, decorators } = render();

      find('PayWithButtonGroup').prop('updateQuery')(query);
      expect(decorators.store.dispatch).not.toHaveBeenCalledWith(setIsPointsPay(true));
    });
  });

  describe('when clicking use points + pay', () => {
    it('dispatches the updateQuery action with payWith=points', () => {
      const query = { payWith: 'pointsPlusPay' };

      const { find, decorators } = render();

      find('PayWithButtonGroup').prop('updateQuery')(query);
      expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery({ payWith: 'points' }));
    });

    it('dispatches the setIsPointsPay=true action', () => {
      const query = { payWith: 'pointsPlusPay' };

      const { find, decorators } = render();

      find('PayWithButtonGroup').prop('updateQuery')(query);
      expect(decorators.store.dispatch).toHaveBeenCalledWith(setIsPointsPay(true));
    });
  });
});

describe('when starting in points mode', () => {
  beforeEach(() => {
    getIsPointsPay.mockReturnValue(false);
    getQueryPayWith.mockReturnValue('points');
  });

  describe('when clicking use cash', () => {
    it('dispatches the updateQuery action', () => {
      const query = { payWith: 'cash' };

      const { find, decorators } = render();

      find('PayWithButtonGroup').prop('updateQuery')(query);
      expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery(query));
    });

    it('does NOT dispatch the setIsPointsPay=true action', () => {
      const query = { payWith: 'cash' };

      const { find, decorators } = render();

      find('PayWithButtonGroup').prop('updateQuery')(query);
      expect(decorators.store.dispatch).not.toHaveBeenCalledWith(setIsPointsPay(true));
    });
  });

  describe('when clicking use points + pay', () => {
    it('does NOT dispatch the updateQuery action', () => {
      const query = { payWith: 'pointsPlusPay' };

      const { find, decorators } = render();

      find('PayWithButtonGroup').prop('updateQuery')(query);
      expect(decorators.store.dispatch).not.toHaveBeenCalledWith(updateQuery({ payWith: 'points' }));
    });

    it('dispatches the setIsPointsPay=true action', () => {
      const query = { payWith: 'pointsPlusPay' };

      const { find, decorators } = render();

      find('PayWithButtonGroup').prop('updateQuery')(query);
      expect(decorators.store.dispatch).toHaveBeenCalledWith(setIsPointsPay(true));
    });
  });
});

describe('when starting in points + pay mode ', () => {
  beforeEach(() => {
    getIsPointsPay.mockReturnValue(true);
    getQueryPayWith.mockReturnValue('points');
  });

  describe('when clicking use cash', () => {
    it('dispatches the updateQuery action', () => {
      const query = { payWith: 'cash' };

      const { find, decorators } = render();

      find('PayWithButtonGroup').prop('updateQuery')(query);
      expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery(query));
    });

    it('dispatches the setIsPointsPay=false action', () => {
      const query = { payWith: 'cash' };

      const { find, decorators } = render();

      find('PayWithButtonGroup').prop('updateQuery')(query);
      expect(decorators.store.dispatch).toHaveBeenCalledWith(setIsPointsPay(false));
    });
  });

  describe('when clicking use points', () => {
    it('dispatches the updateQuery action', () => {
      const query = { payWith: 'points' };

      const { find, decorators } = render();

      find('PayWithButtonGroup').prop('updateQuery')(query);
      expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery(query));
    });

    it('dispatches the setIsPointsPay=false action', () => {
      const query = { payWith: 'points' };

      const { find, decorators } = render();

      find('PayWithButtonGroup').prop('updateQuery')(query);
      expect(decorators.store.dispatch).toHaveBeenCalledWith(setIsPointsPay(false));
    });
  });
});

describe('it should disable the <PayWithButtonGroup />', () => {
  it('when a standard property is loading', () => {
    getIsLoadingStandard.mockReturnValue(true);
    const { find } = render();
    expect(find('PayWithButtonGroup')).toHaveProp({ isDisabled: true });
  });

  it('when an exclusive property is loading', () => {
    getIsLoadingExclusive.mockReturnValue(true);
    const { find } = render();
    expect(find('PayWithButtonGroup')).toHaveProp({ isDisabled: true });
  });

  it('when there is an invalid query', () => {
    getHasValidQuery.mockReturnValue(false);
    const { find } = render();
    expect(find('PayWithButtonGroup')).toHaveProp({ isDisabled: true });
  });
});
