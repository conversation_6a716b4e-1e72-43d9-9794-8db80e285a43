import { useDataLayer } from 'hooks/useDataLayer';
import PropTypes from 'prop-types';
import React, { forwardRef } from 'react';
import { Disclosure } from '@qga/roo-ui/components';
import PageBlock from 'components/PageBlock';

const DisclosureSection = forwardRef(({ children, id, title, ...rest }, ref) => {
  const { emitInteractionEvent } = useDataLayer();

  const onDisclosureActioned = (isExpanded, disclosureTitle) =>
    emitInteractionEvent({ type: `Collapsible Section`, value: `${disclosureTitle} ${isExpanded ? 'Expanded' : 'Collapsed'}` });

  return (
    <PageBlock bg="white" narrow ref={ref}>
      <div id={id} />
      <Disclosure {...rest} pt={0} headerTitle={title} variant="large" onToggle={(isExpanded) => onDisclosureActioned(isExpanded, title)}>
        {children}
      </Disclosure>
    </PageBlock>
  );
});

DisclosureSection.displayName = 'DisclosureSection';
DisclosureSection.propTypes = {
  children: PropTypes.node.isRequired,
  id: PropTypes.string.isRequired,
  title: PropTypes.string.isRequired,
};

export default DisclosureSection;
