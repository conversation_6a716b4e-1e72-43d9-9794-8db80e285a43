import React from 'react';
import FromPrice from './FromPrice';
import { useDataLayer } from 'hooks/useDataLayer';
import { mountUtils } from 'test-utils';
import { getAvailableRoomTypes, getHasValidQuery, getIsLoading } from 'store/propertyAvailability/propertyAvailabilitySelectors';
import { getQueryCheckIn, getQueryCheckOut, getQueryAdults, getQueryChildren, getQueryInfants } from 'store/router/routerSelectors';
import LoaderSkeletonCard from './LoaderSkeletonCard';
import ResultLoader from 'components/Loader/ResultLoader';
import { getCountry } from 'store/property/propertySelectors';

jest.mock('hooks/useDataLayer');
jest.mock('store/router/routerSelectors');
jest.mock('store/property/propertySelectors');
jest.mock('store/propertyAvailability/propertyAvailabilitySelectors');
jest.mock('components/Loader/ResultLoader', () => jest.fn(({ children }) => children));

const emitInteractionEvent = jest.fn();

const checkIn = new Date(2025, 3, 10);
const checkOut = new Date(2025, 3, 12);
let availableRoomTypes = [
  {
    offers: [
      {
        charges: {
          total: { amount: 540, currency: 'AUD' },
          totalBeforeDiscount: { amount: 800, currency: 'AUD' },
        },
        type: 'special',
      },
    ],
  },
];
const availableRoomTypesPoints = [
  {
    offers: [
      {
        charges: {
          total: { amount: 2000, currency: 'PTS' },
          totalBeforeDiscount: { amount: 800, currency: 'PTS' },
        },
        type: 'special',
      },
    ],
  },
];
const country = 'Australia';
const decorators = { theme: true, store: true, router: true };
const render = () => mountUtils(<FromPrice />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
  getQueryCheckIn.mockReturnValue(checkIn);
  getQueryCheckOut.mockReturnValue(checkOut);
  getQueryAdults.mockReturnValue(2);
  getQueryChildren.mockReturnValue(1);
  getQueryInfants.mockReturnValue(0);
  getHasValidQuery.mockReturnValue(true);
  getAvailableRoomTypes.mockReturnValue(availableRoomTypes);
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  getCountry.mockReturnValue(country);
});

describe('<FromPrice/>', () => {
  describe('when loading', () => {
    beforeEach(() => {
      getIsLoading.mockReturnValue(true);
    });

    it('renders the ResultLoader', () => {
      const { find } = render();

      expect(find(ResultLoader)).toHaveProp({
        isLoading: true,
        skeletonResultCount: 1,
        skeletonCardComponent: LoaderSkeletonCard,
      });
    });
  });

  describe('when not loading', () => {
    beforeEach(() => {
      getIsLoading.mockReturnValue(false);
    });

    it('renders the FromPriceBox', () => {
      const { findByTestId } = render();
      expect(findByTestId('from-price-box')).toExist();
    });

    it('Shows the number of nights', () => {
      const { findByText } = render();
      const numberOfNights = findByText('2 nights');
      expect(numberOfNights).toExist();
    });

    describe('Currency is cash', () => {
      it('shows the currency after the number of nights', () => {
        const { findByTestId } = render();
        expect(findByTestId('currency-symbol')).toExist();
      });

      it('Shows the price', () => {
        const { wrapper } = render();
        expect(wrapper).toIncludeText('$540');
      });

      it('Shows the currency without the *', () => {
        const { wrapper } = render();
        expect(wrapper).toIncludeText('AUD');
        expect(wrapper).not.toIncludeText('*');
      });

      it('should not contains <CampaignPriceMessage>', () => {
        const { find } = render();
        expect(find('CampaignPriceMessage').text()).toBe('');
      });

      it('should render <PriceBeforeDiscount/> if the room has discount( totalBeforeDiscount.amount !== total.amount )', () => {
        const { find } = render();
        expect(find('PriceBeforeDiscount')).toExist();
      });
    });

    describe('when the currency is PTS', () => {
      beforeEach(() => {
        getAvailableRoomTypes.mockReturnValue(availableRoomTypesPoints);
      });

      it('Shows the * if the currency is points', () => {
        const { wrapper } = render();
        expect(wrapper).toIncludeText('PTS*');
      });

      it('should render <PriceBeforeDiscount/> if the room has discount( totalBeforeDiscount.amount = total.amount )', () => {
        const { find } = render();
        expect(find('PriceBeforeDiscount')).toExist();
      });

      it('renders the <CampaignPriceMessage> with the correct props', () => {
        expect(render().find('CampaignPriceMessage')).toHaveProp({
          currency: 'PTS',
          offerType: 'special',
          country,
        });
      });
    });

    describe('View Room button', () => {
      it('renders the View Rooms button with the correct link', () => {
        const { find } = render();

        expect(find('ButtonLink')).toHaveProp({
          href: '#rooms',
        });
      });

      it('dispatches an event to the data layer', () => {
        const { findByTestId } = render();
        findByTestId('view-rooms').simulate('click');
        expect(emitInteractionEvent).toHaveBeenCalledWith({
          type: 'From Price',
          value: `View Room Selected`,
        });
      });
    });

    describe('if there is no availability', () => {
      beforeEach(() => {
        getAvailableRoomTypes.mockReturnValue([]);
      });
      it('does not render the FromPriceBox', () => {
        const { findByTestId } = render();
        expect(findByTestId('from-price-box')).not.toExist();
      });
    });
  });
});

describe('when the query is not valid', () => {
  beforeEach(() => {
    getHasValidQuery.mockReturnValue(false);
  });

  it('does NOT render', () => {
    const { findByTestId } = render();
    expect(findByTestId('from-price-box')).not.toExist();
  });
});
