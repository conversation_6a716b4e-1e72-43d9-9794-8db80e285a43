import React, { memo, useCallback } from 'react';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import { Flex, Box, Text, Button, Link } from '@qga/roo-ui/components';
import { mediaQuery } from 'lib/styledSystem';
import Currency from 'components/Currency';
import CampaignPriceMessage from 'components/CampaignPriceMessage';
import PriceBeforeDiscount from 'components/PriceBeforeDiscount';
import { getCountry } from 'store/property/propertySelectors';
import { getAvailableRoomTypes, getHasValidQuery, getIsLoading } from 'store/propertyAvailability/propertyAvailabilitySelectors';
import { getQueryCheckIn, getQueryCheckOut, getQueryAdults, getQueryChildren, getQueryInfants } from 'store/router/routerSelectors';
import NumberOfNights from 'components/NumberOfNights';
import { useSelector } from 'react-redux';
import { useDataLayer } from 'hooks/useDataLayer';
import ResultLoader from 'components/Loader/ResultLoader';
import LoaderSkeletonCard from './LoaderSkeletonCard';

const Wrapper = styled(Flex)`
  flex-direction: row;
  justify-content: space-between;
  max-width: 100%;
  ${mediaQuery.minWidth.sm} {
    max-width: 586px;
  }
`;

const ButtonLink = styled(Button)`
  width: 100%;
  font-size: ${themeGet('fontSizes.base')};
  padding-top: 10px;
  padding-bottom: 10px;
  max-width: 196px;
  ${mediaQuery.minWidth.sm} {
    padding: ${themeGet('space.2')} ${themeGet('space.6')};
    flex-grow: initial;
  }

  &:hover {
    color: ${themeGet('colors.white')};
  }
`;

const NightsAndGuests = () => {
  const checkIn = useSelector(getQueryCheckIn);
  const checkOut = useSelector(getQueryCheckOut);
  const adults = useSelector(getQueryAdults);
  const children = useSelector(getQueryChildren);
  const infants = useSelector(getQueryInfants);
  const totalGuests = adults + children + infants;
  const styleNightAndGuests = { fontSize: '14px', lineHeight: '24px', fontWeight: 500 };

  return (
    <Flex flexDirection="row" style={{ textTransform: 'uppercase', ...styleNightAndGuests }} data-testid="nights-guests">
      <NumberOfNights checkIn={checkIn} checkOut={checkOut} style={styleNightAndGuests} />
      <Text style={styleNightAndGuests}>&nbsp;• {totalGuests} guests from</Text>
    </Flex>
  );
};

const OfferPrice = () => {
  const { emitInteractionEvent } = useDataLayer();
  const availableRoomTypes = useSelector(getAvailableRoomTypes);
  const country = useSelector(getCountry);
  const amount = get(availableRoomTypes, '[0].offers[0].charges.total.amount', 0);
  const currency = get(availableRoomTypes, '[0].offers[0].charges.total.currency', '');
  const hasAvailability = amount !== 0;
  const isCurrencyPoints = currency === 'PTS';
  const offerType = get(availableRoomTypes, '[0].offers[0].type');
  const totalBeforeDiscount = get(availableRoomTypes, '[0].offers[0].charges.totalBeforeDiscount', null);
  const hasDiscount = totalBeforeDiscount?.amount !== amount;

  const handleOnClick = useCallback(() => {
    emitInteractionEvent({ type: 'From Price', value: `View Room Selected` });
  }, [emitInteractionEvent]);

  if (isEmpty(availableRoomTypes)) return null;

  return (
    <Box>
      {hasAvailability && (
        <Wrapper data-testid="from-price-box">
          <Flex flexDirection="column" justifyContent="center" mr={[0, 25]}>
            <NightsAndGuests />
            <Flex flexDirection="row" alignItems="baseline" flexWrap="wrap">
              <Flex alignItems="center">
                <Currency
                  amount={amount}
                  currency={currency}
                  roundToCeiling
                  fontSize={32}
                  fontWeight="bold"
                  hideCurrency={true}
                  color="greys.charcoal"
                  data-testid="total-to-pay"
                  alignCurrency="superscript"
                />
                <Text pl={1} pt={1}>
                  {currency}
                  {isCurrencyPoints && <sup>*</sup>}
                </Text>
              </Flex>
            </Flex>
            <Flex flexDirection="row" alignItems="baseline">
              <CampaignPriceMessage currency={currency} offerType={offerType} country={country} />
            </Flex>
            <Flex flexDirection="row" alignItems="baseline">
              {hasDiscount && (
                <PriceBeforeDiscount
                  total={totalBeforeDiscount}
                  hideCurrency={!isCurrencyPoints}
                  fontSize={['xs', 'sm']}
                  lineHeight="0.6"
                  roundToCeiling
                  offerType={offerType}
                />
              )}
            </Flex>
          </Flex>
          <Flex flexDirection="column" justifyContent="flex-start">
            <ButtonLink
              as={Link}
              variant="primary"
              data-testid="view-rooms"
              href="#rooms"
              aria-label={`View rooms`}
              onClick={handleOnClick}
            >
              View Rooms
            </ButtonLink>
          </Flex>
        </Wrapper>
      )}
    </Box>
  );
};

const FromPrice = memo(() => {
  const hasValidQuery = useSelector(getHasValidQuery);
  const isLoading = useSelector(getIsLoading);

  if (!hasValidQuery) return null;

  return (
    <Box mx={[3, 3, 0]} borderBottom={[1, 1, 0]} borderColor="greys.alto" mb={[4, 4, 0]} pb={[6, 6, 0]}>
      <ResultLoader isLoading={isLoading} skeletonResultCount={1} skeletonCardComponent={LoaderSkeletonCard}>
        <OfferPrice />
      </ResultLoader>
    </Box>
  );
});

FromPrice.displayName = 'FromPrice';

export default FromPrice;
