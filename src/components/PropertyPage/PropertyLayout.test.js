import React from 'react';
import PropertyLayout from './PropertyLayout';
import { mountUtils } from 'test-utils';
import { fetchProperty } from 'store/property/propertyActions';
import BackToSearchLink from './BackToSearchLink';
import BackToDealsLink from './BackToDealsLink';
import PropertyFacilities from './PropertyFacilities';
import SearchControls from './SearchControls';
import NoQueryMessage from './NoQueryMessage';
import PropertyAvailabilitySummary from './PropertyAvailabilitySummary';
import PropertyMapStatic from './PropertyMapStatic';
import PropertyMap from 'components/PropertyMap';
import PropertyKnowBeforeYouGo from 'components/PropertyPage/PropertyKnowBeforeYouGo';
import { getPropertyId, getProperty, getTripAdvisorRating } from 'store/property/propertySelectors';
import { getHasValidQuery } from 'store/propertyAvailability/propertyAvailabilitySelectors';
import { getExclusiveOffer, getIsExclusiveOfferAvailable } from 'store/exclusiveOffer/exclusiveOfferSelectors';
import { getFullscreenGalleryContent } from 'store/ui/uiSelectors';
import Waypoints from 'components/Waypoints';
import LegacyBrowserBoundary from 'components/LegacyBrowserBoundary';
import * as config from 'config';
import { useDataLayer } from 'hooks/useDataLayer';
import { useModal } from 'lib/hooks';
import { getAppNavigationIcon, getQueryParams } from 'store/router/routerSelectors';
import { useDispatch } from 'react-redux';
import usePropertyPageExclusiveGa4Event from 'hooks/usePropertyPageExclusiveGa4Event';
import usePropertyPageStandardGa4Event from 'hooks/usePropertyPageStandardGa4Event';

jest.mock('./hooks/useFeaturedPropertyRedirector');
jest.mock('./hooks/useTrackPropertyPageView');
jest.mock('hooks/usePropertyPageExclusiveGa4Event');
jest.mock('hooks/usePropertyPageStandardGa4Event');
jest.mock('config');
jest.mock('store/propertyAvailability/propertyAvailabilitySelectors');
jest.mock('store/exclusiveOffer/exclusiveOfferSelectors');
jest.mock('store/ui/uiSelectors');
jest.mock('store/router/routerSelectors');
jest.mock('store/property/propertyActions');
jest.mock('./BackToSearchLink', () => jest.fn().mockReturnValue(null));
jest.mock('./BackToDealsLink', () => jest.fn().mockReturnValue(null));
jest.mock('./PropertyMapStatic', () => () => null);
jest.mock('./PropertyFacilities', () => () => null);
jest.mock('./SearchControls', () => () => null);
jest.mock('./PropertyAvailabilitySummary', () => () => null);
jest.mock('components/TripAdvisorReviewsModal', () => () => null);
jest.mock('components/PropertyMap', () => () => null);
jest.mock('components/Image', () => () => null);
jest.mock('store/property/propertySelectors');
jest.mock('components/PropertyPage/PropertyKnowBeforeYouGo', () => () => null);
jest.mock('components/PropertyPage/FromPrice', () => () => null);
jest.mock('components/Waypoints', () => () => 'Waypoints');
jest.mock('store/user/userSelectors');
jest.mock('components/LegacyBrowserBoundary');
jest.mock('hooks/useDataLayer');
jest.mock('lib/hooks');
jest.mock('components/FourOhFourPage', () => () => 'FourOhFourPage');
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: jest.fn(),
}));

mountUtils.mockComponent('ExclusiveContactDetails');
mountUtils.mockComponent('RequestCallbackModal');
mountUtils.mockComponent('ExclusiveOfferConditions');
mountUtils.mockComponent('ExclusiveOfferCrossSellBanner');
mountUtils.mockComponent('ExclusiveOffersFetcher');
mountUtils.mockComponent('ExclusiveOfferUnavailableDialog');
mountUtils.mockComponent('FullscreenImageGallery');
mountUtils.mockComponent('InPageNavBar');
mountUtils.mockComponent('MemberFavouritesButton');
mountUtils.mockComponent('OfferDetails');
mountUtils.mockComponent('PropertyAvailability');
mountUtils.mockComponent('PropertyDescription');
mountUtils.mockComponent('PropertyHeader');
mountUtils.mockComponent('PropertyHelmet');
mountUtils.mockComponent('RecommendedProperties');
mountUtils.mockComponent('ShareProperty');
mountUtils.mockComponent('WelcomeMessage');
mountUtils.mockComponent('StandardOfferMessage');
mountUtils.mockComponent('ResultLoader');
mountUtils.mockComponent('MobileAppLeftNavigationIcon');

const propertyId = '12345';
const emitInteractionEvent = jest.fn();
const standardViewItemEvent = jest.fn();
const exclusiveViewItemEvent = jest.fn();
const openModal = jest.fn();
const dispatch = jest.fn();

const property = {
  address: {
    streetAddress: ['Plot 109, Malula Village', 'Moshi-Arusha Road'],
    suburb: 'Arusha',

    state: null,
    postcode: null,
    country: 'Tanzania',
    countryCode: 'TZ',
  },
  name: 'Airport Planet Lodge',
  images: [
    {
      urlLarge: 'http://lorempixel.com/1000/600/nature/1/',
      urlSmall: 'http://lorempixel.com/250/150/nature/1/',
      caption: 'Nature 1',
    },
  ],
  propertyFacilities: ['pool', 'parking'],
  description: 'property description',
  mainImage: {
    urlLarge: 'http://lorempixel.com/1000/600/nature/1/',
    caption: 'Nature 1',
  },
  latitude: 123,
  longitude: 456,
  checkInInstructions: null,
  knowBeforeYouGoDescription: null,
  roomInformation: null,
  roomFeesDescription: null,
  mandatoryFeesDescription: null,
};

const tripAdvisorRating = { reviewCount: 1, averageRating: 5 };

const fetchPropertyActionValue = 'fetchPropertyActionValue';
const decorators = { router: true, theme: true, store: true };

const render = (props = {}) => mountUtils(<PropertyLayout {...props} />, { decorators: decorators });

beforeEach(() => {
  jest.clearAllMocks();
  useDispatch.mockReturnValue(dispatch);
  fetchProperty.mockReturnValue(fetchPropertyActionValue);
  getPropertyId.mockReturnValue(propertyId);
  getProperty.mockReturnValue(property);
  getTripAdvisorRating.mockReturnValue(tripAdvisorRating);
  getHasValidQuery.mockReturnValue(true);
  getFullscreenGalleryContent.mockReturnValue({ images: [], startIndex: 0 });
  LegacyBrowserBoundary.mockImplementation(({ children }) => children);
  config.PAYWITH_TOGGLE_ENABLED = true;
  config.EXCLUSIVE_OFFERS_ENABLED = true;
  useModal.mockReturnValue({ openModal, modalProps: { isOpen: false } });
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  getIsExclusiveOfferAvailable.mockReturnValue(false);
  getExclusiveOffer.mockReturnValue(null);
  getQueryParams.mockReturnValue({});
  usePropertyPageExclusiveGa4Event.mockReturnValue({ exclusiveViewItemEvent });
  usePropertyPageStandardGa4Event.mockReturnValue({ standardViewItemEvent });
});

describe('when there is no property', () => {
  it('renders nothing', () => {
    getProperty.mockReturnValue(null);
    const { wrapper } = render();
    expect(wrapper.html()).toEqual(null);
  });
});

describe('PropertyHelmet', () => {
  it('renders the PropertyHelmet component', () => {
    const { find } = render();
    expect(find('PropertyHelmet')).toHaveProp({ property });
  });
});

describe('RequestCallbackModal', () => {
  it('renders the RequestCallbackModal component', () => {
    const { find } = render();
    expect(find('RequestCallbackModal')).toHaveProp({ interactionType: 'Exclusive Offers' });
  });
});

describe('MobileAppLeftNavigationIcon when prop icon Name exists', () => {
  beforeEach(() => {
    getAppNavigationIcon.mockReturnValue('back');
  });

  it('renders the MobileAppLeftNavigationIcon with "back"', () => {
    const { find } = render();
    expect(find('MobileAppLeftNavigationIcon')).toHaveProp({ iconName: 'back' });
  });
});

describe('MobileAppLeftNavigationIcon when prop icon Name is undefined', () => {
  beforeEach(() => {
    getAppNavigationIcon.mockReturnValue(undefined);
  });

  it('renders the MobileAppLeftNavigationIcon with "back"', () => {
    const { find } = render();
    expect(find('MobileAppLeftNavigationIcon')).toHaveProp({ iconName: 'back' });
  });
});

describe('Back links', () => {
  describe('when isFromDeals is false', () => {
    it('renders the BackToSearchLink', () => {
      const { find } = render();

      expect(find(BackToSearchLink)).toExist();
      expect(find(BackToDealsLink)).not.toExist();
    });
  });
  describe('when isFromDeals is true', () => {
    beforeEach(() => {
      getQueryParams.mockReturnValue({ dealsRegion: 'sydney', dealType: 'classic-rewards' });
    });

    it('renders the BackToDealsLink', () => {
      const { find } = render();

      expect(find(BackToDealsLink)).toExist();
      expect(find(BackToDealsLink)).toHaveProp({ dealsRegion: 'sydney', dealType: 'classic-rewards' });
      expect(find(BackToSearchLink)).not.toExist();
    });
  });
});

describe('WelcomeMessage', () => {
  it('renders the WelcomeMessage component', () => {
    const { find } = render();
    expect(find('WelcomeMessage')).toExist();
  });
});

describe('Property Header', () => {
  it('renders the PropertyHeader component', () => {
    const { find } = render();
    expect(find('PropertyHeader')).toExist();
  });
});

describe('CampaignMessaging', () => {
  it('renders the CampaignMessaging component', () => {
    const { find } = render();
    expect(find('CampaignMessaging')).toExist();
  });
});

describe('StandardImageGallery', () => {
  it('renders the StandardImageGallery component', () => {
    const { find } = render();
    expect(find('StandardImageGallery')).toHaveProp({ images: property.images });
  });
});

describe('FullscreenImageGallery', () => {
  it('renders the FullscreenImageGallery component closed', () => {
    const { find } = render();
    expect(find('FullscreenImageGallery')).toExist();
    expect(openModal).not.toHaveBeenCalled();
  });

  it('opens the FullscreenImageGallery component', () => {
    getFullscreenGalleryContent.mockReturnValue({ images: ['image1', 'image2'], startIndex: 1 });

    const { find } = render();
    expect(find('FullscreenImageGallery')).toExist();
    expect(openModal).toHaveBeenCalled();
  });
});

describe('SearchControls', () => {
  it('renders the SearchControls component', () => {
    const { find } = render();
    expect(find(SearchControls)).toExist();
  });
});

describe('NoQueryMessage', () => {
  it('renders the NoQueryMessage component', () => {
    const { find } = render();
    expect(find(NoQueryMessage)).toHaveProp({ propertyName: property.name, hasQuery: true });
  });
});

describe('PropertyAvailabilitySummary', () => {
  it('renders the PropertyAvailabilitySummary component', () => {
    const { find } = render();
    expect(find(PropertyAvailabilitySummary)).toHaveProp({ propertyName: property.name });
  });
});

describe('PropertyAvailability', () => {
  it('renders the PropertyAvailability component', () => {
    const { find } = render();
    expect(find('PropertyAvailability')).toExist();
  });
});

describe('PropertyFacilities', () => {
  const facilities = property.propertyFacilities;

  it('renders the PropertyFacilities summary component in the PropertySummaryAside', () => {
    const { find } = render();
    expect(find('FacilitiesWrapper').find(PropertyFacilities)).toHaveProp({
      summary: true,
      facilities,
    });
  });

  describe('without PropertyFacilities', () => {
    beforeEach(() => {
      getProperty.mockReturnValue({ ...property, propertyFacilities: [] });
    });

    it('does not render the PropertyFacilities component in the PropertySummaryMain', () => {
      const { find } = render();
      expect(find('PropertySummaryMain').find(PropertyFacilities)).not.toExist();
    });

    it('does not render the PropertyFacilities component in the PropertySummaryAside', () => {
      const { find } = render();
      expect(find('PropertySummaryAside').find(PropertyFacilities)).not.toExist();
    });

    it('does not render the PropertyFacilities component in the PropertyDescriptionWrapper', () => {
      const { find } = render();
      expect(find('PropertyDescriptionWrapper').find(PropertyFacilities)).not.toExist();
    });
  });
});

describe('<PropertyMapStatic />', () => {
  it('renders the PropertyMapStatic component with the correct coordinates', () => {
    const { find } = render();
    expect(find(PropertyMapStatic).at(0)).toHaveProp({ latitude: property.latitude, longitude: property.longitude });
  });
});

describe('<PropertyMap />', () => {
  it('renders the PropertyMap component with the property info', () => {
    const { find } = render();
    expect(find(PropertyMap)).toHaveProp({ property });
  });
});

describe('<PropertyKnowBeforeYouGo />', () => {
  it('renders the component', () => {
    getProperty.mockReturnValue({ ...property, checkInInstructions: 'check-in before 14:00' });
    const { find } = render();
    expect(find(PropertyKnowBeforeYouGo)).toExist();
  });
});

describe('points club UI', () => {
  it('does render the points club banner', () => {
    const { find } = render();
    const pointsClubBanner = find('DynamicMessageBox');
    expect(pointsClubBanner).toExist();
  });
});

describe('Waypoints', () => {
  it('renders the Waypoints component', () => {
    const { find } = render();
    expect(find(Waypoints)).toExist();
  });
});

it('renders <RecommendedProperties />', () => {
  const { find } = render();

  expect(find('RecommendedProperties')).toExist();
});

describe('InPageNavBar', () => {
  it('renders the InPageNavBar component', () => {
    const { find } = render();
    expect(find('InPageNavBar')).toExist();
  });
});

describe('exclusive offers', () => {
  it('renders ExclusiveOffersFetcher', () => {
    const { find } = render();
    expect(find('ExclusiveOffersFetcher')).toExist();
  });

  it('renders the ExclusiveOfferCrossSellBanner component', () => {
    const { find } = render();
    expect(find('ExclusiveOfferCrossSellBanner')).toExist();
  });

  it('renders  ExclusiveOfferUnavailableDialog', () => {
    const { find } = render();
    expect(find('ExclusiveOfferUnavailableDialog')).toExist();
  });

  describe('when the property has no exclusive offers', () => {
    it('does NOT render OfferDetails', () => {
      const { find } = render();

      expect(find('OfferDetails')).not.toExist();
    });
  });

  describe('when the property has exclusive offers', () => {
    beforeEach(() => {
      getExclusiveOffer.mockReturnValue({ exclusive: true });
      getIsExclusiveOfferAvailable.mockReturnValue(true);
    });

    it('renders OfferDetails', () => {
      const { find } = render({ isExclusiveOffer: true });

      expect(find('OfferDetails')).toExist();
    });
  });

  describe('when EXCLUSIVE_OFFERS_ENABLED is false', () => {
    beforeEach(() => {
      config.EXCLUSIVE_OFFERS_ENABLED = false;
    });

    it('does not render  ExclusiveOfferCrossSellBanner', () => {
      const { find } = render();
      expect(find('ExclusiveOfferCrossSellBanner')).not.toExist();
    });
  });
});

describe('disclosures', () => {
  beforeEach(() => {
    getExclusiveOffer.mockReturnValue({
      highlights: { title: 'title', description: 'description' },
      terms: { hotelTerms: 'T&Cs' },
    });

    getIsExclusiveOfferAvailable.mockReturnValue(true);
  });

  describe('highlights', () => {
    it('renders the disclosure', () => {
      const { find } = render();

      expect(find('DisclosureSection[id="highlights"]')).toHaveProp('title', 'Highlights');
    });
  });

  describe('offer terms', () => {
    it('renders the disclosure', () => {
      const { find } = render();

      expect(find('DisclosureSection[id="offer-terms"]')).toHaveProp('title', 'Offer Terms');
    });
  });

  describe('about this property', () => {
    it('renders the disclosure', () => {
      const { find } = render();

      expect(find('DisclosureSection[id="about-property"]')).toHaveProp('title', 'About this property');
    });

    it('renders the property description', () => {
      const { find } = render();

      expect(find('PropertyDescription')).toExist();
    });
  });

  describe('facilities', () => {
    it('renders the disclosure', () => {
      const { find } = render();

      expect(find('DisclosureSection[id="facilities"]')).toHaveProp('title', 'Facilities');
    });
  });

  describe('location', () => {
    it('renders the disclosure', () => {
      const { find } = render();

      expect(find('DisclosureSection[id="location"]')).toHaveProp('title', 'Location');
    });
  });

  describe('property policies', () => {
    it('renders the disclosure', () => {
      const { find } = render();

      expect(find('DisclosureSection[id="property-policies"]')).toHaveProp('title', 'Property policies');
    });
  });
});
