import React from 'react';
import { useSelector } from 'react-redux';
import omit from 'lodash/omit';
import { getExclusiveOffer } from 'store/exclusiveOffer/exclusiveOfferSelectors';
import { getSearchLinkQuery } from 'store/propertyAvailability/propertyAvailabilitySelectors';
import stringifyQueryValues from 'lib/search/stringifyQueryValues';
import BackLink from 'components/BackLink';
import { useDataLayer } from 'hooks/useDataLayer';
import { HOTELS_URL } from 'config';

const BackToSearchLink = () => {
  const { emitInteractionEvent } = useDataLayer();
  const searchQuery = useSelector(getSearchLinkQuery);
  const isExclusiveOffer = useSelector(getExclusiveOffer);
  const { location: searchLocation, searchType = 'list' } = searchQuery;
  const searchQueryString = stringifyQueryValues(omit(searchQuery, ['searchType']));
  const labelText = `Hotels in ${searchLocation}`;

  const linkProps = isExclusiveOffer
    ? { as: 'a', href: `${HOTELS_URL}/exclusive-offers`, rel: 'noopener noreferrer' }
    : { to: `/search/${searchType}?${searchQueryString}` };

  const handleOnClick = () => {
    emitInteractionEvent({ type: 'Return Search Page', value: 'Link Selected' });
  };

  return <BackLink {...linkProps} label={labelText} onClick={handleOnClick} />;
};

export default BackToSearchLink;
