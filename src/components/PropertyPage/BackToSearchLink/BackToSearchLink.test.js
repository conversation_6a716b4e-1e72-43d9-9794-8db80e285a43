import React from 'react';
import { mountUtils } from 'test-utils';
import BackToSearchLink from './BackToSearchLink';
import { getSearchLinkQuery } from 'store/propertyAvailability/propertyAvailabilitySelectors';
import { useDataLayer } from 'hooks/useDataLayer';
import { getExclusiveOffer } from 'store/exclusiveOffer/exclusiveOfferSelectors';
import { HOTELS_URL } from 'config';

jest.mock('hooks/useDataLayer');
jest.mock('store/propertyAvailability/propertyAvailabilitySelectors');
jest.mock('store/exclusiveOffer/exclusiveOfferSelectors');

const emitInteractionEvent = jest.fn();

const decorators = { store: true, router: true };
const render = () => mountUtils(<BackToSearchLink />, { decorators });

describe('BackToSearchLink', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    getExclusiveOffer.mockReturnValue(null);
    useDataLayer.mockReturnValue({ emitInteractionEvent });
  });

  describe('with a searchType', () => {
    const searchQuery = {
      location: 'Melbourne',
      adults: 1,
      searchType: 'map',
    };

    beforeEach(() => {
      getSearchLinkQuery.mockReturnValue(searchQuery);
    });

    describe('when its exclusive offer', () => {
      it('should render BackLink and redirect the user to Exclusive Offer page', () => {
        getExclusiveOffer.mockReturnValue({ exclusiveOffer: true });
        const { find } = render();
        expect(find('BackLink')).toHaveProp({ as: 'a', href: `${HOTELS_URL}/exclusive-offers`, rel: 'noopener noreferrer' });
      });
    });

    describe('when its NOT exclusive offer', () => {
      it('should render BackLink and redirect the user to proeprySearch page', () => {
        const { find } = render();
        expect(find('BackLink')).toHaveProp({ label: 'Hotels in Melbourne', to: '/search/map?adults=1&location=Melbourne' });
      });
    });
  });

  describe('without a searchType', () => {
    const searchQuery = {
      location: 'Melbourne',
      adults: 1,
    };

    beforeEach(() => {
      getSearchLinkQuery.mockReturnValue(searchQuery);
    });

    it('renders the to with link to list', () => {
      const { find } = render();
      expect(find('BackLink')).toHaveProp({ to: '/search/list?adults=1&location=Melbourne' });
    });
  });

  describe('with a bounding box search', () => {
    const searchQuery = {
      location: 'Melbourne',
      neLat: 1,
      neLng: 2,
      swLat: 3,
      swLng: 4,
      adults: 1,
      searchType: 'map',
    };

    beforeEach(() => {
      getSearchLinkQuery.mockReturnValue(searchQuery);
    });

    it('renders the expected location', () => {
      const { find } = render();
      expect(find('BackLink')).toHaveProp({ label: 'Hotels in Melbourne' });
    });

    it('renders the expected to', () => {
      const { find } = render();
      expect(find('BackLink')).toHaveProp({ to: '/search/map?adults=1&location=Melbourne&neLat=1&neLng=2&swLat=3&swLng=4' });
    });
  });

  describe('BackLink when clicked', () => {
    beforeEach(() => {
      getSearchLinkQuery.mockReturnValue({});
    });

    it('passes a callback to the BackLink that logs a GA event', () => {
      const { find } = render();
      find('BackLink').simulate('click');

      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Return Search Page',
        value: 'Link Selected',
      });
    });
  });
});
