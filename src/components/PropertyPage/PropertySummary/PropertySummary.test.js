import React from 'react';
import { mountUtils } from 'test-utils';
import PropertySummary from './PropertySummary';
import { getExclusiveOffer } from 'store/exclusiveOffer/exclusiveOfferSelectors';

jest.mock('store/ui/uiSelectors');
jest.mock('store/exclusiveOffer/exclusiveOfferSelectors');
jest.mock('store/property/propertySelectors');

mountUtils.mockComponent('ExclusiveContactDetails');
mountUtils.mockComponent('ExclusiveOfferConditions');
mountUtils.mockComponent('MemberFavourites');
mountUtils.mockComponent('PropertyFacilities');
mountUtils.mockComponent('PropertyMapStatic');
mountUtils.mockComponent('ShareProperty');

const menuItems = [
  {
    name: 'photos',
    text: 'Photos',
    id: 'photos',
    justifyContent: 'flex-start',
  },
  {
    name: 'rooms',
    text: 'Rooms',
    id: 'rooms',
    justifyContent: 'flex-start',
  },
  {
    name: 'highlights',
    text: 'Highlights',
    id: 'highlights',
    justifyContent: 'flex-start',

    isExclusive: true,
  },
  {
    name: 'offer-terms',
    text: 'Offer Terms',
    id: 'offer-terms',
    justifyContent: 'center',

    isExclusive: true,
  },
  {
    name: 'about-property',
    text: 'About this property',
    id: 'about-property',
    justifyContent: 'center',
  },
  {
    name: 'location',
    text: 'Location',
    id: 'location',
    justifyContent: 'flex-end',
  },
  {
    name: 'property-policies',
    text: 'Property policies',
    id: 'property-policies',
    justifyContent: 'flex-end',
  },
];

let property = {
  address: {
    streetAddress: ['Plot 109, Malula Village', 'Moshi-Arusha Road'],
    suburb: 'Arusha',
    state: 'Arusha',
    postcode: '1234',
    country: 'Tanzania',
    countryCode: 'TZ',
  },
  name: 'Airport Planet Lodge',
  propertyFacilities: ['pool', 'parking'],
  latitude: 123,
  longitude: 456,
  renovations: 'renovations',
};

beforeEach(() => {
  jest.clearAllMocks();
});

const defaultProps = { property, hasPropertyFacilities: true, menuItems };
const decorators = { theme: true, store: true };

const render = (props) => mountUtils(<PropertySummary {...defaultProps} {...props} />, { decorators });

describe('<PropertyDescription />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    getExclusiveOffer.mockReturnValue(null);
  });

  it('renders the PropertyFacilities', () => {
    const { find } = render();
    expect(find('PropertyFacilities')).toHaveProp({ facilities: property.propertyFacilities, summary: true });
  });

  it('does NOT render the PropertyFacilities', () => {
    const { find } = render({ hasPropertyFacilities: false });
    expect(find('PropertyFacilities')).not.toExist();
  });

  it('does NOT render the ExclusiveOfferConditions', () => {
    const { find } = render();
    expect(find('ExclusiveOfferConditions')).not.toExist();
  });

  it('does NOT render the ExclusiveContactDetails', () => {
    const { find } = render();
    expect(find('ExclusiveContactDetails')).not.toExist();
  });

  it('renders the favourites button', () => {
    const { find } = render();
    expect(find('MemberFavourites')).toExist();
  });

  it('renders <ShareProperty />', () => {
    const { find } = render();
    expect(find('ShareProperty')).toExist();
  });

  it('passes props to PropertyMapStatic', () => {
    const { find } = render();
    expect(find('PropertyMapStatic')).toHaveProp({
      locationName: property.name,
      latitude: property.latitude,
      longitude: property.longitude,
    });
  });

  describe('Renovations', () => {
    describe('with renovations', () => {
      it('renders the renovations text block', () => {
        const { find } = render();
        expect(find('Renovations')).toHaveProp({ renovations: property.renovations });
      });
    });

    describe('without renovations', () => {
      beforeEach(() => {
        property = property = {
          address: {
            streetAddress: ['Plot 109, Malula Village', 'Moshi-Arusha Road'],
            suburb: 'Arusha',
            state: 'Arusha',
            postcode: '1234',
            country: 'Tanzania',
            countryCode: 'TZ',
          },
        };
      });

      it('does not render the renovations text block', () => {
        const { find } = render({ property: property });
        expect(find('Renovations')).not.toExist();
      });
    });

    describe('when renovations is undefined', () => {
      const propertyWithoutRenovations = { ...property, renovations: 'undefined' };

      it('does not render the renovations text block', () => {
        const { find } = render({ property: propertyWithoutRenovations });
        expect(find('Renovations')).not.toExist();
      });
    });
  });

  describe('when in exclusive offer mode', () => {
    beforeEach(() => {
      getExclusiveOffer.mockReturnValue({ exclusive: true });
    });

    it('renders the ExclusiveContactDetails', () => {
      const { find } = render();
      expect(find('ExclusiveContactDetails')).toExist();
    });

    it('renders the ExclusiveOfferConditions', () => {
      const { find } = render();
      expect(find('ExclusiveOfferConditions')).toExist();
    });
  });
});
