import React, { useCallback, useState } from 'react';
import PropTypes from 'prop-types';
import styled from '@emotion/styled';
import { useSelector } from 'react-redux';
import { getIsExclusive } from 'store/property/propertySelectors';
import { getExclusiveOffer } from 'store/exclusiveOffer/exclusiveOfferSelectors';
import { Wrapper, Flex, Box, Heading, Hide } from '@qga/roo-ui/components';
import PropertyMapStatic from 'components/PropertyPage/PropertyMapStatic';
import PropertyFacilities from 'components/PropertyPage/PropertyFacilities';
import { useDataLayer } from 'hooks/useDataLayer';
import Renovations from 'components/PropertyPage/Renovations';
import MemberFavourites from 'components/PropertyPage/MemberFavourites';
import ShareProperty from 'components/PropertyPage/ShareProperty';
import { useBreakpoints } from 'hooks/useBreakpoints';
import ExclusiveOfferConditions from 'components/PropertyPage/ExclusiveOfferConditions';
import ExclusiveContactDetails from 'components/PropertyPage/ExclusiveContactDetails';

const FacilitiesWrapper = styled(Box)`
  flex-grow: 1;
`;

const PropertySummary = ({ property, hasPropertyFacilities, menuItems, menuRefPositionOffset }) => {
  const isExclusive = useSelector(getIsExclusive);
  const exclusiveOffer = useSelector(getExclusiveOffer);
  const { isLessThanBreakpoint } = useBreakpoints();
  const isMobile = isLessThanBreakpoint(1);
  const { emitInteractionEvent } = useDataLayer();

  const [activeSection, setActiveSection] = useState(menuItems?.[0]);
  const parsedMenuItems = menuItems.filter(({ isExclusive: isItemExclusive }) => isExclusive || !isItemExclusive);
  const menuItem = parsedMenuItems.find((item) => item.name === 'property-policies');

  /* eslint-disable-next-line no-unused-vars */
  const handleOnClick = useCallback(
    () => () => {
      if (activeSection !== menuItem?.name) {
        setActiveSection(menuItem);

        scrollTo({
          behavior: 'instant',
          top: menuItem.ref.current.offsetTop + menuRefPositionOffset,
        });
      }

      emitInteractionEvent({ type: 'Check policy', value: 'Check Policy Link Selected' });
    },
    [parsedMenuItems, emitInteractionEvent], // eslint-disable-line react-hooks/exhaustive-deps
  );

  return (
    <Wrapper borderBottom={1} borderColor="greys.alto" px={[3, 3, 0]}>
      <Flex flex="1 1 auto" flexDirection={['column', 'column', 'row']} mb={[4, 4, 6]}>
        <FacilitiesWrapper borderBottom={[1, 1, 0]} borderColor="greys.alto" pb={3}>
          {exclusiveOffer && (
            <>
              <ExclusiveOfferConditions />
              <Hide md lg xl>
                <ExclusiveContactDetails />
              </Hide>
            </>
          )}
          {hasPropertyFacilities && (
            <>
              <Heading.h3 color="greys.charcoal" fontWeight={['bold', 'bold', 'normal']} fontSize={['base', 'base', 'lg']}>
                Most popular facilities
              </Heading.h3>
              <PropertyFacilities facilities={property.propertyFacilities} summary={isMobile} interactionEventValue="Top Link Selected" />
            </>
          )}
        </FacilitiesWrapper>
        <Flex flexDirection="column" minWidth={['100%', '100%', '400px']}>
          <Flex pb={[0, 0, 4]} pt={4} alignItems="center">
            <MemberFavourites />
            <ShareProperty />
          </Flex>
          <PropertyMapStatic
            locationName={property.name}
            latitude={property.latitude}
            longitude={property.longitude}
            address={property.address}
            width={[640, 640, 400]}
            height={[400, 400, 220]}
          />
        </Flex>
      </Flex>
      {property.renovations && property.renovations !== 'undefined' && (
        <Box>
          <Renovations renovations={property.renovations} />
        </Box>
      )}
    </Wrapper>
  );
};

PropertySummary.propTypes = {
  property: PropTypes.object.isRequired,
  hasPropertyFacilities: PropTypes.bool.isRequired,
  menuItems: PropTypes.array.isRequired,
  menuRefPositionOffset: PropTypes.number,
};

PropertySummary.defaultProps = {
  menuRefPositionOffset: 120,
};

export default PropertySummary;
