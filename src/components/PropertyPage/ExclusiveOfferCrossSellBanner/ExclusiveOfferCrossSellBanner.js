import React from 'react';
import { useSelector } from 'react-redux';
import { useRouter } from 'next/router';
import PageBlock from 'components/PageBlock';
import { Box, DetailedBanner } from '@qga/roo-ui/components';
import { iconFoodWine } from '@qga/roo-ui/assets';
import { useDataLayer } from 'hooks/useDataLayer';
import { getIsExclusiveOfferAvailable } from 'store/exclusiveOffer/exclusiveOfferSelectors';
import { getIsExclusive, getPropertyId } from 'store/property/propertySelectors';
import { getQueryParams } from 'store/router/routerSelectors';

const ExclusiveOfferCrossSellBanner = () => {
  const { emitInteractionEvent } = useDataLayer();
  const isExclusiveOfferAvailable = useSelector(getIsExclusiveOfferAvailable);
  const isExclusive = useSelector(getIsExclusive);
  const propertyId = useSelector(getPropertyId);

  const queryParams = useSelector(getQueryParams);
  const router = useRouter();

  const bannerIcon = { src: iconFoodWine, alt: 'Food & wine logo' };
  const bannerCta = { label: 'View Exclusive Offers', type: 'text', iconName: 'arrowForward' };
  const exclusiveOffersUrl = `/properties/${propertyId}/exclusive-offers`;

  const handleBannerClick = () => {
    emitInteractionEvent({ type: 'Exclusive Offers Cross Sell Banner', value: 'View Exclusive Offers Selected' });
    router.push({
      pathname: exclusiveOffersUrl,
      query: queryParams,
    });
  };

  if (isExclusive || !isExclusiveOfferAvailable) return null;

  return (
    <PageBlock backgroundColor="greys.porcelain" py={[4, 6]}>
      <DetailedBanner
        icon={bannerIcon}
        heading="Want to treat yourself with extra special inclusions & great savings?"
        cta={bannerCta}
        layout="row"
        onClick={handleBannerClick}
      >
        <Box>There are limited time special offers available for this hotel for specific dates or nights stay.</Box>
      </DetailedBanner>
    </PageBlock>
  );
};

export default ExclusiveOfferCrossSellBanner;
