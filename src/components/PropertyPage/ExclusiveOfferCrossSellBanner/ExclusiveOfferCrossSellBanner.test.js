import React from 'react';
import { useRouter } from 'next/router';
import ExclusiveOfferCrossSellBanner from './ExclusiveOfferCrossSellBanner';
import { useDataLayer } from 'hooks/useDataLayer';
import { getIsExclusiveOfferAvailable } from 'store/exclusiveOffer/exclusiveOfferSelectors';
import { getIsExclusive, getPropertyId } from 'store/property/propertySelectors';
import { getQueryParams } from 'store/router/routerSelectors';
import { mountUtils } from 'test-utils';

jest.mock('hooks/useDataLayer');
jest.mock('lib/hooks');
jest.mock('store/exclusiveOffer/exclusiveOfferSelectors');
jest.mock('store/property/propertySelectors');
jest.mock('store/router/routerSelectors');
const mockRouter = {
  push: jest.fn(),
};

jest.mock('next/router', () => ({
  useRouter: jest.fn(() => mockRouter),
}));

const emitInteractionEvent = jest.fn();

const render = () => mountUtils(<ExclusiveOfferCrossSellBanner />, { decorators: { store: true, theme: true } });

beforeEach(() => {
  jest.clearAllMocks();

  getIsExclusive.mockReturnValue(false);
  getPropertyId.mockReturnValue('123');
  getQueryParams.mockReturnValue({ query: 'param' });
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  useRouter.mockReturnValue(mockRouter);
});

describe('when exclusive offers are not available', () => {
  beforeEach(() => {
    getIsExclusiveOfferAvailable.mockReturnValue(false);
  });

  it('does NOT render', () => {
    const { wrapper } = render();

    expect(wrapper).toBeEmptyRender();
  });
});

describe('when exclusive offers are available', () => {
  beforeEach(() => {
    getIsExclusiveOfferAvailable.mockReturnValue(true);
  });

  describe('when viewing an exclusive offer page', () => {
    beforeEach(() => {
      getIsExclusive.mockReturnValue(true);
    });

    it('does NOT render', () => {
      const { wrapper } = render();

      expect(wrapper).toBeEmptyRender();
    });
  });

  it('has the required heading', () => {
    const { find } = render();

    expect(find('HeadingWithLink').text()).toContain('Want to treat yourself with extra special inclusions & great savings?');
  });

  it('has the required icon', () => {
    const { find } = render();

    expect(find('Image')).toHaveProp({ src: 'restaurant.svg', alt: 'Food & wine logo' });
  });

  it('has the required message', () => {
    const { find } = render();

    expect(find('ContentWithLink').text()).toContain(
      'There are limited time special offers available for this hotel for specific dates or nights stay.',
    );
  });

  it('has the call to action', () => {
    const { find } = render();

    expect(find('CustomNakedButton').text()).toContain('View Exclusive Offers');
  });

  describe('when clicking the call to action', () => {
    it('emits an event to the data layer', () => {
      const { find } = render();
      find('CustomNakedButton').simulate('click');

      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Exclusive Offers Cross Sell Banner',
        value: 'View Exclusive Offers Selected',
      });
    });

    it('redirects the user to the exclusive offers page', () => {
      const { find } = render();

      find('CustomNakedButton').simulate('click');

      expect(mockRouter.push).toHaveBeenCalledWith({ pathname: '/properties/123/exclusive-offers', query: { query: 'param' } });
    });
  });
});
