import React from 'react';
import { mountUtils } from 'test-utils';
import { fetchSiteMessage } from 'store/siteMessage/siteMessageActions';
import PollingSiteMessage from './PollingSiteMessage';
import { getSiteMessage } from 'store/siteMessage/siteMessageSelectors';
import { act } from 'react-dom/test-utils';

jest.mock('store/siteMessage/siteMessageSelectors');

jest.useFakeTimers();
jest.spyOn(global, 'setInterval');
jest.spyOn(global, 'clearInterval');

const searchSiteMessage = 'Search Message';

const propertySiteMessage = null;

const bookingSiteMessage = 'Booking Message';

beforeEach(() => {
  getSiteMessage.mockReturnValue({
    search: searchSiteMessage,
    property: propertySiteMessage,
    booking: bookingSiteMessage,
  });
});

const decorators = { theme: true, store: true };
const render = (props) => mountUtils(<PollingSiteMessage page="search" {...props} />, { decorators });

describe('with a message for the current page', () => {
  it('renders the site message for the given page', () => {
    const { find } = render();
    expect(find('Text').text()).toEqual('Search Message');
  });
});

describe('without a message for the current page', () => {
  it('does not render a message', () => {
    const { find } = render({ page: 'property' });
    expect(find('Box')).toHaveLength(0);
    expect(find('Text')).toHaveLength(0);
    expect(find('Icon')).toHaveLength(0);
  });
});

describe('fetching messages', () => {
  let setIntervalRef;

  beforeEach(() => {
    jest.clearAllMocks();
    setIntervalRef = 'setIntervalRef';
    setInterval.mockReturnValue(setIntervalRef);
  });

  it('dispatches the fetchSiteMessage action', () => {
    const { decorators } = render();
    expect(decorators.store.dispatch).toHaveBeenCalledWith(fetchSiteMessage({ page: 'search' }));
  });

  it('fetches messages on an interval and clears interval on unmount', () => {
    const { wrapper } = render();
    expect(setInterval).toHaveBeenCalledWith(expect.any(Function), 120000);
    act(() => {
      wrapper.unmount();
    });
    expect(clearInterval).toHaveBeenCalledWith(setIntervalRef);
  });
});
