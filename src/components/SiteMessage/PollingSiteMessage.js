import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useSelector, useDispatch } from 'react-redux';
import { Box, Icon, Text } from '@qga/roo-ui/components';
import { fetchSiteMessage as fetchSiteMessageFromStore } from 'store/siteMessage/siteMessageActions';
import Visibility from 'lib/browser/visibility';
import { getSiteMessage } from 'store/siteMessage/siteMessageSelectors';

const POLLING_INTERVAL = 120000;

const PollingSiteMessage = React.memo(({ page }) => {
  const messages = useSelector(getSiteMessage);
  const dispatch = useDispatch();

  useEffect(() => {
    const fetchSiteMessage = () => dispatch(fetchSiteMessageFromStore({ page }));

    fetchSiteMessage();

    const pollerRef = Visibility.every(POLLING_INTERVAL, fetchSiteMessage);

    return () => Visibility.stop(pollerRef);
  }, [page]); // eslint-disable-line react-hooks/exhaustive-deps
  // This is a componentDidMount case. We need to run this hook once per page.

  const getMessage = () => messages[page];
  const hasMessage = () => !!getMessage();

  if (!hasMessage()) return null;

  return (
    <Box p={3} textAlign="center" bg="ui.errorBackground" fontSize="sm">
      <Icon name="warning" color="ui.error" mr={3} />
      <Text>{getMessage()}</Text>{' '}
    </Box>
  );
});

PollingSiteMessage.displayName = 'PollingSiteMessage';

PollingSiteMessage.propTypes = {
  page: PropTypes.oneOf(['search', 'property', 'booking']).isRequired,
};

export default PollingSiteMessage;
