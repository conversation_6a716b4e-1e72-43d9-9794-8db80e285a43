import React from 'react';
import { mountUtils } from 'test-utils';
import Pagination from './Pagination';
import { useRouter } from 'next/router';
import { useDataLayer } from 'hooks/useDataLayer';

jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));
jest.mock('hooks/useDataLayer');

window.scrollTo = jest.fn();
const emitInteractionEvent = jest.fn();

const defaultQuery = {
  page: 2,
  sortBy: 'popularity',
};
const defaultProps = {
  pageSize: 10,
  total: 100,
  page: 2,
};

const decorators = { store: true, theme: true };
const render = (props) => mountUtils(<Pagination {...defaultProps} {...props} />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
  useRouter.mockReturnValue({ pathname: '/search', query: defaultQuery });
  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

it('renders the summary with correct props', () => {
  const { find } = render();
  expect(find('Summary')).toHaveProp({
    from: 11,
    to: 20,
    total: 100,
  });
});

it('renders the previous link with correct props', () => {
  const { findByTestId } = render();
  expect(findByTestId('prev-link')).toHaveProp({
    href: '/search?page=1&sortBy=popularity',
  });
});

it('renders the page links with correct props', () => {
  const { find } = render();
  expect(find('PageLinks')).toHaveProp({
    page: 2,
    currentQuery: defaultQuery,
    pathname: '/search',
    pageSize: 10,
    total: 100,
  });
});

it('renders the next link with correct props', () => {
  const { findByTestId } = render();
  expect(findByTestId('next-link')).toHaveProp({
    href: '/search?page=3&sortBy=popularity',
  });
});

describe('emits a gtm event when pagination buttons are clicked', () => {
  it('emits a gtm event for the next button', () => {
    const { findByTestId } = render();

    findByTestId('next-link').simulate('click');

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Page Number',
      value: 'Page 3',
    });
  });

  it('emits a gtm event for the previous button', () => {
    const { findByTestId } = render();

    findByTestId('prev-link').simulate('click');

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Page Number',
      value: 'Page 1',
    });
  });

  it('emits a gtm event for the pagination button', () => {
    const { findByTestId } = render();

    findByTestId('page-link').first().simulate('click');

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Page Number',
      value: 'Page 1',
    });
  });
});

describe('when showSortOrder is true', () => {
  describe('with a valid sort order', () => {
    it('shows the sort order when the order matches', () => {
      const { findByTestId } = render({ showSortOrder: true });
      expect(findByTestId('sorted-by')).toHaveText('Sorted by Popularity');
    });
  });

  describe('with an invalid sort order', () => {
    it('does NOT the sort order when the order matches', () => {
      useRouter.mockReturnValue({ pathname: '/search', query: { page: 2, sortBy: null } });
      const { findByTestId } = render({ showSortOrder: true });
      expect(findByTestId('sorted-by')).not.toExist();
    });
  });
});
