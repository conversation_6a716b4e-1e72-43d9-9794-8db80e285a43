import React from 'react';
import { mountUtils } from 'test-utils';
import CampaignMessaging from './CampaignMessaging';
import CampaignMessageLink from './CampaignMessageLink';
import {
  getCampaignName,
  getCampaignTitle,
  getCampaignMessage,
  getCampaignUrl,
  getCampaignTracking,
} from 'store/campaign/campaignSelectors';
import * as config from 'config';

jest.mock('config');
jest.mock('store/campaign/campaignSelectors', () => ({
  getCampaignName: jest.fn(),
  getCampaignTitle: jest.fn(),
  getCampaignMessage: jest.fn(),
  getCampaignUrl: jest.fn(),
  getCampaignUrlLabel: jest.fn(),
  getCampaignTracking: jest.fn(),
}));
jest.mock('store/ui/uiSelectors');
jest.mock('store/campaign/campaignSelectors');

const decorators = { router: true, store: true, theme: true };
const render = (props) => mountUtils(<CampaignMessaging {...props} />, { decorators });

describe('when there is no active campaign', () => {
  beforeEach(() => {
    config.CAMPAIGN_BANNER_ENABLED = true;
    jest.resetAllMocks();
    getCampaignName.mockReturnValue(null);
    getCampaignTitle.mockReturnValue(null);
    getCampaignMessage.mockReturnValue(null);
    getCampaignUrl.mockReturnValue(null);
    getCampaignTracking.mockReturnValue(null);
  });

  describe('and showDefaultMessage is true', () => {
    it('does display the title', () => {
      const { findByTestId } = render({ showDefaultMessage: true });
      expect(findByTestId('title')).toHaveText('Enjoy more Classic Hotel Rewards');
    });

    it('does display the message', () => {
      const { findByTestId } = render({ showDefaultMessage: true });
      expect(findByTestId('message')).toHaveText('when you use points.');
    });
  });

  describe('and showDefaultMessage is false', () => {
    it('does not display the title', () => {
      const { findByTestId } = render({ showDefaultMessage: false });
      expect(findByTestId('title')).not.toExist();
    });
  });
});

describe('when there is an active campaign', () => {
  beforeEach(() => {
    config.CAMPAIGN_BANNER_ENABLED = true;
    jest.resetAllMocks();
    getCampaignTitle.mockReturnValue('7 day hotel mega sale');
    getCampaignMessage.mockReturnValue('Earn 3 Qantas Points^ per A$1 spent');
    getCampaignUrl.mockReturnValue(null);
  });

  it('renders the CampaignMessageLink', () => {
    const { find } = render();
    expect(find(CampaignMessageLink)).toExist();
  });

  it('displays the title', () => {
    const { findByTestId } = render();
    expect(findByTestId('title')).toHaveText('7 day hotel mega sale');
  });

  it('displays the message on desktop only', () => {
    const { findByTestId, find } = render();
    expect(findByTestId('message')).toHaveText('Earn 3 Qantas Points^ per A$1 spent');
    expect(find('BannerHide[xs] [data-testid="message"]')).toExist();
  });

  it('does NOT display the message if the message text is blank', () => {
    getCampaignMessage.mockReturnValue('');
    const { find } = render();
    expect(find('BannerHide[xs]')).not.toExist();
    expect(find('[data-testid="message"]')).not.toExist();
  });

  it('does NOT render if CAMPAIGN_BANNER_ENABLED is false', () => {
    config.CAMPAIGN_BANNER_ENABLED = false;
    const { find, findByTestId } = render();
    expect(find('CampaignMessageLink')).not.toExist();
    expect(findByTestId('title')).not.toExist();
    expect(findByTestId('message')).not.toExist();
  });
});

describe('when there is a CTA campaign', () => {
  beforeEach(() => {
    config.CAMPAIGN_BANNER_ENABLED = true;
    jest.resetAllMocks();
    getCampaignTitle.mockReturnValue('7 day hotel mega sale');
    getCampaignMessage.mockReturnValue('Earn 3 Qantas Points^ per A$1 spent');
    getCampaignUrl.mockReturnValue('qantas.com');
  });

  it('renders the CampaignMessageLink', () => {
    const { find } = render();
    expect(find(CampaignMessageLink)).toExist();
  });

  it('displays the title', () => {
    const { findByTestId } = render();
    expect(findByTestId('title')).toHaveText('7 day hotel mega sale');
  });

  it('displays the message on desktop only', () => {
    const { findByTestId, find } = render();
    expect(findByTestId('message')).toHaveText('Earn 3 Qantas Points^ per A$1 spent');
    expect(find('BannerHide[xs] [data-testid="message"]')).toExist();
  });

  it('does NOT display the message if the message text is blank', () => {
    getCampaignUrl.mockReturnValue(undefined);
    getCampaignMessage.mockReturnValue('');
    const { find } = render();
    expect(find('BannerHide[xs]')).not.toExist();
    expect(find('[data-testid="message"]')).not.toExist();
  });

  it('does NOT render if CAMPAIGN_BANNER_ENABLED is false', () => {
    config.CAMPAIGN_BANNER_ENABLED = false;
    const { wrapper } = render();
    expect(wrapper.html()).toBeNull();
  });
});
