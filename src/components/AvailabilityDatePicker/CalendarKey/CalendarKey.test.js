import React from 'react';
import { mountUtils } from 'test-utils';
import CalendarKey from './CalendarKey';

const props = {
  month: 7,
  year: 2018,
  weekdayNames: ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Sabtu'],
};

const render = () => mountUtils(<CalendarKey {...props} />, { decorators: { store: true, theme: true } });

describe('CalendarKey', () => {
  it('renders the names of the days of the week', () => {
    const { findByText } = render();
    props.weekdayNames.forEach((name) => {
      expect(findByText(name)).toExist();
    });
  });

  it('distributes the width equally', () => {
    expect(render().find('[data-testid="weekday-box"]').at(0).prop('width')).toEqual('calc(100% / 7)');
  });
});
