import React from 'react';
import { act } from 'react-dom/test-utils';
import { mountUtils } from 'test-utils';
import { format, parse } from 'lib/date';
import range from 'lodash/range';
import { addDays } from 'date-fns';
import { FOCUS } from 'components/DatePicker/constants';
import Calendar from './Calendar';
import { getCalendarStays } from 'store/calendar/calendarSelectors';
import { useBreakpoints } from 'hooks/useBreakpoints';

jest.mock('store/calendar/calendarSelectors');
jest.mock('hooks/useBreakpoints');

const dateToString = (date) => (date instanceof Date ? format(date, 'yyyy-MM-dd') : date);
const rangeInclusive = (start = 0, end = 0) => range(start, end + 1);

const defaultProps = {
  minDate: parse('2018-07-01'),
  onRangeSelected: jest.fn,
  monthsToDisplay: 1,
  stacked: true,
  focusedInput: FOCUS.START_DATE,
};

const sortedAvailabilityStays = [
  { available: true, date: '2018-07-15', 'min-stay': 2 },
  { available: true, date: '2018-07-16', 'min-stay': 2 },
  { available: true, date: '2018-07-17', 'min-stay': 3 },
  { available: true, date: '2018-11-15', 'min-stay': 3 },
  { available: true, date: '2018-11-16', 'min-stay': 3 },
  { available: true, date: '2018-11-17', 'min-stay': 3 },
  { available: true, date: '2022-12-15', 'min-stay': 3 },
  { available: true, date: '2022-12-17', 'min-stay': 3 },
];

const render = (props) => mountUtils(<Calendar {...defaultProps} {...props} />, { decorators: { store: true } });

beforeEach(() => {
  getCalendarStays.mockReturnValue(sortedAvailabilityStays);
  useBreakpoints.mockReturnValue({ isLessThanBreakpoint: () => false });
});

describe('when two dates are set', () => {
  describe('and those dates are in the same month as the minDate', () => {
    const startDate = parse('2018-07-15');
    const endDate = parse('2018-07-17');

    it('displays the correct month', () => {
      const { find } = render({ startDate, endDate });
      const MonthWrapper = find('MonthWrapper');
      expect(MonthWrapper.find('Text').first().text()).toEqual('Jul 2018');
    });
  });

  describe('and those dates are in advance of the minDate', () => {
    const startDate = parse('2018-11-15');
    const endDate = parse('2018-11-17');

    it('displays the correct month', () => {
      const { find } = render({ startDate, endDate });
      const MonthWrapper = find('MonthWrapper');
      expect(MonthWrapper.find('Text').first().text()).toEqual('Nov 2018');
    });
  });
});

describe('when changing months', () => {
  // TODO: implement tests
});

describe('preselected range', () => {
  const startDate = parse('2018-07-15');
  const endDate = parse('2018-07-17');

  it('highlights dates in range', () => {
    const { find } = render({ startDate, endDate });
    rangeInclusive(15, 17).forEach((index) => {
      const day = find('CalendarDay').at(index - 1);
      expect(day.props()).toEqual(expect.objectContaining({ highlighted: true }));
    });
  });

  it('selects start & end dates', () => {
    const { find } = render({ startDate, endDate });
    const day15 = find('CalendarDay').at(14);
    const day17 = find('CalendarDay').at(16);

    expect(day15.props()).toEqual(expect.objectContaining({ selected: true }));
    expect(day17.props()).toEqual(expect.objectContaining({ selected: true }));
  });
});

describe('changing month via keyboard', () => {
  const currentYear = new Date().getFullYear();

  it('changes to the next month when navigating', () => {
    const endOfMonthProps = {
      ...defaultProps,
      startDate: parse(`${currentYear}-06-28`),
      endDate: parse(`${currentYear}-06-30`),
      initialDisplayDate: parse(`${currentYear}-03-01`),
    };

    const nextWeek = addDays(endOfMonthProps.endDate, 7);
    const { find, wrapper } = render({ ...endOfMonthProps });

    let month = find('CalendarMonth');
    expect(month.prop('month')).toEqual(5);

    act(() => {
      month.prop('onChangeMonth')(nextWeek);
    });
    wrapper.update();

    month = find('CalendarMonth');
    expect(month.prop('month')).toEqual(6);
    expect(month.prop('year')).toEqual(currentYear);
  });

  it('changes to the previous month when navigating', () => {
    const startOfMonthProps = {
      ...defaultProps,
      startDate: parse(`${currentYear}-06-01`),
      endDate: parse(`${currentYear}-06-03`),
      initialDisplayDate: parse(`${currentYear}-03-01`),
    };

    const previousWeek = addDays(startOfMonthProps.startDate, -7);
    const { find, wrapper } = render({ ...startOfMonthProps });

    let month = find('CalendarMonth');
    expect(month.prop('month')).toEqual(5);

    act(() => {
      month.prop('onChangeMonth')(previousWeek);
    });
    wrapper.update();

    month = find('CalendarMonth');
    expect(month.prop('month')).toEqual(4);
    expect(month.prop('year')).toEqual(currentYear);
  });

  it('changes to a new year when navigating', () => {
    const endOfYearProps = {
      ...defaultProps,
      startDate: parse(`${currentYear}-12-28`),
      endDate: parse(`${currentYear}-12-30`),
      initialDisplayDate: parse(`${currentYear}-12-01`),
    };

    const nextWeek = addDays(endOfYearProps.endDate, 7);
    const { find, wrapper } = render({ ...endOfYearProps });

    let month = find('CalendarMonth');
    expect(month.prop('year')).toEqual(currentYear);

    act(() => {
      month.prop('onChangeMonth')(nextWeek);
    });
    wrapper.update();

    month = find('CalendarMonth');
    expect(month.prop('year')).toEqual(currentYear + 1);
  });
});

describe('with start and end date', () => {
  let onChangeDates;
  let props;

  beforeEach(() => {
    onChangeDates = jest.fn();
    props = {
      onChangeDates,
      startDate: parse('2018-07-02'),
      endDate: parse('2018-07-03'),
      initialDisplayDate: parse('2018-07-02'),
    };
  });

  describe('when the start date is focused', () => {
    describe('and selected date is before the end date', () => {
      it('sets the start date and retains the end date', () => {
        const { find } = render({ ...props, focusedInput: FOCUS.START_DATE });
        find('CalendarDay').at(0).find('button').simulate('click');
        expect(dateToString(onChangeDates.mock.calls[0][0].startDate)).toEqual('2018-07-01');
        expect(dateToString(onChangeDates.mock.calls[0][0].endDate)).toEqual(null);
      });
    });

    describe('and selected date is after the end date', () => {
      it('sets the start date and resets end date', () => {
        const { find } = render({ ...props, focusedInput: FOCUS.START_DATE });
        find('CalendarDay').at(4).find('button').simulate('click');
        expect(dateToString(onChangeDates.mock.calls[0][0].startDate)).toEqual('2018-07-05');
        expect(dateToString(onChangeDates.mock.calls[0][0].endDate)).toEqual(null);
      });
    });
  });

  describe('when the end date is focused', () => {
    describe('and selected date is before the start date', () => {
      it('sets the start date and resets the end date', () => {
        const { find } = render({ ...props, focusedInput: FOCUS.END_DATE });
        find('CalendarDay').at(0).find('button').simulate('click');
        expect(dateToString(onChangeDates.mock.calls[0][0].startDate)).toEqual('2018-07-01');
        expect(dateToString(onChangeDates.mock.calls[0][0].endDate)).toEqual(null);
      });
    });

    describe('and selected date is after the start date', () => {
      it('sets the end date', () => {
        const { find } = render({ ...props, focusedInput: FOCUS.END_DATE });
        find('CalendarDay').at(19).find('button').simulate('click');
        expect(dateToString(onChangeDates.mock.calls[0][0].startDate)).toEqual('2018-07-20');
        expect(dateToString(onChangeDates.mock.calls[0][0].endDate)).toEqual(null);
      });
    });
  });

  describe('Neither are focused', () => {
    describe('and selected date is before the start date', () => {
      it('sets the start date and resets the end date', () => {
        const { find } = render({ ...props, focusedInput: FOCUS.NONE });
        find('CalendarDay').at(0).find('button').simulate('click');
        expect(dateToString(onChangeDates.mock.calls[0][0].startDate)).toEqual('2018-07-01');
        expect(dateToString(onChangeDates.mock.calls[0][0].endDate)).toEqual(null);
      });
    });

    describe('and selected date is after the start date', () => {
      it('sets the start date and resets the end date', () => {
        const { find } = render({ ...props, focusedInput: FOCUS.NONE });
        find('CalendarDay').at(19).find('button').simulate('click');
        expect(dateToString(onChangeDates.mock.calls[0][0].startDate)).toEqual('2018-07-20');
        expect(dateToString(onChangeDates.mock.calls[0][0].endDate)).toEqual(null);
      });
    });
  });
});

describe('when only start date is set', () => {
  let onChangeDates;
  let props;

  beforeEach(() => {
    onChangeDates = jest.fn();
    props = {
      onChangeDates,
      startDate: parse('2018-07-15'),
      initialDisplayDate: parse('2018-07-02'),
    };
  });

  describe('and the start date is focused', () => {
    it('selects start date', () => {
      const { find } = render({ focusedInput: FOCUS.START_DATE, ...props });
      expect(find('CalendarDay').at(14).props()).toEqual(expect.objectContaining({ selected: true }));
    });

    describe('with mouse on a past date', () => {
      it('does not highlight range', () => {
        const { find } = render({ focusedInput: FOCUS.START_DATE, ...props });
        find('CalendarDay').at(10).find('button').simulate('mouseEnter');
        expect(find('CalendarDay').at(12).props()).toEqual(expect.objectContaining({ highlighted: false }));
        expect(find('CalendarDay').at(13).props()).toEqual(expect.objectContaining({ highlighted: false }));
      });
    });

    describe('with mouse on a future date', () => {
      it('does not highlight the range', () => {
        const { find } = render({ focusedInput: FOCUS.START_DATE, ...props });
        find('CalendarDay').at(17).find('button').simulate('mouseEnter');
        expect(find('CalendarDay').at(15).props()).toEqual(expect.objectContaining({ highlighted: false }));
        expect(find('CalendarDay').at(16).props()).toEqual(expect.objectContaining({ highlighted: false }));
      });
    });

    describe('when clicking on a past date', () => {
      it('passes the date to the onChangeDates', () => {
        const { find } = render({ focusedInput: FOCUS.START_DATE, ...props });
        find('CalendarDay').at(11).find('button').simulate('click');
        expect(dateToString(onChangeDates.mock.calls[0][0].startDate)).toEqual('2018-07-12');
        expect(dateToString(onChangeDates.mock.calls[0][0].endDate)).toEqual(null);
      });
    });

    describe('when clicking on a future date', () => {
      it('passes the dates to the onChangeDates', () => {
        const { find } = render({ focusedInput: FOCUS.START_DATE, ...props });
        find('CalendarDay').at(17).find('button').simulate('click');
        expect(dateToString(onChangeDates.mock.calls[0][0].startDate)).toEqual('2018-07-15');
        expect(dateToString(onChangeDates.mock.calls[0][0].endDate)).toEqual('2018-07-18');
      });
    });
  });
  describe('and the end date is focused', () => {
    it('shows the start date as selected', () => {
      const { find } = render({ focusedInput: FOCUS.END_DATE, ...props });
      expect(find('CalendarDay').at(14).props()).toEqual(expect.objectContaining({ selected: true }));
    });

    describe('when hovering on a past date', () => {
      it('does not highlight range', () => {
        const { find } = render({ focusedInput: FOCUS.END_DATE, ...props });
        find('CalendarDay').at(10).find('button').simulate('mouseEnter');
        expect(find('CalendarDay').at(12).props()).toEqual(expect.objectContaining({ highlighted: false }));
        expect(find('CalendarDay').at(13).props()).toEqual(expect.objectContaining({ highlighted: false }));
      });
    });

    describe('when hovering on a future date', () => {
      it('highlights the range', () => {
        const { find } = render({ focusedInput: FOCUS.END_DATE, ...props });
        find('CalendarDay').at(17).find('button').simulate('mouseEnter');
        expect(find('CalendarDay').at(15).props()).toEqual(expect.objectContaining({ highlighted: true }));
        expect(find('CalendarDay').at(16).props()).toEqual(expect.objectContaining({ highlighted: true }));
      });
    });

    describe('when clicking on a past date', () => {
      it('passes the date to the onChangeDates', () => {
        const { find } = render({ focusedInput: FOCUS.END_DATE, ...props });
        find('CalendarDay').at(11).find('button').simulate('click');
        expect(dateToString(onChangeDates.mock.calls[0][0].startDate)).toEqual('2018-07-12');
        expect(dateToString(onChangeDates.mock.calls[0][0].endDate)).toEqual(null);
      });
    });

    describe('when clicking on a future date', () => {
      it('passes the dates to the onChangeDates', () => {
        const { find } = render({ focusedInput: FOCUS.END_DATE, ...props });
        find('CalendarDay').at(17).find('button').simulate('click');
        expect(dateToString(onChangeDates.mock.calls[0][0].startDate)).toEqual('2018-07-15');
        expect(dateToString(onChangeDates.mock.calls[0][0].endDate)).toEqual('2018-07-18');
      });
    });
  });

  describe('and no input is focused', () => {
    it('selects start date', () => {
      const { find } = render({ focusedInput: FOCUS.NONE, ...props });
      expect(find('CalendarDay').at(14).props()).toEqual(expect.objectContaining({ selected: true }));
    });

    describe('with mouse on a past date', () => {
      it('does not highlight range', () => {
        const { find } = render({ focusedInput: FOCUS.NONE, ...props });
        find('CalendarDay').at(10).find('button').simulate('mouseEnter');
        expect(find('CalendarDay').at(12).props()).toEqual(expect.objectContaining({ highlighted: false }));
        expect(find('CalendarDay').at(13).props()).toEqual(expect.objectContaining({ highlighted: false }));
      });
    });

    describe('with mouse on a future date', () => {
      it('does not highlight the range', () => {
        const { find } = render({ focusedInput: FOCUS.NONE, ...props });
        find('CalendarDay').at(17).find('button').simulate('mouseEnter');
        expect(find('CalendarDay').at(15).props()).toEqual(expect.objectContaining({ highlighted: true }));
        expect(find('CalendarDay').at(16).props()).toEqual(expect.objectContaining({ highlighted: true }));
      });
    });

    describe('when clicking on a past date', () => {
      it('passes the date to the onChangeDates', () => {
        const { find } = render({ focusedInput: FOCUS.NONE, ...props });
        find('CalendarDay').at(11).find('button').simulate('click');
        expect(dateToString(onChangeDates.mock.calls[0][0].startDate)).toEqual('2018-07-12');
        expect(dateToString(onChangeDates.mock.calls[0][0].endDate)).toEqual(null);
      });
    });

    describe('when clicking on a future date', () => {
      it('passes the dates to the onChangeDates', () => {
        const { find } = render({ focusedInput: FOCUS.NONE, ...props });
        find('CalendarDay').at(17).find('button').simulate('click');
        expect(dateToString(onChangeDates.mock.calls[0][0].startDate)).toEqual('2018-07-15');
        expect(dateToString(onChangeDates.mock.calls[0][0].endDate)).toEqual('2018-07-18');
      });
    });
  });
});

describe('when the boundaries prop is set', () => {
  const props = {
    initialDisplayDate: parse('2022-12-16'),
    boundaries: {
      start: parse('2022-12-15'),
      end: parse('2022-12-17'),
    },
  };

  it('disables days before the start boundary', () => {
    const { find } = render(props);
    for (let i = 0; i < 13; i++) {
      expect(find('CalendarDay').at(i).props()).toEqual(expect.objectContaining({ selectable: false, disabled: true }));
    }
  });

  it('enables days between the start and end boundaries', () => {
    const { find } = render(props);
    for (let i = 14; i < 16; i++) {
      expect(find('CalendarDay').at(i).props()).toEqual(expect.objectContaining({ selectable: true, disabled: false }));
    }
  });

  it('disables days after the end boundary', () => {
    const { find } = render(props);
    for (let i = 17; i < 31; i++) {
      expect(find('CalendarDay').at(i).props()).toEqual(expect.objectContaining({ selectable: false, disabled: true }));
    }
  });
});

describe('when the maxSelectableDays property is set to 4', () => {
  let onChangeDates;
  let props;

  beforeEach(() => {
    onChangeDates = jest.fn();
    props = {
      onChangeDates,
      startDate: parse('2018-07-02'),
      endDate: parse('2018-07-05'),
      initialDisplayDate: parse('2018-07-02'),
      maxSelectableDays: 4,
    };
  });

  describe('and the start date is focused', () => {
    describe('and an earlier date is selected', () => {
      it('resets the end date and selects the start date', () => {
        const { find } = render({ ...props, focusedInput: FOCUS.START_DATE });
        find('CalendarDay').at(0).find('button').simulate('click');
        expect(dateToString(onChangeDates.mock.calls[0][0].startDate)).toEqual('2018-07-01');
        expect(dateToString(onChangeDates.mock.calls[0][0].endDate)).toEqual(null);
      });
    });

    describe('and a date after the max selected range is selected', () => {
      it('sets the start date', () => {
        const { find } = render({ ...props, focusedInput: FOCUS.START_DATE });
        find('CalendarDay').at(30).find('button').simulate('click');
        expect(dateToString(onChangeDates.mock.calls[0][0].startDate)).toEqual('2018-07-31');
      });
    });
  });

  describe('and the end date is focused', () => {
    describe('and an later date is selected', () => {
      it('does not call onChangeDates', () => {
        const { find } = render({ ...props, focusedInput: FOCUS.END_DATE });
        find('CalendarDay').at(30).find('button').simulate('click');
        expect(onChangeDates).not.toHaveBeenCalled();
      });
    });
  });

  describe('Neither are focused', () => {
    describe('and the selected dates are more than the max selected range', () => {
      it('sets the start date and resets the end date', () => {
        const { find } = render({ ...props, focusedInput: FOCUS.NONE });
        find('CalendarDay').at(0).find('button').simulate('click');
        find('CalendarDay').at(30).find('button').simulate('click');
        expect(dateToString(onChangeDates.mock.calls[0][0].startDate)).toEqual('2018-07-01');
        expect(dateToString(onChangeDates.mock.calls[0][0].endDate)).toEqual(null);
      });
    });
  });
});

describe('when the maxSelectableDays property is set to null', () => {
  let onChangeDates;
  let props;

  beforeEach(() => {
    onChangeDates = jest.fn();
    props = {
      onChangeDates,
      startDate: parse('2018-07-02'),
      endDate: parse('2018-07-05'),
      initialDisplayDate: parse('2018-07-02'),
      maxSelectableDays: null,
    };
  });

  describe('and the start date is focused', () => {
    describe('and an date outside the default max days is selected', () => {
      it('sets the end date', () => {
        const { find } = render({ ...props, focusedInput: FOCUS.START_DATE });
        find('CalendarDay').at(0).find('button').simulate('click');
        expect(dateToString(onChangeDates.mock.calls[0][0].startDate)).toEqual('2018-07-01');
        expect(dateToString(onChangeDates.mock.calls[0][0].endDate)).toEqual(null);
      });
    });

    describe('and a date after the max selected range is selected', () => {
      it('sets the start date', () => {
        const { find } = render({ ...props, focusedInput: FOCUS.START_DATE });
        find('CalendarDay').at(30).find('button').simulate('click');
        expect(dateToString(onChangeDates.mock.calls[0][0].startDate)).toEqual('2018-07-31');
      });
    });
  });

  describe('and the end date is focused', () => {
    describe('and an later date is selected', () => {
      it('calls onChangeDates', () => {
        const { find } = render({ ...props, focusedInput: FOCUS.END_DATE });
        find('CalendarDay').at(30).find('button').simulate('click');
        expect(onChangeDates).toHaveBeenCalled();
      });
    });
  });

  describe('Neither are focused', () => {
    it('sets the start date and resets the end date', () => {
      const { find } = render({ ...props, focusedInput: FOCUS.NONE });
      find('CalendarDay').at(0).find('button').simulate('click');
      find('CalendarDay').at(30).find('button').simulate('click');
      expect(dateToString(onChangeDates.mock.calls[0][0].startDate)).toEqual('2018-07-01');
      expect(dateToString(onChangeDates.mock.calls[0][0].endDate)).toEqual(null);
    });
  });
});
