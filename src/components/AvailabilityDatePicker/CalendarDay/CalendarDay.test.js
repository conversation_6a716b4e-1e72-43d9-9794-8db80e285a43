import React from 'react';
import theme from 'lib/theme';
import { mountUtils } from 'test-utils';
import { useBreakpoints } from 'hooks/useBreakpoints';
import { CalendarDay, CalendarEmptyDay } from './';

jest.mock('hooks/useBreakpoints', () => ({ useBreakpoints: jest.fn() }));
jest.mock('react-popper', () => ({
  usePopper: () => ({
    styles: {},
    attributes: {},
  }),
}));

const defaultProps = { time: '2021-01-18' };
const decorators = { theme: true };
const render = (props) =>
  mountUtils(
    <CalendarDay {...defaultProps} {...props}>
      7
    </CalendarDay>,
    { decorators },
  );

describe('<CalendarDay/>', () => {
  beforeEach(() => {
    useBreakpoints.mockReturnValue({ isLessThanBreakpoint: () => false });
  });

  describe('selected', () => {
    it('passes the selected status to the wrapper element', () => {
      const { find } = render({ selected: true });
      expect(find('DayWrapper').props().selected).toEqual(true);
    });

    it('passes the selected status to the button element', () => {
      const { find } = render({ selected: true });
      expect(find('Button').props().selected).toEqual(true);
    });

    it('renders with the correct colors', () => {
      const { find } = render({ selected: true });
      expect(find('Button')).toHaveStyleRule(
        'background',
        'linear-gradient(90deg,rgb(222 222 222) 0%,rgb(222 222 222) 50%,rgba(0,0,0,0) 50%,rgba(0,0,0,0) 100%)',
      );
      expect(find('Button')).toHaveStyleRule('background-color', 'transparent', { target: ':hover' });
    });
  });

  describe('selectable', () => {
    it('renders with the correct colors', () => {
      const { find } = render({ selectable: true });
      expect(find('Button')).not.toHaveStyleRule('background');
      expect(find('Button')).toHaveStyleRule('background-color', 'transparent', { target: ':hover' });
    });
  });

  describe('highlighted', () => {
    it('renders with the correct colors', () => {
      const { find } = render({ highlighted: true });
      expect(find('Button')).toHaveStyleRule('background', theme.colors.greys.alto);
      expect(find('Button')).toHaveStyleRule('background-color', 'transparent', { target: ':hover' });
    });
  });

  describe('selected and highlighted', () => {
    it('renders with the correct colors', () => {
      const { find } = render({ selected: true, highlighted: true });

      expect(find('Button')).toHaveStyleRule(
        'background',
        'linear-gradient(90deg,rgb(222 222 222) 0%,rgb(222 222 222) 50%,rgba(0,0,0,0) 50%,rgba(0,0,0,0) 100%)',
      );
      expect(find('Button')).toHaveStyleRule('background-color', 'transparent', { target: ':hover' });
    });
  });

  describe('disabled', () => {
    it('renders with the correct colors', () => {
      const { find } = render({ disabled: true, selectable: false });
      expect(find('Button')).toHaveStyleRule('background-color', 'transparent', { target: ':disabled' });
    });
    it('renders with the correct text decoration', () => {
      const { find } = render({ disabled: true, selectable: false });
      expect(find('Button')).toHaveStyleRule('text-decoration', 'line-through', { target: ':disabled' });
    });
  });
});

describe('<CalendarEmptyDay/>', () => {
  it('renders without border color', () => {
    const renderEmptyDay = () => mountUtils(<CalendarEmptyDay />, { decorators });
    const { wrapper } = renderEmptyDay();
    expect(wrapper).toHaveStyleRule('border-color', 'transparent');
  });
});

describe('when selecting a day that meets the min stay requirement of', () => {
  beforeEach(() => {
    useBreakpoints.mockReturnValue({ isLessThanBreakpoint: () => true });
  });
  describe('invalid date', () => {
    it('it renders the tooltip', () => {
      const { find } = render({
        isMinStay: false,
        selected: false,
        disabled: true,
        startDate: { isStartDate: true, startDateMinStay: 2 },
      });
      find('Button').simulate('click');

      expect(find('MinStayMessageWrapper')).toHaveText('2 nights minimum');
    });
  });

  describe('valid date', () => {
    it('does not render the tooltip with min stay message', () => {
      const { find } = render({
        isMinStay: false,
        selected: false,
        disabled: true,
        startDate: { isStartDate: false, startDateMinStay: 2 },
      });
      find('Button').simulate('click');

      expect(find('MinStayMessageWrapper')).not.toHaveText('2 nights minimum');
    });
  });
});
