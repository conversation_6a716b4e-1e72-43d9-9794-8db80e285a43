import React from 'react';
import CancellationRefundSummary from './CancellationRefundSummary';
import { mountUtils } from 'test-utils';
import { addDays, subDays, format as formatDate } from 'date-fns';
import { DISPLAY_DATE_FORMAT } from 'config';

const yesterday = subDays(new Date(), 1);
const tomorrow = addDays(new Date(), 1);
const nextWeek = addDays(new Date(), 7);

const nonRefundableCancellationPolicy = {
  isNonrefundable: true,
  description: 'Non Refundable',
  cancellationWindows: [],
};

const refundableCancellationPolicy = {
  isNonrefundable: false,
  description: 'Refundable',
  cancellationWindows: [
    {
      startTime: tomorrow.toString(),
      formattedBeforeDate: formatDate(tomorrow, DISPLAY_DATE_FORMAT),
      nights: '1',
    },
  ],
};

// intentionally has windows in reverse order to test sorting
const refundableCancellationPolicyWithMultipleWindows = {
  isNonrefundable: false,
  description: 'Refundable',
  cancellationWindows: [
    {
      startTime: nextWeek.toString(),
      formattedBeforeDate: formatDate(nextWeek, DISPLAY_DATE_FORMAT),
      nights: '1',
    },
    {
      startTime: tomorrow.toString(),
      formattedBeforeDate: formatDate(tomorrow, DISPLAY_DATE_FORMAT),
      nights: '1',
    },
  ],
};

const refundableCancellationPolicyWithCurrentWindow = {
  isNonrefundable: false,
  description: 'Refundable',
  cancellationWindows: [
    {
      startTime: yesterday.toString(),
      formattedBeforeDate: formatDate(yesterday, DISPLAY_DATE_FORMAT),
      nights: '1',
    },
  ],
};

const createCancellationPolicyWithCurrentPenalties = (penalties) => ({
  isNonrefundable: false,
  description: 'description',
  cancellationWindows: [
    {
      ...penalties,
      startTime: yesterday.toString(),
      formattedBeforeDate: formatDate(yesterday, DISPLAY_DATE_FORMAT),
    },
  ],
});

const decorators = { theme: true, store: true };
const render = (props = {}) => mountUtils(<CancellationRefundSummary {...props} />, { decorators });

describe('when isNonrefundable is true', () => {
  it('Displays the "Non-refundable" message', () => {
    const { findByTestId } = render({ cancellationPolicy: nonRefundableCancellationPolicy });
    expect(findByTestId('cancellation-policy-message')).toHaveText('Non-refundable');
  });

  it('does NOT display the "freeCancellation" icon', () => {
    const { find } = render({ cancellationPolicy: nonRefundableCancellationPolicy });
    expect(find('Icon')).not.toExist();
  });
});

describe('when isNonRefundable is false', () => {
  it('displays the "Free Cancellation" message', () => {
    const { findByTestId } = render({ cancellationPolicy: refundableCancellationPolicy });
    expect(findByTestId('cancellation-policy-message')).toHaveText('Free cancellation');
  });

  it('displays the "freeCancellation" icon', () => {
    const { find } = render({ cancellationPolicy: refundableCancellationPolicy });
    expect(find('Icon')).toHaveProp({ name: 'freeCancellation', color: 'green' });
  });

  it('Displays the before date message', () => {
    const { findByTestId } = render({ cancellationPolicy: refundableCancellationPolicy });
    expect(findByTestId('free-cancellation-before-date')).toHaveText(`before ${formatDate(tomorrow, DISPLAY_DATE_FORMAT)}`);
  });

  describe('with multiple cancellation windows out of chronological order', () => {
    it('Displays the before date message for the earliest cancellation window', () => {
      const { findByTestId } = render({ cancellationPolicy: refundableCancellationPolicyWithMultipleWindows });
      expect(findByTestId('free-cancellation-before-date')).toHaveText(`before ${formatDate(tomorrow, DISPLAY_DATE_FORMAT)}`);
    });
  });

  describe('when the cancellationWindow startTime is less than now', () => {
    it('Displays the "partially-refundable" message', () => {
      const { findByTestId } = render({ cancellationPolicy: refundableCancellationPolicyWithCurrentWindow });
      expect(findByTestId('cancellation-policy-message')).toHaveText('Cancellation Policy');
    });
  });

  describe('with zero nights, zero percentage and zero fixedCost penalty on the current window', () => {
    it('displays as Free cancellation', () => {
      const { findByTestId } = render({
        cancellationPolicy: createCancellationPolicyWithCurrentPenalties({ fixedCost: '0', percentage: '0', nights: '0' }),
      });
      expect(findByTestId('cancellation-policy-message')).toHaveText('Free cancellation');
    });
  });

  [{ fixedCost: '1' }, { percentage: '10%' }, { nights: '1' }].forEach((penalty) => {
    describe(`with a penalty of ${JSON.stringify(penalty)} on the current window`, () => {
      it('displays as partially-refundable', () => {
        const { findByTestId } = render({
          cancellationPolicy: createCancellationPolicyWithCurrentPenalties(penalty),
        });
        expect(findByTestId('cancellation-policy-message')).toHaveText('Cancellation Policy');
      });
    });
  });

  it('without any penalty on the current window', () => {
    const { findByTestId } = render({ cancellationPolicy: createCancellationPolicyWithCurrentPenalties({}) });
    const cancellationPolicyMessage = findByTestId('cancellation-policy-message');
    expect(cancellationPolicyMessage).toHaveText('Free cancellation');
  });

  describe('when there is no window with a penalty', () => {
    const cancellationPolicy = {
      isNonrefundable: false,
      description: 'description',
      cancellationWindows: [
        {
          startTime: yesterday.toString(),
          formattedBeforeDate: formatDate(yesterday, DISPLAY_DATE_FORMAT),
          nights: '0',
        },
      ],
    };

    it('displays as Free cancellation', () => {
      const { findByTestId } = render({ cancellationPolicy });
      const cancellationPolicyMessage = findByTestId('cancellation-policy-message');
      expect(cancellationPolicyMessage).toHaveText('Free cancellation');
    });

    it('displays the "freeCancellation" icon', () => {
      const { find } = render({ cancellationPolicy });

      expect(find('Icon')).toHaveProp({ name: 'freeCancellation', color: 'green' });
    });

    it('does not display a before date', () => {
      const { findByTestId } = render({ cancellationPolicy });
      expect(findByTestId('free-cancellation-before-date')).not.toExist();
    });
  });
});

describe('when hideWhenNonRefundable is true', () => {
  it('does NOT display the "Non-refundable" message', () => {
    const { findByTestId } = render({ cancellationPolicy: nonRefundableCancellationPolicy, hideWhenNonRefundable: true });
    expect(findByTestId('cancellation-policy-message')).not.toExist();
  });

  it('displays the "Non-refundable" message', () => {
    const { findByTestId } = render({ cancellationPolicy: refundableCancellationPolicy, hideWhenNonRefundable: true });
    expect(findByTestId('cancellation-policy-message')).toExist();
  });
});

describe('when hideBeforeDate is true', () => {
  it('does NOT display the cancellation before date message', () => {
    const { findByTestId } = render({ cancellationPolicy: refundableCancellationPolicy, hideBeforeDate: true });
    expect(findByTestId('free-cancellation-before-date')).not.toExist();
  });

  it('does NOT display the cancellation before date message with multiple cancellation windows', () => {
    const { findByTestId } = render({ cancellationPolicy: refundableCancellationPolicyWithMultipleWindows, hideBeforeDate: true });
    expect(findByTestId('free-cancellation-before-date')).not.toExist();
  });
});

describe('Clicking the Non Refundable link', () => {
  it('Calls the handleOnClick callback ', () => {
    const handleOnClick = jest.fn();
    const { findByTestId } = render({ cancellationPolicy: refundableCancellationPolicy, handleOnClick });

    findByTestId('cancellation-policy-message').simulate('click');
    expect(handleOnClick).toHaveBeenCalled();
  });
});
