import React from 'react';
import PropTypes from 'prop-types';
import { Wrapper } from '@qga/roo-ui/components';
import NonRefundable from './components/NonRefundable';
import FlexibleCancellation from './components/FlexibleCancellation';

const CancellationRefundSummary = ({ cancellationPolicy, fontSize, hideBeforeDate, hideWhenNonRefundable, handleOnClick, ...rest }) => {
  const { isNonrefundable, cancellationWindows } = cancellationPolicy;

  return (
    <Wrapper {...rest}>
      {isNonrefundable ? (
        <NonRefundable handleOnClick={handleOnClick} fontSize={fontSize} hideWhenNonRefundable={hideWhenNonRefundable} />
      ) : (
        <FlexibleCancellation
          cancellationWindows={cancellationWindows}
          handleOnClick={handleOnClick}
          fontSize={fontSize}
          hideBeforeDate={hideBeforeDate}
          hideWhenNonRefundable={hideWhenNonRefundable}
        />
      )}
    </Wrapper>
  );
};

CancellationRefundSummary.propTypes = {
  cancellationPolicy: PropTypes.shape({
    isNonrefundable: PropTypes.bool.isRequired,
    description: PropTypes.string.isRequired,
    cancellationWindows: PropTypes.array.isRequired,
  }).isRequired,
  fontSize: PropTypes.oneOfType([PropTypes.string, PropTypes.array]),
  hideBeforeDate: PropTypes.bool,
  hideWhenNonRefundable: PropTypes.bool,
  handleOnClick: PropTypes.func,
};

CancellationRefundSummary.defaultProps = {
  fontSize: 'base',
  hideBeforeDate: false,
  hideWhenNonRefundable: false,
  handleOnClick: null,
};

export default CancellationRefundSummary;
