import { isBefore } from 'date-fns';
import React from 'react';
import { Flex, Icon, Text } from '@qga/roo-ui/components';
import TextButton from 'components/TextButton';
import PropTypes from 'prop-types';
import { resolveFirstCancellationWindowWithPenalty } from 'components/CancellationRefundSummary/utils';

const FlexibleCancellation = ({ cancellationWindows, handleOnClick, fontSize, hideBeforeDate, hideWhenNonRefundable }) => {
  const firstCancellationWindowWithPenalty = resolveFirstCancellationWindowWithPenalty(cancellationWindows);
  const text = 'Free cancellation';

  if (!firstCancellationWindowWithPenalty || isBefore(new Date(), new Date(firstCancellationWindowWithPenalty.startTime))) {
    return (
      <Flex flexWrap="wrap" alignItems="center">
        <Icon name="freeCancellation" color="green" size={[18, 22]} mr={2} />
        {handleOnClick ? (
          <TextButton
            onClick={handleOnClick}
            fontSize={fontSize}
            fontWeight="bold"
            color="green"
            hoverColor="green"
            data-testid="cancellation-policy-message"
            pr={1}
          >
            {text}
          </TextButton>
        ) : (
          <Text fontSize={fontSize} fontWeight="bold" color="green" data-testid="cancellation-policy-message" pr={1}>
            {text}
          </Text>
        )}
        {firstCancellationWindowWithPenalty && !hideBeforeDate && (
          <Text color="greys.steel" display="block" fontSize={fontSize} data-testid="free-cancellation-before-date">
            before {firstCancellationWindowWithPenalty.formattedBeforeDate}
          </Text>
        )}
      </Flex>
    );
  } else if (hideWhenNonRefundable) {
    return null;
  } else {
    return handleOnClick ? (
      <TextButton
        onClick={handleOnClick}
        fontSize={fontSize}
        fontWeight="bold"
        color="greys.steel"
        hoverColor="greys.steel"
        data-testid="cancellation-policy-message"
      >
        Cancellation Policy
      </TextButton>
    ) : (
      <Text fontSize={fontSize} fontWeight="bold" color="greys.steel" data-testid="cancellation-policy-message">
        Cancellation Policy
      </Text>
    );
  }
};

FlexibleCancellation.propTypes = {
  cancellationWindows: PropTypes.array.isRequired,
  handleOnClick: PropTypes.func,
  fontSize: PropTypes.oneOfType([PropTypes.string, PropTypes.array]),
  hideBeforeDate: PropTypes.bool.isRequired,
  hideWhenNonRefundable: PropTypes.bool.isRequired,
};

FlexibleCancellation.defaultProps = {
  handleOnClick: null,
};

export default FlexibleCancellation;
