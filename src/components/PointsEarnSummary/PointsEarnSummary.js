import React from 'react';
import PropTypes from 'prop-types';
import { Text } from '@qga/roo-ui/components';
import PointsEarnDisplay from 'components/PointsEarnDisplay';
import { POINTS_EARN_ENABLED } from 'config';

const PointsEarnSummary = ({ total, base, fontSize, ...rest }) => {
  if (!POINTS_EARN_ENABLED || total <= 0) {
    return null;
  }

  return (
    <Text data-testid="points-earn-summary-text" color="greys.charcoal" fontSize={fontSize} textAlign="end" {...rest}>
      {'Earn '}
      <PointsEarnDisplay total={total} base={base} fontSize={fontSize} fontWeight="bold" />
      <Text color="greys.charcoal" fontSize={fontSize} fontWeight="bold">
        {' PTS^'}
      </Text>
    </Text>
  );
};

PointsEarnSummary.propTypes = {
  total: PropTypes.number,
  base: PropTypes.number,
  fontSize: PropTypes.oneOfType([PropTypes.string, PropTypes.array]),
};

PointsEarnSummary.defaultProps = {
  base: undefined,
  fontSize: 'sm',
};

export default PointsEarnSummary;
