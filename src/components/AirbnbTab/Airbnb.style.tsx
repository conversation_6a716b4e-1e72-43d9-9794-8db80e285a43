import { Link, Button, Flex } from '@qga/roo-ui/components';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { mediaQuery } from 'lib/styledSystem';

export const SubmitButton = styled(Button)`
  &:hover {
    color: ${themeGet('colors.white')};
  }
`;

export const FindOutMoreLink = styled(Link)`
  font-size: ${themeGet('fontSizes.base')};
  color: ${themeGet('colors.greys.charcoal')};
  text-decoration: underline;
`;

export const LoginContainer = styled(Flex)`
  align-items: flex-end;
  flex-grow: 1;
  align-items: center;
  padding-right: 0;
  width: 100%;

  ${mediaQuery.minWidth.sm} {
    padding-right: 1.5rem;
    width: 50%;
  }
`;
