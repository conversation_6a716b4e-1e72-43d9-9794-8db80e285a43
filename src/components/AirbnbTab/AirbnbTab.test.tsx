import React from 'react';
import { mocked, mountUtils } from 'test-utils';
import AirbnbTab from './AirbnbTab';
import { useDataLayer } from 'hooks/useDataLayer';
import { act } from 'react-dom/test-utils';
import { getIsAuthenticated } from 'store/user/userSelectors';
import { useRouter } from 'next/router';

const mockAirbnbRedirect = jest.fn();

jest.mock('hooks/useDataLayer');
jest.mock('store/user/userSelectors');
jest.mock('components/AirbnbPage/AirbnbRedirect/useAirbnbRedirect', () => {
  return jest.fn(() => ({
    handleAirbnbRedirect: mockAirbnbRedirect,
  }));
});
jest.mock('next/router', () => ({
  useRouter: jest.fn(() => ({
    isReady: jest.fn(),
  })),
}));

mountUtils.mockComponent('LocationAutocompleter');
mountUtils.mockComponent('AvailabilityDatePicker');
mountUtils.mockComponent('OccupantPicker');
mountUtils.mockComponent('FindOutMoreLink');

const emitInteractionEvent = jest.fn();

const decorators = { theme: true, store: true, router: true, helmet: true };
const render = () => mountUtils(<AirbnbTab isLandingPage={false} />, { decorators });

describe('<AirbnbTab />', () => {
  describe('when authenticated', () => {
    beforeEach(() => {
      jest.clearAllMocks();
      mocked(useRouter).mockReturnValue({ isReady: true });
      mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
      mocked(getIsAuthenticated).mockReturnValue(true);
    });

    it('renders the LocationAutocompleter', () => {
      const { find } = render();
      expect(find('LocationAutocompleter')).toExist();
    });

    it('renders the AvailabilityDatePicker', () => {
      const { find } = render();
      expect(find('AvailabilityDatePicker')).toExist();
    });

    it('renders the OccupantPicker', () => {
      const { find } = render();
      expect(find('LocationAutocompleter')).toExist();
    });

    it('renders the submit button', () => {
      const { find } = render();
      expect(find('SubmitButton')).toExist();
    });

    it('updates the search URL when the location changes to a geographic location', () => {
      const { find, wrapper } = render();

      act(() => {
        find('LocationAutocompleter').prop('updateQuery')({ location: 'Melbourne, VIC, Australia' });
      });
      wrapper.update();

      act(() => {
        find('AvailabilityDatePicker').prop('updateQuery')({ checkIn: '2022-05-29', checkOut: '2022-05-31' });
      });
      wrapper.update();

      find('SubmitButton').simulate('click');

      expect(mockAirbnbRedirect).toHaveBeenCalledWith(
        'checkin=2022-05-29&checkout=2022-05-31&guests=2&location=Melbourne%2C%20VIC%2C%20Australia',
      );
    });

    it('updates the search URL when the check in and check out dates are set', () => {
      const { find, wrapper } = render();

      act(() => {
        find('LocationAutocompleter').prop('updateQuery')({ location: 'Melbourne, VIC, Australia' });
      });
      wrapper.update();

      act(() => {
        find('AvailabilityDatePicker').prop('updateQuery')({ checkIn: '2022-05-29', checkOut: '2022-05-31' });
      });
      wrapper.update();

      find('SubmitButton').simulate('click');

      expect(mockAirbnbRedirect).toHaveBeenCalledWith(
        'checkin=2022-05-29&checkout=2022-05-31&guests=2&location=Melbourne%2C%20VIC%2C%20Australia',
      );
    });

    it('updates the search URL when the occupant picker is set', () => {
      const { find, wrapper } = render();

      act(() => {
        find('LocationAutocompleter').prop('updateQuery')({ location: 'Melbourne, VIC, Australia' });
      });
      wrapper.update();

      act(() => {
        find('AvailabilityDatePicker').prop('updateQuery')({ checkIn: '2022-05-29', checkOut: '2022-05-31' });
      });
      wrapper.update();

      act(() => {
        find('OccupantPicker').prop('updateQuery')({ children: 2 });
      });
      wrapper.update();

      find('SubmitButton').simulate('click');

      expect(mockAirbnbRedirect).toHaveBeenCalledWith(
        'checkin=2022-05-29&checkout=2022-05-31&guests=4&location=Melbourne%2C%20VIC%2C%20Australia',
      );
    });

    it('does NOT render the FieldError', () => {
      const { find } = render();
      expect(find('FieldError')).not.toExist();
    });

    it('tracks when the user updates the location search', () => {
      const { find, wrapper } = render();

      act(() => {
        find('LocationAutocompleter').prop('updateQuery')({ location: 'Melbourne, VIC, Australia' });
      });
      wrapper.update();

      expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Location Search', value: 'Melbourne, VIC, Australia Selected' });
    });

    it('tracks when the user updates the checkin and checkout dates', () => {
      const { find, wrapper } = render();

      act(() => {
        find('AvailabilityDatePicker').prop('updateQuery')({ checkIn: '2022-05-29', checkOut: '2022-05-31' });
      });
      wrapper.update();

      expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Date Calendar', value: 'Checkin Date Selected' });
      expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Date Calendar', value: 'Checkout Date Selected' });
    });

    it('tracks when the user updates the guest count', () => {
      const { find, wrapper } = render();

      act(() => {
        find('OccupantPicker').prop('updateQuery')({ adults: 1, children: 2, infants: 3 });
      });
      wrapper.update();

      expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Guests Dropdown', value: 'Guests Adults 1 Selected' });
      expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Guests Dropdown', value: 'Guests Children 2 Selected' });
      expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Guests Dropdown', value: 'Guests Infants 3 Selected' });
    });
  });

  describe('when not authenticated', () => {
    beforeEach(() => {
      jest.clearAllMocks();
      mocked(useRouter).mockReturnValue({ isReady: true });
      mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
      mocked(getIsAuthenticated).mockReturnValue(false);
    });

    it('sends correct tracking event on cta click', () => {
      const { findByTestId, wrapper } = render();

      act(() => {
        findByTestId('search-airbnb-cta').simulate('click');
      });
      wrapper.update();
      expect(findByTestId('search-airbnb-cta')).toHaveText('Continue to Frequent Flyer log in');
      expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'CTA Clicked', value: 'Continue to Frequent Flyer log in' });
    });
  });
});
