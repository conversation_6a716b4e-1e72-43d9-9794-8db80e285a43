import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { Box, Flex, NakedButton, Text } from '@qga/roo-ui/components';
import LocationAutocompleter from 'components/LocationAutocompleter';
import OccupantPicker from 'components/OccupantPicker';
import AvailabilityDatePicker from 'components/AvailabilityDatePicker';
import stringifyQueryValues from 'lib/search/stringifyQueryValues';
import { useDataLayer } from 'hooks/useDataLayer';
import capitalize from 'lodash/capitalize';
import QffWidget from 'components/QffWidget/QffWidget';
import { useIsAuthenticated } from 'lib/oauth';
import { AirbnbTab as AirbnbTabProps } from 'types/content';
import useAirbnbRedirect from 'components/AirbnbPage/AirbnbRedirect/useAirbnbRedirect';
import { SubmitButton, LoginContainer } from './Airbnb.style';
import { useSelector, useDispatch } from 'react-redux';
import { useBreakpoints } from 'hooks/useBreakpoints';
import { updateQuery } from 'store/search/searchActions';
import { getQueryParams } from 'store/router/routerSelectors';
import { addDays } from 'lib/date';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { useRouter } from 'next/router';

type SearchQueryProps = {
  location?: string;
  checkIn?: string;
  checkOut?: string;
  adults?: number;
  children?: number;
  infants?: number;
  payWith?: string;
  id?: number;
};

type ErrorPayload = {
  message?: string;
};

type SearchErrorProps = {
  location?: ErrorPayload;
  dates?: ErrorPayload;
};

const labelOptions = {
  color: 'greys.charcoal',
  fontWeight: 'bold',
  fontSize: 'sm',
  lineHeight: 1.25,
  mb: 2,
  style: { fontFamily: 'Jetstar' },
};

const mapQueryToGAPayload = (query) =>
  Object.keys(query)
    .map((inputType) => {
      switch (inputType) {
        case 'location':
          return { type: 'Location Search', value: `${query.location} Selected` };
        case 'checkIn':
          return { type: 'Date Calendar', value: 'Checkin Date Selected' };
        case 'checkOut':
          return { type: 'Date Calendar', value: 'Checkout Date Selected' };
        case 'adults':
          return { type: 'Guests Dropdown', value: `Guests Adults ${query.adults} Selected` };
        case 'children':
          return { type: 'Guests Dropdown', value: `Guests Children ${query.children} Selected` };
        case 'infants':
          return { type: 'Guests Dropdown', value: `Guests Infants ${query.infants} Selected` };
        case 'payWith':
          return { type: 'Points and Cash Toggle', value: `${capitalize(query.payWith)} Selected` };
        default:
          return null;
      }
    })
    .filter(Boolean);

const StyledNakedButton = styled(NakedButton)`
  font-size: ${themeGet('fontSizes.sm')} !important;
  line-height: 1.5 !important;
  border: none !important;
`;

const AirbnbTab = ({ isLandingPage }: AirbnbTabProps) => {
  const router = useRouter();
  const { emitInteractionEvent } = useDataLayer();
  const { isLessThanBreakpoint, isGreaterThanOrEqualToBreakpoint } = useBreakpoints();
  const dispatch = useDispatch();
  const today = new Date();
  const isMobile = isLessThanBreakpoint(0);
  const isAuthenticated = useIsAuthenticated();
  const query = useSelector(getQueryParams);
  const [searchQuery, setSearchQuery] = useState<SearchQueryProps>({
    checkIn: query.checkIn ? query.checkIn : today,
    checkOut: query.checkOut ? query.checkOut : addDays(today, 1),
  });
  useEffect(() => {
    if (router.isReady) {
      setSearchQuery({
        location: query.location ? query.location : null,
        checkIn: query.checkIn ? query.checkIn : today,
        checkOut: query.checkOut ? query.checkOut : addDays(today, 1),
        adults: query.adults ? Number(query.adults) : 2,
        children: query.children ? Number(query.children) : 0,
        infants: query.infants ? Number(query.infants) : 0,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router.isReady]);

  const [searchError, setSearchError] = useState<SearchErrorProps>({});
  const { location, checkIn, checkOut, adults, children, infants } = searchQuery;
  const checkInDate = checkIn ? new Date(checkIn) : undefined;
  const checkOutDate = checkOut ? new Date(checkOut) : undefined;
  const [selectedDates, setselectedDates] = useState({ startDate: checkInDate, endDate: checkOutDate });
  const selectedOccupants = useMemo(() => ({ adults, children, infants }), [adults, children, infants]);
  const { handleAirbnbRedirect } = useAirbnbRedirect();

  const getLimitCharacter = () => {
    let limit;

    if (isLessThanBreakpoint(0)) {
      limit = 27;
    } else if (isLessThanBreakpoint(1) && isGreaterThanOrEqualToBreakpoint(0)) {
      limit = 100;
    } else if (isLessThanBreakpoint(2) && isGreaterThanOrEqualToBreakpoint(1)) {
      limit = 30;
    } else {
      limit = 35;
    }
    return limit;
  };

  const onUpdateQuery = (payload) => {
    setSearchQuery({ ...searchQuery, ...payload });

    const queryEvents = mapQueryToGAPayload(payload);
    queryEvents.forEach((gaPayload) => {
      emitInteractionEvent(gaPayload);
    });
  };

  const onSubmitQuery = () => {
    const { location, adults = 0, children = 0, infants = 0, checkIn: checkin, checkOut: checkout } = searchQuery;
    const guests = adults + children + infants;
    const searchQueryString = stringifyQueryValues({ location, guests, checkin, checkout });

    if (isAuthenticated) emitInteractionEvent({ type: 'Successful Search', value: location || 'No location' });
    else emitInteractionEvent({ type: 'CTA Clicked', value: 'Continue to Frequent Flyer log in' });

    const payload = {
      location: searchQuery.location,
      checkIn: searchQuery.checkIn,
      checkOut: searchQuery.checkOut,
      adults: searchQuery.adults,
      children: searchQuery.children,
      infants: searchQuery.infants,
    };
    dispatch(updateQuery(payload));
    handleAirbnbRedirect(searchQueryString);
  };

  const onRouteToLocation = (payload) => {
    if (searchError) {
      setSearchError({});
    }
    onUpdateQuery({ ...payload });
  };

  const onClickOutside = useCallback(() => {
    setselectedDates({ startDate: undefined, endDate: undefined });
  }, [setselectedDates]);

  return (
    <>
      <Box>
        {!isLandingPage && (
          <Text
            fontSize={['base', 'lg']}
            fontWeight="bold"
            lineHeight={['22px', 1.25]}
            display={['block']}
            pb={6}
            style={{ fontFamily: 'Jetstar' }}
          >
            {isMobile ? 'Earn 1 Qantas Point per A$1 spent++' : 'Book an Airbnb and earn 1 Qantas Point per A$1 spent++'}
          </Text>
        )}
      </Box>
      <Flex flexWrap="wrap" position="relative">
        <Box pr={[0, 0, 3]} width={['100%', '100%', '35%']} order={1}>
          <LocationAutocompleter
            title="Where would you like to go?"
            label="Where would you like to go?"
            locationName={location}
            labelOptions={labelOptions}
            updateQuery={onRouteToLocation}
            placeholder="Enter a destination"
            error={!!searchError?.location}
            returnProperties={false}
            isOptional={true}
            limit={getLimitCharacter()}
          />
        </Box>

        <Box pr={[0, 0, 3]} pt={[4, 4, 0]} width={['100%', '100%', '35%']} order={[3, 2]} data-testid="stay-date-picker">
          <AvailabilityDatePicker
            selectedDates={selectedDates}
            labelOptions={labelOptions}
            updateQuery={onUpdateQuery}
            clearSelectedDates={onClickOutside}
            anchorX="right"
            isLimited={true}
            isOptional={true}
          />
        </Box>

        <Box pt={[4, 4, 0]} width={['100%', '100%', '30%']} order={[4, 3]} data-testid="occupant-picker">
          <OccupantPicker
            occupants={selectedOccupants}
            labelOptions={labelOptions}
            updateQuery={onUpdateQuery}
            viewThreshold={0}
            verboseInMobile
            multiroomEnabled={false}
          />
        </Box>

        <LoginContainer flexDirection={['column-reverse', 'row', 'row']} mt={[4, 6, 6]} order={isMobile ? 7 : 6}>
          <QffWidget />
        </LoginContainer>
        <Box width={['100%', '50%', '30%']} mt={6} ml="auto" order={isMobile ? 6 : 7}>
          <SubmitButton
            as={StyledNakedButton}
            variant="primary"
            onClick={onSubmitQuery}
            aria-label="Search Airbnb"
            width="100%"
            data-testid="search-airbnb-cta"
          >
            {isAuthenticated ? 'Search Airbnb' : 'Continue to Frequent Flyer log in'}
          </SubmitButton>
        </Box>
      </Flex>
    </>
  );
};

export default AirbnbTab;
