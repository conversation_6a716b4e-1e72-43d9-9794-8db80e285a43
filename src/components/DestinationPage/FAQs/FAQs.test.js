import React from 'react';
import FAQs from './FAQs';
import { mountUtils } from 'test-utils';
import { getDestination } from 'store/destination/destinationSelectors';

jest.mock('store/destination/destinationSelectors');

const render = () => mountUtils(<FAQs />, { decorators: { store: true } });

const faqsContent = [
  {
    question: 'Question One',
    mdAnswer: 'Answer One',
  },
  {
    question: 'Question Two',
    mdAnswer: 'Answer Two',
  },
  {
    question: 'Question Three',
    mdAnswer: 'Answer Three',
  },
];

describe('<FAQs />', () => {
  beforeEach(() => {
    getDestination.mockReturnValue({
      faqs: faqsContent,
    });
  });

  it('renders the heading with the correct text', () => {
    const { find } = render();

    expect(find('Heading').text()).toEqual('Frequently Asked Questions');
  });

  it('renders all questions and answers returned by the selector', () => {
    const { findByTestId } = render();
    const questionsAnswersWrapper = findByTestId('questions-answers-wrapper');

    questionsAnswersWrapper.children().map((node, index) => expect(node.props()).toEqual(faqsContent[index]));
  });

  it('should render the Q&A block', () => {
    const { findByTestId } = render();

    expect(findByTestId('question-answer-block').first()).toExist();
  });

  it('should render the question and answer', () => {
    const { find } = render();

    expect(find('Disclosure').first()).toHaveProp({
      headerTitle: 'Question One',
    });

    expect(find('Markdown').first().text()).toContain('Answer One');
  });
});
