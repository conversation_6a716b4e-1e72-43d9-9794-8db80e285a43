import React from 'react';
import { Box, Disclosure, Heading, Flex } from '@qga/roo-ui/components';
import { useSelector } from 'react-redux';
import { getDestination } from 'store/destination/destinationSelectors';
import Markdown from 'components/Markdown/Markdown';

const QuestionAnswerBlock = (questionAnswerPair) => {
  const { question, mdAnswer: answer } = questionAnswerPair;
  return (
    <Flex flexDirection="column" data-testid="question-answer-block">
      <Disclosure expand={false} headerTitle={question} mb={4} fontSize={['sm', 'base']}>
        <Markdown content={answer} />
      </Disclosure>
    </Flex>
  );
};

const FAQs = () => {
  const { faqs } = useSelector(getDestination);

  return (
    <Box width={[1, 2 / 3]} mx={'auto'} p={0} pt={10}>
      <Heading textAlign="center" pb={[5, 12]}>
        Frequently Asked Questions
      </Heading>
      <Box borderBottom="thin solid #d9d9d9" />
      <Flex flexDirection="column" data-testid="questions-answers-wrapper">
        {faqs.map((questionAnswerPair, index) => (
          <QuestionAnswerBlock key={`faq-${index}`} {...questionAnswerPair} />
        ))}
      </Flex>
    </Box>
  );
};

export default FAQs;
