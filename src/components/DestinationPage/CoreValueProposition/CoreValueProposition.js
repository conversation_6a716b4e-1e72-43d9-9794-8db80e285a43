import React from 'react';
import styled from '@emotion/styled';
import PropTypes from 'prop-types';
import { Text, Flex, Heading, Image } from '@qga/roo-ui/components';
import { noFeesImage, pointsPlusPayImage, findImage } from './CoreValuePropositionAssets';
import { themeGet } from 'styled-system';
import PageBlock from 'components/PageBlock';
import { mediaQuery } from 'lib/styledSystem';
import { HOTELS_BRAND_NAME } from 'config';

const PageBlockStyled = styled(PageBlock)`
  flex-direction: column;
  text-align: center;
  background: ${themeGet('colors.greys.porcelain')};
`;

const CoreValueWrapper = styled(Flex)`
  flex-direction: column;
  justify-content: top;
  align-items: center;
  padding: ${themeGet('space.4')};
  width: 100%;

  ${mediaQuery.minWidth.sm} {
    width: 49%;
  }

  ${mediaQuery.minWidth.md} {
    width: 32%;
  }
`;

const CoreValue = ({ image, elementDescription }) => {
  const { src, alt } = image;
  return (
    <CoreValueWrapper width={[1, 1 / 4]} mb={[1, 2]}>
      <Image src={src} alt={alt} size={50} />
      <Text fontSize="md" mt={[3, 4]} mx={[0, 3, 11]}>
        {elementDescription}
      </Text>
    </CoreValueWrapper>
  );
};

CoreValue.propTypes = {
  image: PropTypes.shape({
    src: PropTypes.string,
    alt: PropTypes.string,
  }).isRequired,
  elementDescription: PropTypes.string.isRequired,
};

const CoreValueProposition = () => (
  <PageBlockStyled mx={[0, 25]}>
    <Heading mx="auto" mb={10} fontSize="lg">
      Why book with {HOTELS_BRAND_NAME}?
    </Heading>
    <Flex flexDirection={['column', 'row']} flexWrap="wrap" justifyContent="center" mt={10} mx="auto" data-testid="elements-wrapper">
      <CoreValue image={pointsPlusPayImage} elementDescription="Earn 3 Qantas Points per $1 spent^, or use points to book**" />
      <CoreValue image={noFeesImage} elementDescription="No booking fees" />
      <CoreValue image={findImage} elementDescription="Search thousands of hotels across Australia and New Zealand" />
    </Flex>
  </PageBlockStyled>
);

export default CoreValueProposition;
