import React from 'react';
import CoreValueProposition from './CoreValueProposition';
import { mountUtils } from 'test-utils';

const cardsContent = [
  {
    image: {
      src: 'points-plus-pay.svg',
      alt: 'use points plus pay image',
    },
    elementDescription: 'Earn 3 Qantas Points per $1 spent^, or use points to book**',
  },
  {
    image: {
      src: 'no-fees.svg',
      alt: 'use no fees image',
    },
    elementDescription: 'No booking fees',
  },
  {
    image: {
      src: 'find.svg',
      alt: 'find image',
    },
    elementDescription: 'Search thousands of hotels across Australia and New Zealand',
  },
];

const render = () => {
  return mountUtils(<CoreValueProposition />);
};

describe('<CoreValueProposition />', () => {
  it('renders the heading', () => {
    const { find } = render();

    expect(find('Heading')).toExist();
  });

  it('renders all 3 cards with the correct icon and text', () => {
    const { find } = render();
    const cards = find('CoreValue');

    expect(cards).toHaveLength(3);
    cards.map((node, index) => expect(node.props()).toEqual(cardsContent[index]));
  });

  it('renders the find icon and default "find" message', () => {
    const { find } = render();
    expect(find('CoreValue').at(2).props()).toEqual(cardsContent[2]);
  });
});
