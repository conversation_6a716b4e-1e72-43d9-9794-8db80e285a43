import React from 'react';
import TopAreas from './TopAreas';
import { mountUtils } from 'test-utils';

mountUtils.mockComponent('AreaCard');

const mockArea = {
  regionName: 'Sydney CBD',
  url: '/path/to/somewhere',
};
const mockAreas = [mockArea, mockArea];

const defaultProps = {
  regionName: 'Sydney',
  title: 'Top areas',
};

const decorators = { theme: true };
const render = (props) => mountUtils(<TopAreas {...defaultProps} {...props} />, { decorators });

describe('without areas', () => {
  it('does NOT show a heading', () => {
    const { find } = render();

    expect(find('Heading')).not.toExist();
  });

  it('does NOT show the area cards', () => {
    const { find } = render();

    expect(find('AreaCard')).not.toExist();
  });
});

describe('with areas', () => {
  it('does NOT show a heading', () => {
    const { find } = render({ areas: mockAreas });

    expect(find('Heading').text()).toEqual('Top areas in Sydney');
  });

  it('does NOT show the area cards', () => {
    const { find } = render({ areas: mockAreas });

    expect(find('AreaCard')).toHaveLength(2);
  });
});
