import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { Box, Card } from '@qga/roo-ui/components';
import { mediaQuery } from 'lib/styledSystem';
import ResponsiveLink from 'components/DestinationPage/ResponsiveLink';

export const Grid = styled(Box)`
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;

  > * {
    width: 100%;
  }

  ${mediaQuery.minWidth.sm} {
    > * {
      width: 49%;
    }
  }

  ${mediaQuery.minWidth.md} {
    > * {
      width: 32%;
    }
  }
`;

export const CardWrapper = styled(Card)`
  margin-bottom: ${themeGet('space.2')};
  padding: 0;
  transition: all 0.1s linear;

  ${mediaQuery.minWidth.sm} {
    margin: ${themeGet('space.1')} ${themeGet('space.1')};
  }

  ${mediaQuery.minWidth.md} {
    margin: ${themeGet('space.2')} ${themeGet('space.2')};
  }

  &:hover {
    box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.1);
  }
`;

export const NakedLink = styled(ResponsiveLink)`
  height: 100%;
  text-decoration: none;
  width: 100%;
`;

export const TextWrapper = styled(Box)`
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.7) 85%);
`;
