import React from 'react';
import CardWithImage from './CardWithImage';
import { mountUtils } from 'test-utils';
import { getBrowser } from 'store/userEnvironment/userEnvironmentSelectors';

jest.mock('store/userEnvironment/userEnvironmentSelectors');
mountUtils.mockComponent('ResponsiveImage');
const mockOnClick = jest.fn();

const defaultProps = {
  mainImage: {
    assets: {
      medium: 'path/to/image.jpg',
    },
    caption: 'image caption',
  },
  onClick: mockOnClick,
  regionName: 'Sydney CBD',
  url: 'https://www.qantas.com/search-this-subregion',
};

const decorators = { router: true, store: true };
const render = () => mountUtils(<CardWithImage {...defaultProps} />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
  getBrowser.mockReturnValue({ name: 'Chrome' });
});

it('renders the link', () => {
  const { find } = render();

  expect(find('NakedLink').first()).toHaveProp({
    onClick: mockOnClick,
    url: 'https://www.qantas.com/search-this-subregion',
  });
});

it('shows the region name', () => {
  const { findByTestId } = render();

  expect(findByTestId('region-name').text()).toEqual('Sydney CBD');
});

it('shows the ResponsiveImage', () => {
  const { find } = render();

  expect(find('ResponsiveImage')).toHaveProp({
    alt: 'image caption',
  });
});
