import PropTypes from 'prop-types';
import React from 'react';
import styled from '@emotion/styled';
import { Flex, Text } from '@qga/roo-ui/components';
import { CardWrapper, NakedLink, TextWrapper } from 'components/DestinationPage/TopAreas/primitives';
import ResponsiveImage from 'components/DestinationPage/ResponsiveImage';

const FlexWrapper = styled(Flex)`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
`;

const CardWithImage = ({ mainImage, onClick, regionName, url }) => (
  <CardWrapper position="relative">
    <NakedLink url={url} onClick={onClick} aria-label={regionName}>
      <ResponsiveImage mainImage={mainImage} alt={mainImage.caption} height={[150, 200]} lazy />
      <FlexWrapper flexDirection="column" justifyContent="flex-end" height="100%">
        <TextWrapper p={4}>
          <Text color="white" fontSize="lg" data-testid="region-name">
            {regionName}
          </Text>
        </TextWrapper>
      </FlexWrapper>
    </NakedLink>
  </CardWrapper>
);

CardWithImage.propTypes = {
  mainImage: PropTypes.shape({
    assets: PropTypes.shape({
      large: PropTypes.string,
      medium: PropTypes.string.isRequired,
      small: PropTypes.string,
    }).isRequired,
    caption: PropTypes.string.isRequired,
  }).isRequired,
  onClick: PropTypes.func.isRequired,
  regionName: PropTypes.string.isRequired,
  url: PropTypes.string.isRequired,
};

export default CardWithImage;
