import PropTypes from 'prop-types';
import React from 'react';
import { Flex, Icon, Text } from '@qga/roo-ui/components';
import { CardWrapper, NakedLink } from 'components/DestinationPage/TopAreas/primitives';

const CardWithText = ({ onClick, regionName, totalProperties, url }) => (
  <CardWrapper borderRadius="3px">
    <NakedLink onClick={onClick} url={url} aria-label={regionName}>
      <Flex alignItems="center" bg="white" justifyContent="space-between" py={3} px={5}>
        <Flex flexDirection="column">
          <Text color="greys.charcoal" fontSize="base" data-testid="region-name">
            {regionName}
          </Text>
          {totalProperties && (
            <Text fontSize="sm" color="greys.steel" data-testid="property-count">
              {totalProperties} properties
            </Text>
          )}
        </Flex>
        <Icon name="chevronRight" color="brand.primary" />
      </Flex>
    </NakedLink>
  </CardWrapper>
);

CardWithText.propTypes = {
  mainImage: PropTypes.object,
  onClick: PropTypes.func.isRequired,
  regionName: PropTypes.string.isRequired,
  url: PropTypes.string.isRequired,
  totalProperties: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
};

CardWithText.defaultProps = {
  mainImage: {},
  totalProperties: null,
};

export default CardWithText;
