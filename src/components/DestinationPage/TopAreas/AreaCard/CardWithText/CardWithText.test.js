import React from 'react';
import Card<PERSON>ithText from './CardWithText';
import { mountUtils } from 'test-utils';

const mockOnClick = jest.fn();

const defaultProps = {
  onClick: mockOnClick,
  regionName: 'Sydney CBD',
  url: 'https://www.qantas.com/search-this-subregion',
};

const decorators = { router: true };
const render = (props) => mountUtils(<CardWithText {...defaultProps} {...props} />, { decorators });

it('renders the link', () => {
  const { find } = render();

  expect(find('NakedLink').first()).toHaveProp({
    onClick: mockOnClick,
    url: 'https://www.qantas.com/search-this-subregion',
  });
});

it('shows the region name', () => {
  const { findByTestId } = render();

  expect(findByTestId('region-name').text()).toEqual('Sydney CBD');
});

it('shows the icon', () => {
  const { find } = render();

  expect(find('Icon')).toExist();
});

describe('when totalProperties is available', () => {
  it('renders the property count', () => {
    const { findByTestId } = render({ totalProperties: 10 });

    expect(findByTestId('property-count').text()).toEqual('10 properties');
  });
});
