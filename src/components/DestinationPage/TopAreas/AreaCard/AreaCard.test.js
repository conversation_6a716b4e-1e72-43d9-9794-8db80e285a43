import React from 'react';
import AreaCard from './AreaCard';
import { mountUtils } from 'test-utils';
import { useDataLayer } from 'hooks/useDataLayer';
import { getBrowser } from 'store/userEnvironment/userEnvironmentSelectors';

jest.mock('store/userEnvironment/userEnvironmentSelectors');
jest.mock('hooks/useDataLayer');
mountUtils.mockComponent('CardWithImage');
mountUtils.mockComponent('CardWithText');

const emitInteractionEvent = jest.fn();

const mainImage = {
  caption: 'Manly Beach, Sydney',
  assets: {
    large: 'www.urlLarge.webp',
    medium: 'www.urlMedium.webp',
    small: 'www.urlSmall.webp',
    legacy: 'www.urlSmall.jpg',
  },
};

const defaultProps = {
  regionName: 'Sydney',
  title: 'Top Areas',
  url: '/path/to/somewhere',
};

const decorators = { router: true, store: true, theme: true };
const render = (props) => mountUtils(<AreaCard {...defaultProps} {...props} />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
  getBrowser.mockReturnValue({ name: 'Chrome' });
  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

describe('with an image', () => {
  it('renders <CardWithImage />', () => {
    const { find } = render({ mainImage });

    expect(find('CardWithImage')).toHaveProp({
      mainImage,
      regionName: 'Sydney',
      url: '/path/to/somewhere',
    });
  });

  it('emits an event to the data layer when clicked', () => {
    const { find } = render({ mainImage });
    find('CardWithImage').props().onClick();

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Top Areas Gallery',
      value: 'Sydney Selected',
    });
  });
});

describe('without an image', () => {
  it('renders <CardWithText />', () => {
    const { find } = render({ totalProperties: 10 });

    expect(find('CardWithText')).toHaveProp({
      regionName: 'Sydney',
      totalProperties: 10,
      url: '/path/to/somewhere',
    });
  });

  it('emits an event to the data layer when clicked', () => {
    const { find } = render();
    find('CardWithText').props().onClick();

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Top Areas Gallery',
      value: 'Sydney Selected',
    });
  });
});
