import React, { useCallback, Fragment } from 'react';
import PropTypes from 'prop-types';
import startCase from 'lodash/startCase';
import { useDataLayer } from 'hooks/useDataLayer';
import CardWithImage from './CardWithImage';
import Card<PERSON>ithText from './CardWithText';

const AreaCard = ({ title, mainImage, regionName, regionAvailabilitySearchUrl, totalProperties, url: destinationUrl }) => {
  const { emitInteractionEvent } = useDataLayer();

  const handleClick = useCallback(() => {
    emitInteractionEvent({ type: `${startCase(title)} Gallery`, value: `${startCase(regionName)} Selected` });
  }, [regionName, title, emitInteractionEvent]);

  const url = destinationUrl || regionAvailabilitySearchUrl;

  return (
    <Fragment>
      {mainImage ? (
        <CardWithImage mainImage={mainImage} onClick={handleClick} regionName={regionName} url={url} />
      ) : (
        <CardWithText onClick={handleClick} regionName={regionName} totalProperties={totalProperties} url={url} />
      )}
    </Fragment>
  );
};

AreaCard.propTypes = {
  mainImage: PropTypes.object,
  regionName: PropTypes.string.isRequired,
  regionAvailabilitySearchUrl: PropTypes.string,
  title: PropTypes.string.isRequired,
  totalProperties: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  url: PropTypes.string,
};

AreaCard.defaultProps = {
  mainImage: null,
  regionAvailabilitySearchUrl: null,
  totalProperties: null,
  url: null,
};

export default AreaCard;
