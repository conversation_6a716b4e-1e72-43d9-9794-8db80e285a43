import isEmpty from 'lodash/isEmpty';
import PropTypes from 'prop-types';
import React from 'react';
import { Heading, Box } from '@qga/roo-ui/components';
import AreaCard from './AreaCard';
import { Grid } from './primitives';

const TopAreas = ({ areas, regionName, title }) => {
  if (isEmpty(areas)) return null;

  return (
    <Box width={1} bg="dusty" flexDirection="column">
      <Heading.h2 fontSize="lg" mx="auto" color="greys.charcoal" display="block" mb={10} textAlign="center">
        {title} in {regionName}
      </Heading.h2>

      <Grid>
        {areas.map((area, index) => (
          <AreaCard key={`tile-${index}`} title={title} {...area} />
        ))}
      </Grid>
    </Box>
  );
};

TopAreas.propTypes = {
  areas: PropTypes.arrayOf(PropTypes.object),
  regionName: PropTypes.string,
  title: PropTypes.string.isRequired,
};

TopAreas.defaultProps = {
  areas: [],
  regionName: null,
};

export default TopAreas;
