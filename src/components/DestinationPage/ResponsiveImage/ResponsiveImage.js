import PropTypes from 'prop-types';
import React from 'react';
import Image from 'components/Image';
import { useSelector } from 'react-redux';
import { getBrowser } from 'store/userEnvironment/userEnvironmentSelectors';
import { PUBLIC_PATH } from 'config';
import { isSafariOrIE } from 'lib/browser/isSafariOrIE';

const getSrc = ({ mainImage, isSafariOrIEBrowser }) => {
  const legacyMainImageSrc = `${PUBLIC_PATH}${mainImage.assets?.legacy}`;
  const mediumMainImageSrc = `${PUBLIC_PATH}${mainImage.assets?.medium}`;

  return isSafariOrIEBrowser ? legacyMainImageSrc : mediumMainImageSrc;
};

const ResponsiveImage = ({ mainImage, alt, ...rest }) => {
  const browser = useSelector(getBrowser);
  const isSafariOrIEBrowser = isSafariOrIE(browser);

  const src = getSrc({ isSafariOrIEBrowser, mainImage });

  return <Image {...rest} src={src} alt={alt} />;
};

ResponsiveImage.propTypes = {
  mainImage: PropTypes.object.isRequired,
  alt: PropTypes.string.isRequired,
  lazy: PropTypes.bool,
};

ResponsiveImage.defaultProps = {
  lazy: false,
};

export default ResponsiveImage;
