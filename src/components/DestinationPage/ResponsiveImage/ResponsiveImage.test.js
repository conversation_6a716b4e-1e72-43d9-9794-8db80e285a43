import React from 'react';
import ResponsiveImage from './ResponsiveImage';
import { mountUtils } from 'test-utils';
import { getBrowser } from 'store/userEnvironment/userEnvironmentSelectors';
import { PUBLIC_PATH } from 'config';

mountUtils.mockComponent('Image');

jest.mock('store/userEnvironment/userEnvironmentSelectors');

const props = {
  alt: 'image description',
  mainImage: {
    assets: {
      small: 'destinations/australia/nsw/byron-bay-lighthouse.mobile.webp',
      medium: 'destinations/australia/nsw/byron-bay-lighthouse.tablet.webp',
      large: 'destinations/australia/nsw/byron-bay-lighthouse.desktop.webp',
      legacy: 'destinations/australia/nsw/byron-bay-lighthouse.desktop.jpg',
    },
  },
};
const decorators = { router: true, store: true };
const render = () => mountUtils(<ResponsiveImage {...props} />, { decorators });

describe('<ResponsiveImage />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('when not in legacy browser', () => {
    it('renders the image with provided source', () => {
      getBrowser.mockReturnValue({ name: 'Chrome' });

      const { find } = render();
      const expectedSrc = `${PUBLIC_PATH}${props.mainImage.assets?.medium}`;

      expect(find('Image')).toHaveProp({
        src: expectedSrc,
        alt: props.alt,
      });
    });
  });

  describe('when in legacy browser', () => {
    it('renders the image with the legacy source', () => {
      getBrowser.mockReturnValue({ name: 'Internet Explorer' });

      const { find } = render();
      const expectedSrc = `${PUBLIC_PATH}${props.mainImage.assets?.legacy}`;

      expect(find('Image')).toHaveProp({
        src: expectedSrc,
        alt: props.alt,
      });
    });
  });
});
