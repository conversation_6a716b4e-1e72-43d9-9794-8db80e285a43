import React, { Fragment } from 'react';
import { useSelector } from 'react-redux';
import { Box, Flex, Heading } from '@qga/roo-ui/components';
import { getDestination } from 'store/destination/destinationSelectors';
import Markdown from 'components/Markdown';
import PageBlock from 'components/PageBlock';
import Hero from 'components/DestinationPage/Hero/Hero';

const DestinationContent = () => {
  const { heroImage, mdContent, regionName } = useSelector(getDestination) || {};

  return (
    <Fragment>
      {heroImage && (
        <Hero {...heroImage} height={440}>
          <Flex backgroundColor="rgba(0, 0, 0, 0.5)" alignItems="center" justifyContent="center" height="100%">
            <Box textAlign="center">
              <Heading.h2 color="white" display="block" fontSize={['28px', '3xl']} pb={10} mb={0}>
                Discover {regionName}
              </Heading.h2>
            </Box>
          </Flex>
        </Hero>
      )}

      {!heroImage && (
        <Flex justifyContent="center" width={1}>
          <Heading.h2 color="greys.charcoal" display="block" fontSize={['28px', '3xl']} mb={0}>
            Discover {regionName}
          </Heading.h2>
        </Flex>
      )}

      <PageBlock mt={10} maxWidth={820}>
        <Markdown content={mdContent} />
      </PageBlock>
    </Fragment>
  );
};

export default DestinationContent;
