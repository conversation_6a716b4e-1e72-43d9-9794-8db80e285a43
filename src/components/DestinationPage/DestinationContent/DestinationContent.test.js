import React from 'react';
import { mountUtils } from 'test-utils';
import { getDestination } from 'store/destination/destinationSelectors';
import DestinationContent from './DestinationContent';
import { getBrowser } from 'store/userEnvironment/userEnvironmentSelectors';

jest.mock('store/userEnvironment/userEnvironmentSelectors');

mountUtils.mockComponent('Markdown');
jest.mock('store/destination/destinationSelectors');

const decorators = { router: true, store: true, theme: true };
const render = () => mountUtils(<DestinationContent />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
});

describe('without a hero image', () => {
  beforeEach(() => {
    getBrowser.mockReturnValue({ name: 'Chrome' });
    getDestination.mockReturnValue({
      mdContent: 'content',
      regionName: 'Sydney',
    });
  });

  it('shows the content block', () => {
    const { find } = render();

    expect(find('Markdown')).toHaveProp({
      content: 'content',
    });
  });
});

describe('with a hero image', () => {
  beforeEach(() => {
    getDestination.mockReturnValue({
      mdContent: 'content',
      regionName: 'Sydney',
      heroImage: {
        assets: {
          large: 'dir/image.large.webp',
          medium: 'dir/image.medium.webp',
          small: 'dir/image.small.webp',
          legacyLarge: 'dir/image.large.jpg',
          legacySmall: 'dir/image.small.jpg',
        },
      },
    });
  });

  it('shows the content block', () => {
    const { find } = render();

    expect(find('Markdown')).toHaveProp({
      content: 'content',
    });
  });
});
