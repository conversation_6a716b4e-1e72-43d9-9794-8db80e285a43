import React from 'react';
import { BackgroundImage, Heading } from '@qga/roo-ui/components';
import { mountUtils } from 'test-utils';
import Markdown from 'components/Markdown';
import { getDestination } from 'store/destination/destinationSelectors';
import Header from './Header';
import { getBrowser } from 'store/userEnvironment/userEnvironmentSelectors';

jest.mock('store/userEnvironment/userEnvironmentSelectors');
jest.mock('store/destination/destinationSelectors');

const destinationMock = {
  mdSummary: 'Summary text',
  regionName: 'Sydney',
  heroImage: {
    assets: {
      large: 'dir/image.large.webp',
      medium: 'dir/image.medium.webp',
      small: 'dir/image.small.webp',
      legacyLarge: 'dir/image.large.jpg',
      legacySmall: 'dir/image.small.jpg',
    },
    position: 'top center',
  },
};

const decorators = { router: true, store: true, theme: true };
const render = (props) => mountUtils(<Header {...props} />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
  getBrowser.mockReturnValue({ name: 'Chrome' });
});

describe('with a hero image', () => {
  beforeEach(() => {
    getDestination.mockReturnValue(destinationMock);
  });

  it('shows a background image', () => {
    const { find } = render();

    expect(find('Hero')).toHaveProp({
      assets: {
        large: 'dir/image.large.webp',
        medium: 'dir/image.medium.webp',
        small: 'dir/image.small.webp',
        legacyLarge: 'dir/image.large.jpg',
        legacySmall: 'dir/image.small.jpg',
      },
      position: 'top center',
    });
  });

  it('shows a heading', () => {
    const { find } = render();

    expect(find(Heading.h1).text()).toEqual('Sydney');
  });

  it('shows the summary', () => {
    const { find } = render();

    expect(find(Markdown)).toHaveProp({
      content: destinationMock.mdSummary,
      disableParsingRawHTML: false,
    });
  });
});

describe('without a hero image', () => {
  beforeEach(() => {
    getDestination.mockReturnValue({
      mdSummary: 'Summary text',
      regionName: 'Sydney',
    });
  });

  it('does NOT show a background image', () => {
    const { find } = render();

    expect(find(BackgroundImage)).not.toExist();
  });

  it('shows a heading', () => {
    const { find } = render();

    expect(find(Heading.h1).text()).toEqual('Sydney');
  });

  it('shows the summary', () => {
    const { find } = render();

    expect(find(Markdown).text()).toEqual('Summary text');
  });
});

it('shows a heading', () => {
  const { find } = render({ headingText: 'Melbourne Hotels' });

  expect(find(Heading.h1).text()).toEqual('Melbourne Hotels');
});
