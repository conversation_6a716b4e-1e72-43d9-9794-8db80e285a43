import PropTypes from 'prop-types';
import React from 'react';
import { useSelector } from 'react-redux';
import { Box, Flex, Heading } from '@qga/roo-ui/components';
import { getDestination } from 'store/destination/destinationSelectors';
import Markdown from 'components/Markdown';
import <PERSON> from 'components/DestinationPage/Hero/Hero';
import { PageBlockWhite } from 'components/DestinationPage/Templates/primitives';
import { rem } from 'polished';

const Header = ({ headingText, ...rest }) => {
  const { heroImage, mdSummary, regionName } = useSelector(getDestination) || {};
  const pageHeading = headingText || regionName;

  return (
    <Box {...rest}>
      {heroImage && (
        <Hero {...heroImage} height={[rem('200px'), rem('440px')]}>
          <Flex alignItems="flex-end" justifyContent="center" height="100%">
            <Box backgroundColor="white" maxWidth={858} minWidth={2 / 3} px={8} textAlign="center">
              <Heading.h1 color="greys.charcoal" display="block" fontSize={['xl', '4xl']} pt={5} mb={0}>
                {pageHeading}
              </Heading.h1>
            </Box>
          </Flex>
        </Hero>
      )}

      <PageBlockWhite>
        {!heroImage && (
          <Flex justifyContent="center" bm={3}>
            <Heading.h1 color="greys.charcoal" display="block" mb={0} fontSize={['3xl', '4xl']}>
              {pageHeading}
            </Heading.h1>
          </Flex>
        )}

        {mdSummary && (
          <Flex justifyContent="center" mt={heroImage ? 0 : 2}>
            <Box maxWidth={700} fontSize="base" textAlign="center">
              <Markdown content={mdSummary} disableParsingRawHTML={false} />
            </Box>
          </Flex>
        )}
      </PageBlockWhite>
    </Box>
  );
};

Header.propTypes = {
  headingText: PropTypes.string,
};

Header.defaultProps = {
  headingText: '',
};

export default Header;
