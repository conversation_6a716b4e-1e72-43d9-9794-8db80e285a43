import React from 'react';
import CountryTemplate from './CountryTemplate';
import { mountUtils } from 'test-utils';
import { getDestination } from 'store/destination/destinationSelectors';

jest.mock('store/destination/destinationSelectors');

mountUtils.mockComponent('Header');
mountUtils.mockComponent('TopAreas');
mountUtils.mockComponent('CoreValueProposition');

const decorators = { store: true, theme: true };
const render = () => mountUtils(<CountryTemplate />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();

  getDestination.mockReturnValue({
    areas: ['area1', 'area2'],
    states: ['state1', 'state2'],
    regionName: 'Australia',
  });
});

it('renders the <Header /> component', () => {
  const { find } = render();

  expect(find('Header')).toExist();
});

it('renders the top cities', () => {
  const { find } = render();

  expect(find('TopAreas[title="Top destinations"]')).toHaveProp({
    areas: ['area1', 'area2'],
    regionName: 'Australia',
    title: 'Top destinations',
  });
});

it('renders the top states', () => {
  const { find } = render();

  expect(find('TopAreas[title="Top states"]')).toHaveProp({
    areas: ['state1', 'state2'],
    regionName: 'Australia',
    title: 'Top states',
  });
});

it('renders the CoreValueProposition', () => {
  const { find } = render();

  expect(find('CoreValueProposition')).toExist();
});
