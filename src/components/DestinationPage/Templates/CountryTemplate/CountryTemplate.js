import React, { Fragment } from 'react';
import { useSelector } from 'react-redux';
import Header from 'components/DestinationPage/Header';
import TopAreas from 'components/DestinationPage/TopAreas';
import CoreValueProposition from 'components/DestinationPage/CoreValueProposition';
import { getDestination } from 'store/destination/destinationSelectors';
import { PageBlockPorcelain, PageBlockWhite } from 'components/DestinationPage/Templates/primitives';

const CountryTemplate = () => {
  const { areas, regionName, states } = useSelector(getDestination);

  return (
    <Fragment>
      <Header />
      <PageBlockPorcelain>
        <TopAreas areas={areas} regionName={regionName} title="Top destinations" />
      </PageBlockPorcelain>
      <PageBlockWhite>
        <TopAreas areas={states} regionName={regionName} title="Top states" />
      </PageBlockWhite>
      <PageBlockPorcelain>
        <CoreValueProposition />
      </PageBlockPorcelain>
    </Fragment>
  );
};

export default CountryTemplate;
