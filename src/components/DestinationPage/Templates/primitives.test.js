import React from 'react';
import { mount } from 'enzyme';
import { PageBlockPorcelain, PageBlockWhite } from './primitives';

describe('<PageBlockWhite />', () => {
  let props;

  const render = () => mount(<PageBlockWhite {...props} />);

  beforeEach(() => {
    props = {
      children: <span>Children</span>,
    };
  });

  it('renders the children component', () => {
    expect(render().contains(props.children)).toBe(true);
  });
});

describe('<PageBlockPorcelain />', () => {
  let props;

  const render = () => mount(<PageBlockPorcelain {...props} />);

  beforeEach(() => {
    props = {
      children: <span>Children</span>,
    };
  });

  it('renders the children component', () => {
    expect(render().contains(props.children)).toBe(true);
  });
});
