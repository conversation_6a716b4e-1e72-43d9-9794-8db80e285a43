import React from 'react';
import CityTemplate from './CityTemplate';
import { mountUtils } from 'test-utils';
import { getDestination, getMerchandise } from 'store/destination/destinationSelectors';
import LegacyBrowserBoundary from 'components/LegacyBrowserBoundary';

jest.mock('store/destination/destinationSelectors');
jest.mock('components/LegacyBrowserBoundary');

mountUtils.mockComponent('Header');
mountUtils.mockComponent('InPageNavBar');
mountUtils.mockComponent('CoreValueProposition');
mountUtils.mockComponent('DestinationContent');
mountUtils.mockComponent('FAQs');
mountUtils.mockComponent('LatestDeals');
mountUtils.mockComponent('TopAreas');
mountUtils.mockComponent('DiscoverMore');

const decorators = { store: true, theme: true };
const render = () => mountUtils(<CityTemplate />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();

  LegacyBrowserBoundary.mockImplementation(({ children }) => children);

  getDestination.mockReturnValue({
    faqs: [
      {
        question: 'Question',
        mdAnswer: 'Answer',
      },
    ],
    regionName: 'Melbourne',
  });
  getMerchandise.mockReturnValue({ subRegions: [] });
});

it('renders the <Header /> component', () => {
  const { find } = render();

  expect(find('Header')).toHaveProp({
    headingText: 'Melbourne Hotels',
  });
});

it('renders the <InPageNavBar /> component', () => {
  const { find } = render();

  expect(find('InPageNavBar')).toExist();
});

it('renders the latest deals section', () => {
  const { find } = render();

  expect(find('LatestDeals')).toExist();
});

it('renders the destination content section ', () => {
  const { find } = render();

  expect(find('DestinationContent')).toExist();
});

it('renders the CoreValueProposition', () => {
  const { find } = render();

  expect(find('CoreValueProposition')).toExist();
});

it('renders the FAQs component', () => {
  const { find } = render();

  expect(find('FAQs')).toExist();
});

describe('without discoverMoreAreas', () => {
  it('does not render the DiscoverMore component', () => {
    const { find } = render();

    expect(find('DiscoverMore')).not.toExist();
  });
});

describe('with discoverMoreAreas', () => {
  beforeEach(() => {
    getDestination.mockReturnValue({
      discoverMoreAreas: [
        {
          regionName: 'Foo',
          url: '/path/to/foo',
        },
      ],
      regionName: 'Melbourne',
    });
  });

  it('renders the DiscoverMore component', () => {
    const { find } = render();

    expect(find('DiscoverMore')).toExist();
  });
});

describe('with no subRegions', () => {
  it('does not render TopAreas', () => {
    const { find } = render();

    expect(find('TopAreas')).not.toExist();
  });
});

describe('with subRegions', () => {
  beforeEach(() => {
    getMerchandise.mockReturnValue({
      subRegions: [
        {
          regionName: 'Melbourne CBD',
          totalProperties: 33,
          regionAvailabilitySearchUrl: '/search-this-subregion',
        },
      ],
    });
  });

  it('renders TopAreas', () => {
    const { find } = render();

    expect(find('TopAreas')).toHaveProp({
      areas: [
        {
          regionName: 'Melbourne CBD',
          totalProperties: 33,
          regionAvailabilitySearchUrl: '/search-this-subregion',
        },
      ],
      regionName: 'Melbourne',
      title: 'Top areas',
    });
  });
});
