import React, { Fragment, useCallback, useRef } from 'react';
import { useSelector } from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import { getDestination, getMerchandise } from 'store/destination/destinationSelectors';
import Header from 'components/DestinationPage/Header';
import InPageNavBar from 'components/InPageNavBar';
import CoreValueProposition from 'components/DestinationPage/CoreValueProposition';
import DestinationContent from 'components/DestinationPage/DestinationContent';
import FAQs from 'components/DestinationPage/FAQs';
import PageBlock from 'components/PageBlock';
import LatestDeals from 'components/DestinationPage/LatestDeals';
import TopAreas from 'components/DestinationPage/TopAreas';
import DiscoverMore from 'components/DestinationPage/DiscoverMore';
import { PageBlockPorcelain, PageBlockWhite } from 'components/DestinationPage/Templates/primitives';
import { Flex } from '@qga/roo-ui/components';
import LegacyBrowserBoundary from 'components/LegacyBrowserBoundary';

const CityTemplate = () => {
  const { discoverMoreAreas, regionName } = useSelector(getDestination);
  const { subRegions } = useSelector(getMerchandise);
  const hasSubRegions = !isEmpty(subRegions);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const menuItems = [
    { id: 'deals', name: 'deals', linkRef: useRef(), ref: useRef(), text: 'Latest deals' },
    { id: 'destination-content', name: 'destination-content', linkRef: useRef(), ref: useRef(), text: `Discover ${regionName}` },
    { id: 'faqs', name: 'faqs', linkRef: useRef(), ref: useRef(), text: 'Frequently asked questions' },
  ];

  const getMenuRef = useCallback((name) => menuItems.find((item) => item.name === name).ref, [menuItems]);

  return (
    <Fragment>
      <Header headingText={`${regionName} Hotels`} pb={0} bg="white" />
      <InPageNavBar justifyContent={['flex-start', 'center']} menuItems={menuItems} shadow menuRefPositionOffset={0} />

      <LegacyBrowserBoundary>
        <PageBlock id="deals" pt={15} pb={5} bg="greys.porcelain" ref={getMenuRef('deals')}>
          <LatestDeals />
        </PageBlock>
      </LegacyBrowserBoundary>

      {hasSubRegions && (
        <PageBlockPorcelain>
          <TopAreas areas={subRegions} regionName={regionName} title="Top areas" />
        </PageBlockPorcelain>
      )}

      <Flex
        id="destination-content"
        pb={10}
        alignItems="center"
        backgroundColor="white"
        flexDirection="column"
        ref={getMenuRef('destination-content')}
      >
        <DestinationContent />
      </Flex>

      <PageBlockPorcelain>
        <CoreValueProposition />
      </PageBlockPorcelain>

      <PageBlock id="faqs" pt={5} pb={10} px={[6, 0]} bg="white" ref={getMenuRef('faqs')}>
        <FAQs />
      </PageBlock>

      {discoverMoreAreas && (
        <PageBlockWhite>
          <DiscoverMore areas={discoverMoreAreas} headingText="Discover more" />
        </PageBlockWhite>
      )}
    </Fragment>
  );
};

export default CityTemplate;
