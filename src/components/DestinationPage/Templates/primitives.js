import React from 'react';
import PropTypes from 'prop-types';
import PageBlock from 'components/PageBlock';

export const PageBlockWhite = ({ children }) => (
  <PageBlock pb={10} pt={15} bg="white">
    {children}
  </PageBlock>
);
PageBlockWhite.propTypes = {
  children: PropTypes.node.isRequired,
};

export const PageBlockPorcelain = ({ children }) => (
  <PageBlock pb={10} pt={15} bg="greys.porcelain">
    {children}
  </PageBlock>
);
PageBlockPorcelain.propTypes = {
  children: PropTypes.node.isRequired,
};
