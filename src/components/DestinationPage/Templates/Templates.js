import React, { Fragment } from 'react';
import { useSelector } from 'react-redux';
import { getDestination } from 'store/destination/destinationSelectors';
import CountryTemplate from './CountryTemplate';
import StateTemplate from './StateTemplate';
import CityTemplate from './CityTemplate';

const Templates = () => {
  const { template } = useSelector(getDestination) || {};

  return (
    <Fragment>
      {template === 'country' && <CountryTemplate />}
      {template === 'state' && <StateTemplate />}
      {template === 'city' && <CityTemplate />}
    </Fragment>
  );
};

export default Templates;
