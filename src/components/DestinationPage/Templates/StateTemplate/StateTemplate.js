import React, { Fragment } from 'react';
import { useSelector } from 'react-redux';
import Header from 'components/DestinationPage/Header';
import TopAreas from 'components/DestinationPage/TopAreas';
import CoreValueProposition from 'components/DestinationPage/CoreValueProposition';
import DiscoverMore from 'components/DestinationPage/DiscoverMore';
import { getDestination } from 'store/destination/destinationSelectors';
import { PageBlockWhite, PageBlockPorcelain } from 'components/DestinationPage/Templates/primitives';

const StateTemplate = () => {
  const { discoverMoreAreas, regionName, subRegions } = useSelector(getDestination);

  return (
    <Fragment>
      <Header />
      <PageBlockPorcelain>
        <TopAreas areas={subRegions} title="Top areas" regionName={regionName} />
      </PageBlockPorcelain>
      <PageBlockWhite>
        <DiscoverMore areas={discoverMoreAreas} headingText="Discover more" />
      </PageBlockWhite>
      <PageBlockPorcelain>
        <CoreValueProposition regionName={regionName} />
      </PageBlockPorcelain>
    </Fragment>
  );
};

export default StateTemplate;
