import React from 'react';
import StateTemplate from './StateTemplate';
import { mountUtils } from 'test-utils';
import { getDestination } from 'store/destination/destinationSelectors';

jest.mock('store/destination/destinationSelectors');

mountUtils.mockComponent('Header');
mountUtils.mockComponent('TopAreas');
mountUtils.mockComponent('CoreValueProposition');

const decorators = { store: true, theme: true };
const render = () => mountUtils(<StateTemplate />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();

  getDestination.mockReturnValue({
    regionName: 'New South Wales',
    subRegions: ['subRegion1', 'subRegion2'],
  });
});

it('renders the <Header /> component', () => {
  const { find } = render();

  expect(find('Header')).toExist();
});

it('renders the top cities', () => {
  const { find } = render();

  expect(find('TopAreas').first()).toHaveProp({
    areas: ['subRegion1', 'subRegion2'],
    regionName: 'New South Wales',
    title: 'Top areas',
  });
});

it('renders the CoreValueProposition', () => {
  const { find } = render();

  expect(find('CoreValueProposition')).toExist();
});
