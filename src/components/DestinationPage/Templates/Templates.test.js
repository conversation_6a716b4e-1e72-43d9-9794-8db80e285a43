import React from 'react';
import Templates from './Templates';
import { mountUtils } from 'test-utils';
import { getDestination, getMerchandiseStatus } from 'store/destination/destinationSelectors';

jest.mock('store/destination/destinationSelectors');

mountUtils.mockComponent('CityTemplate');
mountUtils.mockComponent('StateTemplate');
mountUtils.mockComponent('CountryTemplate');

const decorators = { store: true, theme: true };
const render = () => mountUtils(<Templates />, { decorators });

afterEach(() => {
  jest.clearAllMocks();
});

describe('with a country template', () => {
  beforeEach(() => {
    getDestination.mockReturnValue({
      template: 'country',
    });
  });

  it('renders <CountryTemplate />', () => {
    const { find } = render();

    expect(find('CountryTemplate')).toExist();
  });
});

describe('with a city template', () => {
  beforeEach(() => {
    getDestination.mockReturnValue({
      template: 'city',
    });
    getMerchandiseStatus.mockReturnValue('resolved');
  });

  it('renders <CityTemplate />', () => {
    const { find } = render();

    expect(find('CityTemplate')).toExist();
  });
});

describe('with a state template', () => {
  beforeEach(() => {
    getDestination.mockReturnValue({
      template: 'state',
    });
  });

  it('renders <StateTemplate />', () => {
    const { find } = render();

    expect(find('StateTemplate')).toExist();
  });
});
