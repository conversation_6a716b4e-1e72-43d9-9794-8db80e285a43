import React from 'react';
import LoaderComponent from './LoaderComponent';
import { mountUtils } from 'test-utils';

mountUtils.mockComponent('LoadingIndicator');

const decorators = { router: true, theme: true };
const render = () => mountUtils(<LoaderComponent />, { decorators });

it('returns the LoadingIndicator', () => {
  const { find } = render();

  expect(find('LoadingIndicator')).toExist();
});
