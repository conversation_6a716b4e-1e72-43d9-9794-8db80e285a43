import React from 'react';
import { mountUtils } from 'test-utils';
import <PERSON> from './Hero';
import { PUBLIC_PATH } from 'config';
import { getBrowser } from 'store/userEnvironment/userEnvironmentSelectors';

jest.mock('store/userEnvironment/userEnvironmentSelectors');

const decorators = { theme: true, store: true };
const props = {
  assets: {
    small: 'small.webp',
    medium: 'medium.webp',
    large: 'large.webp',
    legacyLarge: 'legacy.jpg',
    legacySmall: 'legacy.jpg',
  },
  position: 'position',
  children: <div>children</div>,
};
const render = () => mountUtils(<Hero {...props} />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
  getBrowser.mockReturnValue({ name: 'Chrome' });
});

describe('when in legacy browser', () => {
  it('renders the background image with the expected props', () => {
    getBrowser.mockReturnValue({ name: 'Internet Explorer' });

    const { find } = render();

    expect(find('ResponsiveBackgroundImage')).toHaveProp({
      backgroundPosition: 'position',
      mobile: `${PUBLIC_PATH}${props.assets.legacySmall}`,
      tablet: `${PUBLIC_PATH}${props.assets.legacyLarge}`,
      desktop: `${PUBLIC_PATH}${props.assets.legacyLarge}`,
    });
  });
});

describe('when not in legacy browser', () => {
  it('renders the background image with the expected props', () => {
    getBrowser.mockReturnValue({ name: 'Chrome' });

    const { find } = render();

    expect(find('ResponsiveBackgroundImage')).toHaveProp({
      backgroundPosition: 'position',
      mobile: `${PUBLIC_PATH}${props.assets.small}`,
      tablet: `${PUBLIC_PATH}${props.assets.medium}`,
      desktop: `${PUBLIC_PATH}${props.assets.large}`,
    });
  });
});

it('shows a background image', () => {
  const { find } = render();

  expect(find('ResponsiveBackgroundImage').text()).toEqual('children');
});
