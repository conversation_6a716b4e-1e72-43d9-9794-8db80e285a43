import styled from '@emotion/styled';
import PropTypes from 'prop-types';
import React from 'react';
import { useSelector } from 'react-redux';
import { BackgroundImage } from '@qga/roo-ui/components';
import { mediaQuery } from 'lib/styledSystem';
import { PUBLIC_PATH } from 'config';
import { isSafariOrIE } from 'lib/browser/isSafariOrIE';
import { getBrowser } from 'store/userEnvironment/userEnvironmentSelectors';

const ResponsiveBackgroundImage = styled(BackgroundImage)`
  background-image: url(${(props) => props.mobile});

  ${mediaQuery.minWidth.sm} {
    background-image: url(${(props) => props.tablet});
  }

  ${mediaQuery.minWidth.md} {
    background-image: url(${(props) => props.desktop});
  }
`;

const Hero = ({ height, assets, position, children }) => {
  const browser = useSelector(getBrowser);
  const isSafariOrIEBrowser = isSafariOrIE(browser);

  const { small, medium, large, legacyLarge, legacySmall } = assets;

  const mobile = `${PUBLIC_PATH}${small}`;
  const tablet = `${PUBLIC_PATH}${medium}`;
  const desktop = `${PUBLIC_PATH}${large}`;
  const legacySrcMobile = `${PUBLIC_PATH}${legacySmall}`;
  const legacySrcDesktop = `${PUBLIC_PATH}${legacyLarge}`;

  const src = isSafariOrIEBrowser
    ? { mobile: legacySrcMobile, tablet: legacySrcDesktop, desktop: legacySrcDesktop }
    : { mobile, tablet, desktop };

  return (
    <ResponsiveBackgroundImage {...src} backgroundPosition={position} height={height} mx={0} px={0} width={1}>
      {children}
    </ResponsiveBackgroundImage>
  );
};

Hero.propTypes = {
  children: PropTypes.node.isRequired,
  assets: PropTypes.shape({
    small: PropTypes.string.isRequired,
    medium: PropTypes.string.isRequired,
    large: PropTypes.string.isRequired,
    legacyLarge: PropTypes.string.isRequired,
    legacySmall: PropTypes.string.isRequired,
  }),
  height: PropTypes.oneOfType([PropTypes.string, PropTypes.array, PropTypes.number]),
  position: PropTypes.string,
};

Hero.defaultProps = {
  assets: {},
  height: '100%',
  position: '50% 50%',
};

export default Hero;
