import React from 'react';
import { mountUtils } from 'test-utils';
import DestinationHelmet from './DestinationHelmet';
import { HOTELS_BRAND_NAME, HOTELS_URL, PUBLIC_PATH } from 'config';
import { STORE_STATUS } from 'lib/enums/store';
import { getPathName } from 'store/router/routerSelectors/routerGetters';

jest.mock('store/router/routerSelectors/routerGetters');

const pathname = '/australia/vic/melbourne/';

const canonicalLink = `${HOTELS_URL}/australia/vic/melbourne/`;
const decorators = { helmet: true, router: true, store: true };
const render = (props) => mountUtils(<DestinationHelmet {...props} />, { decorators });

const mockCity = {
  regionName: 'Melbourne',
  status: STORE_STATUS.RESOLVED,
  template: 'city',
};

const mockState = {
  regionName: 'Victoria',
  status: STORE_STATUS.RESOLVED,
  template: 'state',
};

describe('when the destination resolves', () => {
  beforeEach(() => {
    getPathName.mockReturnValue(pathname);
  });
  it('sets the correct canonical link on helmet', () => {
    const { find } = render({ destination: mockCity });
    expect(find('link')).toHaveProp({ href: canonicalLink });
  });

  test.each`
    name                      | content
    ${'hotels-booking-stage'} | ${'destination-guide'}
    ${'og:site_name'}         | ${HOTELS_BRAND_NAME}
    ${'og:url'}               | ${canonicalLink}
    ${'robots'}               | ${'index, follow'}
  `('sets the meta for $name', ({ name, content }) => {
    const { find } = render({ destination: mockCity });
    expect(find(`meta[name="${name}"]`)).toHaveProp({ content });
  });

  describe('when the destination is not a city', () => {
    it('sets the title', () => {
      const { find } = render({ destination: mockState });
      expect(find('title')).toHaveText(`Victoria Hotels & Accommodation | ${HOTELS_BRAND_NAME}`);
    });

    test.each`
      name             | content
      ${'description'} | ${`Compare and book hotels in Victoria. Choose from over 200,000 accommodation options + earn 3 Qantas Points per $1 spent with ${HOTELS_BRAND_NAME}`}
      ${'og:title'}    | ${`Victoria Hotels & Accommodation | ${HOTELS_BRAND_NAME}`}
    `('sets the meta for $name', ({ name, content }) => {
      const { find } = render({ destination: mockState });
      expect(find(`meta[name="${name}"]`)).toHaveProp({ content });
    });
  });

  describe('when the destination is a city', () => {
    it('sets the title', () => {
      const { find } = render({ destination: mockCity });
      expect(find('title')).toHaveText(`Melbourne Accommodation - Find Melbourne Hotel Deals | ${HOTELS_BRAND_NAME}`);
    });

    test.each`
      name             | content
      ${'description'} | ${`Compare and book hotels in Melbourne. Choose from over 200,000 accommodation options + earn 3 Qantas Points per $1 spent with ${HOTELS_BRAND_NAME}`}
      ${'og:title'}    | ${`Melbourne Accommodation - Find Melbourne Hotel Deals | ${HOTELS_BRAND_NAME}`}
    `('sets the meta for $name', ({ name, content }) => {
      const { find } = render({ destination: mockCity });
      expect(find(`meta[name="${name}"]`)).toHaveProp({ content });
    });
  });

  describe('when the destination has a hero image', () => {
    it('sets the og:image meta tag', () => {
      const destinationWithHero = {
        ...mockCity,
        heroImage: {
          assets: {
            large: 'destinations/path/to/image.jpg',
          },
        },
      };

      const { find } = render({ destination: destinationWithHero });
      expect(find(`meta[name="og:image"]`)).toHaveProp({ content: `${PUBLIC_PATH}destinations/path/to/image.jpg` });
    });
  });
});

describe('when the destination cannot be found', () => {
  const destination = {
    status: STORE_STATUS.FAILED,
  };

  it('sets the correct page title with helmet', () => {
    const { find } = render({ destination });
    expect(find('title')).toHaveText(`Hotels & Accommodation | ${HOTELS_BRAND_NAME}`);
  });

  it('sets the correct canonical link on helmet', () => {
    const { find } = render({ destination });
    expect(find('link')).toHaveProp({ href: canonicalLink });
  });

  test.each`
    name                      | content
    ${'description'}          | ${`Compare and book hotels with ${HOTELS_BRAND_NAME}. Choose from over 200,000 accommodation options + earn 3 Qantas Points per $1 spent with ${HOTELS_BRAND_NAME}`}
    ${'hotels-booking-stage'} | ${'destination-guide'}
    ${'robots'}               | ${'noindex, nofollow'}
  `('sets the meta for $name', ({ name, content }) => {
    const { find } = render({ destination });
    expect(find(`meta[name="${name}"]`)).toHaveProp({ content });
  });
});
