import React from 'react';
import { useSelector } from 'react-redux';
import { getPathName } from 'store/router/routerSelectors/routerGetters';
import PropTypes from 'prop-types';
import Head from 'next/head';
import { HOTELS_BRAND_NAME, HOTELS_URL, PUBLIC_PATH } from 'config';
import { STORE_STATUS } from 'lib/enums/store';

const canonicalLink = (pathname) => `${HOTELS_URL}${pathname}`;

const getMeta = ({ destination, pathname }) => {
  const meta = [
    { name: 'hotels-booking-stage', content: 'destination-guide' },
    { name: 'og:site_name', content: HOTELS_BRAND_NAME },
  ];

  const hasResolved = pathname && destination.status === STORE_STATUS.RESOLVED;
  const heroImage = destination?.heroImage?.assets?.large;

  if (hasResolved) {
    meta.push({
      name: 'description',
      content: `Compare and book hotels in ${destination.regionName}. Choose from over 200,000 accommodation options + earn 3 Qantas Points per $1 spent with ${HOTELS_BRAND_NAME}`,
    });
    meta.push({ name: 'robots', content: 'index, follow' });
    meta.push({ name: 'og:title', content: getPageTitle(destination) });
    meta.push({ name: 'og:url', content: canonicalLink(pathname) });

    heroImage && meta.push({ name: 'og:image', content: `${PUBLIC_PATH}${destination.heroImage.assets.large}` });
  } else {
    meta.push({
      name: 'description',
      content: `Compare and book hotels with ${HOTELS_BRAND_NAME}. Choose from over 200,000 accommodation options + earn 3 Qantas Points per $1 spent with ${HOTELS_BRAND_NAME}`,
    });
    meta.push({ name: 'robots', content: 'noindex, nofollow' });
  }

  return meta;
};

const getPageTitle = (destination = {}) => {
  const { regionName, template } = destination;
  const isCity = regionName && template === 'city';
  const isCountryOrState = regionName && ['country', 'state'].includes(template);

  if (isCity) {
    return `${regionName} Accommodation - Find ${regionName} Hotel Deals | ${HOTELS_BRAND_NAME}`;
  }

  if (isCountryOrState) {
    return `${regionName} Hotels & Accommodation | ${HOTELS_BRAND_NAME}`;
  }

  return `Hotels & Accommodation | ${HOTELS_BRAND_NAME}`;
};

const DestinationHelmet = (props) => {
  const pathname = useSelector(getPathName);
  return (
    <Head>
      <title>{getPageTitle(props.destination)}</title>
      {getMeta({ ...props, pathname }).map(({ name, content }) => (
        <meta key={name} name={name} content={content} />
      ))}
      <link rel="canonical" href={canonicalLink(pathname)} />
    </Head>
  );
};

DestinationHelmet.propTypes = {
  destination: PropTypes.shape({
    heroImage: PropTypes.shape({
      assets: PropTypes.shape({
        large: PropTypes.string,
      }),
    }),
    regionName: PropTypes.string,
    status: PropTypes.string.isRequired,
  }).isRequired,
};

export default DestinationHelmet;
