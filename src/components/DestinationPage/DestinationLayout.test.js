import React from 'react';
import DestinationLayout from './DestinationLayout';
import { mountUtils } from 'test-utils';
import { STORE_STATUS } from 'lib/enums/store';
import { fetchMerchandise } from 'store/destination/destinationActions';
import { getDestination } from 'store/destination/destinationSelectors';

jest.mock('store/destination/destinationSelectors');

mountUtils.mockComponent('Breadcrumbs');
mountUtils.mockComponent('DestinationHelmet');
mountUtils.mockComponent('FourOhFourPage');
mountUtils.mockComponent('Templates');

const decorators = { helmet: true, router: true, store: true, theme: true };

const render = () => mountUtils(<DestinationLayout />, { decorators });

const breadcrumbs = [{ title: 'title', url: 'url' }];
const destination = {
  breadcrumbs,
  regionName: 'Melbourne',
  mdSummary: '# MD Summary',
  status: STORE_STATUS.RESOLVED,
  template: 'city',
};

beforeEach(() => {
  jest.clearAllMocks();
});

describe('when the destination has failed', () => {
  beforeEach(() => {
    getDestination.mockReturnValue({
      status: STORE_STATUS.FAILED,
    });
  });

  it('renders the 404 page', () => {
    const { find } = render();

    expect(find('FourOhFourPage')).toExist();
  });
});

describe('when the destination is pending', () => {
  beforeEach(() => {
    getDestination.mockReturnValue({
      status: STORE_STATUS.PENDING,
    });
  });

  it('shows the loading spinner', () => {
    const { find } = render();

    expect(find('Loader')).toHaveProp({
      isLoading: true,
    });
  });
});

describe('when the destination is resolved', () => {
  beforeEach(() => {
    getDestination.mockReturnValue({ ...destination, breadcrumbs });
  });

  it('renders <DestinationHelmet />', () => {
    const { find } = render();

    expect(find('DestinationHelmet')).toExist();
  });

  it('hides the loading spinner', () => {
    const { find } = render();

    expect(find('Loader')).toHaveProp({
      isLoading: false,
    });
  });

  describe('when the template is NOT country', () => {
    it('shows the breadcrumbs', () => {
      const { find } = render();

      expect(find('Breadcrumbs')).toHaveProp({
        crumbs: breadcrumbs,
      });
    });
  });

  describe('when the template is country', () => {
    beforeEach(() => {
      getDestination.mockReturnValue({
        template: 'country',
      });
    });

    it('does NOT show the breadcrumbs', () => {
      const { find } = render();

      expect(find('Breadcrumbs')).not.toExist();
    });
  });

  it('renders the <Templates /> component', () => {
    const { find } = render();

    expect(find('Templates')).toExist();
  });

  describe('when the destination contains a regionId', () => {
    beforeEach(() => {
      getDestination.mockReturnValue({ ...destination, regionId: 1234 });
    });

    it('calls fetchMerchandise', () => {
      const action = fetchMerchandise({ limit: 4, regionId: 1234, subRegionLimit: 6 });
      const { decorators } = render();

      expect(decorators.store.dispatch).toHaveBeenCalledWith(action);
    });
  });

  describe('when the destination does NOT contain a regionId', () => {
    beforeEach(() => {
      getDestination.mockReturnValue({ ...destination, regionId: null });
    });

    it('does NOT call fetchMerchandise', () => {
      const action = fetchMerchandise({ limit: 4, regionId: 1234, subRegionLimit: 6 });
      const { decorators } = render();

      expect(decorators.store.dispatch).not.toHaveBeenCalledWith(action);
    });
  });
});
