import PropTypes from 'prop-types';
import React from 'react';
import { Link } from '@qga/roo-ui/components';
import AppLink from 'components/AppLink';

const isHttpRegex = new RegExp(/^https?/);

const ResponsiveLink = ({ children, url, ...rest }) => {
  const isExternal = isHttpRegex.test(url);
  const linkProps = isExternal ? { as: 'a', href: url } : { as: AppLink, to: url };

  return (
    <Link {...rest} {...linkProps}>
      {children}
    </Link>
  );
};

ResponsiveLink.propTypes = {
  children: PropTypes.node,
  url: PropTypes.string.isRequired,
};

ResponsiveLink.defaultProps = {
  children: null,
};

export default ResponsiveLink;
