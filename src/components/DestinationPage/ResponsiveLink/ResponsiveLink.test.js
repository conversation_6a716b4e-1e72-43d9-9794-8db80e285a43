import React from 'react';
import ResponsiveLink from './ResponsiveLink';
import { mountUtils } from 'test-utils';
import AppLink from 'components/AppLink';

const decorators = { router: true, store: true, theme: true };
const render = (props) => mountUtils(<ResponsiveLink {...props}>children</ResponsiveLink>, { decorators });

describe('with an internal link', () => {
  it('renders a router link', () => {
    const { find } = render({ url: '/internal/path' });

    expect(find('Link').first()).toHaveProp({
      as: AppLink,
      to: '/internal/path',
    });
  });

  it('renders children', () => {
    const { find } = render({ url: '/internal/path' });

    expect(find('Link').first().text()).toEqual('children');
  });
});

describe('with an external link', () => {
  it('renders an anchor link', () => {
    const { find } = render({ url: 'http://qantas.hotels' });

    expect(find('Link').first()).toHaveProp({
      as: 'a',
      href: 'http://qantas.hotels',
    });
  });

  it('renders children', () => {
    const { find } = render({ url: 'http://qantas.hotels' });

    expect(find('Link').text()).toEqual('children');
  });
});
