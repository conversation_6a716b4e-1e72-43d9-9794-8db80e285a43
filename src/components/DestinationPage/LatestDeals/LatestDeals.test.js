import React from 'react';
import LatestDeals from './LatestDeals';
import { mountUtils } from 'test-utils';
import { getDestination, getMerchandise } from 'store/destination/destinationSelectors';
import { STORE_STATUS } from 'lib/enums/store';
import { useDataLayer } from 'hooks/useDataLayer';

jest.mock('store/destination/destinationSelectors');
jest.mock('hooks/useDataLayer');

const emitInteractionEvent = jest.fn();

mountUtils.mockComponent('DealsSlider');

const decorators = { router: true, store: true, theme: true };
const render = () => mountUtils(<LatestDeals />, { decorators });

const mockDestination = {
  breadcrumbs: [{ title: 'Australia', url: '/australia' }],
  regionName: 'Melbourne',
  searchLocation: 'searchLocation',
};
const mockSearchUrl = '/search/list?adults=2&location=searchLocation';

beforeEach(() => {
  jest.resetAllMocks();
  getDestination.mockReturnValue(mockDestination);
  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

describe('when the merchandise is pending', () => {
  beforeEach(() => {
    getMerchandise.mockReturnValue({
      status: STORE_STATUS.PENDING,
    });
  });

  it('shows the loading indicator', () => {
    const { find } = render();

    expect(find('Loader')).toHaveProp({ isLoading: true });
  });
});

describe('when the merchandise is resolved', () => {
  beforeEach(() => {
    getMerchandise.mockReturnValue({
      status: STORE_STATUS.RESOLVED,
      totalProperties: 1234,
    });
  });

  it('hides the loading indicator', () => {
    const { find } = render();

    expect(find('Loader')).toHaveProp({ isLoading: false });
  });

  it('shows the heading', () => {
    const { find } = render();

    expect(find('Heading').text()).toEqual('Latest hotel deals in Melbourne');
  });

  it('shows the deals slider', () => {
    const { find } = render();

    expect(find('DealsSlider')).toExist();
  });

  it('shows the search more link', () => {
    const { find } = render();
    const wrapper = find('MoreHotelsButton');

    expect(wrapper).toHaveProp({ to: mockSearchUrl });
    expect(wrapper.text()).toEqual('Search more hotels');
  });

  it('shows the total property count', () => {
    const { findByTestId } = render();

    expect(findByTestId('total-properties').text()).toEqual('Search 1234 hotels in Melbourne');
  });

  it('emits an event to the data layer when clicked', () => {
    const { find } = render();
    find('MoreHotelsButton').simulate('click');

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Search More Button',
      value: 'Button Selected',
    });
  });
});
