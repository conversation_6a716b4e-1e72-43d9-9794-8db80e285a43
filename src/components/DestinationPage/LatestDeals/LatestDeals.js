import React, { useCallback } from 'react';
import { useSelector } from 'react-redux';
import { Box, Flex, Heading, OutlineButton, Text } from '@qga/roo-ui/components';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import DealsSlider from './DealsSlider';
import Loader from 'components/Loader/Loader';
import { STORE_STATUS } from 'lib/enums/store';
import { getDestination, getMerchandise } from 'store/destination/destinationSelectors';
import LoaderComponent from 'components/DestinationPage/LoaderComponent';
import { useDataLayer } from 'hooks/useDataLayer';
import AppLink from 'components/AppLink';

const MoreHotelsButton = styled(OutlineButton)`
  &:hover {
    color: ${themeGet('colors.white')};
  }
`;

const LatestDeals = () => {
  const { regionName, searchLocation } = useSelector(getDestination);
  const { status: merchandiseStatus, totalProperties } = useSelector(getMerchandise);
  const { emitInteractionEvent } = useDataLayer();

  const handleClick = useCallback(() => {
    emitInteractionEvent({ type: 'Search More Button', value: 'Button Selected' });
  }, [emitInteractionEvent]);

  const isLoading = merchandiseStatus === STORE_STATUS.PENDING;
  const searchLink = `/search/list?adults=2&location=${searchLocation}`;

  return (
    <Loader isLoading={isLoading} loaderComponent={LoaderComponent}>
      <Flex justifyContent="space-between" flexDirection="column">
        <Box mx="auto">
          <Heading.h2 fontSize="lg">Latest hotel deals in {regionName}</Heading.h2>
        </Box>

        <DealsSlider />

        <Box mx="auto">
          {totalProperties && (
            <Text color="greys.steel" display="block" fontSize="base" textAlign="center" my={4} data-testid="total-properties">
              Search {totalProperties} hotels in {regionName}
            </Text>
          )}
          <MoreHotelsButton as={AppLink} to={searchLink} onClick={handleClick} variant="primary">
            Search more hotels
          </MoreHotelsButton>
        </Box>
      </Flex>
    </Loader>
  );
};

export default LatestDeals;
