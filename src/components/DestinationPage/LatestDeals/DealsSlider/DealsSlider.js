import React, { Fragment, useCallback, useEffect, useState } from 'react';
import { find, isEmpty, reduce, startCase, uniqBy } from 'lodash';
import { useDispatch, useSelector } from 'react-redux';
import { Flex, Text } from '@qga/roo-ui/components';
import { Grid } from './primitives';
import Filters from './Filters';
import Deal from './Deal';
import { useDataLayer } from 'hooks/useDataLayer';
import { getDestination, getMerchandise } from 'store/destination/destinationSelectors';
import { emitLatestDealsResults } from 'store/destination/destinationActions';
import useSelectItemEvent from 'hooks/useSelectItemEvent';
import { getPointsConversion } from 'store/pointsBurnTiers/pointsBurnSelectors';

const NoAvailableDeals = () => (
  <Flex alignItems="center" justifyContent="center" py={2}>
    <Text fontSize="base" data-testid="no-offers">
      Sorry, we currently have no offers in this area.
    </Text>
  </Flex>
);

const DealsSlider = () => {
  const merchandise = useSelector(getMerchandise);
  const { emitInteractionEvent } = useDataLayer();
  const [filter, setFilter] = useState('popular');

  const properties = getProperties({ merchandise, filter });
  const filters = buildFilters(merchandise);

  const [fired, setFired] = useState(false);
  const dispatch = useDispatch();
  const destination = useSelector(getDestination);
  const location = destination?.searchLocation;
  const { fireSelectItemEvent } = useSelectItemEvent();
  const pointsConversion = useSelector(getPointsConversion);
  const latestDealsResult = reduce(
    properties,
    (accum, result) => {
      const mappedProperty = {
        property: { id: result?.id, name: result?.name, hasOffer: result?.offer?.promotion ? true : false },
        roomType: { name: result?.offer?.roomType },
        offer: {
          charges: {
            total: { amount: result?.charges?.total?.amount ? result.charges.total.amount.toString() : '', currency: 'AUD' },
            totalCash: {
              amount: result?.charges?.totalCash?.amount ? result?.charges?.totalCash?.amount.toString() : '0',
              currency: 'AUD',
            },
          },
        },
      };
      return [...accum, mappedProperty];
    },
    [],
  );

  useEffect(() => {
    const hasLevels = !!pointsConversion?.levels?.length;
    const query = { payWith: 'cash', location: location };
    if (!isEmpty(latestDealsResult) && !fired && hasLevels) {
      dispatch(
        emitLatestDealsResults({
          results: latestDealsResult,
          query: query,
          listName: 'Latest Deals Destination Page',
          category: 'jetstar',
          type: 'list',
          currency: 'AUD',
          pointsConversion: pointsConversion,
        }),
      );
      setFired(true);
    }
  }, [dispatch, fired, latestDealsResult, location, pointsConversion]);

  const handleFilterChange = useCallback(
    (selection) => {
      const { label, value } = find(filters, ['value', selection]);

      setFilter(value);
      emitInteractionEvent({ type: 'Latest Deals Navigation', value: `${startCase(label)} Selected` });
    },
    [filters, emitInteractionEvent],
  );
  const hasDeals = properties && properties.length > 0;

  const handleClick = (result) =>
    fireSelectItemEvent({
      listName: `Latest hotel deals in ${destination.regionName}`,
      location: destination.regionName,
      type: 'list',
      property: {
        id: result.id,
        name: result.name,
        category: 'hotels',
        propertyFacilities: result.offer?.inclusions ?? [],
      },
      offer: {
        name: result.offer?.roomType,
        promotion: result.offer?.promotion,
        charges: {
          total: { amount: result?.charges?.total?.amount ? result.charges.total.amount.toString() : '', currency: 'AUD' },
        },
      },
      payWith: 'cash',
      roomType: null,
      query: null,
    });

  return (
    <Fragment>
      {filters && <Filters onChange={handleFilterChange} items={filters} value={filter} />}
      {hasDeals ? (
        <Fragment>
          <Grid>
            {properties.map((property, index) => (
              <Deal key={`property-${index}`} {...property} onClick={() => handleClick(property)} />
            ))}
          </Grid>
        </Fragment>
      ) : (
        <NoAvailableDeals />
      )}
    </Fragment>
  );
};

const buildFilters = ({ subRegions }) => {
  if (subRegions) {
    const items = [{ label: 'Popular', value: 'popular' }];

    for (const subRegion of subRegions) {
      items.push({ label: subRegion.regionName, value: subRegion.regionId });
    }

    /**
     * NOTE: We have to add this in due to the merchandising API
     * occasionally returning duplicate sub regions.
     *
     * We can remove this step if/when they filter duplicates.
     */
    const uniqueItems = uniqBy(items, 'label');

    return uniqueItems;
  }
};

const getProperties = ({ filter, merchandise }) => {
  if (filter === 'popular') {
    return merchandise?.properties;
  } else {
    const subRegion = find(merchandise?.subRegions, ['regionId', filter]);

    return subRegion?.properties;
  }
};

export default DealsSlider;
