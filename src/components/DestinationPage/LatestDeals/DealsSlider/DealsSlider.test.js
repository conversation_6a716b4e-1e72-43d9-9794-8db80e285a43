import React from 'react';
import { act } from 'react-dom/test-utils';
import DealsSlider from './DealsSlider';
import { mountUtils } from 'test-utils';
import { getDestination, getMerchandise } from 'store/destination/destinationSelectors';
import { STORE_STATUS } from 'lib/enums/store';
import { useDataLayer } from 'hooks/useDataLayer';
import { useDispatch } from 'react-redux';
import { emitLatestDealsResults } from 'store/destination/destinationActions';
import { getPointsConversion } from 'store/pointsBurnTiers/pointsBurnSelectors';

jest.mock('store/destination/destinationSelectors');
jest.mock('hooks/useDataLayer');
jest.mock('store/pointsBurnTiers/pointsBurnSelectors');
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: jest.fn(),
}));

const emitInteractionEvent = jest.fn();
const dispatch = jest.fn();

mountUtils.mockComponent('Filters');
mountUtils.mockComponent('Deal');

const decorators = { store: true, theme: true };
const render = () => mountUtils(<DealsSlider />, { decorators });

const mockProperties = [
  {
    id: '1',
    name: 'Property 1',
    charges: {
      total: {
        amount: '300.00',
        currency: 'AUD',
      },
    },
    offer: { promotion: 'promotion', roomType: 'room 1' },
  },
  {
    id: '2',
    name: 'Property 2',
    charges: {
      total: {
        amount: '400.00',
        currency: 'AUD',
      },
    },
    offer: { roomType: 'room 2' },
  },
];

const mockMerchandise = {
  properties: mockProperties,
  subRegions: [
    { regionName: 'CBD', regionId: '123', properties: mockProperties },
    { regionName: 'Bondi', regionId: '456', properties: [] },
    { regionName: 'Bondi', regionId: '789', properties: [] },
  ],
};

const regionName = 'Melbourne, VIC, Australia';
const query = { location: regionName, payWith: 'cash' };

const latestDealsResult = [
  {
    offer: {
      charges: {
        total: {
          amount: '300.00',
          currency: 'AUD',
        },
        totalCash: {
          amount: '0',
          currency: 'AUD',
        },
      },
    },
    roomType: {
      name: 'room 1',
    },
    property: {
      id: '1',
      name: 'Property 1',
      hasOffer: true,
    },
  },
  {
    offer: {
      charges: {
        total: {
          amount: '400.00',
          currency: 'AUD',
        },
        totalCash: {
          amount: '0',
          currency: 'AUD',
        },
      },
    },
    roomType: {
      name: 'room 2',
    },
    property: {
      id: '2',
      name: 'Property 2',
      hasOffer: false,
    },
  },
];

const pointsConversion = {
  levels: [
    { min: 0, max: 150, rate: 0.00824 },
    { min: 150, max: 400, rate: 0.00834 },
    { min: 400, max: 650, rate: 0.00848 },
    { min: 650, max: 900, rate: 0.00875 },
    { min: 900, max: null, rate: 0.00931 },
  ],
  name: 'VERSION11',
};

const payload = {
  results: latestDealsResult,
  query: query,
  listName: 'Latest Deals Destination Page',
  category: 'jetstar',
  type: 'list',
  currency: 'AUD',
  pointsConversion: pointsConversion,
};

beforeEach(() => {
  jest.resetAllMocks();
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  useDispatch.mockReturnValue(dispatch);
  getPointsConversion.mockReturnValue(pointsConversion);
});

describe('when merchandise is available', () => {
  beforeEach(() => {
    getMerchandise.mockReturnValue(mockMerchandise);
    getDestination.mockReturnValue({ searchLocation: regionName });
  });

  describe('subRegions', () => {
    describe('when available', () => {
      it('shows the filters', () => {
        const { find } = render();

        expect(find('Filters')).toHaveProp({
          value: 'popular',
        });
      });

      it('filters duplicates from the subRegions', () => {
        const { find } = render();

        expect(find('Filters')).toHaveProp({
          items: [
            { label: 'Popular', value: 'popular' },
            { label: 'CBD', value: '123' },
            { label: 'Bondi', value: '456' },
          ],
        });
      });
    });

    describe('when not available', () => {
      beforeEach(() => {
        getMerchandise.mockReturnValue({ subRegions: null });
      });

      it('does not show the filters', () => {
        const { find } = render();

        expect(find('Filters')).not.toExist();
      });
    });
  });

  it('shows the deals', () => {
    const { find } = render();

    expect(find('Deal')).toHaveLength(2);
  });

  it('hides the `no offers` messaging', () => {
    const { findByTestId } = render();

    expect(findByTestId('no-offers')).not.toExist();
  });

  it('emits an event to the data layer when clicked', () => {
    const { find } = render();
    act(() => {
      find('Filters').props().onChange('456');
    });

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Latest Deals Navigation',
      value: 'Bondi Selected',
    });
  });

  it('emits emitLatestDealsResults', () => {
    render();
    expect(dispatch).toHaveBeenCalledWith(emitLatestDealsResults(payload));
  });
});

describe('when merchandise is unavailable', () => {
  beforeEach(() => {
    getMerchandise.mockReturnValue({
      status: STORE_STATUS.FAILED,
    });
  });

  it('hides the filters', () => {
    const { find } = render();

    expect(find('Filters')).not.toExist();
  });

  it('shows the `no offers` messaging', () => {
    const { findByTestId } = render();

    expect(findByTestId('no-offers')).toHaveText('Sorry, we currently have no offers in this area.');
  });

  it('hides the deals', () => {
    const { find } = render();

    expect(find('Deal')).not.toExist();
  });
});
