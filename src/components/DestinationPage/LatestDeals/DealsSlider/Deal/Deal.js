import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { parseUrl, stringify } from 'query-string';
import startCase from 'lodash/startCase';
import { Box, Flex, Heading, Text, StarRating } from '@qga/roo-ui/components';
import Image from 'components/Image';
import PromotionalSash from 'components/PromotionalSash';
import Currency from 'components/Currency';
import { CardWithHover } from 'components/DestinationPage/LatestDeals/DealsSlider/primitives';
import { useDataLayer } from 'hooks/useDataLayer';
import AppLink from 'components/AppLink';
import ExpandedClickableArea from 'components/ExpandedClickableArea';

const Deal = ({ address, charges, id: propertyId, mainImage, name, offer, pointsEarned, propertyAvailabilityUrl, rating, onClick }) => {
  const { emitInteractionEvent } = useDataLayer();

  const handleClick = useCallback(() => {
    onClick && onClick();
    emitInteractionEvent({ type: 'Property Card', value: `${startCase(name)} Selected` });
  }, [onClick, emitInteractionEvent, name]);

  const promotionName = offer?.promotion;

  const { caption: imageCaption, urlMedium, urlLarge } = mainImage || {};
  const { currency, total } = charges;
  const ariaCurrency = currency === 'PTS' ? 'points' : 'dollars';

  const parsedUrl = parseUrl(propertyAvailabilityUrl);
  const queryString = stringify({ ...parsedUrl.query, recommended: 'true' });
  const propertyLink = `/properties/${propertyId}?${queryString}`;

  const imageSrcSet = `${urlLarge} 2x, ${urlMedium} 1x`;

  return (
    <CardWithHover>
      <ExpandedClickableArea>
        <Flex flexDirection="column" height="100%">
          <Box position="relative" mb={4}>
            <Image alt={imageCaption} src={urlMedium} srcSet={imageSrcSet} lazy height={175} />

            {promotionName && (
              <Box position="absolute" left={10} top={0}>
                <PromotionalSash promotionName={promotionName} />
              </Box>
            )}
          </Box>

          <Box flex={1} px={5}>
            <AppLink
              onClick={handleClick}
              to={propertyLink}
              aria-label={`${name} from ${parseInt(total.amount)} ${ariaCurrency}`}
              data-expanded-clickable-area-target
            >
              <Heading.h3 color="greys.charcoal" fontSize="base" mb={0}>
                {name}
              </Heading.h3>
            </AppLink>
            <Text display="block" color="greys.steel" fontSize="sm" mb={2} data-testid="hotel-suburb">
              {address.suburb}
            </Text>
            <StarRating rating={rating.value} ratingType={rating.type} size={16} />
          </Box>

          <Flex alignItems="flex-end" flexDirection="column" px={5}>
            <Text color="greys.charcoal" mb={1}>
              1 night from
            </Text>
            <Flex alignItems="flex-end" data-testid="deal-price">
              <Text color="brand.primary" mx={1}>
                {currency}
              </Text>
              <Currency
                amount={total.amount}
                currency={currency}
                hideCurrency
                roundToCeiling
                fontSize="xl"
                color="brand.primary"
                fontWeight="bold"
              />
            </Flex>

            {pointsEarned?.qffPoints && <Text color="greys.charcoal">Earn {pointsEarned.qffPoints} Qantas Points^</Text>}
          </Flex>
        </Flex>
      </ExpandedClickableArea>
    </CardWithHover>
  );
};

Deal.propTypes = {
  address: PropTypes.shape({
    suburb: PropTypes.string.isRequired,
  }).isRequired,
  charges: PropTypes.shape({
    currency: PropTypes.string.isRequired,
    total: PropTypes.shape({
      amount: PropTypes.number.isRequired,
    }).isRequired,
  }).isRequired,
  id: PropTypes.string.isRequired,
  mainImage: PropTypes.shape({
    caption: PropTypes.string,
    urlLarge: PropTypes.string,
    urlMedium: PropTypes.string,
    urlSmall: PropTypes.string,
  }),
  name: PropTypes.string.isRequired,
  offer: PropTypes.shape({
    promotion: PropTypes.string,
  }),
  pointsEarned: PropTypes.shape({
    qffPoints: PropTypes.number.isRequired,
  }).isRequired,
  propertyAvailabilityUrl: PropTypes.string.isRequired,
  rating: PropTypes.shape({
    type: PropTypes.string.isRequired,
    value: PropTypes.number.isRequired,
  }).isRequired,
  onClick: PropTypes.func,
};

Deal.defaultProps = {
  offer: {
    promotion: null,
  },
  mainImage: {},
};

export default Deal;
