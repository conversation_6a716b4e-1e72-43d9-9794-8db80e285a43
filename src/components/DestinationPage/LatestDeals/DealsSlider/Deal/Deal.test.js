import React from 'react';
import Deal from './Deal';
import { mountUtils } from 'test-utils';
import { useDataLayer } from 'hooks/useDataLayer';

mountUtils.mockComponent('Image');

jest.mock('hooks/useDataLayer');

const emitInteractionEvent = jest.fn();

const defaultProps = {
  address: {
    suburb: 'Sydney',
  },
  charges: {
    total: {
      amount: 298.51,
    },
    currency: 'AUD',
  },
  id: '1234',
  mainImage: {
    caption: 'caption',
    urlLarge: 'urlLarge.jpg',
    urlMedium: 'urlMedium.jpg',
    urlSmall: 'urlSmall.jpg',
  },
  name: 'Hotel name',
  pointsEarned: {
    qffPoints: 501,
  },
  propertyAvailabilityUrl: 'https://www.qantas.com/hotels/properties/1234?adults=2&checkIn=2020-06-28&checkOut=2020-06-29',
  rating: {
    type: 'AAA',
    value: 4.5,
  },
};

const decorators = { router: true, theme: true, store: true };
const render = (props) => mountUtils(<Deal {...defaultProps} {...props} />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

it('renders the link', () => {
  const { find } = render();

  expect(find('AppLink')).toHaveProp({
    to: '/properties/1234?adults=2&checkIn=2020-06-28&checkOut=2020-06-29&recommended=true',
    'aria-label': 'Hotel name from 298 dollars',
  });
});

it('shows the image', () => {
  const { find } = render();

  expect(find('Image')).toHaveProp({
    alt: 'caption',
    src: 'urlMedium.jpg',
    srcSet: 'urlLarge.jpg 2x, urlMedium.jpg 1x',
  });
});

it('shows the hotel name', () => {
  const { find } = render();

  expect(find('Heading').text()).toEqual('Hotel name');
});

it('shows the hotel suburb', () => {
  const { findByTestId } = render();

  expect(findByTestId('hotel-suburb').text()).toEqual('Sydney');
});

it('shows the star rating', () => {
  const { find } = render();

  expect(find('StarRating')).toHaveProp({
    rating: 4.5,
    ratingType: 'AAA',
  });
});

it('shows the deal price', () => {
  const { findByTestId } = render();

  expect(findByTestId('deal-price').text()).toEqual('AUD$299');
});

describe('with a promotion', () => {
  it('shows the promotional sash', () => {
    const offer = { promotion: '99% discount' };
    const { find } = render({ offer });

    expect(find('PromotionalSash')).toHaveProp({
      promotionName: '99% discount',
    });
  });
});

it('emits an event to the data layer when clicked', () => {
  const { find } = render();
  find('AppLink').simulate('click');

  expect(emitInteractionEvent).toHaveBeenCalledWith({
    type: 'Property Card',
    value: 'Hotel Name Selected',
  });
});
