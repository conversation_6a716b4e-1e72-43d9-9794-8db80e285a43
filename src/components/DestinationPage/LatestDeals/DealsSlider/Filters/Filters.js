import PropTypes from 'prop-types';
import React, { useCallback } from 'react';
import { Box, Flex, Hide } from '@qga/roo-ui/components';
import { NavLink, NavSelect } from 'components/DestinationPage/LatestDeals/DealsSlider/primitives';

const DesktopFilters = ({ items, onChange, value: currentValue }) => (
  <Flex>
    {items.map((item, index) => (
      <NavLink key={`desktop-filter-${index}`} onClick={() => onChange(item.value)} isActive={currentValue === item.value}>
        {item.label}
      </NavLink>
    ))}
  </Flex>
);

DesktopFilters.propTypes = {
  items: PropTypes.array,
  onChange: PropTypes.func.isRequired,
  value: PropTypes.string.isRequired,
};

DesktopFilters.defaultProps = {
  items: [],
};

const MobileFilters = ({ items, onChange, value }) => {
  const handleChange = useCallback(
    (event) => {
      onChange(event.target.value);
    },
    [onChange],
  );

  return (
    <NavSelect onChange={handleChange} value={value} width="100%">
      {items.map((item, index) => (
        <option key={`mobile-filter-${index}`} value={item.value}>
          {item.label}
        </option>
      ))}
    </NavSelect>
  );
};

MobileFilters.propTypes = {
  items: PropTypes.array,
  onChange: PropTypes.func.isRequired,
  value: PropTypes.string.isRequired,
};

MobileFilters.defaultProps = {
  items: [],
};

const Filters = (props) => (
  <Flex alignItems="flex-start" py={6}>
    <Hide as={Box} xs width={1}>
      <DesktopFilters {...props} />
    </Hide>

    <Hide as={Box} sm md lg width={1}>
      <MobileFilters {...props} />
    </Hide>
  </Flex>
);

export default Filters;
