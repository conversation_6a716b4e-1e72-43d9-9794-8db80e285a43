import React from 'react';
import Filters from './Filters';
import { mountUtils } from 'test-utils';

const items = [
  { label: 'Popular', value: 'popular' },
  { label: 'CBD', value: 'CBD' },
  { label: 'Beach', value: 'Beach' },
];
const mockOnChange = jest.fn();
const props = {
  items,
  onChange: mockOnChange,
  value: 'popular',
};

const decorators = { theme: true };
const render = () => mountUtils(<Filters {...props} />, { decorators });

beforeEach(() => {
  jest.resetAllMocks();
});

describe('desktop', () => {
  it('renders the menu', () => {
    const { find } = render();

    expect(find('NavLink')).toHaveLength(3);
  });

  describe('when selecting the filter', () => {
    it('triggers an onChange event', () => {
      const { find } = render();

      const wrapper = find('NavLink').at(1);

      wrapper.simulate('click');
      wrapper.update();

      expect(mockOnChange).toHaveBeenCalledWith('CBD');
    });
  });
});

describe('mobile', () => {
  it('renders the select dropdown', () => {
    const { find } = render();

    expect(find('NavSelect')).toExist();
  });

  it('renders the filter options', () => {
    const { find } = render();

    expect(find('option')).toHaveLength(3);
  });

  describe('when selecting the filter', () => {
    it('triggers an onChange event', () => {
      const { find } = render();

      const wrapper = find('select');

      wrapper.simulate('change', { target: { value: 'Beach' } });
      wrapper.update();

      expect(mockOnChange).toHaveBeenCalledWith('Beach');
    });
  });
});
