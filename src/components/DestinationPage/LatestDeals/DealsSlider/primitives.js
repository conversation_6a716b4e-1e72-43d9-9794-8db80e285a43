import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { Box, Card, NakedButton, Select } from '@qga/roo-ui/components';
import { mediaQuery } from 'lib/styledSystem';

export const CardWithHover = styled(Card)`
  padding: 0;
  padding-bottom: ${themeGet('space.4')};
  margin-bottom: ${themeGet('space.4')};
  position: relative;
  overflow: hidden;

  box-shadow: ${themeGet('shadows.hard')};
  transition: all 0.1s linear;

  text-decoration: none;

  &:hover {
    box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.1);
  }
`;

export const Grid = styled(Box)`
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;

  > * {
    width: 100%;
  }

  ${mediaQuery.minWidth.sm} {
    > * {
      width: 49%;
    }
  }

  ${mediaQuery.minWidth.md} {
    > * {
      width: 24%;
    }
  }
`;

export const NavLink = styled(NakedButton)`
  background: transparent;
  color: ${(props) => (props.variant === 'dark' ? 'white' : themeGet('colors.greys.charcoal'))};
  padding: ${themeGet('space.2')} 0;
  border-bottom: 3px solid;
  border-color: ${(props) => (props.isActive ? themeGet('colors.brand.primary') : 'transparent')};
  transition: border-color 0.2s ease-in-out;
  font-size: ${themeGet('fontSizes.base')};

  &:first-of-type {
    margin-right: ${themeGet('space.5')};
  }

  &:not(:first-of-type) {
    margin: 0 ${themeGet('space.5')};
  }

  &:hover,
  &:focus {
    border-color: ${themeGet('colors.brand.primary')};
    color: ${(props) => (props.variant === 'dark' ? 'white' : themeGet('colors.greys.charcoal'))};
  }
`;

export const NavSelect = styled(Select)`
  padding: ${themeGet('space.4')} ${themeGet('space.2')};
  font-size: ${themeGet('fontSizes.base')};
  background-color: ${themeGet('colors.white')};
  box-shadow: ${themeGet('shadows.hard')};
  border-radius: 0.25rem;
  border: 0;
  appearance: none;

  &:focus {
    border: ${themeGet('focus.outline')};
  }
`;
