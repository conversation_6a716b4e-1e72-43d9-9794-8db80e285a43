import React, { Fragment, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useSelector, useDispatch } from 'react-redux';
import Breadcrumbs from 'components/Breadcrumbs';
import PageBlock from 'components/PageBlock';
import { fetchMerchandise, clearMerchandise } from 'store/destination/destinationActions';
import { getDestination } from 'store/destination/destinationSelectors';
import Templates from './Templates';
import DestinationHelmet from './DestinationHelmet';
import Loader from 'components/Loader';
import { STORE_STATUS } from 'lib/enums/store';
import LoaderComponent from './LoaderComponent';
import FourOhFourPage from 'components/FourOhFourPage';
import { useUnmount } from 'react-use';

const DestinationLayout = () => {
  const dispatch = useDispatch();
  const destination = useSelector(getDestination) || {};
  const { breadcrumbs, regionId, status, template } = destination;

  const hasFailed = status === STORE_STATUS.FAILED;
  const isLoading = status === STORE_STATUS.PENDING;
  const isCountry = template === 'country';

  useUnmount(() => {
    dispatch(clearMerchandise());
  });

  useEffect(() => {
    if (regionId) {
      dispatch(fetchMerchandise({ limit: 4, regionId, subRegionLimit: 6 }));
    }
  }, [dispatch, regionId]);

  return (
    <Fragment>
      {hasFailed ? (
        <FourOhFourPage />
      ) : (
        <Fragment>
          <DestinationHelmet destination={destination} />

          {breadcrumbs && !isCountry && (
            <PageBlock bg="white" px={[4, 6, 10]} display={['none', 'block']}>
              <Breadcrumbs context="Destination" crumbs={breadcrumbs} />
            </PageBlock>
          )}

          <Loader isLoading={isLoading} loaderComponent={LoaderComponent}>
            <Templates />
          </Loader>
        </Fragment>
      )}
    </Fragment>
  );
};

DestinationLayout.propTypes = {
  country: PropTypes.string,
  state: PropTypes.string,
  area: PropTypes.string,
};

export default DestinationLayout;
