import React from 'react';
import DiscoverMore from './DiscoverMore';
import { mountUtils } from 'test-utils';
import { getBrowser } from 'store/userEnvironment/userEnvironmentSelectors';

jest.mock('store/userEnvironment/userEnvironmentSelectors');
mountUtils.mockComponent('DiscoverMoreTile');

const defaultProps = {
  headingText: 'Discover more hotels in Australia',
};

const decorators = { router: true, store: true };
const render = (props) => mountUtils(<DiscoverMore {...defaultProps} {...props} />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
  getBrowser.mockReturnValue({ name: 'Chrome' });
});

it('renders the heading', () => {
  const { find } = render();

  expect(find('Heading').text()).toEqual(defaultProps.headingText);
});

describe('with no areas', () => {
  it('does NOT render any DiscoverMoreTile components', () => {
    const { find } = render();

    expect(find('DiscoverMoreTile')).not.toExist();
  });
});

describe('with areas', () => {
  it('renders DiscoverMoreTile components', () => {
    const areas = [
      { regionName: 'NSW', url: 'someUrl' },
      { regionName: 'Vic', url: 'someUrl' },
    ];
    const { find } = render({ areas });

    expect(find('DiscoverMoreTile')).toHaveLength(2);
  });
});
