import React, { useCallback } from 'react';
import styled from '@emotion/styled';
import PropTypes from 'prop-types';
import { Text, Flex, Box } from '@qga/roo-ui/components';
import startCase from 'lodash/startCase';
import { useDataLayer } from 'hooks/useDataLayer';
import ResponsiveImage from 'components/DestinationPage/ResponsiveImage';
import AppLink from 'components/AppLink';

const NakedLink = styled(AppLink)`
  text-decoration: none;
`;

const TextWrapper = styled(Flex)`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.35);
`;

const tileHeight = [150, 200];

const DiscoverMoreTile = ({ mainImage, regionName: areaName, url }) => {
  const { emitInteractionEvent } = useDataLayer();

  const handleClick = useCallback(() => {
    emitInteractionEvent({ type: 'Discover More Gallery', value: `${startCase(areaName)} Selected` });
  }, [areaName, emitInteractionEvent]);

  return (
    <Box position="relative" mx={[2, 2, 1]} height={[155, 210]}>
      <NakedLink as={Text} to={url} onClick={handleClick} data-testid="link" aria-label={areaName}>
        <ResponsiveImage
          mainImage={mainImage}
          alt={`Explore ${mainImage?.caption}`}
          maxHeight={tileHeight}
          height={['100%', '100%', 200]}
          width="100%"
          lazy
        />
        <TextWrapper height={tileHeight}>
          <Text color="white" fontSize="xl" data-testid="area-name" p={2}>
            {areaName}
          </Text>
        </TextWrapper>
      </NakedLink>
    </Box>
  );
};

DiscoverMoreTile.propTypes = {
  mainImage: PropTypes.shape({
    caption: PropTypes.string,
    assets: PropTypes.shape({
      large: PropTypes.string,
      medium: PropTypes.string,
      small: PropTypes.string,
    }),
  }),
  regionName: PropTypes.string.isRequired,
  url: PropTypes.string.isRequired,
};

DiscoverMoreTile.defaultProps = {
  mainImage: {},
};

export default DiscoverMoreTile;
