import React from 'react';
import DiscoverMoreTile from './DiscoverMoreTile';
import { mountUtils } from 'test-utils';
import { useDataLayer } from 'hooks/useDataLayer';
import { getBrowser } from 'store/userEnvironment/userEnvironmentSelectors';

jest.mock('hooks/useDataLayer');
jest.mock('store/userEnvironment/userEnvironmentSelectors');

mountUtils.mockComponent('ResponsiveImage');
const emitInteractionEvent = jest.fn();

const props = {
  regionName: 'New South Wales',
  url: '/australia/new-south-wales',
  mainImage: {
    caption: 'Fingal Spit, Port Stephens',
    assets: {
      small: 'destinations/australia/nsw/byron-bay-lighthouse.mobile.webp',
      medium: 'destinations/australia/nsw/byron-bay-lighthouse.tablet.webp',
      large: 'destinations/australia/nsw/byron-bay-lighthouse.desktop.webp',
      legacy: 'destinations/australia/nsw/byron-bay-lighthouse.desktop.jpg',
    },
  },
};

const decorators = { router: true, store: true, theme: true };
const render = () => mountUtils(<DiscoverMoreTile aria-label="" {...props} />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
  getBrowser.mockReturnValue({ name: 'Chrome' });
  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

it('renders the ResponsiveImage', () => {
  const { find } = render();
  const expectedAriaLabel = `Explore ${props.mainImage.caption}`;

  expect(find('ResponsiveImage')).toHaveProp({
    alt: expectedAriaLabel,
  });
});

it('renders the area name', () => {
  const { findByTestId } = render();

  expect(findByTestId('area-name').text()).toEqual(props.regionName);
});

it('renders the link', () => {
  const { find } = render();

  expect(find('NakedLink')).toHaveProp({
    to: props.url,
  });
});

it('emits an event to the data layer when clicked', () => {
  const { findByTestId } = render();
  const routerLink = findByTestId('link');

  routerLink.simulate('click');

  expect(emitInteractionEvent).toHaveBeenCalledWith({
    type: 'Discover More Gallery',
    value: 'New South Wales Selected',
  });
});
