import { themeGet } from 'styled-system';
import styled from '@emotion/styled';
import { Box } from '@qga/roo-ui/components';
import { mediaQuery } from 'lib/styledSystem';

export const AreaGrid = styled(Box)`
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  width: 100%;

  > * {
    width: 100%;

    margin-bottom: ${themeGet('space.2')};
  }

  ${mediaQuery.minWidth.sm} {
    > * {
      width: 45%;
    }
  }

  ${mediaQuery.minWidth.md} {
    > * {
      width: 24%;
    }
  }
`;
