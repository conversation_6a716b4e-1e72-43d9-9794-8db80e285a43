import React from 'react';
import { Heading, Flex } from '@qga/roo-ui/components';
import PropTypes from 'prop-types';
import DiscoverMoreTile from './DiscoverMoreTile';
import { AreaGrid } from './primitives';

const DiscoverMore = ({ areas, headingText }) => {
  return (
    <Flex width={1} bg="dusty" flexDirection="column" alignItems="center">
      <Heading.h3 fontSize="lg" color="greys.charcoal" display="block" mb={10}>
        {headingText}
      </Heading.h3>

      <AreaGrid>
        {areas.map((area, index) => (
          <DiscoverMoreTile key={`tile-${index}`} {...area} />
        ))}
      </AreaGrid>
    </Flex>
  );
};

DiscoverMore.propTypes = {
  areas: PropTypes.arrayOf(PropTypes.object),
  headingText: PropTypes.string.isRequired,
};

DiscoverMore.defaultProps = {
  areas: [],
};

export default DiscoverMore;
