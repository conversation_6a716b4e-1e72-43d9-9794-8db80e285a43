import React from 'react';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import Markdown from 'components/Markdown';
import isNil from 'lodash/isNil';
import reject from 'lodash/reject';
import { NakedButton, Text, Box } from '@qga/roo-ui/components';
import Modal from 'components/Modal';
import { useModal } from 'lib/hooks';
import { useDataLayer } from 'hooks/useDataLayer';
import { format } from 'date-fns';
import { ExclusiveOffer } from 'types/content';
import { getSaleDateEnd, getTravelDateEnd, getTravelDateStart } from './helpers';

export const formatDate = (date: string): string => format(new Date(date), 'dd MMM yyyy');

type OfferTermsAndConditionsProps = Pick<ExclusiveOffer, 'saleDates' | 'terms' | 'travelDates'> & {
  roomName?: string;
  minNumberOfNights?: number;
  category: string;
};

const LinkButton = styled(NakedButton)`
  text-decoration: underline;
  margin-left: ${themeGet('space.1')};
  color: ${themeGet('colors.greys.steel')};
  font-size: ${themeGet('fontSizes.xs')};

  ${themeGet('mediaQueries.1')} {
    font-size: ${themeGet('fontSizes.base')};
  }
`;

const OfferTermsAndConditions = ({
  minNumberOfNights,
  roomName,
  saleDates,
  terms,
  travelDates,
  category,
}: OfferTermsAndConditionsProps) => {
  const { emitInteractionEvent } = useDataLayer();
  const { openModal, modalProps } = useModal();

  const saleDateEnd: string = getSaleDateEnd(saleDates?.end);
  const travelDateEnd: string = getTravelDateEnd(travelDates?.end);
  const travelDateStart: string = getTravelDateStart(travelDates?.start);

  const offerEndsText = saleDateEnd && `Offer ends ${formatDate(saleDateEnd)}.`;
  const travelPeriodText =
    travelDateStart && travelDateEnd && `Travel period: ${formatDate(travelDateStart)} to ${formatDate(travelDateEnd)}.`;
  const minNumberOfNightsText = minNumberOfNights && `Price displayed is for ${minNumberOfNights} nights`;
  const roomNameText = roomName && `in a ${roomName}.`;
  const disclaimerText = reject([offerEndsText, travelPeriodText, minNumberOfNightsText, roomNameText], isNil).join(' ');

  const handleOpenModal = () => {
    openModal();
    emitInteractionEvent({
      type: category,
      value: 'Terms and Conditions Clicked',
    });
  };

  return (
    <>
      <Box mb={6} mx={[4, 4, 0]}>
        <Text color="greys.steel" fontSize={['xs', 'base']} data-testid="disclaimer-copy">
          {disclaimerText}
        </Text>
        <LinkButton onClick={handleOpenModal} data-testid="terms-and-conditions-button">
          Conditions apply.
        </LinkButton>
      </Box>

      <Modal {...modalProps} fullScreen={false} padding={8} title="Offer Terms and Conditions" footerComponent={null}>
        <Markdown content={terms} />
      </Modal>
    </>
  );
};

export default OfferTermsAndConditions;
