import { RichDate } from 'types/content';

export const getSaleDateEnd = (end: string | RichDate): string => {
  if ((end as RichDate).local) {
    return (end as RichDate).local;
  }
  return end as string;
};

export const getTravelDateEnd = (end: string | RichDate): string => {
  if ((end as RichDate).local) {
    return (end as RichDate).local;
  }
  return end as string;
};

export const getTravelDateStart = (start: string | RichDate): string => {
  if ((start as RichDate).local) {
    return (start as RichDate).local;
  }
  return start as string;
};
