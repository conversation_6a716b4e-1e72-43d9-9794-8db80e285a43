import React from 'react';
import { mountUtils } from 'test-utils';
import { useDataLayer } from 'hooks/useDataLayer';
import TravelInsuranceDisclaimer from './TravelInsuranceDisclaimer';

jest.mock('store/ui/uiSelectors');
jest.mock('hooks/useDataLayer');
const emitInteractionEvent = jest.fn();

const decorators = { store: true };
const render = () => mountUtils(<TravelInsuranceDisclaimer />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();

  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

describe('<TravelInsuranceDisclaimer />', () => {
  it('renders the travel insurance disclaimers', () => {
    const { find } = render();

    expect(find('TravelInsuranceDisclaimers')).toHaveText(
      '≈ Qantas Travel Insurance policies are managed by nib Travel Services (Australia) Pty Limited (nib) ABN 81 *********** AFSL 308461 and underwritten by Pacific International Insurance Pty Ltd (Pacific), ABN 83 ***********, NZBN 9429041356500. In New Zealand, Pacific is a member of the Insurance and Financial Services Ombudsman dispute resolution scheme. Qantas (AR 261363) is nib’s authorised agent and acts on behalf of nib. Before you buy you should consider your personal circumstances as well as the  Product Disclosure Statement (PDS) and Target Market Determination (TMD) in Australia, or Policy Document in New Zealand. Qantas Frequent Flyer (QFF) members who are the primary holder of a Qantas Travel Insurance policy will earn 1 Qantas Point per A$1 value of the premium paid. Excludes Cancellation and Baggage policies. Qantas Points will be credited within 6 weeks after the trip departure date listed on your policy. Qantas may amend or withdraw this offer at any time. ',
    );
  });

  it('product disclosure link goes to the correct url', () => {
    const { findByTestId } = render();

    expect(findByTestId('product-disclosure-link')).toHaveProp({ href: 'https://travel.insurance.qantas.com/au/policies' });
  });

  it('emits an interaction event when the Product Disclosure link is clicked', () => {
    const { findByTestId } = render();

    findByTestId('product-disclosure-link').simulate('click');

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Travel insurance product disclosure',
      value: 'Travel insurance product disclosure selected',
    });
  });

  it('target market determination link goes to the correct url', () => {
    const { findByTestId } = render();

    expect(findByTestId('target-market-determination-link')).toHaveProp({ href: 'https://travel.insurance.qantas.com/au/policies' });
  });

  it('emits an interaction event when the Target Market Determination link is clicked', () => {
    const { findByTestId } = render();

    findByTestId('target-market-determination-link').simulate('click');

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Travel insurance target market determination',
      value: 'Travel insurance target market determination selected',
    });
  });

  it('policy document link goes to the correct url', () => {
    const { findByTestId } = render();

    expect(findByTestId('policy-document-link')).toHaveProp({ href: 'https://travel.insurance.qantas.com/nz/policies' });
  });

  it('emits an interaction event when the Policy Document link is clicked', () => {
    const { findByTestId } = render();

    findByTestId('policy-document-link').simulate('click');

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Travel insurance policy document',
      value: 'Travel insurance policy document selected',
    });
  });
});
