import React from 'react';
import { mountUtils } from 'test-utils';
import Disclaimer from './Disclaimer';
import { emitUserInteraction } from 'store/userEnvironment/userEnvironmentActions';
import { getCampaignTermsAndConditions } from 'store/campaign/campaignSelectors';
import { getPathName } from 'store/router/routerSelectors';
import * as config from 'config';

jest.mock('config');
jest.mock('store/campaign/campaignSelectors', () => ({ getCampaignTermsAndConditions: jest.fn() }));
jest.mock('store/ui/uiSelectors');
jest.mock('store/router/routerSelectors');

const pointsPlusPayTermsAndConditions =
  '* Qantas Frequent Flyer members can redeem Qantas Points when booking hotel accommodation through qantas.com/hotels or holiday packages through qantas.com/holidays, using Points Plus Pay. Members cannot redeem points for additional charges paid to the hotel for extras (including cots, breakfasts and other incidentals) on check-in or check-out (as applicable). Points Plus Pay allows you to choose the number of Qantas Points you redeem above the specified minimum level of 5,000 and pay for the remainder of the booking value with an Accepted Payment Card (including VISA, MasterCard or American Express). Points Plus Pay is not available for Classic Hotel Rewards.';

const decorators = { router: true, store: true, theme: true };

const render = () => mountUtils(<Disclaimer />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
  getCampaignTermsAndConditions.mockReturnValue(null);

  // reset config
  Object.assign(config, jest.requireActual('config'));
});

describe('with POINTS_EARN_ENABLED off', () => {
  beforeEach(() => {
    config.POINTS_EARN_ENABLED = false;
  });

  it('does not render points disclaimer', () => {
    const { findByTestId } = render();
    expect(findByTestId('points-disclaimer-text')).not.toExist();
  });

  it('does not render qbr disclaimer', () => {
    const { findByTestId } = render();
    expect(findByTestId('businessRewardConditionApplyLink')).not.toExist();
  });

  it('shows the savings disclaimer', () => {
    const { findByTestId } = render();
    expect(findByTestId('saving-disclaimer')).toIncludeText(
      "~ Saving is off the hotel's generally available rate for the same property, room type, days, inclusions and conditions.",
    );
  });

  it('shows the airbnb points disclaimer', () => {
    const { findByTestId } = render();
    expect(findByTestId('airbnb-points-disclaimer')).toIncludeText(
      '++ 500 bonus Qantas Points will be awarded to Qantas Frequent Flyer members who make their first Airbnb booking. Qantas Frequent Flyer members will earn 1 Qantas Point per A$1 spent for all Airbnb stays booked through',
    );
  });
});

describe('with POINTS_EARN_ENABLED on', () => {
  beforeEach(() => {
    config.POINTS_EARN_ENABLED = true;
  });

  it('shows the savings disclaimer', () => {
    const { findByTestId } = render();
    expect(findByTestId('saving-disclaimer')).toIncludeText(
      "~ Saving is off the hotel's generally available rate for the same property, room type, days, inclusions and conditions.",
    );
  });

  it('shows the airbnb points disclaimer', () => {
    const { findByTestId } = render();
    expect(findByTestId('airbnb-points-disclaimer')).toIncludeText(
      '++ 500 bonus Qantas Points will be awarded to Qantas Frequent Flyer members who make their first Airbnb booking. Qantas Frequent Flyer members will earn 1 Qantas Point per A$1 spent for all Airbnb stays booked through',
    );
  });

  describe('Frequent Flyer conditions link', () => {
    describe('when the campaign is not present', () => {
      it('displays the default disclaimer', () => {
        const { findByTestId } = render();

        expect(findByTestId('disclaimer-terms-and-conditions')).toExist();
        expect(findByTestId('points-disclaimer-text')).toIncludeText(
          'You must be a Qantas Frequent Flyer member to earn and redeem points. Membership and points are subject to the Qantas Frequent Flyer program terms and conditions.',
        );
      });

      it('displays the default and Points Club terms and conditions', () => {
        const { findByTestId } = render();

        expect(findByTestId('terms-and-conditions')).toExist();
        expect(findByTestId('points-disclaimer-text')).toIncludeText(
          'Qantas Frequent Flyer members will earn 3 Qantas Points per A$1 value unless otherwise specified, for hotel stays booked through qantas.com/hotels, except Classic Hotel Rewards and Airbnb bookings. Points Club members will earn 25% more Qantas Points, and Points Club Plus members will earn 50% more Qantas Points. Qantas Points will be credited to your account at least 8 weeks after check-out. Qantas Points may be earned by the member in whose name the booking is made. Members will not be able to earn points on additional charges paid to the accommodation provider for extras (including cots, breakfasts and other incidentals) on check-in or check-out (as applicable).',
        );
      });

      it('displays the points plus pay terms and conditions', () => {
        const { findByTestId } = render();

        expect(findByTestId('points-plus-pay-t&c')).toExist();
        expect(findByTestId('points-disclaimer-text')).toIncludeText(pointsPlusPayTermsAndConditions);
      });

      it('has a points plus pay link for conditions', () => {
        const { findByTestId } = render();

        expect(findByTestId('pointsPlusPayViewFullTermsAndConditionsLink')).toHaveProp({ href: config.QCOM_TERMS_AND_CONDITIONS_URL });
      });

      it('emits a gtm event when clicked', () => {
        const { findByTestId, decorators } = render();
        const { dispatch } = decorators.store;

        findByTestId('pointsPlusPayViewFullTermsAndConditionsLink').simulate('click');

        expect(dispatch).toHaveBeenCalledWith(expect.objectContaining({ type: emitUserInteraction.type }));
      });
    });

    describe('when the campaign is present', () => {
      const termsAndConditions = 'special terms and conditions for this campaign';
      beforeEach(() => getCampaignTermsAndConditions.mockReturnValue(termsAndConditions));

      it('displays the campaign disclaimer', () => {
        const { findByTestId } = render();

        expect(findByTestId('points-disclaimer-text')).toIncludeText(termsAndConditions);
      });

      it('displays the points plus pay terms and conditions', () => {
        const { findByTestId } = render();

        expect(findByTestId('points-disclaimer-text')).toIncludeText(pointsPlusPayTermsAndConditions);
      });

      it('has a points plus pay link for conditions', () => {
        const { findByTestId } = render();

        expect(findByTestId('pointsPlusPayViewFullTermsAndConditionsLink')).toHaveProp({ href: config.QCOM_TERMS_AND_CONDITIONS_URL });
      });

      it('emits a gtm event when clicked', () => {
        const { findByTestId, decorators } = render();
        const { dispatch } = decorators.store;

        findByTestId('pointsPlusPayViewFullTermsAndConditionsLink').simulate('click');

        expect(dispatch).toHaveBeenCalledWith(expect.objectContaining({ type: emitUserInteraction.type }));
      });
    });
  });

  describe('Business Reward condition Apply link', () => {
    it('has the expected href', () => {
      const { findByTestId } = render();

      expect(findByTestId('businessRewardConditionApplyLink')).toHaveProp({
        href: config.QCOM_TERMS_AND_CONDITIONS_URL,
      });
    });

    it('emits a gtm event for business reward conditions when clicked', () => {
      const { findByTestId, decorators } = render();
      const { dispatch } = decorators.store;

      findByTestId('businessRewardConditionApplyLink').simulate('click');

      expect(dispatch).toHaveBeenCalledWith(expect.objectContaining({ type: emitUserInteraction.type }));
    });
  });
});

describe('<TravelInsuranceDisclaimer />', () => {
  describe('when on the Booking Confirmation page', () => {
    beforeEach(() => {
      getPathName.mockReturnValue('/bookings/123');
      config.TRAVEL_INSURANCE_CROSS_SELL_ENABLED = true;
    });

    describe('TRAVEL_INSURANCE_CROSS_SELL_ENABLED true', () => {
      it('renders <TravelInsuranceDisclaimer />', () => {
        const { findByTestId } = render();

        expect(findByTestId('travel-insurance-disclaimer')).toExist();
        expect(findByTestId('travel-insurance-points-disclaimer')).toExist();
      });
    });

    describe('with TRAVEL_INSURANCE_CROSS_SELL_ENABLED false', () => {
      beforeEach(() => {
        config.TRAVEL_INSURANCE_CROSS_SELL_ENABLED = false;
      });

      it('does NOT render <TravelInsuranceDisclaimer />', () => {
        const { findByTestId } = render();

        expect(findByTestId('travel-insurance-disclaimer')).not.toExist();
        expect(findByTestId('travel-insurance-points-disclaimer')).not.toExist();
      });
    });
  });

  describe('when NOT on the Booking Confirmation page', () => {
    beforeEach(() => {
      getPathName.mockReturnValue('/search/list');
    });

    it('does NOT render <TravelInsuranceDisclaimer />', () => {
      const { findByTestId } = render();

      expect(findByTestId('travel-insurance-disclaimer')).not.toExist();
      expect(findByTestId('travel-insurance-points-disclaimer')).not.toExist();
    });
  });
});
