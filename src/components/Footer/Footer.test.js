import React from 'react';
import { mountUtils } from 'test-utils';
import Footer from './Footer';
import { getIsMobileApp } from 'store/ui/uiSelectors';
import { Footer as GlobalFooter } from '@qga/components';

jest.mock('config');
jest.mock('store/ui/uiSelectors');
jest.mock('@qga/components', () => ({
  Footer: jest.fn(),
  FieldError: jest.fn(),
}));

mountUtils.mockComponent('Disclaimer');

const render = () => mountUtils(<Footer />, { decorators: { theme: true, router: true, store: true } });

describe('<Footer />', () => {
  beforeEach(() => {
    GlobalFooter.mockImplementation(() => <div>GlobalFooter Component</div>);
  });

  it('renders the Disclaimer', () => {
    const { find } = render();

    expect(find('Disclaimer')).toExist();
  });

  describe('when user is NOT using the Qantas Travel App', () => {
    beforeEach(() => {
      getIsMobileApp.mockReturnValue(false);
    });

    it('renders the standard footer', () => {
      const { findByText } = render();
      expect(findByText('GlobalFooter Component')).toExist();
    });
  });

  describe('when user is using the Qantas Travel App', () => {
    beforeEach(() => {
      getIsMobileApp.mockReturnValue(true);
    });
    it('renders the standard footer', () => {
      const { findByText } = render();
      expect(findByText('GlobalFooter Component')).toExist();
    });
  });
});
