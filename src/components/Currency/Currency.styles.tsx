import styled from '@emotion/styled';
import { Box } from '@qga/roo-ui/components';
import { display, fontSize, space, lineHeight, style, color, fontWeight, textStyle, alignItems, themeGet } from 'styled-system';

export const Wrapper = styled(Box)`
  ${lineHeight};
  ${space};
  ${display};
  ${fontSize};
  ${color};
  ${fontWeight};
  ${alignItems}
  display: inline-flex;
  flex-flow: ${(props) => (props.reverse ? 'row-reverse' : 'row')};
`;

const textDecoration = style({
  prop: 'textDecoration',
  cssProperty: 'textDecoration',
});

export const Amount = styled.span`
  ${textStyle};
  ${textDecoration};
  ${fontSize};
  ${lineHeight};
  ${fontWeight};
`;

Amount.displayName = 'Amount';

export const CurrencyText = styled.span`
  ${fontSize};
  ${textStyle};
  ${space};
  ${lineHeight};
  text-transform: 'uppercase';
  margin-left: ${(props) => (props.reverse ? themeGet('space.1') : 0)};
`;

CurrencyText.displayName = 'CurrencyText';
