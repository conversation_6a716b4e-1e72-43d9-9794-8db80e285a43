import React from 'react';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import { Text, Box } from '@qga/roo-ui/components';
import { rem } from 'polished';
import { FormatNumber } from 'components/formatters';
import { Wrapper, Amount, CurrencyText } from './Currency.styles';

const CURRENCY_SYMBOLS = {
  AUD: '$',
};

const resolveCurrencySymbol = (currency) => CURRENCY_SYMBOLS[currency];

interface Props {
  amount?: string | number;
  currency?: string;
  currencyTextFontSize?: string | number | string[] | number[];
  roundToCeiling?: boolean;
  fontSize?: string | number | string[] | number[];
  hideCurrency?: boolean;
  lineHeight?: string | number;
  textDecoration?: string;
  fontWeight?: number | number[] | 'bold' | 'normal';
  alignCurrency?: 'left' | 'right' | 'superscript';
  alignItems?: string;
  color?: string;
}

const Currency = ({
  amount,
  currency,
  currencyTextFontSize = 'xs',
  roundToCeiling = false,
  fontSize = ['sm', 'base'],
  hideCurrency = false,
  alignCurrency = 'right',
  lineHeight = 'tight',
  fontWeight = 'normal',
  textDecoration = 'none',
  alignItems = 'baseline',
  color,
  ...rest
}: Props) => {
  if (amount === null || amount === undefined) return null;

  const amountAsFloat = typeof amount === 'string' ? parseFloat(amount.replace(/[^0-9.]/g, '')) : amount;
  const currencySymbol = resolveCurrencySymbol(currency);
  const number = roundToCeiling ? new Decimal(amountAsFloat).ceil() : new Decimal(amountAsFloat);
  const decimalPlaces = currency === 'PTS' || roundToCeiling ? 0 : 2;
  const isPoints = currency === 'PTS';
  const reverse = alignCurrency === 'left';

  return (
    <Wrapper
      data-testid="currency"
      lineHeight={lineHeight}
      fontWeight={fontWeight}
      isPoints={isPoints}
      reverse={reverse}
      alignItems={alignItems}
      color={color}
      {...rest}
    >
      <Box fontSize={fontSize}>
        {currencySymbol && (
          <Text data-testid="currency-symbol" textStyle="currency" fontSize={fontSize} lineHeight={lineHeight} fontWeight={fontWeight}>
            {alignCurrency === 'superscript' ? <sup style={{ fontSize: rem('20px') }}>{currencySymbol}</sup> : currencySymbol}
          </Text>
        )}
        <Amount textStyle="currency" fontSize={fontSize} lineHeight={lineHeight} textDecoration={textDecoration} data-testid="amount">
          {FormatNumber({ number, decimalPlaces })}
        </Amount>
      </Box>
      {!hideCurrency && (
        <Box mr={reverse ? 1 : 0} ml={reverse ? 0 : 1}>
          <CurrencyText
            fontWeight="normal"
            textStyle="currency"
            fontSize={currencyTextFontSize}
            lineHeight={lineHeight}
            isPoints={isPoints}
            reverse={reverse}
            data-testid="currency-text"
          >
            {currency}
          </CurrencyText>
        </Box>
      )}
    </Wrapper>
  );
};

export default Currency;
