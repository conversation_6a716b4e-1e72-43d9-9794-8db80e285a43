import React from 'react';
import { mocked, mountUtils } from 'test-utils';
import Currency from './Currency';
import { FormatNumber } from 'components/formatters';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';

jest.mock('components/formatters');

const defaultProps = {
  amount: '71.6',
  roundToCeiling: false,
  hideCurrency: false,
};

const render = (props = {}) => mountUtils(<Currency {...defaultProps} {...props} />);

beforeEach(() => {
  jest.clearAllMocks();
  mocked(FormatNumber).mockReturnValue('foo');
});

describe('when in cash mode', () => {
  it('receive the right prop', () => {
    const { find } = render({ currency: 'AUD' });
    expect(find('CurrencyText')).toHaveProp({ isPoints: false });
  });

  it('renders the right style', () => {
    const { find } = render({ currency: 'AUD' });
    expect(find('Wrapper')).toHaveProp({ reverse: false });
  });

  it('parses an amount string into a float', () => {
    render({ currency: 'AUD', amount: '$10,550AUD' });
    expect(FormatNumber).toHaveBeenCalledWith({ number: new Decimal(10550), decimalPlaces: 2 });
  });

  describe('when alignCurrency is left', () => {
    it('renders the right style', () => {
      const { find } = render({ currency: 'AUD', alignCurrency: 'left' });
      expect(find('Wrapper')).toHaveProp({ reverse: true });
    });
  });

  describe('when ceiling amount props is present', () => {
    it('renders the price rounded', () => {
      render({ currency: 'AUD', roundToCeiling: true });
      expect(FormatNumber).toHaveBeenCalledWith({ number: new Decimal(72), decimalPlaces: 0 });
    });
  });

  describe('when ceiling amount props is not present', () => {
    it('renders the price not rounded', () => {
      render({ currency: 'AUD' });
      expect(FormatNumber).toHaveBeenCalledWith({ number: new Decimal(71.6), decimalPlaces: 2 });
    });
  });

  describe('hideCurrency', () => {
    describe('when the hide currency is not present', () => {
      it('shows the currency', () => {
        const { find } = render({ currency: 'AUD' });
        expect(find('CurrencyText')).toHaveText('AUD');
      });
    });

    describe('when the hide currency is present', () => {
      it('does not show the currency', () => {
        const { find } = render({ currency: 'AUD', hideCurrency: true });
        expect(find('CurrencyText')).not.toExist();
      });
    });
  });

  it('displays the currency symbol', () => {
    const { findByTestId } = render({ currency: 'AUD' });
    expect(findByTestId('currency-symbol')).toHaveText('$');
  });
});

describe('when in points mode', () => {
  it('does not round the amount', () => {
    render({ amount: '9900', currency: 'PTS' });
    expect(FormatNumber).toHaveBeenCalledWith({ number: new Decimal(9900), decimalPlaces: 0 });
  });

  it('displays the correct points currency', () => {
    const { find } = render({ amount: '9900', currency: 'PTS' });
    expect(find('CurrencyText')).toHaveText('PTS');
  });

  it('does not display any currency symbol', () => {
    const { findByTestId } = render({ amount: '9900', currency: 'PTS' });
    expect(findByTestId('currency-symbol')).not.toExist();
  });

  it('receives the right prop', () => {
    const { find } = render({ amount: '9900', currency: 'PTS' });
    expect(find('CurrencyText')).toHaveProp({ isPoints: true });
  });
});

describe('when total is null', () => {
  it('does not render', () => {
    const { find } = render({ amount: null, currency: null });
    expect(find('Wrapper')).not.toExist();
  });
});

describe('when amount is a decimal', () => {
  it('renders the amount', () => {
    render({ currency: 'AUD', amount: new Decimal(100.11) });
    expect(FormatNumber).toHaveBeenCalledWith({ number: new Decimal(100.11), decimalPlaces: 2 });
  });
});
