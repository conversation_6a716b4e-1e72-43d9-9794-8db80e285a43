import React from 'react';
import PropTypes from 'prop-types';
import { ImageGallery, Icon } from '@qga/roo-ui/components';
import { CloseButton, FullscreenModal, FullScreenImage } from './primitives';
import { useDispatch } from 'react-redux';
import { setFullscreenGalleryContent } from 'store/ui/uiActions';

const FullscreenImageGallery = ({ images, isOpen, shouldCloseOnOverlayClick, onRequestClose, startIndex = 0 }) => {
  const dispatch = useDispatch();
  const onModalClosed = () => {
    dispatch(setFullscreenGalleryContent({ images: [], startIndex: 0 }));
    onRequestClose();
  };

  return (
    <FullscreenModal isOpen={isOpen} shouldCloseOnOverlayClick={shouldCloseOnOverlayClick} onRequestClose={onModalClosed}>
      <ImageGallery images={images} showFullscreenButton={false} showThumbnails renderItem={FullScreenImage} startIndex={startIndex} />
      <CloseButton onClick={onModalClosed}>
        <Icon name="close" size={[30, 40]} color="white" />
      </CloseButton>
    </FullscreenModal>
  );
};

FullscreenImageGallery.propTypes = {
  images: PropTypes.arrayOf(PropTypes.object).isRequired,
  startIndex: PropTypes.number,
  isOpen: PropTypes.bool.isRequired,
  shouldCloseOnOverlayClick: PropTypes.bool.isRequired,
  onRequestClose: PropTypes.func.isRequired,
};

export default FullscreenImageGallery;
