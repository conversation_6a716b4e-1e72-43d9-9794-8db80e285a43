import React from 'react';
import PropTypes from 'prop-types';
import { NakedButton, Modal, BackgroundImage, Text } from '@qga/roo-ui/components';
import { themeGet } from 'styled-system';
import styled from '@emotion/styled';
import theme from 'lib/theme';

const StyledText = styled(Text)`
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
`;

export const CloseButton = styled(NakedButton)`
  position: fixed;
  top: ${themeGet('space.5')};
  right: ${themeGet('space.5')};
  opacity: 0.8;

  &:focus {
    outline: ${themeGet('focus.outline')};
    opacity: 1;
  }

  &:hover {
    opacity: 1;
  }
`;

export const FullScreenImage = ({ alt, index, total, ...rest }) => (
  <>
    <BackgroundImage alt={alt} backgroundSize="contain" backgroundPosition="center" height={['400px', '660px']} {...rest} />
    <StyledText display="block" color="white" fontSize="md" py="2" px="6" textAlign="left">
      {index}/{total}
      {alt ? ` • ${alt}` : ''}
    </StyledText>
  </>
);

FullScreenImage.propTypes = {
  src: PropTypes.string.isRequired,
  alt: PropTypes.string.isRequired,
  index: PropTypes.number.isRequired,
  total: PropTypes.number.isRequired,
};

export const FullscreenModal = ({ children, ...rest }) => (
  <Modal
    width="100%"
    style={{
      overlay: {
        zIndex: theme.zIndices.fullscreenGallery,
        backgroundColor: 'black',
      },
      content: {
        width: '100%',
        borderRadius: '0px',
        background: 'transparent',
      },
    }}
    {...rest}
  >
    {children}
  </Modal>
);

FullscreenModal.propTypes = {
  children: PropTypes.node.isRequired,
};
