import React from 'react';
import { mountUtils } from 'test-utils';
import WelcomeMessage from './WelcomeMessage';
import { useLogout } from 'lib/oauth';
import { getFirstName } from 'store/user/userSelectors';
import { useDataLayer } from 'hooks/useDataLayer';

jest.mock('store/user/userSelectors');
jest.mock('lib/qffAuth');
jest.mock('hooks/useDataLayer');
jest.mock('lib/oauth');

const emitInteractionEvent = jest.fn();
const logoutMock = jest.fn();
const render = () => mountUtils(<WelcomeMessage />, { decorators: { store: true, theme: true } });

describe('<WelcomeMessage />', () => {
  beforeEach(() => {
    useDataLayer.mockReturnValue({ emitInteractionEvent });
    getFirstName.mockReturnValue('Alan');
    useLogout.mockReturnValue({ logout: logoutMock });
  });

  it('displays the welcome message', () => {
    const { findByTestId } = render();
    expect(findByTestId('welcome-message')).toHaveText('Welcome Alan.');
  });

  it('renders a login button', async () => {
    const { findByTestId } = render();
    findByTestId('logout-button').simulate('click');
    await flushPromises();
    expect(logoutMock).toHaveBeenCalled();
  });
});
