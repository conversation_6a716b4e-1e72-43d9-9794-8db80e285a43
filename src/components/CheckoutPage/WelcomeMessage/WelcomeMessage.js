import React from 'react';
import { useSelector } from 'react-redux';
import { Flex, Box, Heading, Icon } from '@qga/roo-ui/components';
import TextButton from 'components/TextButton';
import { getFirstName } from 'store/user/userSelectors';
import { useLogout } from 'lib/oauth';

const LoginMessaging = () => {
  const firstName = useSelector(getFirstName);
  const { logout } = useLogout();

  return (
    <Flex px={[4, 8]} py={4} mb={8} borderBottom={1} borderColor="greys.alto" bg="white">
      <Box mr={[3, 5]}>
        <Icon name="checkCircle" color="green" size={32} />
      </Box>
      <Flex justifyContent="space-between" flex="1 1 auto">
        <Heading.h2 fontWeight="bold" fontSize="md" mb={0} lineHeight="32px" data-testid="welcome-message">
          Welcome {firstName}.
        </Heading.h2>
        <Box>
          <TextButton onClick={logout} lineHeight="2" textStyle="button" data-testid="logout-button">
            LOG OUT
          </TextButton>
        </Box>
      </Flex>
    </Flex>
  );
};

export default LoginMessaging;
