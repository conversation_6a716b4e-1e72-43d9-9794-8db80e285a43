import React from 'react';
import PropTypes from 'prop-types';
import isArray from 'lodash/isArray';
import find from 'lodash/find';
import { themeGet } from 'styled-system';
import styled from '@emotion/styled';
import { Text, Flex, StarRating, Box } from '@qga/roo-ui/components';
import Image from 'components/Image';
import PromotionalSash from 'components/PromotionalSash';
import TripAdvisorRating from 'components/TripAdvisorRating';
import OfferDetails from './OfferDetails';
import RoomTypeDetails from './RoomTypeDetails';
import QuoteItem from 'components/CheckoutPage/QuoteDetails/QuoteItem';

const AbsolutePromoBox = styled(Box)`
  position: absolute;
  top: 0;
  left: ${themeGet('space.2')};
  z-index: 1;
`;

const getTripAdvisorRating = (customerRatings) => (isArray(customerRatings) ? find(customerRatings, ['source', 'trip_advisor']) : null);

const PropertyDetails = ({ property, roomType, offer }) => {
  const { rating, ratingType, mainImage, name } = property;
  const { urlMedium, urlLarge, caption } = mainImage || {};
  const hasPromotion = !!offer?.promotion;
  const hasRoomAndOffer = !!(roomType && offer);
  const propertyImageSrcSet = mainImage ? `${urlLarge} 2x, ${urlMedium} 1x` : undefined;
  const tripAdvisorRating = getTripAdvisorRating(property.customerRatings);

  return (
    <Flex flexDirection="column" position="relative" borderRadius="defaultRoundTopOnly" overflow="hidden">
      {hasPromotion && (
        <AbsolutePromoBox>
          <PromotionalSash promotionName={offer.promotion.name} />
        </AbsolutePromoBox>
      )}
      <Image height="200px" lazy src={urlMedium} alt={caption} srcSet={propertyImageSrcSet} />
      <Box px={[3, 8]} bg="white">
        <QuoteItem>
          <Flex>
            <Flex flexDirection="column" width="100%">
              <Text fontSize="base" fontWeight="bold" color="greys.charcoal">
                {name}
              </Text>
              <Flex mt={2}>
                <StarRating rating={rating} ratingType={ratingType} size={16} mr={4} />
                {tripAdvisorRating && <TripAdvisorRating rating={tripAdvisorRating} />}
              </Flex>
            </Flex>
          </Flex>
          {hasRoomAndOffer && (
            <Box mt={2}>
              <RoomTypeDetails roomType={roomType} />
              {' - '}
              <OfferDetails offer={offer} />
            </Box>
          )}
        </QuoteItem>
      </Box>
    </Flex>
  );
};

PropertyDetails.propTypes = {
  property: PropTypes.object.isRequired,
  roomType: PropTypes.object,
  offer: PropTypes.object,
};

PropertyDetails.defaultProps = {
  roomType: null,
  offer: null,
};

export default PropertyDetails;
