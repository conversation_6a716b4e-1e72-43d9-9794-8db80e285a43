import React, { Fragment } from 'react';
import { useSelector } from 'react-redux';
import { getQuote, getStayDates, getOccupants } from 'store/quote/quoteSelectors';
import get from 'lodash/get';
import { Wrapper, Box, Text } from '@qga/roo-ui/components';
import PropertyDetails from './PropertyDetails';
import StayDates from './StayDates';
import PriceDetails from './PriceDetails';
import QuoteItem from './QuoteItem';
import CancellationRefundModal from 'components/CancellationRefundModal';
import OccupantsSummary from 'components/OccupantsSummary';
import PointsEarnSummary from './PointsEarnSummary';

const QuoteDetails = ({ ...rest }) => {
  const quote = useSelector(getQuote);
  const { checkIn, checkOut } = useSelector(getStayDates) || {};
  const occupants = useSelector(getOccupants);

  if (!quote) return null;

  const { property, offer, roomType } = quote;
  const inclusions = get(offer, 'inclusions');
  const cancellationPolicy = get(offer, 'cancellationPolicy');
  const formattedInclusionDescriptions = (inclusions || []).map((inclusion) => inclusion.description).join(', ');

  return (
    <Wrapper {...rest}>
      <Box mb={[0, 10]}>
        <PropertyDetails property={property} roomType={roomType} offer={offer} />
        {offer && (
          <Fragment>
            <Box boxShadow="hard" borderRadius="defaultRoundBottomOnly" overflow="hidden">
              <Box px={[3, 8]} bg="white">
                <QuoteItem>
                  <OccupantsSummary fontSize="base" occupants={occupants} />
                  <StayDates checkIn={checkIn} checkOut={checkOut} />
                  <CancellationRefundModal cancellationPolicy={cancellationPolicy} fontSize="sm" mt={2} flexDirection="row" />
                </QuoteItem>
                <QuoteItem borderBottom={0}>
                  {formattedInclusionDescriptions.length > 0 && (
                    <Text fontSize="sm" display="block" mb={2} data-testid="inclusion-descriptions">
                      {formattedInclusionDescriptions}
                    </Text>
                  )}
                  <PointsEarnSummary pointsEarned={offer.pointsEarned} />
                </QuoteItem>
              </Box>
              <PriceDetails property={property} offer={offer} checkIn={checkIn} checkOut={checkOut} p={[3, 8]} bg="lightBlue" />
            </Box>
          </Fragment>
        )}
      </Box>
    </Wrapper>
  );
};

export default QuoteDetails;
