import React from 'react';
import PropTypes from 'prop-types';
import { Flex, Box, Text } from '@qga/roo-ui/components';
import PointsEarnDisplay from 'components/PointsEarnDisplay';
import PointsPerDollar from 'components/PointsPerDollar';
import useCheckoutPointsEarned from 'components/CheckoutPage/hooks/useCheckoutPointsEarned';
import { POINTS_EARN_ENABLED, POINTS_PER_DOLLAR_DEFAULT } from 'config';

const PointsEarnSummary = ({ pointsEarned: initialPointsEarned }) => {
  const { pointsEarned } = useCheckoutPointsEarned({ pointsEarned: initialPointsEarned });

  if (!POINTS_EARN_ENABLED) {
    return null;
  }

  const pointsPerDollar = pointsEarned?.maxQffEarnPpd ? pointsEarned.maxQffEarnPpd : POINTS_PER_DOLLAR_DEFAULT;
  return (
    <Box>
      {pointsEarned.qffPoints.total > 0 && (
        <Box>
          <Flex flexDirection="row" alignItems="center">
            <Text ontSize="sm" lineHeight="tight">
              Earn{' '}
              <PointsEarnDisplay total={pointsEarned.qffPoints.total} base={pointsEarned.qffPoints.base} data-testid="total-qff-points" />
            </Text>
            <Text fontWeight="bold">&nbsp;PTS&nbsp;•&nbsp;</Text>
            <PointsPerDollar pointsPerDollar={pointsPerDollar} fontSize={['xs', 'sm']} />
          </Flex>
          <Text display="block" fontSize="sm">
            plus <PointsEarnDisplay total={pointsEarned.qbrPoints.total} data-testid="total-qbr-points" fontWeight="normal" /> PTS for your
            business**
          </Text>
        </Box>
      )}
      {pointsEarned.qffPoints.total === 0 && (
        <Text display="block" fontSize="sm" data-testid="checkout-summary-no-points-earned">
          You will not earn Qantas Points when using points
        </Text>
      )}
    </Box>
  );
};

PointsEarnSummary.propTypes = {
  pointsEarned: PropTypes.object,
};

PointsEarnSummary.defaultProps = {
  pointsEarned: null,
};

export default PointsEarnSummary;
