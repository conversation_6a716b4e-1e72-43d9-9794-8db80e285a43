import React from 'react';
import PointsEarnSummary from './PointsEarnSummary';
import useCheckoutPointsEarned from 'components/CheckoutPage/hooks/useCheckoutPointsEarned';
import { mountUtils } from 'test-utils';
import * as config from 'config';

jest.mock('config');
jest.mock('components/CheckoutPage/hooks/useCheckoutPointsEarned');

mountUtils.mockComponent('PointsPerDollar');

const pointsEarned = {
  qffPoints: { total: 1234 },
  qbrPoints: { total: 234 },
};

const render = () =>
  mountUtils(<PointsEarnSummary pointsEarned={pointsEarned} />, {
    decorators: { store: true, theme: true, router: true },
  });

beforeEach(() => {});

describe('with POINTS_EARN_ENABLED on', () => {
  beforeEach(() => {
    config.POINTS_EARN_ENABLED = true;
    useCheckoutPointsEarned.mockReturnValue({ pointsEarned });
  });

  it('renders the qff points earned', () => {
    const { find } = render();
    expect(find('PointsEarnDisplay[data-testid="total-qff-points"]')).toHaveProp({ total: pointsEarned.qffPoints.total });
  });

  it('renders the qbr points earned', () => {
    const { find } = render();
    expect(find('PointsEarnDisplay[data-testid="total-qbr-points"]')).toHaveProp({ total: pointsEarned.qbrPoints.total });
  });

  it('should show <PointsPerDollar>', () => {
    const { find } = render();
    expect(find('PointsPerDollar')).toExist();
  });
});

describe('with POINTS_EARN_ENABLED off', () => {
  beforeEach(() => {
    config.POINTS_EARN_ENABLED = false;
    useCheckoutPointsEarned.mockReturnValue({ pointsEarned });
  });

  it('renders noting', () => {
    const { wrapper } = render();
    expect(wrapper.html()).toEqual(null);
  });
});

describe('when user is using points or offer is classic', () => {
  beforeEach(() => {
    config.POINTS_EARN_ENABLED = true;
    useCheckoutPointsEarned.mockReturnValue({ pointsEarned: { qffPoints: { total: 0 }, qbrPoints: { total: 234 } } });
  });
  it('renders the appropriate message', () => {
    const { findByTestId } = render();
    expect(findByTestId('checkout-summary-no-points-earned')).toHaveText('You will not earn Qantas Points when using points');
  });
});
