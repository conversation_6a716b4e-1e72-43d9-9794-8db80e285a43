import React from 'react';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import { mountUtils } from 'test-utils';
import PriceDetails from './PriceDetails';
import { getPayableLaterDueDate } from 'store/quote/quoteSelectors';
import {
  getPointsAmount,
  getVoucherAmount,
  getPayableNowCashAmount,
  getPayableLaterCashAmount,
  getTravelPassAmount,
} from 'store/checkout/checkoutSelectors';

jest.mock('store/checkout/checkoutSelectors');
jest.mock('store/quote/quoteSelectors');

mountUtils.mockComponent('PaymentBreakdown');
mountUtils.mockComponent('PriceBreakdownModal');

const payableAtPropertyTotal = {
  amount: new Decimal(10),
  currency: 'AUD',
};

const property = {
  name: 'Test hotel',
};

const offer = {
  type: 'standard',
  charges: {
    payableAtProperty: { total: payableAtPropertyTotal },
  },
};

const checkIn = new Date(2020, 9, 1);
const checkOut = new Date(2020, 9, 2);
const pointsAmount = new Decimal(1000);
const voucherAmount = new Decimal(40);
const travelPassAmount = new Decimal(100);
const payableNowCashAmount = new Decimal(100);
const payableLaterCashAmount = new Decimal(200);
const payableLaterDueDate = new Date(2020, 10, 10);

const baseProps = {
  property,
  offer,
  checkIn,
  checkOut,
};

const render = (props) =>
  mountUtils(<PriceDetails {...baseProps} {...props} />, { decorators: { theme: true, store: true, router: true } });

beforeEach(() => {
  getPointsAmount.mockReturnValue(pointsAmount);
  getVoucherAmount.mockReturnValue(voucherAmount);
  getTravelPassAmount.mockReturnValue(travelPassAmount);
  getPayableNowCashAmount.mockReturnValue(payableNowCashAmount);
  getPayableLaterCashAmount.mockReturnValue(payableLaterCashAmount);
  getPayableLaterDueDate.mockReturnValue(payableLaterDueDate);
});

it('renders the PaymentBreakdown', () => {
  const { find } = render();
  expect(find('PaymentBreakdown')).toHaveProp({
    payableNowCashAmount,
    payableLaterCashAmount,
    payableLaterDueDate,
    pointsAmount,
    travelPassAmount,
    payableAtProperty: payableAtPropertyTotal,
  });
});

it('renders the PriceBreakdownModal', () => {
  const { find } = render();
  expect(find('PriceBreakdownModal')).toHaveProp({
    property,
    offer,
    checkIn,
    checkOut,
  });
});

it('renders the voucher details', () => {
  const { find } = render();
  expect(find('Currency[data-testid="voucher-amount"]')).toHaveProp({
    amount: voucherAmount.negated(),
    currency: 'AUD',
  });
});

describe('without a voucher', () => {
  it('does not render the voucher details', () => {
    getVoucherAmount.mockReturnValue(new Decimal(0));
    const { findByTestId } = render();
    expect(findByTestId('voucher-amount')).not.toExist();
  });
});
