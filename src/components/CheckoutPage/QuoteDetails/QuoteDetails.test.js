import React from 'react';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import merge from 'lodash/merge';
import { mountUtils } from 'test-utils';
import QuoteDetails from './QuoteDetails';
import { getPointsAmount } from 'store/checkout/checkoutSelectors';
import { getStayDates, getQuote, getOccupants } from 'store/quote/quoteSelectors';

jest.mock('store/checkout/checkoutSelectors');
jest.mock('store/quote/quoteSelectors');
jest.mock('store/ui/uiSelectors');

mountUtils.mockComponent('PropertyDetails');
mountUtils.mockComponent('StayDates');
mountUtils.mockComponent('OfferDetails');
mountUtils.mockComponent('RoomTypeDetails');
mountUtils.mockComponent('PriceDetails');
mountUtils.mockComponent('CancellationRefundSummary');
mountUtils.mockComponent('OccupantsSummary');
mountUtils.mockComponent('PointsEarnSummary');

const quote = {
  property: { name: 'name' },
  roomType: { name: 'name' },
  offer: {
    type: 'classic',
    inclusions: [],
    cancellationPolicy: {
      isNonrefundable: true,
      description: 'sorry no refunds',
      cancellationWindows: [],
    },
    pointsEarned: {
      qbrPoints: {
        total: 3000,
      },
      qffPoints: {
        base: 5139,
        total: 5139,
      },
    },
  },
};

const decorators = { theme: true, store: true };
const render = (props) => mountUtils(<QuoteDetails {...props} />, { decorators });

const stayDates = { checkIn: new Date(2020, 9, 1), checkOut: new Date(2020, 9, 2) };
const occupants = { adults: 2, children: 2, infants: 2 };

beforeEach(() => {
  getQuote.mockReturnValue(quote);
  getStayDates.mockReturnValue(stayDates);
  getPointsAmount.mockReturnValue(new Decimal(10000));
  getOccupants.mockReturnValue(occupants);
});

it('passes props onto the wrapper', () => {
  const props = { color: 'tomato' };
  const { find } = render(props);
  expect(find('Wrapper')).toHaveProp(props);
});

describe('with a quote in the store', () => {
  it('returns <PropertyDetails /> with the correct props', () => {
    const { find } = render();
    expect(find('PropertyDetails')).toHaveProp({
      property: quote.property,
      roomType: quote.roomType,
      offer: quote.offer,
    });
  });

  it('renders <StayDates /> with the correct props', () => {
    const { find } = render();
    expect(find('StayDates')).toHaveProp({
      checkIn: stayDates.checkIn,
      checkOut: stayDates.checkOut,
    });
  });

  describe('OccupantsSummary', () => {
    it('displays the OccupantsSummary component', () => {
      const { find } = render();
      expect(find('OccupantsSummary')).toHaveProp({
        occupants: { adults: 2, children: 2, infants: 2 },
      });
    });
  });

  describe('PointsEarnSummary', () => {
    it('renders the PointsEarnSummary with expected props', () => {
      const { find } = render();

      expect(find('PointsEarnSummary')).toHaveProp({
        pointsEarned: quote.offer.pointsEarned,
      });
    });
  });

  describe('Inclusions', () => {
    it('displays a comma separated list of inclusions when they exist', () => {
      const QuoteWithInclusions = merge({}, quote, {
        offer: {
          inclusions: [
            { name: 'wi-fi', description: 'wi-fi Included' },
            { name: 'breakfast', description: 'breakfast Included' },
          ],
        },
      });

      getQuote.mockReturnValue(QuoteWithInclusions);

      const { findByTestId } = render();
      expect(findByTestId('inclusion-descriptions')).toHaveText('wi-fi Included, breakfast Included');
    });

    it('does not display a comma separated list of inclusions  when they do not exist', () => {
      const { findByTestId } = render();
      expect(findByTestId('inclusion-descriptions')).not.toExist();
    });
  });

  describe('CancellationRefundSummary', () => {
    it('displays the CancellationRefundModal component with correct props', () => {
      const { find } = render();

      expect(find('CancellationRefundSummary')).toHaveProp({
        cancellationPolicy: quote.offer.cancellationPolicy,
        fontSize: 'sm',
      });
    });
  });

  it('renders the <PriceDetails /> with the correct props', () => {
    const notClassicQuote = merge({}, quote, { offer: { type: 'not classic' } });
    getQuote.mockReturnValue(notClassicQuote);
    const { find } = render();

    expect(find('PriceDetails')).toHaveProp({
      offer: notClassicQuote.offer,
      property: notClassicQuote.property,
      checkIn: stayDates.checkIn,
      checkOut: stayDates.checkOut,
    });
  });
});

describe('without a quote', () => {
  it('renders nothing', () => {
    getQuote.mockReturnValue(null);
    const { wrapper } = render();
    expect(wrapper).toBeEmptyRender();
  });
});

describe('without an offer on the quote due to the quote being unavailable', () => {
  beforeEach(() => {
    getQuote.mockReturnValue({ ...quote, offer: null });
  });

  it('does not render OccupantsSummary', () => {
    const { find } = render();
    expect(find('OccupantsSummary')).not.toExist();
  });

  it('does not render StayDates', () => {
    const { find } = render();
    expect(find('StayDates')).not.toExist();
  });

  it('does not render PriceDetails', () => {
    const { find } = render();
    expect(find('PriceDetails')).not.toExist();
  });

  it('does not render CancellationRefundModal', () => {
    const { find } = render();
    expect(find('CancellationRefundSummary')).not.toExist();
  });

  it('does not render the offer inclusions', () => {
    const { findByTestId } = render();
    expect(findByTestId('inclusion-descriptions')).not.toExist();
  });

  it('does not render the qff-points-display', () => {
    const { findByTestId } = render();
    expect(findByTestId('"qff-points-display')).not.toExist();
  });
  it('does not render the qbr-points-display', () => {
    const { findByTestId } = render();
    expect(findByTestId('qbr-points-display')).not.toExist();
  });
});
