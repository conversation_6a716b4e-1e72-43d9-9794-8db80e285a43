import React, { Fragment } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useMount, useUnmount } from 'react-use';
import { Container, Flex, Heading, Box } from '@qga/roo-ui/components';
import get from 'lodash/get';
import CustomerDetails from './CustomerDetails';
import QuoteDetails from './QuoteDetails';
import CheckoutHelmet from './CheckoutHelmet';
import CheckoutKnowBeforeYouGo from './CheckoutKnowBeforeYouGo';
import { getQuote, getIsAvailable, getQuoteError, getIsLoading } from 'store/quote/quoteSelectors';
import BackToPropertyLink from './BackToPropertyLink';
import ClassicMessaging from './ClassicMessaging';
import QuoteUnavailable from './QuoteUnavailable';
import { PriceIncreaseDialog, InventoryUnavailableDialog, FailureDialog } from './BookingErrorDialogs';
import WelcomeMessage from 'components/WelcomeMessage';
import MobileAppLeftNavigationIcon from 'components/MobileAppLeftNavigationIcon';

import { clearPointsLevels } from 'store/pointsConversion/pointsConversionActions';
import { setBookingClientRequestId, clearFormData } from 'store/checkout/checkoutActions';
import createBookingClientRequestId from 'lib/checkout/createBookingClientRequestId';
import { resolveDeviceFingerprint } from 'store/userEnvironment/userEnvironmentActions';
import useBeginCheckoutEvent from 'hooks/useBeginCheckoutEvent';

const CheckoutLayout = () => {
  const quote = useSelector(getQuote);
  const error = useSelector(getQuoteError);
  const isQuoteLoading = useSelector(getIsLoading);
  const isQuoteAvailable = useSelector(getIsAvailable);
  const dispatch = useDispatch();

  const isQuoteUnavailable = !isQuoteAvailable && !isQuoteLoading;
  const propertyName = get(quote, 'property.name');
  const isClassic = get(quote, 'offer.type') === 'classic';

  useMount(() => {
    dispatch(resolveDeviceFingerprint());
    dispatch(setBookingClientRequestId(createBookingClientRequestId()));
  });

  useUnmount(() => {
    dispatch(clearPointsLevels());
    dispatch(clearFormData());
  });

  useBeginCheckoutEvent();

  return (
    <Fragment>
      <CheckoutHelmet />
      <MobileAppLeftNavigationIcon iconName="back" />
      <WelcomeMessage />
      <BackToPropertyLink />
      <QuoteDetails display={['block', 'none']} bg="white" flexBasis="372px" minWidth="372px" />
      <Container>
        <Flex flexDirection={['column-reverse', 'column-reverse', 'row']} height="100%" px={[0, 5, 0]}>
          <Box flex="1 1 auto" pt={10} pr={[0, 0, 10]} minHeight={['auto', 'auto', '100vh']}>
            {isQuoteUnavailable && <QuoteUnavailable propertyName={propertyName} error={error} />}
            {quote && (
              <Fragment>
                {isQuoteAvailable && (
                  <Fragment>
                    <Heading.h1 mb={8} display={['none', 'block']} data-testid="heading">
                      Let&apos;s review your booking and pay
                    </Heading.h1>
                    <CheckoutKnowBeforeYouGo property={quote.property} />
                    {isClassic && <ClassicMessaging quote={quote} />}
                    <CustomerDetails quote={quote} mb={10} />
                  </Fragment>
                )}
              </Fragment>
            )}
          </Box>

          {quote && <QuoteDetails display={['none', 'block']} flexBasis="372px" minWidth="372px" pt={10} />}
          <FailureDialog />
          <PriceIncreaseDialog />
          <InventoryUnavailableDialog />
        </Flex>
      </Container>
    </Fragment>
  );
};

export default CheckoutLayout;
