import React from 'react';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import merge from 'lodash/merge';
import { mountUtils } from 'test-utils';
import PriceBreakdownModal from './PriceBreakdownModal';
import {
  getPointsAmount,
  getVoucherAmount,
  getPayableNowCashAmount,
  getPayableLaterCashAmount,
  getTravelPassAmount,
} from 'store/checkout/checkoutSelectors';
import { getPayableLaterDueDate } from 'store/quote/quoteSelectors';
import { useDataLayer } from 'hooks/useDataLayer';

jest.mock('store/checkout/checkoutSelectors');
jest.mock('store/quote/quoteSelectors');
jest.mock('hooks/useDataLayer');
jest.mock('store/ui/uiSelectors');

mountUtils.mockComponent('PaymentBreakdown');

const voucherAmount = new Decimal(50);
const travelPassAmount = new Decimal(100);
const pointsAmount = new Decimal(1000);
const payableLaterDueDate = new Date(2020, 10, 10);
const payableNowCashAmount = new Decimal(100);
const payableLaterCashAmount = new Decimal(100);

const payableAtPropertyTotal = {
  amount: '10.00',
  currency: 'AUD',
};

const payableAtBookingBaseRate = {
  amount: '300.00',
  currency: 'AUD',
};

const property = {
  name: 'Test hotel',
};

const taxDisplayable = {
  amount: '12.00',
  currency: 'AUD',
};

const extraOccupantCharge = {
  amount: '50.00',
  currency: 'AUD',
};

const offer = {
  type: 'standard',
  charges: {
    payableAtProperty: { total: payableAtPropertyTotal },
    payableAtBooking: { baseRate: payableAtBookingBaseRate, taxDisplayable, extraOccupantCharge },
  },
};

const checkIn = new Date(2020, 9, 1);
const checkOut = new Date(2020, 9, 2);

const defaultProps = {
  property,
  offer,
  checkIn,
  checkOut,
};

const emitInteractionEvent = jest.fn();

const render = (props) => {
  const result = mountUtils(<PriceBreakdownModal {...defaultProps} {...props} />, { decorators: { theme: true, store: true } });
  const { findByTestId } = result;
  findByTestId('price-breakdown-button').simulate('click');
  return result;
};

beforeEach(() => {
  getVoucherAmount.mockReturnValue(voucherAmount);
  getTravelPassAmount.mockReturnValue(travelPassAmount);
  getPointsAmount.mockReturnValue(pointsAmount);
  getPayableNowCashAmount.mockReturnValue(payableNowCashAmount);
  getPayableLaterCashAmount.mockReturnValue(payableLaterCashAmount);
  getPayableLaterDueDate.mockReturnValue(payableLaterDueDate);
  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

it('renders the property name', () => {
  const { findByTestId } = render();
  expect(findByTestId('property-name')).toHaveText(property.name);
});

it('renders the base rate', () => {
  const { findByTestId } = render();
  expect(findByTestId('stay-duration-and-base-rate')).toHaveText('1 night stay$300.00AUD');
});

describe('for classic offers', () => {
  it('renders the base-rate in points', () => {
    const classicOffer = { ...offer, type: 'classic' };

    const { findByTestId } = render({ offer: classicOffer });
    expect(findByTestId('stay-duration-and-base-rate')).toHaveText('1 night stay1,000PTS');
  });
});

it('renders the tax displayable fee', () => {
  const { findByTestId } = render();
  expect(findByTestId('tax')).toHaveText('Tax and service fee$12.00AUD');
});

describe('without taxDisplayable', () => {
  it('does not rende the tax', () => {
    const offerWithoutTax = merge({}, offer, {
      charges: {
        payableAtBooking: { taxDisplayable: { amount: '0' } },
      },
    });

    const { findByTestId } = render({ offer: offerWithoutTax });
    expect(findByTestId('tax')).not.toExist();
  });
});

it('renders the extra occupant charge', () => {
  const { findByTestId } = render();
  expect(findByTestId('extra-occupant')).toHaveText('Extra occupant charge$50.00AUD');
});

describe('without extraOccupantCharge', () => {
  it('does not render the charge', () => {
    const offerWithoutTax = merge({}, offer, {
      charges: {
        payableAtBooking: { extraOccupantCharge: { amount: '0' } },
      },
    });

    const { findByTestId } = render({ offer: offerWithoutTax });
    expect(findByTestId('extra-occupant')).not.toExist();
  });
});

it('dispatches an event to the data layer', () => {
  render();
  expect(emitInteractionEvent).toHaveBeenCalledWith({
    type: 'Summary View',
    value: 'Price Breakdown Selected',
  });
});

it('renders the voucher', () => {
  const { findByTestId } = render();
  expect(findByTestId('voucher')).toHaveText('Voucher$-50.00AUD');
});

describe('without a voucher', () => {
  it('does not render the voucher', () => {
    getVoucherAmount.mockReturnValue(new Decimal(0));
    const { findByTestId } = render();
    expect(findByTestId('voucher')).not.toExist();
  });
});

it('renders the PaymentBreakdown', () => {
  const { find } = render();
  expect(find('PaymentBreakdown')).toHaveProp({
    payableNowCashAmount,
    payableLaterCashAmount,
    payableLaterDueDate,
    pointsAmount,
    travelPassAmount,
    payableAtProperty: payableAtPropertyTotal,
  });
});
