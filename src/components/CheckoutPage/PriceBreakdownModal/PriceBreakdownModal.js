import React, { Fragment, useCallback } from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import { differenceInCalendarDays } from 'date-fns';
import isDate from 'lodash/isDate';
import get from 'lodash/get';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import { Heading, Flex, Box } from '@qga/roo-ui/components';
import TextButton from 'components/TextButton';
import Modal from 'components/Modal';
import { useModal } from 'lib/hooks';
import {
  getPointsAmount,
  getVoucherAmount,
  getPayableNowCashAmount,
  getPayableLaterCashAmount,
  getTravelPassAmount,
} from 'store/checkout/checkoutSelectors';
import { getPayableLaterDueDate } from 'store/quote/quoteSelectors';
import { useDataLayer } from 'hooks/useDataLayer';
import PaymentBreakdown from 'components/PaymentBreakdown';
import Currency from 'components/Currency';

const priceStyling = { flex: '1 1 auto', fontSize: ['sm', 'base'] };
const priceFontSize = ['sm', 'base'];

const createLineItemCharge = (charge, apiPath) => {
  const lineItemAmount = new Decimal(get(charge, apiPath, 0));
  return { amount: lineItemAmount.toNumber(), currency: 'AUD' };
};

const PriceBreakdownModal = ({ property, offer, checkIn, checkOut }) => {
  const voucherAmount = useSelector(getVoucherAmount);
  const pointsAmount = useSelector(getPointsAmount);
  const travelPassAmount = useSelector(getTravelPassAmount);
  const payableNowCashAmount = useSelector(getPayableNowCashAmount);
  const payableLaterCashAmount = useSelector(getPayableLaterCashAmount);
  const payableLaterDueDate = useSelector(getPayableLaterDueDate);

  const { name } = property;
  const { charges, type } = offer;
  const baseRate = get(charges, 'payableAtBooking.baseRate');
  const payableAtProperty = get(charges, 'payableAtProperty.total', {});
  const taxDisplayableCharge = createLineItemCharge(charges, 'payableAtBooking.taxDisplayable.amount');
  const extraOccupantCharge = createLineItemCharge(charges, 'payableAtBooking.extraOccupantCharge.amount');
  const hasTaxDisplayableCharge = taxDisplayableCharge.amount > 0;
  const hasExtraOccupantCharge = extraOccupantCharge.amount > 0;
  const hasValidDates = isDate(checkOut) && isDate(checkIn);
  const stayDuration = `${differenceInCalendarDays(checkOut, checkIn)} night stay`;

  const isClassic = type === 'classic';
  const rateTotal = !isClassic ? baseRate : { amount: pointsAmount.toNumber(), currency: 'PTS' };

  const totalVoucher = { amount: voucherAmount.negated().toNumber(), currency: 'AUD' };

  const { openModal, modalProps } = useModal();
  const { emitInteractionEvent } = useDataLayer();
  const handleOnClick = useCallback(() => {
    openModal();
    emitInteractionEvent({ type: 'Summary View', value: 'Price Breakdown Selected' });
  }, [openModal, emitInteractionEvent]);

  const handleClose = useCallback(() => {
    modalProps.onRequestClose();
    emitInteractionEvent({ type: 'Summary View', value: 'Price Breakdown Closed' });
  }, [modalProps, emitInteractionEvent]);

  return (
    <Fragment>
      <Flex flexDirection="row">
        <TextButton onClick={handleOnClick} color="greys.steel" textAlign="left" data-testid="price-breakdown-button">
          Price breakdown
        </TextButton>
      </Flex>
      <Modal {...modalProps} onRequestClose={handleClose} padding={[4, 9]} title="Price breakdown">
        <Box mb="4" pb={[3, 1]} mx={[0, 11]} borderBottom={1} borderColor="greys.alto">
          <Heading.h4 fontSize={['base', 'md']} mb={[3, 5]} data-testid="property-name">
            {name}
          </Heading.h4>
          {hasValidDates && (
            <Flex data-testid="stay-duration-and-base-rate">
              <Box {...priceStyling}>{stayDuration}</Box>
              <Currency amount={rateTotal.amount} currency={rateTotal.currency} fontSize={priceFontSize} />
            </Flex>
          )}
          {hasExtraOccupantCharge && (
            <Flex data-testid="extra-occupant">
              <Box {...priceStyling}>Extra occupant charge</Box>
              <Currency amount={extraOccupantCharge.amount} currency={extraOccupantCharge.currency} fontSize={priceFontSize} />
            </Flex>
          )}
          {hasTaxDisplayableCharge && (
            <Flex data-testid="tax">
              <Box {...priceStyling}>Tax and service fee</Box>
              <Currency amount={taxDisplayableCharge.amount} currency={taxDisplayableCharge.currency} fontSize={priceFontSize} />
            </Flex>
          )}
          {!voucherAmount.isZero() && (
            <Flex data-testid="voucher">
              <Box {...priceStyling}>Voucher</Box>
              <Currency amount={totalVoucher.amount} currency={totalVoucher.currency} fontSize={priceFontSize} />
            </Flex>
          )}
          <Flex>
            <Box {...priceStyling}>Processing fee</Box>
            <Currency amount={0} currency="AUD" fontSize={priceFontSize} />
          </Flex>
        </Box>
        <Box mx={[0, 11]}>
          <PaymentBreakdown
            payableNowCashAmount={payableNowCashAmount}
            payableLaterCashAmount={payableLaterCashAmount}
            payableLaterDueDate={payableLaterDueDate}
            pointsAmount={pointsAmount}
            travelPassAmount={travelPassAmount}
            payableAtProperty={payableAtProperty}
          />
        </Box>
      </Modal>
    </Fragment>
  );
};

PriceBreakdownModal.propTypes = {
  property: PropTypes.shape({
    name: PropTypes.string.isRequired,
  }).isRequired,
  offer: PropTypes.shape({
    type: PropTypes.string.isRequired,
    charges: PropTypes.shape({
      payableAtProperty: PropTypes.shape({
        amount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        currency: PropTypes.string,
      }).isRequired,
    }).isRequired,
  }).isRequired,
  checkIn: PropTypes.instanceOf(Date).isRequired,
  checkOut: PropTypes.instanceOf(Date).isRequired,
};

export default PriceBreakdownModal;
