import * as sessionStorage from 'lib/browser/sessionStorage';

export const setIsLoggedInFromStep = (isLoggedInFromStep) => {
  sessionStorage.set('isLoggedInFromStep', isLoggedInFromStep);
};

export const getIsLoggedInFromStep = () => {
  return sessionStorage.get('isLoggedInFromStep') || false;
};

export const resetIsLoggedInFromStep = () => {
  sessionStorage.set('isLoggedInFromStep', false);
};

export const registerFormDataEntered = (formData) => {
  sessionStorage.set('formData', JSON.stringify(formData));
};

export const getFormDataEntered = () => {
  return sessionStorage.get('formData');
};

export const clearFormDataEntered = () => {
  sessionStorage.set('formData', null);
};
