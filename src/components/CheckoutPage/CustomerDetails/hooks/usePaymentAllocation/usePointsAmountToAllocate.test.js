// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import { renderHook } from 'test-utils';
import { getInitialCashAmount, getPaymentMode, getTotalPayableAfterCredits, getPayWith } from 'store/checkout/checkoutSelectors';
import { getPointsBalance } from 'store/user/userSelectors';
import { useIsAuthenticated } from 'lib/oauth';
import usePointsConverters from 'hooks/points/usePointsConverters';
import { PAYMENT_MODES, PAYMENT_METHODS } from 'lib/enums/payment';
import usePointsAmountToAllocate from './usePointsAmountToAllocate';
import { MIN_POINTS_AMOUNT } from 'config';
import { getIsClassic, getTotalPayableAtBooking, getTotalPayableAtBookingInPoints } from 'store/quote/quoteSelectors';

jest.mock('store/checkout/checkoutSelectors');
jest.mock('hooks/points/usePointsConverters');
jest.mock('store/user/userSelectors');
jest.mock('store/quote/quoteSelectors');
jest.mock('lib/oauth');

const POINTS_CONVERSION_MULTIPLIER = 1000; // stubbed out multiplier for points/cash conversions
const convertCashToPoints = ({ cash }) => new Decimal(cash).times(POINTS_CONVERSION_MULTIPLIER);
const convertPointsToCash = ({ points }) => new Decimal(points).dividedBy(POINTS_CONVERSION_MULTIPLIER);

const totalPayableAtBooking = new Decimal(200);
const totalPayableAtBookingInPoints = new Decimal(2000000);
const totalPayableAfterCredits = new Decimal(200);
const initialCashAmount = new Decimal(40);

beforeEach(() => {
  usePointsConverters.mockReturnValue({
    convertCashToPoints,
    convertPointsToCash,
  });

  getPayWith.mockReturnValue(PAYMENT_METHODS.CASH);
  getPaymentMode.mockReturnValue(null);
  getInitialCashAmount.mockReturnValue(initialCashAmount);
  getTotalPayableAtBooking.mockReturnValue(totalPayableAtBooking);
  getTotalPayableAtBookingInPoints.mockReturnValue(totalPayableAtBookingInPoints);
  getTotalPayableAfterCredits.mockReturnValue(totalPayableAfterCredits);
  useIsAuthenticated.mockReturnValue(false);
  getIsClassic.mockReturnValue(false);
});

describe('when not authenticated', () => {
  describe('when payWith is cash', () => {
    it('returns zero points', () => {
      getPayWith.mockReturnValue(PAYMENT_METHODS.CASH);
      const { result } = renderHook(() => usePointsAmountToAllocate(), { store: true });
      expect(result.current).toEqual({ pointsAmountToAllocate: new Decimal(0), pointsAmountInCashToAllocate: new Decimal(0) });
    });
  });

  describe('when payWith is points', () => {
    it('returns correct points', () => {
      getPayWith.mockReturnValue(PAYMENT_METHODS.POINTS);

      const { result } = renderHook(() => usePointsAmountToAllocate(), { store: true });
      expect(result.current).toEqual({
        pointsAmountToAllocate: convertCashToPoints({ cash: totalPayableAfterCredits.minus(initialCashAmount) }),
        pointsAmountInCashToAllocate: totalPayableAfterCredits.minus(initialCashAmount),
      });
    });

    describe('when in deposit mode', () => {
      it('returns zero points', () => {
        getPayWith.mockReturnValue(PAYMENT_METHODS.POINTS);
        getPaymentMode.mockReturnValue(PAYMENT_MODES.DEPOSIT);

        const { result } = renderHook(() => usePointsAmountToAllocate(), { store: true });
        expect(result.current).toEqual({ pointsAmountToAllocate: new Decimal(0), pointsAmountInCashToAllocate: new Decimal(0) });
      });
    });
  });

  it('returns zero points', () => {
    const { result } = renderHook(() => usePointsAmountToAllocate(), { store: true });
    expect(result.current).toEqual({ pointsAmountToAllocate: new Decimal(0), pointsAmountInCashToAllocate: new Decimal(0) });
  });
});

describe('when authenticated', () => {
  beforeEach(() => {
    useIsAuthenticated.mockReturnValue(true);
  });

  describe('with a points balance below the minimum', () => {
    it('returns zero points', () => {
      getPointsBalance.mockReturnValue(MIN_POINTS_AMOUNT.minus(1));
      const { result } = renderHook(() => usePointsAmountToAllocate(), { store: true });
      expect(result.current).toEqual({ pointsAmountToAllocate: new Decimal(0), pointsAmountInCashToAllocate: new Decimal(0) });
    });
  });

  describe('with a points balance equal to the total payable', () => {
    beforeEach(() => {
      getPointsBalance.mockReturnValue(totalPayableAfterCredits.times(POINTS_CONVERSION_MULTIPLIER));
    });

    describe('when in deposit mode', () => {
      it('returns zero points', () => {
        getPaymentMode.mockReturnValue(PAYMENT_MODES.DEPOSIT);
        const { result } = renderHook(() => usePointsAmountToAllocate(), { store: true });
        expect(result.current).toEqual({ pointsAmountToAllocate: new Decimal(0), pointsAmountInCashToAllocate: new Decimal(0) });
      });
    });

    describe('when payWith is cash', () => {
      it('returns zero points', () => {
        getPayWith.mockReturnValue(PAYMENT_METHODS.CASH);
        const { result } = renderHook(() => usePointsAmountToAllocate(), { store: true });
        expect(result.current).toEqual({ pointsAmountToAllocate: new Decimal(0), pointsAmountInCashToAllocate: new Decimal(0) });
      });
    });

    describe('when payWith is points and full payment mode', () => {
      beforeEach(() => {
        getPayWith.mockReturnValue(PAYMENT_METHODS.POINTS);
        getPaymentMode.mockReturnValue(PAYMENT_MODES.FULL);
      });

      it('returns the remaining points', () => {
        const { result } = renderHook(() => usePointsAmountToAllocate(), { store: true });
        expect(result.current).toEqual({
          pointsAmountToAllocate: convertCashToPoints({ cash: totalPayableAfterCredits.minus(initialCashAmount) }),
          pointsAmountInCashToAllocate: totalPayableAfterCredits.minus(initialCashAmount),
        });
      });

      describe('when initialCashAmount takes the points below the minimum', () => {
        it('returns MIN_POINTS_AMOUNT', () => {
          getInitialCashAmount.mockReturnValue(totalPayableAfterCredits.minus(1));
          const { result } = renderHook(() => usePointsAmountToAllocate(), { store: true });
          expect(result.current).toEqual({
            pointsAmountToAllocate: MIN_POINTS_AMOUNT,
            pointsAmountInCashToAllocate: convertPointsToCash({ points: MIN_POINTS_AMOUNT }),
          });
        });
      });

      describe('when the points balance is below the amount calculated', () => {
        it('returns the points balance', () => {
          const pointsBalance = new Decimal(10000);
          getInitialCashAmount.mockReturnValue(new Decimal(0));
          getPointsBalance.mockReturnValue(pointsBalance);
          const { result } = renderHook(() => usePointsAmountToAllocate(), { store: true });
          expect(result.current).toEqual({
            pointsAmountToAllocate: pointsBalance,
            pointsAmountInCashToAllocate: convertPointsToCash({ points: pointsBalance }),
          });
        });
      });

      describe('when the totalPayableAtBookingInPoints is less the the minimum points amount', () => {
        it('returns zero points', () => {
          getTotalPayableAtBookingInPoints.mockReturnValue(MIN_POINTS_AMOUNT.minus(1));
          const { result } = renderHook(() => usePointsAmountToAllocate(), { store: true });
          expect(result.current).toEqual({
            pointsAmountToAllocate: new Decimal(0),
            pointsAmountInCashToAllocate: new Decimal(0),
          });
        });
      });
    });
  });
});

describe('with a classic offer', () => {
  beforeEach(() => {
    getIsClassic.mockReturnValue(true);
    useIsAuthenticated.mockReturnValue(true);
  });

  it('returns the provided classic amount payable at booking', () => {
    const { result } = renderHook(() => usePointsAmountToAllocate(), { store: true });
    expect(result.current).toEqual({
      pointsAmountToAllocate: totalPayableAtBookingInPoints,
      pointsAmountInCashToAllocate: totalPayableAtBooking,
    });
  });
});
