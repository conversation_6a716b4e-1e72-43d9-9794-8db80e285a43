/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useCallback, useRef } from 'react';
import { useUpdateEffect, useEffectOnce } from 'react-use';
import { useSelector, useDispatch } from 'react-redux';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import {
  getPaymentMode,
  getPointsAmount,
  getPayableNowCashAmount,
  getTotalPayableAfterCredits,
  getPointsAmountInCash,
  getTravelPassAmount,
} from 'store/checkout/checkoutSelectors';
import { useIsAuthenticated } from 'lib/oauth';
import { getPointsConversionTierName } from 'store/pointsConversion/pointsConversionSelectors';
import usePointsAmountToAllocate from './usePointsAmountToAllocate';
import useCashPayableNowToAllocate from './useCashPayableNowToAllocate';
import usePointsConverters from 'hooks/points/usePointsConverters';
import { updatePayments } from 'store/checkout/checkoutActions';
import { PAYMENT_MODES } from 'lib/enums/payment';
import { getQuote } from 'store/quote/quoteSelectors';

// Responsible for the allocation of payment methods during initialization and when individual payment amounts are
// changed through user interaction. This should be the first port of call for adding any logic relating to updating
// payment methods in order to centralise this as much as possible.
const usePaymentAllocation = () => {
  const { convertCashToPoints } = usePointsConverters();
  const dispatch = useDispatch();

  const payableNowCashAmount = useSelector(getPayableNowCashAmount);
  const pointsAmount = useSelector(getPointsAmount);
  const pointsAmountInCash = useSelector(getPointsAmountInCash);
  const travelPassAmount = useSelector(getTravelPassAmount);
  const pointsConversionTierName = useSelector(getPointsConversionTierName);
  const paymentMode = useSelector(getPaymentMode);
  const totalPayableAfterCredits = useSelector(getTotalPayableAfterCredits);
  const isAuthenticated = useIsAuthenticated();
  const { pointsAmountToAllocate, pointsAmountInCashToAllocate } = usePointsAmountToAllocate();
  const cashPayableNowAmountToAllocate = useCashPayableNowToAllocate({ pointsAmountInCashToAllocate });
  const latestTotalPayableAfterCreditsRef = useRef(totalPayableAfterCredits);
  const latestPaymentModeRef = useRef(paymentMode);
  const latestIsAuthenticatedRef = useRef(isAuthenticated);
  const quote = useSelector(getQuote);

  // usePaymentAllocation must be initially evaluated in the presence of a quote. Behaviour exists in the allocation logic that
  // depends on changes to values such as totalPayableAfterVoucher / totalPayableAfterCredits only occurring after user interaction
  // such as adding a voucher.
  // If this hook executes prior to a quote existing, it will evaluate against default zero values which will then
  // cause behaviour to trigger when the quote eventually loads that should not be part of initialization.
  if (!quote) {
    throw new Error(
      'usePaymentAllocation must be evaluated in the presence of a quote. Please move this hook into a component that is initialized once the quote exists.',
    );
  }

  // Initialize the allocations based on the amounts calculated by
  // `usePointsAmountToAllocate` and `useCashPayableNowToAllocate`
  const initializeAllocations = useCallback(() => {
    dispatch(
      updatePayments({
        points: {
          amount: pointsAmountToAllocate,
          amountInCash: pointsAmountInCashToAllocate,
          tierVersionCode: pointsConversionTierName,
        },
        cash: {
          payableNow: {
            amount: cashPayableNowAmountToAllocate,
          },
        },
      }),
    );

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    cashPayableNowAmountToAllocate.toNumber(),
    pointsAmountToAllocate.toNumber(),
    pointsAmountInCashToAllocate.toNumber(),
    updatePayments,
  ]);

  // initialise the allocations once
  useEffectOnce(initializeAllocations, [initializeAllocations]);

  // reset the allocations whenever the payment mode changes or the user logs out
  useEffect(() => {
    const shouldInitializeAllocations =
      latestPaymentModeRef.current !== paymentMode || (latestIsAuthenticatedRef.current !== isAuthenticated && !isAuthenticated);

    latestPaymentModeRef.current = paymentMode;
    latestIsAuthenticatedRef.current = isAuthenticated;

    if (shouldInitializeAllocations) {
      initializeAllocations();
    }
  }, [paymentMode, isAuthenticated, initializeAllocations]);

  // Rebalance the allocations if the totalPayableAfterCredits changes.
  // One overarching rule is to attempt to preserve TravelPass and Points amounts that a user has selected as much as possible,
  // while adjusting the cash amounts to balance things out
  useUpdateEffect(() => {
    const shouldUpdateAllocations = !latestTotalPayableAfterCreditsRef.current.equals(totalPayableAfterCredits);
    // || !latestMinimumDepositAmountRef.current.equals(minimumDepositAmount);

    latestTotalPayableAfterCreditsRef.current = totalPayableAfterCredits;
    // latestMinimumDepositAmountRef.current = minimumDepositAmount;

    if (shouldUpdateAllocations) {
      let newPointsAmount = pointsAmount;
      let newPointsAmountInCash = pointsAmountInCash;
      let newPayableNowCashAmount = payableNowCashAmount;

      // For deposit payments, first reduce the cash deposit amount and if there is still
      // some left over, reduce the points amount until the payable later is zero or positive
      if (paymentMode === PAYMENT_MODES.DEPOSIT) {
        let payableLaterCashAmount = totalPayableAfterCredits.minus(payableNowCashAmount).minus(pointsAmountInCash);
        if (payableLaterCashAmount.lessThan(0)) {
          newPayableNowCashAmount = newPayableNowCashAmount.plus(payableLaterCashAmount); // plus a negative amount

          if (newPayableNowCashAmount.lessThan(0)) {
            newPointsAmountInCash = newPointsAmountInCash.plus(newPayableNowCashAmount); // plus a negative amount
            newPointsAmount = convertCashToPoints({ cash: newPointsAmountInCash });
            newPayableNowCashAmount = new Decimal(0);
          }
        }

        // If we've removed a voucher that previously reduced the deposit amount,
        // we need to add the shortfall back to the cash payable now amount
        //const depositShortfall = minimumDepositAmount.minus(newPointsAmountInCash).minus(newPayableNowCashAmount);

        // if (depositShortfall.greaterThan(0)) {
        //   newPayableNowCashAmount = newPayableNowCashAmount.plus(depositShortfall);
        // }
        // For full payments if a voucher is added or removed adjust the cash amount
        // if possible before adjusting the points amount
      } else {
        if (totalPayableAfterCredits.greaterThan(newPointsAmountInCash)) {
          newPayableNowCashAmount = totalPayableAfterCredits.minus(newPointsAmountInCash);
        } else {
          newPointsAmountInCash = totalPayableAfterCredits;
          newPointsAmount = convertCashToPoints({ cash: newPointsAmountInCash });
          newPayableNowCashAmount = new Decimal(0);
        }
      }

      let newTravelPassAmount = travelPassAmount;

      // adding a voucher when we've already allocated a TravelPass has put us in the negative.
      if (totalPayableAfterCredits.lessThan(0)) {
        newTravelPassAmount = travelPassAmount.plus(totalPayableAfterCredits); // adding a negative number
        newPayableNowCashAmount = new Decimal(0);
        newPointsAmountInCash = new Decimal(0);
        newPointsAmount = new Decimal(0);
      }

      dispatch(
        updatePayments({
          points: {
            amount: newPointsAmount,
            amountInCash: newPointsAmountInCash,
            tierVersionCode: pointsConversionTierName,
          },
          cash: {
            payableNow: {
              amount: newPayableNowCashAmount,
            },
          },
          travelPass: {
            amount: newTravelPassAmount,
          },
        }),
      );
    }
  }, [
    convertCashToPoints,
    payableNowCashAmount.toNumber(),
    pointsAmount.toNumber(),
    pointsAmountInCash.toNumber(),
    totalPayableAfterCredits.toNumber(),
    updatePayments,
  ]);
};

export default usePaymentAllocation;
