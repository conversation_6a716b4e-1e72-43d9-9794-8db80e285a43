import { useSelector } from 'react-redux';
import { getMinimumDepositAmount, getPaymentMode, getTotalPayableAfterCredits } from 'store/checkout/checkoutSelectors';
import { getIsClassic } from 'store/quote/quoteSelectors';
import { PAYMENT_MODES } from 'lib/enums/payment';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';

const useCashPayableNowToAllocate = ({ pointsAmountInCashToAllocate }) => {
  const paymentMode = useSelector(getPaymentMode);
  const totalPayableAfterCredits = useSelector(getTotalPayableAfterCredits);
  const minimumDepositAmount = useSelector(getMinimumDepositAmount);
  const isClassic = useSelector(getIsClassic);

  if (isClassic) {
    return new Decimal(0);
  } else if (paymentMode === PAYMENT_MODES.DEPOSIT) {
    return Decimal.min(minimumDepositAmount, totalPayableAfterCredits);
  } else {
    return totalPayableAfterCredits.minus(pointsAmountInCashToAllocate);
  }
};

export default useCashPayableNowToAllocate;
