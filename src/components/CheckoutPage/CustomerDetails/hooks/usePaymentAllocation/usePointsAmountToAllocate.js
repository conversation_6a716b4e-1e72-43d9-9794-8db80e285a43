import { useSelector } from 'react-redux';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import { getInitialCashAmount, getPaymentMode, getTotalPayableAfterCredits, getPayWith } from 'store/checkout/checkoutSelectors';
import { getIsClassic, getTotalPayableAtBooking, getTotalPayableAtBookingInPoints } from 'store/quote/quoteSelectors';
import { getPointsBalance } from 'store/user/userSelectors';
import { MIN_POINTS_AMOUNT } from 'config';
import { PAYMENT_MODES, PAYMENT_METHODS } from 'lib/enums/payment';
import usePointsConverters from 'hooks/points/usePointsConverters';
import { useIsAuthenticated } from 'lib/oauth';

const usePointsAmountToAllocate = () => {
  const { convertCashToPoints, convertPointsToCash } = usePointsConverters();
  const totalPayableAfterCredits = useSelector(getTotalPayableAfterCredits);
  const paymentMode = useSelector(getPaymentMode);
  const payWith = useSelector(getPayWith);
  const isAuthenticated = useIsAuthenticated();
  const pointsBalance = useSelector(getPointsBalance);
  const initialCashAmount = useSelector(getInitialCashAmount);
  const isClassic = useSelector(getIsClassic);
  const totalPayableAtBookingInPoints = useSelector(getTotalPayableAtBookingInPoints);
  const totalPayableAtBooking = useSelector(getTotalPayableAtBooking);

  let pointsAmountToAllocate;

  if (isClassic) {
    pointsAmountToAllocate = totalPayableAtBookingInPoints;
  } else if (
    isAuthenticated &&
    pointsBalance.greaterThanOrEqualTo(MIN_POINTS_AMOUNT) &&
    totalPayableAtBookingInPoints.greaterThanOrEqualTo(MIN_POINTS_AMOUNT)
  ) {
    if (paymentMode === PAYMENT_MODES.DEPOSIT || payWith !== PAYMENT_METHODS.POINTS) {
      pointsAmountToAllocate = new Decimal(0);
    } else {
      pointsAmountToAllocate = convertCashToPoints({ cash: totalPayableAfterCredits.minus(initialCashAmount) });
      pointsAmountToAllocate = Decimal.max(pointsAmountToAllocate, MIN_POINTS_AMOUNT);
      pointsAmountToAllocate = Decimal.min(pointsAmountToAllocate, pointsBalance);
    }
  } else if (!isAuthenticated && payWith === PAYMENT_METHODS.POINTS && paymentMode !== PAYMENT_MODES.DEPOSIT) {
    pointsAmountToAllocate = convertCashToPoints({ cash: totalPayableAfterCredits.minus(initialCashAmount) });
    pointsAmountToAllocate = Decimal.max(pointsAmountToAllocate, MIN_POINTS_AMOUNT);
  } else {
    pointsAmountToAllocate = new Decimal(0);
  }

  const pointsAmountInCashToAllocate = isClassic ? totalPayableAtBooking : convertPointsToCash({ points: pointsAmountToAllocate });

  return {
    pointsAmountToAllocate,
    pointsAmountInCashToAllocate,
  };
};

export default usePointsAmountToAllocate;
