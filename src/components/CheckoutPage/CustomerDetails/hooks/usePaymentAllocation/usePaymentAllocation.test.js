// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import { renderHook } from 'test-utils';
import {
  getPaymentMode,
  getPointsAmount,
  getTravelPassAmount,
  getPayableNowCashAmount,
  getTotalPayableAfterCredits,
  getPointsAmountInCash,
  getMinimumDepositAmount,
} from 'store/checkout/checkoutSelectors';
import { getPointsConversionTierName } from 'store/pointsConversion/pointsConversionSelectors';
import usePointsAmountToAllocate from './usePointsAmountToAllocate';
import useCashPayableNowToAllocate from './useCashPayableNowToAllocate';
import usePointsConverters from 'hooks/points/usePointsConverters';
import usePaymentAllocation from './usePaymentAllocation';
import { PAYMENT_MODES } from 'lib/enums/payment';
import { updatePayments } from 'store/checkout/checkoutActions';
import { useIsAuthenticated } from 'lib/oauth';
import { getQuote } from 'store/quote/quoteSelectors';

jest.mock('store/checkout/checkoutSelectors');
jest.mock('lib/oauth');
jest.mock('store/quote/quoteSelectors');
jest.mock('store/pointsConversion/pointsConversionSelectors');
jest.mock('hooks/points/usePointsConverters');
jest.mock('./usePointsAmountToAllocate');
jest.mock('./useCashPayableNowToAllocate');

const POINTS_CONVERSION_MULTIPLIER = 1000; // stubbed out multiplier for points/cash conversions

const convertCashToPoints = ({ cash }) => new Decimal(cash).times(POINTS_CONVERSION_MULTIPLIER);

const travelPassAmount = new Decimal(10);
const totalPayableAfterCredits = new Decimal(200);
const pointsAmount = new Decimal(10000);
const pointsAmountInCash = new Decimal(10);
const payableNowCashAmount = new Decimal(100);
const minimumDepositAmount = new Decimal(0);

const pointsAmountToAllocate = new Decimal(10000);
const pointsAmountInCashToAllocate = pointsAmountToAllocate.dividedBy(POINTS_CONVERSION_MULTIPLIER);
const cashPayableNowAmountToAllocate = new Decimal(100);
const pointsConversionTierName = 'pointsConversionTierName';
const quote = 'quote';

beforeEach(() => {
  usePointsConverters.mockReturnValue({
    convertCashToPoints,
  });
  getQuote.mockReturnValue(quote);
  getPaymentMode.mockReturnValue(null);
  getPointsAmount.mockReturnValue(pointsAmount);
  getTravelPassAmount.mockReturnValue(travelPassAmount);
  getPointsAmountInCash.mockReturnValue(pointsAmountInCash);
  getPayableNowCashAmount.mockReturnValue(payableNowCashAmount);
  getTotalPayableAfterCredits.mockReturnValue(totalPayableAfterCredits);
  getPointsConversionTierName.mockReturnValue(pointsConversionTierName);
  usePointsAmountToAllocate.mockReturnValue({ pointsAmountToAllocate, pointsAmountInCashToAllocate });
  useCashPayableNowToAllocate.mockReturnValue(cashPayableNowAmountToAllocate);
  getMinimumDepositAmount.mockReturnValue(minimumDepositAmount);
});

it('throws an error if quote is not present', () => {
  getQuote.mockReturnValue(null);
  const { result } = renderHook(() => usePaymentAllocation(), { store: true });
  expect(result.error.message).toEqual(
    'usePaymentAllocation must be evaluated in the presence of a quote. Please move this hook into a component that is initialized once the quote exists.',
  );
});

it('allocates the calculated amounts', () => {
  const { store } = renderHook(() => usePaymentAllocation(), { store: true });

  expect(store.dispatch).toHaveBeenCalledWith(
    updatePayments({
      points: {
        amount: pointsAmountToAllocate,
        amountInCash: pointsAmountInCashToAllocate,
        tierVersionCode: pointsConversionTierName,
      },
      cash: {
        payableNow: {
          amount: cashPayableNowAmountToAllocate,
        },
      },
    }),
  );
});

describe('on changing of the payment mode', () => {
  it('updates the allocations', () => {
    const { store } = renderHook(() => usePaymentAllocation(), { store: true });
    expect(store.dispatch).toHaveBeenCalled();
    store.dispatch.mockClear();

    getPaymentMode.mockReturnValue(PAYMENT_MODES.DEPOSIT);
    store.triggerUpdate();

    expect(store.dispatch).toHaveBeenCalledWith(
      updatePayments({
        points: {
          amount: pointsAmountToAllocate,
          amountInCash: pointsAmountInCashToAllocate,
          tierVersionCode: pointsConversionTierName,
        },
        cash: {
          payableNow: {
            amount: cashPayableNowAmountToAllocate,
          },
        },
      }),
    );
  });
});

describe('on changing of the authentication state', () => {
  let updatePaymentsAction;

  beforeEach(() => {
    useIsAuthenticated.mockReturnValue(false);
    updatePaymentsAction = updatePayments({
      points: {
        amount: pointsAmountToAllocate,
        amountInCash: pointsAmountInCashToAllocate,
        tierVersionCode: pointsConversionTierName,
      },
      cash: {
        payableNow: {
          amount: cashPayableNowAmountToAllocate,
        },
      },
    });
  });

  describe('when switching from logged in to logged out', () => {
    it('updates the payment allocations', () => {
      getPaymentMode.mockReturnValue(PAYMENT_MODES.DEPOSIT);
      useIsAuthenticated.mockReturnValue(true);
      const { store } = renderHook(() => usePaymentAllocation(), { store: true });

      expect(store.dispatch).toHaveBeenCalled();

      useIsAuthenticated.mockReturnValue(false);
      store.triggerUpdate();

      expect(store.dispatch).toHaveBeenCalledWith(updatePaymentsAction);
    });
  });

  describe('when switching from logged out to logged in', () => {
    it('does not update the payment allocations', () => {
      getPaymentMode.mockReturnValue(PAYMENT_MODES.DEPOSIT);
      useIsAuthenticated.mockReturnValue(false);
      const { store } = renderHook(() => usePaymentAllocation(), { store: true });

      expect(store.dispatch).toHaveBeenCalled();
      store.dispatch.mockClear();

      useIsAuthenticated.mockReturnValue(true);
      store.triggerUpdate();

      expect(store.dispatch).not.toHaveBeenCalledWith(updatePaymentsAction);
    });
  });
});

describe('on changing of totalPayableAfterCredits', () => {
  describe('when in DEPOSIT PAYMENT mode', () => {
    beforeEach(() => {
      getPaymentMode.mockReturnValue(PAYMENT_MODES.DEPOSIT);
    });

    it('updates the allocations, reducing the payableNow amount', () => {
      const { store } = renderHook(() => usePaymentAllocation(), { store: true });
      store.dispatch.mockClear();

      const newTotalPayableAfterCredits = new Decimal(180);
      getTotalPayableAfterCredits.mockReturnValue(newTotalPayableAfterCredits);
      store.triggerUpdate();

      expect(store.dispatch).toHaveBeenCalledWith(
        updatePayments({
          points: {
            amount: pointsAmount,
            amountInCash: pointsAmountInCash,
            tierVersionCode: pointsConversionTierName,
          },
          cash: {
            payableNow: {
              amount: payableNowCashAmount,
            },
          },
          travelPass: {
            amount: travelPassAmount,
          },
        }),
      );
    });

    describe('when the voucher reduces the total by more than the current payable later amount', () => {
      it('reduces the payable now cash amount and then the points amount', () => {
        const { store } = renderHook(() => usePaymentAllocation(), { store: true });
        store.dispatch.mockClear();

        const newTotalPayableAfterCredits = new Decimal(100);
        getTotalPayableAfterCredits.mockReturnValue(newTotalPayableAfterCredits);
        store.triggerUpdate();

        const adjustedPayableNowAmount = new Decimal(90);

        //simulate the change to the payable now amount to correctly trigger the update to payableLater
        getPayableNowCashAmount.mockReturnValue(adjustedPayableNowAmount);
        store.triggerUpdate();

        expect(store.dispatch).toHaveBeenCalledWith(
          updatePayments({
            points: {
              amount: pointsAmount,
              amountInCash: pointsAmountInCash,
              tierVersionCode: pointsConversionTierName,
            },
            cash: {
              payableNow: {
                amount: adjustedPayableNowAmount,
              },
            },
            travelPass: {
              amount: travelPassAmount,
            },
          }),
        );
      });
    });

    describe('when the voucher reduces the total by more than the current payable later amount and the payableNowCash amount', () => {
      it('reduces the payable now cash amount and then the points amount', () => {
        const { store } = renderHook(() => usePaymentAllocation(), { store: true });
        store.dispatch.mockClear();

        const newTotalPayableAfterCredits = new Decimal(7);
        getTotalPayableAfterCredits.mockReturnValue(newTotalPayableAfterCredits);
        store.triggerUpdate();

        const adjustedPayableNowAmount = new Decimal(0);
        const adjustedPointsInCashAmount = new Decimal(7);
        const adjustedPointsAmount = adjustedPointsInCashAmount.times(POINTS_CONVERSION_MULTIPLIER);

        //simulate the change to the payable now amount to correctly trigger the update to payableLater
        getPayableNowCashAmount.mockReturnValue(adjustedPayableNowAmount);
        getPointsAmountInCash.mockReturnValue(adjustedPointsInCashAmount);
        getPointsAmount.mockReturnValue(adjustedPointsAmount);

        store.triggerUpdate();

        expect(store.dispatch).toHaveBeenCalledWith(
          updatePayments({
            points: {
              amount: adjustedPointsAmount,
              amountInCash: adjustedPointsInCashAmount,
              tierVersionCode: pointsConversionTierName,
            },
            cash: {
              payableNow: {
                amount: adjustedPayableNowAmount,
              },
            },
            travelPass: {
              amount: travelPassAmount,
            },
          }),
        );
      });
    });
  });

  describe('when in FULL PAYMENT mode', () => {
    beforeEach(() => {
      getPaymentMode.mockReturnValue(PAYMENT_MODES.FULL);
    });

    it('updates the allocations, reducing the cash amount while preserving the points amount', () => {
      const { store } = renderHook(() => usePaymentAllocation(), { store: true });
      store.dispatch.mockClear();
      const voucherAmount = new Decimal(20);

      const newTotalPayableAfterCredits = totalPayableAfterCredits.minus(voucherAmount);
      getTotalPayableAfterCredits.mockReturnValue(newTotalPayableAfterCredits);
      store.triggerUpdate();

      expect(store.dispatch).toHaveBeenCalledWith(
        updatePayments({
          points: {
            amount: pointsAmount,
            amountInCash: pointsAmountInCash,
            tierVersionCode: pointsConversionTierName,
          },
          cash: {
            payableNow: {
              amount: totalPayableAfterCredits.minus(voucherAmount).minus(pointsAmountInCash),
            },
          },
          travelPass: {
            amount: travelPassAmount,
          },
        }),
      );
    });

    describe('with totalPayableAfterCredits that is greater than the payableNowCash allocation', () => {
      it('updates the allocations, zeroing the cash amount and reducing the points amount', () => {
        const { store } = renderHook(() => usePaymentAllocation(), { store: true });
        store.dispatch.mockClear();

        const newTotalPayableAfterCredits = new Decimal(5);
        getTotalPayableAfterCredits.mockReturnValue(newTotalPayableAfterCredits);
        store.triggerUpdate();

        expect(store.dispatch).toHaveBeenCalledWith(
          updatePayments({
            points: {
              amount: newTotalPayableAfterCredits.times(POINTS_CONVERSION_MULTIPLIER),
              amountInCash: newTotalPayableAfterCredits,
              tierVersionCode: pointsConversionTierName,
            },
            cash: {
              payableNow: {
                amount: new Decimal(0),
              },
            },
            travelPass: {
              amount: travelPassAmount,
            },
          }),
        );
      });
    });

    describe('with a voucher that covers the full booking amount', () => {
      it('updates the allocations, zeroing the cash amount and reducing the points amount', () => {
        const { store } = renderHook(() => usePaymentAllocation(), { store: true });
        store.dispatch.mockClear();

        const newTotalPayableAfterCredits = new Decimal(0);
        getTotalPayableAfterCredits.mockReturnValue(newTotalPayableAfterCredits);
        store.triggerUpdate();

        expect(store.dispatch).toHaveBeenCalledWith(
          updatePayments({
            points: {
              amount: new Decimal(0),
              amountInCash: new Decimal(0),
              tierVersionCode: pointsConversionTierName,
            },
            cash: {
              payableNow: {
                amount: new Decimal(0),
              },
            },
            travelPass: {
              amount: travelPassAmount,
            },
          }),
        );
      });
    });
  });

  describe('when the totalPayableAfterCredits is negative due to a voucher being added alongside a travelPass', () => {
    it('reduces the travelPass by the negative amount and zeros other payment methods', () => {
      const { store } = renderHook(() => usePaymentAllocation(), { store: true });
      store.dispatch.mockClear();

      const newTotalPayableAfterCredits = new Decimal(-10);
      getTotalPayableAfterCredits.mockReturnValue(newTotalPayableAfterCredits);
      store.triggerUpdate();

      expect(store.dispatch).toHaveBeenCalledWith(
        updatePayments({
          points: {
            amount: new Decimal(0),
            amountInCash: new Decimal(0),
            tierVersionCode: pointsConversionTierName,
          },
          cash: {
            payableNow: {
              amount: new Decimal(0),
            },
          },
          travelPass: {
            amount: travelPassAmount.plus(newTotalPayableAfterCredits), //adding a negative amount
          },
        }),
      );
    });
  });
});
