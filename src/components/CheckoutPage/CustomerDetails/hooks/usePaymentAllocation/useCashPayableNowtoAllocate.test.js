// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import { renderHook } from 'test-utils';
import { getMinimumDepositAmount, getPaymentMode, getTotalPayableAfterCredits } from 'store/checkout/checkoutSelectors';
import { getIsClassic } from 'store/quote/quoteSelectors';
import { PAYMENT_MODES } from 'lib/enums/payment';
import useCashPayableNowToAllocate from './useCashPayableNowToAllocate';

jest.mock('store/checkout/checkoutSelectors');
jest.mock('store/quote/quoteSelectors');

const totalPayableAfterCredits = new Decimal(200);
const pointsAmountInCashToAllocate = new Decimal(50);
const minimumDepositAmount = new Decimal(40);

beforeEach(() => {
  getIsClassic.mockReturnValue(false);
  getPaymentMode.mockReturnValue(null);
  getMinimumDepositAmount.mockReturnValue(minimumDepositAmount);
  getTotalPayableAfterCredits.mockReturnValue(totalPayableAfterCredits);
});

describe('without the payment mode set', () => {
  it('returns the balance after points amount', () => {
    const { result } = renderHook(() => useCashPayableNowToAllocate({ pointsAmountInCashToAllocate }), { store: true });
    expect(result.current).toEqual(totalPayableAfterCredits.minus(pointsAmountInCashToAllocate));
  });
});

describe('with full payment mode', () => {
  it('returns the balance after points amount', () => {
    getPaymentMode.mockReturnValue(PAYMENT_MODES.FULL);
    const { result } = renderHook(() => useCashPayableNowToAllocate({ pointsAmountInCashToAllocate }), { store: true });
    expect(result.current).toEqual(totalPayableAfterCredits.minus(pointsAmountInCashToAllocate));
  });
});

describe('with deposit payment mode', () => {
  it('returns the minimum deposit amount', () => {
    getPaymentMode.mockReturnValue(PAYMENT_MODES.DEPOSIT);
    const { result } = renderHook(() => useCashPayableNowToAllocate({ pointsAmountInCashToAllocate }), { store: true });
    expect(result.current).toEqual(minimumDepositAmount);
  });
});

describe('when isClassic', () => {
  beforeEach(() => {
    getIsClassic.mockReturnValue(true);
  });
  it('returns zero', () => {
    const { result } = renderHook(() => useCashPayableNowToAllocate({ pointsAmountInCashToAllocate }), { store: true });
    expect(result.current).toEqual(new Decimal(0));
  });
});
