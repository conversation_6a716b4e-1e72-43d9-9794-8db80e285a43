import React from 'react';
import { mountUtils } from 'test-utils';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import { getPayableLaterCashAmount } from 'store/checkout/checkoutSelectors';
import { getPayableLaterDueDate } from 'store/quote/quoteSelectors';
import PayLaterSummary from './PayLaterSummary';

jest.mock('store/checkout/checkoutSelectors');
jest.mock('store/quote/quoteSelectors');

const payableLaterCashAmount = new Decimal(200);
const payableLaterDueDate = new Date(2020, 9, 9);

const render = () => mountUtils(<PayLaterSummary />, { decorators: { store: true } });

beforeEach(() => {
  getPayableLaterCashAmount.mockReturnValue(payableLaterCashAmount);
  getPayableLaterDueDate.mockReturnValue(payableLaterDueDate);
});

it('renders the pay-later-amount', () => {
  const { findByTestId } = render();
  expect(findByTestId('pay-later-amount')).toHaveText('$200.00AUD');
});

it('renders the payable-later-due-date-message', () => {
  const { findByTestId } = render();
  expect(findByTestId('payable-later-due-date-message')).toHaveText(
    'Final payment will be automatically charged to your credit card on Fri 9 Oct, 2020',
  );
});
