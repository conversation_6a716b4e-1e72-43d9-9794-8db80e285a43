import React, { useEffect, useCallback, useRef, Fragment } from 'react';
import PropTypes from 'prop-types';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import { useSelector, useDispatch } from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import noop from 'lodash/noop';
import { Box, Button } from '@qga/roo-ui/components';
import { StepHeader, EDITING } from 'components/StepWizard';
import FullPointsPay from './FullPointsPay';
import DepositPointsPay from './DepositPointsPay';
import PaymentMode from './PaymentMode';
import PayLaterSummary from './PayLaterSummary';
import theme from 'lib/theme';
import { getPointsErrors, getTravelPassErrors, getVoucherErrors } from 'store/booking/bookingSelectors';
import { useDataLayer } from 'hooks/useDataLayer';
import { PAYMENT_MODES, PAYMENT_METHODS } from 'lib/enums/payment';
import { getPaymentMode, getTotalPayableAfterCredits } from 'store/checkout/checkoutSelectors';
import { getQueryPayWith } from 'store/router/routerSelectors';
import { useIsAuthenticated } from 'lib/oauth';
import { updateQuery } from 'store/search/searchActions';
import * as config from 'config';

const PaymentOptions = ({ step, ...rest }) => {
  const { updateFormData, hasState, edit, containerRef, stepNumber, completeStep } = step;
  const dispatch = useDispatch();
  const isAuthenticated = useIsAuthenticated();
  const paymentMode = useSelector(getPaymentMode);
  const payWith = useSelector(getQueryPayWith);
  const totalPayableAfterCredits = useSelector(getTotalPayableAfterCredits);
  const pointsErrors = useSelector(getPointsErrors);
  const voucherErrors = useSelector(getVoucherErrors);
  const travelPassErrors = useSelector(getTravelPassErrors);
  const { emitInteractionEvent } = useDataLayer();
  const hasErrors = !isEmpty(pointsErrors) || !isEmpty(voucherErrors) || !isEmpty(travelPassErrors);
  const hasErrorsRef = useRef(hasErrors);

  const isFullPaymentMode = paymentMode === PAYMENT_MODES.FULL;
  const isDepositPaymentMode = paymentMode === PAYMENT_MODES.DEPOSIT;

  useEffect(() => {
    // we only want to do this if the error state has changed. The edit function can be recreated by
    // the step wizard which will trigger this effect when we don't want it to run
    if (hasErrorsRef.current !== hasErrors && hasErrors) {
      edit();
    }
    hasErrorsRef.current = hasErrors;
  }, [edit, hasErrors, pointsErrors, travelPassErrors, voucherErrors]);

  const handleContinue = useCallback(() => {
    completeStep();
    emitInteractionEvent({ type: 'Continue Button', value: 'Payment Options Button Selected' });
  }, [emitInteractionEvent, completeStep]);

  const handleCashContinue = useCallback(() => {
    dispatch(
      updateFormData({
        payments: {
          cash: {
            payableNow: {
              amount: totalPayableAfterCredits,
            },
          },
          points: {
            amount: new Decimal(0),
            amountInCash: new Decimal(0),
          },
        },
        isPaymentMethodChanged: true,
      }),
    );
    dispatch(updateQuery({ payWith: PAYMENT_METHODS.CASH }));
    completeStep();
    emitInteractionEvent({ type: 'Continue Button', value: 'Pay With Cash Selected' });
  }, [emitInteractionEvent, completeStep, dispatch, totalPayableAfterCredits, updateFormData]);

  return (
    <Box {...rest} ref={containerRef}>
      <Box bg="white" borderRadius="default" boxShadow="hard">
        <StepHeader title="Payment options" edit={edit} hasState={hasState} stepNumber={stepNumber} />

        {hasState(EDITING) && (
          <Box p={[4, 8]}>
            <PaymentMode updateFormData={updateFormData} />

            {paymentMode && (
              <Fragment>
                {isFullPaymentMode && <FullPointsPay />}
                {isDepositPaymentMode && (
                  <Fragment>
                    <DepositPointsPay />
                    <PayLaterSummary />
                  </Fragment>
                )}

                <Box pt={[4, 8]} mt={[4, 8]} borderTop={`1px solid ${theme.colors.greys.alto}`}>
                  {config.POINTS_REDEMPTION_ENABLED && !isAuthenticated && payWith !== PAYMENT_METHODS.CASH ? (
                    <Button
                      onClick={handleCashContinue}
                      variant="primary"
                      width={['100%', 'auto']}
                      type="submit"
                      data-testid="continue-cash-button"
                    >
                      Continue
                    </Button>
                  ) : (
                    <Button onClick={handleContinue} variant="primary" width={['100%', 'auto']} type="submit" data-testid="continue-button">
                      Continue
                    </Button>
                  )}
                </Box>
              </Fragment>
            )}
          </Box>
        )}
      </Box>
    </Box>
  );
};

PaymentOptions.propTypes = {
  step: PropTypes.shape({
    updateFormData: PropTypes.func,
    hasState: PropTypes.func,
    edit: PropTypes.func,
    completeStep: PropTypes.func,
    containerRef: PropTypes.shape({ current: PropTypes.any }),
    stepNumber: PropTypes.number,
  }),
};

PaymentOptions.defaultProps = {
  step: {
    updateFormData: noop,
    hasState: noop,
    edit: noop,
  },
};

export default PaymentOptions;
