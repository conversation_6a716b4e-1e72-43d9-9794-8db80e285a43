import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { Button, Flex, OutlineButton } from '@qga/roo-ui/components';
import { mediaQuery } from 'lib/styledSystem';

export const PaymentMethodSummaryContainer = styled(Flex)`
  justify-content: space-between;
  align-items: center;
  border-color: ${themeGet('colors.greys.alto')};
  padding: ${themeGet('space.4')};
`;

export const PaymentMethodSummaryButton = styled(OutlineButton)`
  font-size: ${themeGet('fontSizes.sm')};
  padding: ${themeGet('space.1')};
  width: 96px;
  flex-shrink: 0;
  letter-spacing: 1px;

  ${mediaQuery.minWidth.sm} {
    width: 140px;
  }
`;

export const QFFLoginButton = styled(Button)`
  text-transform: none;
  font-size: ${themeGet('fontSizes.sm')};
  letter-spacing: 1px;
  padding: ${themeGet('space.1')};
  width: 96px;
  border-radius: ${themeGet('radii.default')};
  color: ${themeGet('colors.ui.qffLogin')};

  ${mediaQuery.minWidth.sm} {
    padding: ${themeGet('space.3')} ${themeGet('space.6')};
    width: initial;
  }
`;

export const EditPointsPayWrapper = styled(Flex)`
  align-items: center;
  flex-direction: column;
  flex-shrink: 0;

  ${mediaQuery.minWidth.sm} {
    flex-direction: row;
  }
`;
