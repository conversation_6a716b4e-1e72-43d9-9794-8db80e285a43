import { useCallback, useState, useEffect } from 'react';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import formatNumber from 'lib/formatters/formatNumber';

const formattedStringToNumber = (formatted) => (formatted ? Number(formatted.replace(/(,|\$)/g, '')) : 0); //convert formatted string dollars to number - Ie. $99.99 to 99.99
const maskCash = (cash) => cash.replace(/[^0-9.,$]/g, ''); // only allow decimal numeric values and $ sign
const formatCash = (cashAmount) => `$${formatNumber({ number: cashAmount, decimalPlaces: 2 })}`;

const useFormattedCashInput = ({ cashAmount, updateCashAmount, showZeroAmounts = true, adjustUpdatedCashAmount = (amount) => amount }) => {
  const [formattedCashAmount, setFormattedCashAmount] = useState(formatCash(cashAmount));

  const updateCashAmountFromBuffer = useCallback(() => {
    const newCashAmount = formattedCashAmount ? new Decimal(formattedStringToNumber(formattedCashAmount)) : new Decimal(0);
    const adjustedCashAmount = adjustUpdatedCashAmount(newCashAmount);
    updateCashAmount(adjustedCashAmount);
    setFormattedCashAmount(formatCash(adjustedCashAmount));
  }, [adjustUpdatedCashAmount, formattedCashAmount, updateCashAmount]);

  useEffect(() => {
    if (showZeroAmounts) {
      setFormattedCashAmount(formatCash(cashAmount));
    } else {
      setFormattedCashAmount(cashAmount.isZero() ? '' : formatCash(cashAmount));
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [showZeroAmounts, cashAmount.toNumber()]);

  const handleCashInputChange = (e) => setFormattedCashAmount(maskCash(e.target.value));
  const handleCashInputKeyUp = useCallback((e) => e.keyCode === 13 && updateCashAmountFromBuffer(), [updateCashAmountFromBuffer]);
  const handleCashInputBlur = useCallback(() => updateCashAmountFromBuffer(), [updateCashAmountFromBuffer]);

  return { handleCashInputChange, handleCashInputKeyUp, handleCashInputBlur, formattedCashAmount };
};

export default useFormattedCashInput;
