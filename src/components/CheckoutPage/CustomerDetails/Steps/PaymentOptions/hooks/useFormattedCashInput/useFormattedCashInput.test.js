// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import { act } from 'react-test-renderer';
import { renderHook } from 'test-utils';
import useFormattedCashInput from './useFormattedCashInput';

let updateCashAmount;
let adjustUpdatedCashAmount;
const cashAmount = new Decimal(100.11);

beforeEach(() => {
  updateCashAmount = jest.fn();
  adjustUpdatedCashAmount = jest.fn().mockImplementation((v) => v);
});

describe('formattedCashAmount', () => {
  it('returns the cashAmount formatted', () => {
    const { result } = renderHook(() => useFormattedCashInput({ cashAmount, updateCashAmount, adjustUpdatedCashAmount }));

    expect(result.current.formattedCashAmount).toEqual('$100.11');
  });

  describe('with showZeroAmounts false', () => {
    describe('with a non-zero amount', () => {
      it('returns the formatted cash amount', () => {
        const { result } = renderHook(() =>
          useFormattedCashInput({ cashAmount, updateCashAmount, adjustUpdatedCashAmount, showZeroAmounts: true }),
        );
        expect(result.current.formattedCashAmount).toEqual('$100.11');
      });
    });

    describe('with a zero amount', () => {
      it('returns the formatted cash amount as an empty string', () => {
        const cashAmount = new Decimal(0);
        const { result } = renderHook(() =>
          useFormattedCashInput({ cashAmount, updateCashAmount, adjustUpdatedCashAmount, showZeroAmounts: false }),
        );
        expect(result.current.formattedCashAmount).toEqual('');
      });
    });
  });
});

describe('updating field values', () => {
  describe('triggered via blur', () => {
    it('returns the new cash amount', () => {
      const { result } = renderHook(() => useFormattedCashInput({ cashAmount, updateCashAmount, adjustUpdatedCashAmount }));

      act(() => {
        result.current.handleCashInputChange({ target: { value: '10' } });
      });

      act(() => {
        result.current.handleCashInputBlur();
      });

      expect(result.current.formattedCashAmount).toEqual('$10.00');
    });
  });

  describe('triggered via the enter key', () => {
    it('returns the new cash amount', () => {
      const { result } = renderHook(() => useFormattedCashInput({ cashAmount, updateCashAmount, adjustUpdatedCashAmount }));

      act(() => {
        result.current.handleCashInputChange({ target: { value: '10' } });
      });

      act(() => {
        result.current.handleCashInputKeyUp({ keyCode: 13 });
      });

      expect(result.current.formattedCashAmount).toEqual('$10.00');
    });
  });

  describe('adjustUpdatedCashAmount', () => {
    it('returns the adjusted cash amount', () => {
      adjustUpdatedCashAmount.mockReturnValue(new Decimal(99));
      const cashAmount = new Decimal(100.11);
      const { result } = renderHook(() => useFormattedCashInput({ cashAmount, updateCashAmount, adjustUpdatedCashAmount }));

      act(() => {
        result.current.handleCashInputChange({ target: { value: '100' } });
      });

      act(() => {
        result.current.handleCashInputBlur();
      });

      expect(result.current.formattedCashAmount).toEqual('$99.00');
    });
  });

  describe('entering non-cash characters', () => {
    it('masks the input', () => {
      const { result } = renderHook(() => useFormattedCashInput({ cashAmount, updateCashAmount, adjustUpdatedCashAmount }));

      act(() => {
        result.current.handleCashInputChange({ target: { value: 'abc$10.12xyz' } });
      });

      act(() => {
        result.current.handleCashInputBlur();
      });

      expect(result.current.formattedCashAmount).toEqual('$10.12');
    });
  });

  describe('entering more than 2 decimal places', () => {
    it('masks the input', () => {
      const { result } = renderHook(() => useFormattedCashInput({ cashAmount, updateCashAmount, adjustUpdatedCashAmount }));

      act(() => {
        result.current.handleCashInputChange({ target: { value: '$10.123' } });
      });

      act(() => {
        result.current.handleCashInputBlur();
      });

      expect(result.current.formattedCashAmount).toEqual('$10.12');
    });
  });
});
