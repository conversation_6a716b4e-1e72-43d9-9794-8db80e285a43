// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import { MIN_POINTS_AMOUNT } from 'config';
import usePointsConverters from 'hooks/points/usePointsConverters';
import { getPointsBalance } from 'store/user/userSelectors';
import { getTotalPayableAfterCredits, getPayableLaterCashAmount, getPaymentMode, getPointsAmount } from 'store/checkout/checkoutSelectors';
import { renderHook } from 'test-utils';
import usePointsRedemptionAvailable from './usePointsRedemptionAvailable';
import { PAYMENT_MODES } from 'lib/enums/payment';

jest.mock('store/checkout/checkoutSelectors');
jest.mock('hooks/points/usePointsConverters');
jest.mock('store/user/userSelectors');

const POINTS_CONVERSION_MULTIPLIER = 1000; // stubbed out multiplier for points/cash conversions
const convertCashToPoints = ({ cash }) => new Decimal(cash).times(POINTS_CONVERSION_MULTIPLIER);

const initialPointsBalance = new Decimal(5000);

beforeEach(() => {
  usePointsConverters.mockReturnValue({
    convertCashToPoints,
  });

  getPointsBalance.mockReturnValue(initialPointsBalance);
  getTotalPayableAfterCredits.mockReturnValue(new Decimal(100));
  getPointsAmount.mockReturnValue(new Decimal(0));
});

describe('isPointsPayAvailable', () => {
  describe('when the points balance is less than MIN_POINTS_AMOUNT and the totalPayableAfterCredits is less than MIN_POINTS_AMOUNT', () => {
    it('returns false for isPointsPayAvailable', () => {
      getPointsBalance.mockReturnValue(MIN_POINTS_AMOUNT.minus(1));
      getTotalPayableAfterCredits.mockReturnValue(MIN_POINTS_AMOUNT.dividedBy(POINTS_CONVERSION_MULTIPLIER).minus(1));
      const { result } = renderHook(() => usePointsRedemptionAvailable(), { store: true });
      expect(result.current.isPointsPayAvailable).toEqual(false);
    });
  });

  describe('when the points balance is greater than MIN_POINTS_AMOUNT and the totalPayableAfterCredits is greater than MIN_POINTS_AMOUNT', () => {
    it('returns a true for isPointsPayAvailable', () => {
      getPointsBalance.mockReturnValue(MIN_POINTS_AMOUNT.plus(1));
      getTotalPayableAfterCredits.mockReturnValue(MIN_POINTS_AMOUNT.dividedBy(POINTS_CONVERSION_MULTIPLIER).plus(1));
      const { result } = renderHook(() => usePointsRedemptionAvailable(), { store: true });
      expect(result.current.isPointsPayAvailable).toEqual(true);
    });
  });

  describe('when in deposit payment mode', () => {
    beforeEach(() => {
      getPaymentMode.mockReturnValue(PAYMENT_MODES.DEPOSIT);
    });

    describe('when the points balance is less than MIN_POINTS_AMOUNT and the payableLaterCashAmount is less than MIN_POINTS_AMOUNT', () => {
      it('returns false for isPointsPayAvailable', () => {
        getPointsBalance.mockReturnValue(MIN_POINTS_AMOUNT.minus(1));
        getPayableLaterCashAmount.mockReturnValue(MIN_POINTS_AMOUNT.dividedBy(POINTS_CONVERSION_MULTIPLIER).minus(1));
        const { result } = renderHook(() => usePointsRedemptionAvailable(), { store: true });
        expect(result.current.isPointsPayAvailable).toEqual(false);
      });
    });

    describe('when the points balance is greater than MIN_POINTS_AMOUNT and the payableLaterCashAmount is greater than MIN_POINTS_AMOUNT', () => {
      it('returns a true for isPointsPayAvailable', () => {
        getPointsBalance.mockReturnValue(MIN_POINTS_AMOUNT.plus(1));
        getPayableLaterCashAmount.mockReturnValue(MIN_POINTS_AMOUNT.dividedBy(POINTS_CONVERSION_MULTIPLIER).plus(1));
        const { result } = renderHook(() => usePointsRedemptionAvailable(), { store: true });
        expect(result.current.isPointsPayAvailable).toEqual(true);
      });
    });

    describe('when the points balance is greater than MIN_POINTS_AMOUNT and the payableLaterCashAmount is less than MIN_POINTS_AMOUNT', () => {
      it('returns a true for isPointsPayAvailable', () => {
        getPointsBalance.mockReturnValue(MIN_POINTS_AMOUNT.plus(1));
        getPayableLaterCashAmount.mockReturnValue(MIN_POINTS_AMOUNT.dividedBy(POINTS_CONVERSION_MULTIPLIER).minus(1));
        const { result } = renderHook(() => usePointsRedemptionAvailable(), { store: true });
        expect(result.current.isPointsPayAvailable).toEqual(false);
      });
    });

    describe('when the points balance is greater than MIN_POINTS_AMOUNT and the payableLaterCashAmount is less than MIN_POINTS_AMOUNT and there is an allocated pointsAmount', () => {
      it('returns a true for isPointsPayAvailable', () => {
        getPointsAmount.mockReturnValue(new Decimal(1));
        getPointsBalance.mockReturnValue(MIN_POINTS_AMOUNT.plus(1));
        getPayableLaterCashAmount.mockReturnValue(MIN_POINTS_AMOUNT.dividedBy(POINTS_CONVERSION_MULTIPLIER).minus(1));
        const { result } = renderHook(() => usePointsRedemptionAvailable(), { store: true });
        expect(result.current.isPointsPayAvailable).toEqual(true);
      });
    });
  });
});

describe('isBalanceLessThanMinPoints', () => {
  describe('when pointsBalance is less than MIN_POINTS_AMOUNT', () => {
    it('returns true for isBalanceLessThanMinPoints', () => {
      getPointsBalance.mockReturnValue(MIN_POINTS_AMOUNT.minus(1));
      const { result } = renderHook(() => usePointsRedemptionAvailable(), { store: true });
      expect(result.current.isBalanceLessThanMinPoints).toEqual(true);
    });
  });

  describe('when pointsBalance is greater than MIN_POINTS_AMOUNT', () => {
    it('returns false for isBalanceLessThanMinPoints', () => {
      getPointsBalance.mockReturnValue(MIN_POINTS_AMOUNT.plus(1));
      const { result } = renderHook(() => usePointsRedemptionAvailable(), { store: true });
      expect(result.current.isBalanceLessThanMinPoints).toEqual(false);
    });
  });
});

describe('isPayableLessThanMinPoints', () => {
  describe('when totalPayableAfterCreditsInPoints is less than MIN_POINTS_AMOUNT', () => {
    it('returns true for isPayableLessThanMinPoints', () => {
      usePointsConverters.mockReturnValue({ convertCashToPoints: () => MIN_POINTS_AMOUNT.minus(1) });
      const { result } = renderHook(() => usePointsRedemptionAvailable(), { store: true });
      expect(result.current.isPayableLessThanMinPoints).toEqual(true);
    });
  });

  describe('when totalPayableAfterCreditsInPoints is greater than MIN_POINTS_AMOUNT', () => {
    it('returns false for isPayableLessThanMinPoints', () => {
      usePointsConverters.mockReturnValue({ convertCashToPoints: () => MIN_POINTS_AMOUNT.plus(1) });
      const { result } = renderHook(() => usePointsRedemptionAvailable(), { store: true });
      expect(result.current.isPayableLessThanMinPoints).toEqual(false);
    });
  });
});
