import React from 'react';
import PropTypes from 'prop-types';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { Text, Flex, NakedButton, Box, Icon } from '@qga/roo-ui/components';

const StyledButton = styled(NakedButton)`
  border-radius: ${themeGet('radii.exagerated')};
  border: ${themeGet('borders.1')};
  border-color: ${(props) => (props.isActive ? themeGet('colors.blue') : themeGet('colors.greys.alto'))};
  padding: ${themeGet('space.6')} ${themeGet('space.2')} ${themeGet('space.6')} ${themeGet('space.6')};
  width: 100%;
  text-align: left;
  transition:
    background-color 200ms linear,
    box-shadow 200ms linear;

  &:hover {
    box-shadow: ${themeGet('shadows.small')};
  }

  &:focus {
    box-shadow: none;
  }

  &:hover,
  &:focus {
    outline: none;
    border-color: ${themeGet('colors.blue')};
  }
`;

StyledButton.displayName = 'StyledButton';

const ModeButton = ({ children, onClick, label, isActive, ...props }) => {
  const iconType = isActive ? 'checkCircle' : 'circle';
  const iconColor = isActive ? 'blue' : 'greys.porcelain';
  const iconSize = isActive ? 34 : 36;
  return (
    <StyledButton onClick={onClick} {...props} isActive={isActive}>
      <Flex alignItems="center">
        <Flex minWidth="40px" minHeight="40px" alignItems="center" justifyContent="center">
          <Icon name={iconType} color={iconColor} size={iconSize} />
        </Flex>

        <Flex flexDirection="column" alignItems="flex-start">
          <Text ml={4} fontSize={['base', 'md']}>
            {label}
          </Text>
          <Box ml={4}>{children}</Box>
        </Flex>
      </Flex>
    </StyledButton>
  );
};

ModeButton.propTypes = {
  onClick: PropTypes.func.isRequired,
  isActive: PropTypes.bool.isRequired,
  label: PropTypes.string.isRequired,
  children: PropTypes.node,
};

ModeButton.defaultProps = {
  children: null,
};

export default ModeButton;
