import React, { Fragment, useCallback, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Heading, Text, Flex } from '@qga/roo-ui/components';
import { useSelector } from 'react-redux';
import { getPaymentMode } from 'store/checkout/checkoutSelectors';
import { getIsDepositPayAvailable } from 'store/quote/quoteSelectors';
import { PAYMENT_MODES } from 'lib/enums/payment';
import ModeButton from './ModeButton';
import { useDataLayer } from 'hooks/useDataLayer';
import { DEPOSIT_PAY_ENABLED, DEPOSIT_PAY_PERCENTAGE } from 'config';

const DEPOSIT_LABEL = 'Pay a deposit up front';
const FULL_PAYMENT_LABEL = 'Pay in full now';

const PaymentMode = ({ updateFormData }) => {
  const isDepositPayAvailable = useSelector(getIsDepositPayAvailable) && DEPOSIT_PAY_ENABLED;
  const paymentMode = useSelector(getPaymentMode);
  const { emitInteractionEvent } = useDataLayer();

  const setPaymentMode = useCallback(
    (paymentMode) => {
      updateFormData({ paymentMode });
    },
    [updateFormData],
  );

  useEffect(() => {
    if (!paymentMode) setPaymentMode(PAYMENT_MODES.FULL);
  }, [paymentMode, setPaymentMode]);

  const selectDepositPay = useCallback(() => {
    setPaymentMode(PAYMENT_MODES.DEPOSIT);

    emitInteractionEvent({
      type: 'Payment Options Toggle',
      value: 'Deposit Up Front Selected',
    });
  }, [emitInteractionEvent, setPaymentMode]);

  const selectFullPayment = useCallback(() => {
    setPaymentMode(PAYMENT_MODES.FULL);
    emitInteractionEvent({
      type: 'Payment Options Toggle',
      value: 'Pay In Full Selected',
    });
  }, [emitInteractionEvent, setPaymentMode]);

  return isDepositPayAvailable ? (
    <Fragment>
      <Heading.h3 fontSize="md" mb={4}>
        How do you want to pay for your booking?
      </Heading.h3>

      <Flex mb={8} flexDirection={['column', 'row']}>
        <ModeButton
          onClick={selectFullPayment}
          label={FULL_PAYMENT_LABEL}
          mr={4}
          mb={[4, 0]}
          isActive={paymentMode === PAYMENT_MODES.FULL}
          data-testid="full-payment-button"
        />
        <ModeButton
          onClick={selectDepositPay}
          label={DEPOSIT_LABEL}
          isActive={paymentMode === PAYMENT_MODES.DEPOSIT}
          data-testid="deposit-payment-button"
        >
          <Text display="block" color="greys.steel" fontSize="xs" data-testid="payable-later-message">
            Minimum {DEPOSIT_PAY_PERCENTAGE.toString()}% deposit required
          </Text>
        </ModeButton>
      </Flex>
    </Fragment>
  ) : null;
};

PaymentMode.propTypes = {
  updateFormData: PropTypes.func.isRequired,
};

export default PaymentMode;
