import React from 'react';
import { mountUtils } from 'test-utils';
import PaymentMode from './PaymentMode';
import { getPaymentMode } from 'store/checkout/checkoutSelectors';
import { getIsDepositPayAvailable } from 'store/quote/quoteSelectors';
import { PAYMENT_MODES } from 'lib/enums/payment';
import { useDataLayer } from 'hooks/useDataLayer';
import * as config from 'config';

jest.mock('store/quote/quoteSelectors');
jest.mock('store/checkout/checkoutSelectors');
jest.mock('hooks/useDataLayer');
jest.mock('config');

mountUtils.mockComponent('Cash');
mountUtils.mockComponent('Points');
mountUtils.mockComponent('BookingError');

let updateFormData;

const render = () => mountUtils(<PaymentMode updateFormData={updateFormData} />, { decorators: { store: true } });
let emitInteractionEvent;

beforeEach(() => {
  updateFormData = jest.fn();
  emitInteractionEvent = jest.fn();
  config.DEPOSIT_PAY_ENABLED = true;
  config.DEPOSIT_PAY_PERCENTAGE = 10;
  getIsDepositPayAvailable.mockReturnValue(true);
  getPaymentMode.mockReturnValue(null);

  useDataLayer.mockReturnValue({
    emitInteractionEvent,
  });
});

it('sets the payment mode to full payment', () => {
  render();

  expect(updateFormData).toHaveBeenCalledWith({
    paymentMode: PAYMENT_MODES.FULL,
  });
});

it('renders the deposit amount', () => {
  const { findByTestId } = render();
  expect(findByTestId('payable-later-message')).toHaveText(`Minimum ${config.DEPOSIT_PAY_PERCENTAGE}% deposit required`);
});

describe('when in full payment mode', () => {
  beforeEach(() => {
    getPaymentMode.mockReturnValue(PAYMENT_MODES.FULL);
  });

  it('sets the payment mode to full payment', () => {
    const { find } = render();

    expect(find('ModeButton[data-testid="full-payment-button"]')).toHaveProp({
      isActive: true,
    });

    expect(find('ModeButton[data-testid="deposit-payment-button"]')).toHaveProp({
      isActive: false,
    });
  });

  it('does not update the payment mode', () => {
    render();
    expect(updateFormData).not.toHaveBeenCalled();
  });
});

describe('when in deposit payment mode', () => {
  beforeEach(() => {
    getPaymentMode.mockReturnValue(PAYMENT_MODES.DEPOSIT);
  });

  it('sets the payment mode to full payment', () => {
    const { find } = render();

    expect(find('ModeButton[data-testid="full-payment-button"]')).toHaveProp({
      isActive: false,
    });

    expect(find('ModeButton[data-testid="deposit-payment-button"]')).toHaveProp({
      isActive: true,
    });
  });

  it('does not update the payment mode', () => {
    render();
    expect(updateFormData).not.toHaveBeenCalled();
  });
});

describe('when DEPOSIT_PAY_ENABLED = false', () => {
  beforeEach(() => {
    config.DEPOSIT_PAY_ENABLED = false;
  });

  it('does not render the payment mode buttons', () => {
    const { find } = render();

    expect(find('ModeButton[data-testid="full-payment-button"]')).not.toExist();

    expect(find('ModeButton[data-testid="deposit-payment-button"]')).not.toExist();
  });

  it('sets the payment mode to full', () => {
    render();

    expect(updateFormData).toHaveBeenCalledWith({
      paymentMode: PAYMENT_MODES.FULL,
    });
  });
});

describe('when deposit pay is not available', () => {
  beforeEach(() => {
    getIsDepositPayAvailable.mockReturnValue(false);
  });

  it('does not render the payment mode buttons', () => {
    const { find } = render();

    expect(find('ModeButton[data-testid="full-payment-button"]')).not.toExist();

    expect(find('ModeButton[data-testid="deposit-payment-button"]')).not.toExist();
  });

  it('sets the payment mode to full', () => {
    render();

    expect(updateFormData).toHaveBeenCalledWith({
      paymentMode: PAYMENT_MODES.FULL,
    });
  });
});

describe('changing payment mode', () => {
  it('calls updateFormData with the new payment mode', () => {
    const { findByTestId } = render();
    updateFormData.mockClear();

    findByTestId('deposit-payment-button').simulate('click');

    expect(updateFormData).toHaveBeenCalledWith({
      paymentMode: PAYMENT_MODES.DEPOSIT,
    });

    findByTestId('full-payment-button').simulate('click');

    expect(updateFormData).toHaveBeenCalledWith({
      paymentMode: PAYMENT_MODES.FULL,
    });
  });

  it('emits user interaction event', () => {
    const { findByTestId } = render();

    findByTestId('deposit-payment-button').simulate('click');

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Payment Options Toggle',
      value: 'Deposit Up Front Selected',
    });

    findByTestId('full-payment-button').simulate('click');

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Payment Options Toggle',
      value: 'Pay In Full Selected',
    });
  });
});
