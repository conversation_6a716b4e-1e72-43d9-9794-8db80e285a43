import React from 'react';
import { act } from 'react-dom/test-utils';
import { mountUtils } from 'test-utils';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import FullPointsPay from './FullPointsPay';
import { getPointsErrors } from 'store/booking/bookingSelectors';
import {
  getPayableNowCashAmount,
  getTotalPayableAfterCredits,
  getPointsAmount,
  getPointsAmountInCash,
  getFormData,
} from 'store/checkout/checkoutSelectors';
import { updatePayments, updateFormData as updatePersonalInfoFormData } from 'store/checkout/checkoutActions';
import { getPointsBalance, getFirstName } from 'store/user/userSelectors';
import { useLoginUrl } from 'lib/qffAuth';
import { useIsAuthenticated, useLogout } from 'lib/oauth';
import { useDataLayer } from 'hooks/useDataLayer';
import usePointsConverters from 'hooks/points/usePointsConverters';
import * as config from 'config';
import usePointsRedemptionAvailable from 'components/CheckoutPage/CustomerDetails/Steps/PaymentOptions/hooks/usePointsRedemptionAvailable';
import { getPayWithToggleMessage } from 'store/campaign/campaignSelectors';
import { getQueryPayWith } from 'store/router/routerSelectors';
import { PAYMENT_METHODS } from 'lib/enums/payment';
import {
  getIsLoggedInFromStep,
  registerFormDataEntered,
  getFormDataEntered,
  setIsLoggedInFromStep,
} from 'components/CheckoutPage/CustomerDetails/sessionStorage';

jest.mock('config');
jest.mock('store/user/userSelectors');
jest.mock('lib/oauth');
jest.mock('store/checkout/checkoutSelectors');
jest.mock('store/booking/bookingSelectors');
jest.mock('lib/qffAuth');
jest.mock('store/pointsConversion/pointsConversionSelectors');
jest.mock('hooks/useDataLayer');
jest.mock('hooks/points/usePointsConverters');
jest.mock('components/CheckoutPage/CustomerDetails/Steps/PaymentOptions/hooks/usePointsRedemptionAvailable');
jest.mock('store/ui/uiSelectors');
jest.mock('store/campaign/campaignSelectors');
jest.mock('store/router/routerSelectors');
jest.mock('components/CheckoutPage/CustomerDetails/sessionStorage');

mountUtils.mockComponent('HotelsVoucher');
mountUtils.mockComponent('PointsPaySliderForm');
mountUtils.mockComponent('PointsLearnMore');
mountUtils.mockComponent('BookingError');
mountUtils.mockComponent('MinimumPointsMessages');

let baseProps;
const isAuthenticated = true;
const pointsBalance = new Decimal(100000);
const totalPayableAfterCredits = new Decimal(100);
const pointsAmount = new Decimal(20000);
const pointsAmountInCash = new Decimal(20);
const payableNowCashAmount = new Decimal(50);
const firstName = 'Frank';
const pointsErrors = [{ code: 'code', message: 'message' }];
const isPointsPayAvailable = true;
const emitInteractionEvent = jest.fn();

const formDataEntered = {
  firstName: 'Name',
  lastname: 'Last Name',
};

const mockLogout = jest.fn();

const render = (props) => {
  return mountUtils(<FullPointsPay {...baseProps} {...props} />, { decorators: { store: true, theme: true } });
};

const POINTS_CONVERSION_MULTIPLIER = 1000;
const convertPointsToCash = ({ points }) => new Decimal(points).dividedBy(POINTS_CONVERSION_MULTIPLIER);
const convertCashToPoints = ({ cash }) => new Decimal(cash).times(POINTS_CONVERSION_MULTIPLIER);

beforeEach(() => {
  baseProps = {
    updateFormData: jest.fn(),
  };

  Object.assign(config, jest.requireActual('config'));
  config.QFF_ACCOUNT_MANAGEMENT = config.ACCOUNT_MANAGEMENT_TYPES.APP_WIDE;

  useIsAuthenticated.mockReturnValue(isAuthenticated);
  getPointsBalance.mockReturnValue(pointsBalance);
  getTotalPayableAfterCredits.mockReturnValue(totalPayableAfterCredits);
  getPointsAmount.mockReturnValue(pointsAmount);
  getPointsAmountInCash.mockReturnValue(pointsAmountInCash);
  getPayableNowCashAmount.mockReturnValue(payableNowCashAmount);
  getFirstName.mockReturnValue(firstName);
  getPointsErrors.mockReturnValue(pointsErrors);
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  usePointsConverters.mockReturnValue({ convertPointsToCash, convertCashToPoints });
  usePointsRedemptionAvailable.mockReturnValue({
    isPointsPayAvailable,
  });
  useLoginUrl.mockReturnValue({ loginUrl: '/hotels/auth/callback?state=/foo' });
  useLogout.mockReturnValue({ logout: mockLogout });
  getPayWithToggleMessage.mockReturnValue('30% better value when you use points');
  getIsLoggedInFromStep.mockReturnValue(false);
  getFormDataEntered.mockReturnValue(formDataEntered);
  getFormData.mockReturnValue(formDataEntered);
});

afterEach(() => jest.clearAllMocks());

it('renders the HotelsVoucher', () => {
  const { find } = render();
  expect(find('HotelsVoucher')).toExist();
});

describe('when not authenticated', () => {
  beforeEach(() => {
    useIsAuthenticated.mockReturnValue(false);
  });

  it('renders the BookingError', () => {
    const { find } = render();
    expect(find('BookingError')).toExist();
  });

  it('renders the pay with toggle message', () => {
    const { findByTestId } = render();
    expect(findByTestId('pay-with-toggle-message')).toHaveText('30% better value when you use points');
  });

  it('does NOT render the pay with toggle message', () => {
    getPayWithToggleMessage.mockReturnValue(undefined);
    const { findByTestId } = render();
    expect(findByTestId('pay-with-toggle-message')).not.toExist();
  });

  it('renders the PointsLearnMore component', () => {
    const { find } = render();
    expect(find('PointsLearnMore')).toExist();
  });

  describe('with QFF_ACCOUNT_MANAGEMENT = APP_WIDE', () => {
    beforeEach(() => {
      config.QFF_ACCOUNT_MANAGEMENT = config.ACCOUNT_MANAGEMENT_TYPES.APP_WIDE;
    });

    it('renders the Login button component', () => {
      const { findByTestId } = render();
      expect(findByTestId('login-button').text()).toContain('LOGIN');
    });
  });

  describe('with QFF_ACCOUNT_MANAGEMENT = CHECKOUT_ONLY', () => {
    beforeEach(() => {
      config.QFF_ACCOUNT_MANAGEMENT = config.ACCOUNT_MANAGEMENT_TYPES.CHECKOUT_ONLY;
    });

    it('renders the checkout-only Login button component', () => {
      const { findByTestId } = render();
      expect(findByTestId('login-button').text()).toContain('Login to Qantas Frequent Flyer');
    });

    it('renders the oauth login when OAUTH_ENABLED is true', () => {
      config.OAUTH_ENABLED = true;
      const { findByTestId } = render();
      expect(findByTestId('login-button')).toHaveProp({ href: '/hotels/auth/callback?state=/foo' });
    });
  });

  it('does not render the Points+Pay Button', () => {
    const { findByTestId } = render();
    expect(findByTestId('edit-points-pay-button')).not.toExist();
  });

  it('does not render the available points amount', () => {
    const { findByTestId } = render();
    expect(findByTestId('available-points')).not.toExist();
  });
});

describe('when authenticated', () => {
  beforeEach(() => {
    useIsAuthenticated.mockReturnValue(true);
  });

  it('renders the PointsLearnMore component', () => {
    const { find } = render();
    expect(find('PointsLearnMore')).toExist();
  });

  it('renders the pay with toggle message', () => {
    const { findByTestId } = render();
    expect(findByTestId('pay-with-toggle-message')).toHaveText('30% better value when you use points');
  });

  it('does NOT render the pay with toggle message', () => {
    getPayWithToggleMessage.mockReturnValue(undefined);
    const { findByTestId } = render();
    expect(findByTestId('pay-with-toggle-message')).not.toExist();
  });

  it('does not display the login button', () => {
    const { findByTestId } = render();
    expect(findByTestId('login-button')).not.toExist();
  });

  it('renders the BookingError', () => {
    const { find } = render();
    expect(find('BookingError')).toExist();
  });

  it('renders the MinimumPointsMessages', () => {
    const { find } = render();
    expect(find('MinimumPointsMessages')).toExist();
  });

  it('renders the Points+Pay Button', () => {
    const { findByTestId } = render();
    expect(findByTestId('edit-points-pay-button')).toExist();
  });

  it('does NOT renders the Log Out Button if QFF_ACCOUNT_MANAGEMENT is APP_WIDE', () => {
    const { findByTestId } = render();
    expect(findByTestId('log-out-button')).not.toExist();
  });

  it('does render the Log Out Button if QFF_ACCOUNT_MANAGEMENT is CHECKOUT_ONLY', () => {
    config.QFF_ACCOUNT_MANAGEMENT = config.ACCOUNT_MANAGEMENT_TYPES.CHECKOUT_ONLY;
    const { findByTestId } = render();
    expect(findByTestId('log-out-button')).toExist();
  });

  it('renders the current points and cash amounts', () => {
    const { findByTestId } = render();
    expect(findByTestId('points-amount')).toHaveText('20,000PTS');
    expect(findByTestId('cash-amount')).toHaveText('$50.00AUD');
  });

  it('renders the available points amount', () => {
    const { findByTestId } = render();
    expect(findByTestId('available-points')).toHaveText('100,000 points available');
  });

  describe('when points amount selected is zero', () => {
    beforeEach(() => {
      getPointsAmount.mockReturnValue(new Decimal(0));
    });

    it('does not render the points and cash amounts', () => {
      const { findByTestId } = render();
      expect(findByTestId('points-cash-amounts')).not.toExist();
    });

    it('renders an USE button', () => {
      const { findByTestId } = render();
      expect(findByTestId('edit-points-pay-button')).toHaveText('USE');
    });
  });

  describe('when points amount selected is greater than zero', () => {
    it('renders the pointsAmount', () => {
      const { find } = render();
      expect(find('Currency[data-testid="points-amount"]')).toHaveProp({
        amount: pointsAmount,
        currency: 'PTS',
      });
    });

    it('renders the payableNowCashAmount', () => {
      const { find } = render();
      expect(find('Currency[data-testid="cash-amount"]')).toHaveProp({
        amount: payableNowCashAmount,
        currency: 'AUD',
      });
    });

    it('renders an CHANGE button', () => {
      const { findByTestId } = render();
      expect(findByTestId('edit-points-pay-button')).toHaveText('CHANGE');
    });
  });

  describe('clicking on the Points+Pay button', () => {
    it('renders the points+pay slider in a modal', () => {
      const { find, findByTestId } = render();
      findByTestId('edit-points-pay-button').simulate('click');
      expect(find('Modal').find('PointsPaySliderForm')).toHaveProp({
        initialPointsAmount: pointsAmount,
        totalCashAmount: totalPayableAfterCredits,
      });
    });

    describe('when the points are zero', () => {
      it('sets the initial points amount in the slider to the MIN_POINTS_AMOUNT', () => {
        getPointsAmount.mockReturnValue(new Decimal(0));
        const { find, findByTestId } = render();

        findByTestId('edit-points-pay-button').simulate('click');
        expect(find('Modal').find('PointsPaySliderForm')).toHaveProp({
          initialPointsAmount: config.MIN_POINTS_AMOUNT,
        });
      });
    });
  });

  describe('updating points and cash from the modal', () => {
    it('updates points and cash', () => {
      const { find, findByTestId, decorators } = render();
      findByTestId('edit-points-pay-button').simulate('click');

      act(() => {
        find('PointsPaySliderForm')
          .props()
          .updatePointsAndCash({
            cash: {
              amount: new Decimal(500),
            },
            points: {
              amount: new Decimal(50000),
              amountInCash: new Decimal(50),
            },
          });
      });

      expect(decorators.store.dispatch).not.toHaveBeenCalled();

      findByTestId('apply-points-and-cash-button').simulate('click');
      expect(decorators.store.dispatch).toHaveBeenCalledWith(
        updatePayments({
          cash: {
            payableNow: {
              amount: new Decimal(500),
            },
            payableLater: {
              amount: new Decimal(0),
            },
          },
          points: {
            amount: new Decimal(50000),
            amountInCash: new Decimal(50),
          },
        }),
      );
    });
  });

  describe('clearing points and cash from the modal', () => {
    it('zeros points and sets cash to the total payable after voucher', () => {
      const { findByTestId, decorators } = render();
      findByTestId('edit-points-pay-button').simulate('click');
      findByTestId('do-not-use-points-button').simulate('click');

      expect(decorators.store.dispatch).toHaveBeenCalledWith(
        updatePayments({
          cash: {
            payableNow: {
              amount: totalPayableAfterCredits,
            },
            payableLater: {
              amount: new Decimal(0),
            },
          },
          points: {
            amount: new Decimal(0),
            amountInCash: new Decimal(0),
          },
        }),
      );
    });
  });

  describe('when isPointsPayAvailable is false', () => {
    const isPointsPayAvailable = false;

    beforeEach(() => {
      usePointsRedemptionAvailable.mockReturnValue({
        isPointsPayAvailable,
      });
    });

    describe('the edit points pay button', () => {
      it('isDisabled', () => {
        const { findByTestId } = render();
        expect(findByTestId('edit-points-pay-button')).toHaveProp({
          disabled: true,
        });
      });

      it('does not render the Points or cash amounts', () => {
        const { findByTestId } = render();
        expect(findByTestId('points-amount')).not.toExist();
        expect(findByTestId('cash-amount')).not.toExist();
      });
    });
  });
});

describe('is paying with cash and not logged in', () => {
  it('does not render continue with cash prompt', () => {
    getQueryPayWith.mockReturnValue(PAYMENT_METHODS.CASH);
    useIsAuthenticated.mockReturnValue(false);

    const { findByTestId } = render({});
    expect(findByTestId('continue-with-cash')).not.toExist();
  });
});

describe('is paying with points and not logged in', () => {
  describe('and POINTS_REDEMPTION_ENABLED is true', () => {
    beforeEach(() => {
      config.POINTS_REDEMPTION_ENABLED = true;
    });

    it('render continue with cash prompt', () => {
      getQueryPayWith.mockReturnValue(PAYMENT_METHODS.POINTS);
      useIsAuthenticated.mockReturnValue(false);

      const { findByTestId } = render({});
      expect(findByTestId('continue-with-cash')).toExist();
    });
  });
});

describe('is paying with points and is logged in', () => {
  it('does not render continue with cash prompt', () => {
    getQueryPayWith.mockReturnValue(PAYMENT_METHODS.POINTS);
    useIsAuthenticated.mockReturnValue(true);

    const { findByTestId } = render({});
    expect(findByTestId('continue-with-cash')).not.toExist();
  });
});

describe('for oauth', () => {
  beforeEach(() => {
    config.OAUTH_ENABLED = true;
    config.QFF_ACCOUNT_MANAGEMENT = config.ACCOUNT_MANAGEMENT_TYPES.CHECKOUT_ONLY;
    useIsAuthenticated.mockReturnValue(false);
  });

  describe('on rendering', () => {
    it('saves the formData entered in step 1 to the SessionStorage', () => {
      render();
      expect(registerFormDataEntered).toHaveBeenCalledWith(formDataEntered);
    });
  });

  describe('when the user clicks the QFFLoginButton', () => {
    it('sets isLoggedInFromStep to TRUE', () => {
      const { findByTestId } = render();
      findByTestId('login-button').simulate('click');

      expect(setIsLoggedInFromStep).toHaveBeenCalledWith(true);
    });

    it('dispatches the updatePersonalInfoFormData with the correct data', () => {
      const { decorators, findByTestId } = render();
      findByTestId('login-button').simulate('click');

      expect(decorators.store.dispatch).toHaveBeenCalledWith(updatePersonalInfoFormData(formDataEntered));
    });
  });
});
