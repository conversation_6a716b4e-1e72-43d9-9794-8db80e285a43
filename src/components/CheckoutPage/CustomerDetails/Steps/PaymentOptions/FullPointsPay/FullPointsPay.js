import React, { Fragment, useCallback, useEffect, useState, useMemo } from 'react';
import PropTypes from 'prop-types';
import { Flex, Box, OutlineButton, Heading, Button, Text, Link } from '@qga/roo-ui/components';
import { useSelector, useDispatch } from 'react-redux';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import PointsPaySliderForm from 'components/PointsPaySlider/PointsPaySliderForm';
import Currency from 'components/Currency';
import TextButton from 'components/TextButton';
import { getPointsErrors } from 'store/booking/bookingSelectors';
import {
  getPayableNowCashAmount,
  getTotalPayableAfterCredits,
  getPointsAmount,
  getPointsAmountInCash,
  getFormData,
} from 'store/checkout/checkoutSelectors';
import { getQueryPayWith } from 'store/router/routerSelectors';
import { getPointsBalance } from 'store/user/userSelectors';
import { updatePayments, updateFormData as updatePersonalInfoFormData } from 'store/checkout/checkoutActions';
import { useLoginUrl } from 'lib/qffAuth';
import { useDataLayer } from 'hooks/useDataLayer';
import usePointsRedemptionAvailable from 'components/CheckoutPage/CustomerDetails/Steps/PaymentOptions/hooks/usePointsRedemptionAvailable';
import PointsLearnMore from 'components/CheckoutPage/CustomerDetails/Steps/PaymentOptions/PointsLearnMore';
import Modal from 'components/Modal';
import BookingError from 'components/CheckoutPage/BookingError';
import { useModal } from 'lib/hooks';
import * as config from 'config';
import usePointsConverters from 'hooks/points/usePointsConverters';
import {
  PaymentMethodSummaryContainer,
  PaymentMethodSummaryButton,
  QFFLoginButton,
  EditPointsPayWrapper,
} from 'components/CheckoutPage/CustomerDetails/Steps/PaymentOptions/primitives';
import { MinimumPointsMessages } from 'components/CheckoutPage/CustomerDetails/Steps/PaymentOptions/PaymentMethodSummary';
import formatNumber from 'lib/formatters/formatNumber';
import HotelsVoucher from 'components/CheckoutPage/CustomerDetails/Steps/PaymentOptions/HotelsVoucher';
import { getPayWithToggleMessage } from 'store/campaign/campaignSelectors';
import { PAYMENT_METHODS } from 'lib/enums/payment';
import { useIsAuthenticated, useLogout } from 'lib/oauth';
import { registerFormDataEntered, getFormDataEntered, setIsLoggedInFromStep } from 'components/CheckoutPage/CustomerDetails/sessionStorage';

const ModalFooter = ({ applyPointsAndCashFromModal, clearPoints }) => {
  return (
    <Fragment>
      <Box borderTop={1} borderColor="greys.alto" mt={'auto'}>
        <Flex px={6} py={4}>
          <Button onClick={applyPointsAndCashFromModal} mr={4} variant="primary" data-testid="apply-points-and-cash-button">
            Apply
          </Button>
          <OutlineButton onClick={clearPoints} variant="primary" data-testid="do-not-use-points-button">
            Remove
          </OutlineButton>
        </Flex>
      </Box>
    </Fragment>
  );
};

ModalFooter.propTypes = {
  applyPointsAndCashFromModal: PropTypes.func.isRequired,
  clearPoints: PropTypes.func.isRequired,
};

const FullPointsPay = () => {
  const dispatch = useDispatch();
  const { openModal: openPointsPayModal, closeModal: closePointsPayModal, modalProps: pointsPayModalProps } = useModal();
  const isAuthenticated = useIsAuthenticated();
  const pointsBalance = useSelector(getPointsBalance);
  const totalPayableAfterCredits = useSelector(getTotalPayableAfterCredits);
  const pointsAmount = useSelector(getPointsAmount);
  const pointsAmountInCash = useSelector(getPointsAmountInCash);
  const payableNowCashAmount = useSelector(getPayableNowCashAmount);
  const pointsErrors = useSelector(getPointsErrors);
  const hasPointsAmountGreaterThanZero = pointsAmount > 0;
  const [bufferedPointsAndCash, setBufferedPointsAndCash] = useState();
  const { emitInteractionEvent } = useDataLayer();
  const { convertPointsToCash } = usePointsConverters();
  const { isPointsPayAvailable } = usePointsRedemptionAvailable();
  const { loginUrl } = useLoginUrl();
  const { logout } = useLogout();
  const payWithToggleMessage = useSelector(getPayWithToggleMessage);
  const payWith = useSelector(getQueryPayWith);

  const formData = useSelector(getFormData);

  useEffect(() => {
    registerFormDataEntered(formData);
  });

  const savePersonalData = () => {
    setIsLoggedInFromStep(true);
    const formDataEntered = getFormDataEntered();
    dispatch(updatePersonalInfoFormData(formDataEntered));
  };

  const defaultPointsAndCash = useMemo(() => {
    return {
      cash: {
        amount: payableNowCashAmount,
      },
      points: {
        amount: pointsAmount,
        amountInCash: pointsAmountInCash,
      },
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [payableNowCashAmount.toNumber(), pointsAmount.toNumber(), pointsAmountInCash.toNumber()]);

  useEffect(() => {
    setBufferedPointsAndCash(defaultPointsAndCash);
  }, [defaultPointsAndCash]);

  const applyPointsAndCashFromModal = useCallback(() => {
    dispatch(
      updatePayments({
        cash: {
          payableNow: {
            amount: bufferedPointsAndCash.cash.amount,
          },
          payableLater: {
            amount: new Decimal(0),
          },
        },
        points: {
          amount: bufferedPointsAndCash.points.amount,
          amountInCash: bufferedPointsAndCash.points.amountInCash,
        },
      }),
    );

    closePointsPayModal();

    emitInteractionEvent({
      type: 'Use Points and Pay Pop Up',
      value: 'Use Points and Pay Apply Button Selected',
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bufferedPointsAndCash, closePointsPayModal]);

  const editPointsPay = useCallback(() => {
    if (pointsAmount.isZero()) {
      const minPointsAmountInCash = convertPointsToCash({ points: config.MIN_POINTS_AMOUNT });

      setBufferedPointsAndCash({
        cash: {
          amount: totalPayableAfterCredits.minus(minPointsAmountInCash),
        },
        points: {
          amount: config.MIN_POINTS_AMOUNT,
          amountInCash: minPointsAmountInCash,
        },
      });
    }

    openPointsPayModal();

    emitInteractionEvent({
      type: 'Use Points and Pay Pop Up',
      value: 'Use Points and Pay Opened',
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [convertPointsToCash, emitInteractionEvent, openPointsPayModal, pointsAmount.toNumber(), totalPayableAfterCredits.toNumber()]);

  const closePointsPay = useCallback(() => {
    closePointsPayModal();
    setBufferedPointsAndCash(defaultPointsAndCash);

    emitInteractionEvent({
      type: 'Use Points and Pay Pop Up',
      value: 'Use Points and Pay Closed',
    });
  }, [closePointsPayModal, defaultPointsAndCash, emitInteractionEvent]);

  const clearPoints = useCallback(() => {
    dispatch(
      updatePayments({
        cash: {
          payableNow: {
            amount: totalPayableAfterCredits,
          },
          payableLater: {
            amount: new Decimal(0),
          },
        },
        points: {
          amount: new Decimal(0),
          amountInCash: new Decimal(0),
        },
      }),
    );

    closePointsPayModal();
    setBufferedPointsAndCash(defaultPointsAndCash);

    emitInteractionEvent({
      type: 'Use Points and Pay Pop Up',
      value: 'Use Points and Pay Clear Points Link Selected',
    });
  }, [closePointsPayModal, defaultPointsAndCash, dispatch, emitInteractionEvent, totalPayableAfterCredits]);

  return (
    <Fragment>
      <Box mb={10}>
        <Heading.h3 fontSize="md">Use credit or voucher.</Heading.h3>
        <HotelsVoucher mb={4} />
      </Box>
      <Heading.h3 fontSize="md" data-testid="redeem-message">
        Redeem Qantas Points on your hotel booking. <PointsLearnMore mb={4} />
      </Heading.h3>
      {payWithToggleMessage && (
        <Text fontSize="sm" color="brand.primary" display="block" mb={2} data-testid="pay-with-toggle-message">
          {payWithToggleMessage}
        </Text>
      )}
      <BookingError errors={pointsErrors} type="Qantas Points" />
      <Box mb={10}>
        <PaymentMethodSummaryContainer border={1} borderRadius={isAuthenticated ? 'exageratedRoundTopOnly' : 'exagerated'}>
          <Flex alignItems={['left', 'center']} justifyContent="space-between" flexDirection={['column', 'row']}>
            <Text fontWeight="bold" fontSize={['sm', 'base']} mr={2}>
              Qantas Points + Pay
            </Text>
            <Box mr={[0, 4]}>
              {totalPayableAfterCredits.greaterThan(0) && (
                <Fragment>
                  {isPointsPayAvailable && hasPointsAmountGreaterThanZero && (
                    <Fragment>
                      <Currency amount={pointsAmount} currency="PTS" fontSize={['xs', 'base']} data-testid="points-amount" />
                      <Text>
                        <sup>*</sup>
                      </Text>{' '}
                      <Text fontSize={['xs', 'base']}>+</Text>{' '}
                      <Currency amount={payableNowCashAmount} currency="AUD" fontSize={['xs', 'base']} data-testid="cash-amount" />
                    </Fragment>
                  )}
                  {isAuthenticated && <MinimumPointsMessages />}
                </Fragment>
              )}
            </Box>
          </Flex>
          {isAuthenticated && (
            <EditPointsPayWrapper>
              {config.QFF_ACCOUNT_MANAGEMENT === config.ACCOUNT_MANAGEMENT_TYPES.CHECKOUT_ONLY && (
                <TextButton data-testid="log-out-button" onClick={logout} mr={[0, 6]} mb={[2, 0]}>
                  Log out
                </TextButton>
              )}
              <PaymentMethodSummaryButton
                onClick={editPointsPay}
                disabled={!isPointsPayAvailable}
                variant="primary"
                data-testid="edit-points-pay-button"
              >
                {hasPointsAmountGreaterThanZero ? 'CHANGE' : 'USE'}
              </PaymentMethodSummaryButton>
            </EditPointsPayWrapper>
          )}
          {!isAuthenticated && (
            <Fragment>
              {config.QFF_ACCOUNT_MANAGEMENT === config.ACCOUNT_MANAGEMENT_TYPES.APP_WIDE && (
                <PaymentMethodSummaryButton as={Link} href={loginUrl} data-testid="login-button" variant="primary">
                  LOGIN
                </PaymentMethodSummaryButton>
              )}
              {config.QFF_ACCOUNT_MANAGEMENT === config.ACCOUNT_MANAGEMENT_TYPES.CHECKOUT_ONLY && (
                <QFFLoginButton
                  variant="primary"
                  as={Link}
                  href={loginUrl}
                  data-testid="login-button"
                  backgroundColor="ui.qffLoginBackground"
                  onClick={savePersonalData}
                >
                  <Text display={['none', 'block']}>Login to Qantas Frequent Flyer </Text>
                  <Text display={['block', 'none']}>Login</Text>
                </QFFLoginButton>
              )}
            </Fragment>
          )}
        </PaymentMethodSummaryContainer>

        {isAuthenticated && (
          <Box
            bg="greys.porcelain"
            px={4}
            py={2}
            border={1}
            borderColor="greys.alto"
            borderRadius="exageratedRoundBottomOnly"
            borderTop={0}
          >
            <Text fontSize="sm" data-testid="available-points">
              {formatNumber({ number: pointsBalance })} points available
            </Text>
          </Box>
        )}

        {pointsPayModalProps.isOpen && (
          <Modal
            {...pointsPayModalProps}
            onRequestClose={closePointsPay}
            title="Use Points Plus Pay"
            padding={0}
            footerComponent={() => <ModalFooter clearPoints={clearPoints} applyPointsAndCashFromModal={applyPointsAndCashFromModal} />}
          >
            <Box px={[6, 8]} py={[6, 4]} data-testid="modal-wrapper">
              <Text fontSize="sm" color="greys.steel" display="block" mb={1}>
                Book your hotel using a mix of Qantas Points and cash
              </Text>
              {payWithToggleMessage && (
                <Text fontSize="sm" color="brand.primary" display="block" mb={6}>
                  {payWithToggleMessage}
                </Text>
              )}
              <Box my={5}>
                <PointsPaySliderForm
                  initialPointsAmount={bufferedPointsAndCash?.points?.amount}
                  totalCashAmount={totalPayableAfterCredits}
                  updatePointsAndCash={setBufferedPointsAndCash}
                />
              </Box>
              <Text color="greys.steel" fontSize="xs" display="block">
                Minimum {formatNumber({ number: config.MIN_POINTS_AMOUNT })} pts is required
              </Text>
            </Box>
          </Modal>
        )}
      </Box>

      {config.POINTS_REDEMPTION_ENABLED && !isAuthenticated && payWith !== PAYMENT_METHODS.CASH && (
        <>
          <Heading.h3 fontSize="md" data-testid="continue-with-cash">
            Or continue as a guest and pay with cash
          </Heading.h3>
          <Box mb={10}>
            <PaymentMethodSummaryContainer border={1} borderRadius={'exagerated'}>
              <Flex alignItems={['left', 'center']} justifyContent="space-between" flexDirection={['column', 'row']}>
                <Text fontWeight="bold" fontSize={['sm', 'base']} mr={2}>
                  Total ${totalPayableAfterCredits.toNumber()} AUD
                </Text>
              </Flex>
            </PaymentMethodSummaryContainer>
          </Box>
        </>
      )}
    </Fragment>
  );
};

export default FullPointsPay;
