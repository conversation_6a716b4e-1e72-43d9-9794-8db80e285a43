import React from 'react';
import { mountUtils } from 'test-utils';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import PaymentOptions from './PaymentOptions';
import { COMPLETE, INITIAL, EDITING } from 'components/StepWizard';
import { getPaymentMode, getPayableLaterCashAmount } from 'store/checkout/checkoutSelectors';
import { getPointsErrors, getVoucherErrors, getTravelPassErrors } from 'store/booking/bookingSelectors';
import { getPayableLaterDueDate } from 'store/quote/quoteSelectors';
import { useDataLayer } from 'hooks/useDataLayer';
import { PAYMENT_METHODS, PAYMENT_MODES } from 'lib/enums/payment';
import { getIsAuthenticated } from 'store/user/userSelectors';
import { getQueryPayWith } from 'store/router/routerSelectors';
import * as config from 'config';

jest.mock('config');
jest.mock('store/checkout/checkoutSelectors');
jest.mock('store/user/userSelectors');
jest.mock('store/router/routerSelectors');
jest.mock('store/quote/quoteSelectors');
jest.mock('store/booking/bookingSelectors');
jest.mock('hooks/useDataLayer');

mountUtils.mockComponent('StepHeader');
mountUtils.mockComponent('PaymentMode');
mountUtils.mockComponent('FullPointsPay');
mountUtils.mockComponent('DepositPointsPay');
mountUtils.mockComponent('PayLaterSummary');

const mockHasState =
  (expectedState) =>
  (...states) =>
    states.includes(expectedState);

const payableLaterCashAmount = new Decimal(200);
const payableLaterDueDate = new Date(2020, 9, 9);

let step;
let updateFormData;

const render = () => {
  return mountUtils(<PaymentOptions step={step} />, { decorators: { store: true } });
};

const emitInteractionEvent = jest.fn();

beforeEach(() => {
  updateFormData = jest.fn();
  step = {
    updateFormData,
    edit: jest.fn(),
    hasState: jest.fn(),
    stepNumber: 1,
    completeStep: jest.fn(),
  };

  getPayableLaterCashAmount.mockReturnValue(payableLaterCashAmount);
  getPayableLaterDueDate.mockReturnValue(payableLaterDueDate);
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  getPointsErrors.mockReturnValue([]);
  getVoucherErrors.mockReturnValue([]);
  getTravelPassErrors.mockReturnValue([]);
});

it('renders the step header', () => {
  const { find } = render();
  expect(find('StepHeader')).toHaveProp({
    edit: step.edit,
    hasState: step.hasState,
    stepNumber: step.stepNumber,
    title: 'Payment options',
  });
});

[(INITIAL, COMPLETE)].forEach((state) => {
  describe(`with a state of ${state}`, () => {
    beforeEach(() => {
      step.hasState = mockHasState(INITIAL);
    });

    it('does not render the PaymentMode', () => {
      const { find } = render();
      expect(find('PaymentMode')).not.toExist();
    });

    it('does not render the FullPointsPay', () => {
      const { find } = render();
      expect(find('FullPointsPay')).not.toExist();
    });

    it('does not render the DepositPointsPay', () => {
      const { find } = render();
      expect(find('DepositPointsPay')).not.toExist();
    });

    it('does not render the HotelsVoucher', () => {
      const { find } = render();
      expect(find('HotelsVoucher')).not.toExist();
    });

    it('does not render the Vouchers', () => {
      const { find } = render();
      expect(find('Vouchers')).not.toExist();
    });

    it('does not render the Continue button', () => {
      const { findByTestId } = render();
      expect(findByTestId('continue-button')).not.toExist();
    });
  });
});

describe(`with a state of EDITING`, () => {
  beforeEach(() => {
    step.hasState = mockHasState(EDITING);
  });

  it('renders the PaymentMode', () => {
    const { find } = render();
    expect(find('PaymentMode')).toHaveProp({ updateFormData });
  });

  describe('when payment mode has not been set', () => {
    it('does not render the FullPointsPay', () => {
      const { find } = render();
      expect(find('FullPointsPay')).not.toExist();
    });

    it('does not render the DepositPointsPay', () => {
      const { find } = render();
      expect(find('DepositPointsPay')).not.toExist();
    });

    it('does not render the Continue button', () => {
      const { findByTestId } = render();
      expect(findByTestId('continue-button')).not.toExist();
    });
  });

  describe('with a payment mode of FULL', () => {
    beforeEach(() => {
      getPaymentMode.mockReturnValue(PAYMENT_MODES.FULL);
    });

    it('renders the FullPointsPay', () => {
      const { find } = render();
      expect(find('FullPointsPay')).toExist();
    });

    it('does not render the PayLaterSummary', () => {
      const { find } = render();
      expect(find('PayLaterSummary')).not.toExist();
    });

    it('completes the step when clicking the continue button', () => {
      const { findByTestId } = render();

      findByTestId('continue-button').simulate('click');

      expect(step.completeStep).toHaveBeenCalled();
    });

    describe('and POINTS_REDEMPTION_ENABLEDis true', () => {
      beforeEach(() => {
        config.POINTS_REDEMPTION_ENABLED = true;
      });

      describe('is logged out and paying with points', () => {
        beforeEach(() => {
          getQueryPayWith.mockReturnValue(PAYMENT_METHODS.POINTS);
          getIsAuthenticated.mockReturnValue(false);
        });

        it('continue with cash button renders', () => {
          const { findByTestId } = render();
          expect(findByTestId('continue-cash-button')).toExist();
        });
      });
    });

    describe('and POINTS_REDEMPTION_ENABLED is false', () => {
      beforeEach(() => {
        config.POINTS_REDEMPTION_ENABLED = false;
      });

      describe('is logged out and paying with points', () => {
        beforeEach(() => {
          getQueryPayWith.mockReturnValue(PAYMENT_METHODS.POINTS);
          getIsAuthenticated.mockReturnValue(false);
        });

        it('continue with cash button renders', () => {
          const { findByTestId } = render();
          expect(findByTestId('continue-cash-button')).not.toExist();
        });
      });
    });

    describe('is logged out and paying with cash', () => {
      beforeEach(() => {
        getQueryPayWith.mockReturnValue(PAYMENT_METHODS.CASH);
        getIsAuthenticated.mockReturnValue(false);
      });

      it('continue with cash button does not render', () => {
        const { findByTestId } = render();
        expect(findByTestId('continue-cash-button')).not.toExist();
      });

      it('login button does not render', () => {
        const { findByTestId } = render();
        expect(findByTestId('login-button')).not.toExist();
      });

      it('continue button renders', () => {
        const { findByTestId } = render();
        expect(findByTestId('continue-button')).toExist();
      });
    });
  });

  describe('with a payment mode of DEPOSIT', () => {
    beforeEach(() => {
      getPaymentMode.mockReturnValue(PAYMENT_MODES.DEPOSIT);
    });

    it('renders the DepositPointsPay', () => {
      const { find } = render();
      expect(find('DepositPointsPay')).toExist();
    });

    it('renders the PayLaterSummary', () => {
      const { find } = render();
      expect(find('PayLaterSummary')).toExist();
    });
  });

  describe('without any voucher or points errors', () => {
    it('edits the step', () => {
      render();
      expect(step.edit).not.toHaveBeenCalled();
    });
  });

  describe('with voucher errors', () => {
    it('edits the step', () => {
      const { decorators } = render();
      getVoucherErrors.mockReturnValue([{}]);
      decorators.store.triggerUpdate();

      expect(step.edit).toHaveBeenCalled();
    });
  });

  describe('with points errors', () => {
    it('edits the step', () => {
      const { decorators } = render();
      getPointsErrors.mockReturnValue([{}]);
      decorators.store.triggerUpdate();

      expect(step.edit).toHaveBeenCalled();
    });
  });

  describe('with travelPass errors', () => {
    it('edits the step', () => {
      const { decorators } = render();
      getTravelPassErrors.mockReturnValue([{}]);
      decorators.store.triggerUpdate();

      expect(step.edit).toHaveBeenCalled();
    });
  });
});
