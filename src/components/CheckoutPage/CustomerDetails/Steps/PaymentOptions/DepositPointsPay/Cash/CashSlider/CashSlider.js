import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import debounce from 'lodash/debounce';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import Slider from 'rc-slider';
import { useSelector } from 'react-redux';
import { Box, Input, Flex, Text, Icon } from '@qga/roo-ui/components';
import theme from 'lib/theme';
import { getPointsAmountInCash, getMinimumDepositAmount, getTravelPassAmount, getVoucherAmount } from 'store/checkout/checkoutSelectors';
import { getTotalPayableAtBooking } from 'store/quote/quoteSelectors';
import Currency from 'components/Currency';
import { useDataLayer } from 'hooks/useDataLayer';
import useFormattedCashInput from 'components/CheckoutPage/CustomerDetails/Steps/PaymentOptions/hooks/useFormattedCashInput';
import { useSliderKey } from 'lib/hooks/slider';

const UPDATE_DEBOUNCE_MILLIS = 300;

const emitInteractionEventDebounced = debounce(({ emitInteractionEvent }) => {
  emitInteractionEvent({ type: 'Use Cash for Deposit Pop Up', value: 'Slider Selected' });
}, UPDATE_DEBOUNCE_MILLIS);

const CashSlider = ({ cash, updateCash }) => {
  const cashAmount = cash?.amount;
  const pointsAmountInCash = useSelector(getPointsAmountInCash);
  const totalPayableAtBooking = useSelector(getTotalPayableAtBooking);
  const travelPassAmount = useSelector(getTravelPassAmount);
  const voucherAmount = useSelector(getVoucherAmount);
  const nonCashPaymentsAmount = pointsAmountInCash.plus(voucherAmount).plus(travelPassAmount);
  const maxCashAmount = totalPayableAtBooking.minus(nonCashPaymentsAmount);
  const minDepositAmount = useSelector(getMinimumDepositAmount);
  const minCashAmount = Decimal.max(0, minDepositAmount.minus(nonCashPaymentsAmount));
  const payableLaterCashAmount = totalPayableAtBooking.minus(nonCashPaymentsAmount).minus(cashAmount);
  const { emitInteractionEvent } = useDataLayer();
  const { sliderKey, regenerateSliderKey } = useSliderKey();

  const adjustUpdatedCashAmount = (cashAmount) => Decimal.min(Decimal.max(cashAmount, minCashAmount), maxCashAmount);

  const updateCashAmount = (amount) => {
    updateCash({ amount });
    emitInteractionEventDebounced({ emitInteractionEvent });
    regenerateSliderKey();
  };

  const { formattedCashAmount, handleCashInputChange, handleCashInputKeyUp, handleCashInputBlur } = useFormattedCashInput({
    cashAmount,
    updateCashAmount,
    adjustUpdatedCashAmount,
  });

  const onSliderChange = useCallback(
    (value) => {
      const amount = new Decimal(value);
      updateCash({ amount });
      emitInteractionEventDebounced({ emitInteractionEvent });
    },
    [emitInteractionEvent, updateCash],
  );

  return (
    <Box pt={[8, 4]}>
      <Slider
        key={sliderKey}
        min={minCashAmount.toNumber()}
        max={maxCashAmount.toNumber()}
        defaultValue={cashAmount.toNumber()}
        onChange={onSliderChange}
        step={10}
        railStyle={theme.slider.rail}
        trackStyle={theme.slider.track}
        handleStyle={theme.slider.handle}
      />
      <Flex justifyContent="space-between" mt={8}>
        <Box>
          <Box mb={1}>Pay now</Box>
          <Flex alignItems="center">
            <Box width="120px">
              <Input
                id="cash"
                name="cash"
                value={formattedCashAmount}
                onChange={handleCashInputChange}
                onKeyUp={handleCashInputKeyUp}
                onBlur={handleCashInputBlur}
                data-testid="cash-input"
                mb={0}
                px={2}
                py={1}
              />
            </Box>
            <Text ml={2}>AUD</Text>
          </Flex>
        </Box>
        <Box>
          <Box mb={1} textAlign="right">
            <Icon name="depositPay" mr={2} size={22} />
            Pay later
          </Box>
          <Box pt={1} textAlign="right">
            <Currency amount={payableLaterCashAmount} currency="AUD" data-testid="pay-later-amount" />
          </Box>
        </Box>
      </Flex>
    </Box>
  );
};

CashSlider.propTypes = {
  updateCash: PropTypes.func.isRequired,
  cash: PropTypes.shape({
    amount: PropTypes.instanceOf(Decimal).isRequired,
  }).isRequired,
};

export default CashSlider;
