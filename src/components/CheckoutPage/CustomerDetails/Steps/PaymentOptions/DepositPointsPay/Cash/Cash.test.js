import React from 'react';
import { act } from 'react-dom/test-utils';
import { mountUtils } from 'test-utils';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import Cash from './Cash';
import { getPayableNowCashAmount, getTotalPayableAfterCredits } from 'store/checkout/checkoutSelectors';
import { useDataLayer } from 'hooks/useDataLayer';
import { updatePayments } from 'store/checkout/checkoutActions';

jest.mock('store/checkout/checkoutSelectors');
jest.mock('hooks/useDataLayer');
jest.mock('store/ui/uiSelectors');

mountUtils.mockComponent('CashSlider');

const payableNowCashAmount = new Decimal(100);
const totalPayableAfterCredits = new Decimal(200);
const emitInteractionEvent = jest.fn();

const render = () => mountUtils(<Cash />, { decorators: { store: true, theme: true } });

beforeEach(() => {
  getPayableNowCashAmount.mockReturnValue(payableNowCashAmount);
  getTotalPayableAfterCredits.mockReturnValue(totalPayableAfterCredits);
  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

afterEach(() => jest.clearAllMocks());

it('renders the Cash Button enabled', () => {
  const { findByTestId } = render();
  expect(findByTestId('edit-cash-button')).toHaveProp({ disabled: false });
});

describe('when the cash amount is zero', () => {
  beforeEach(() => {
    getPayableNowCashAmount.mockReturnValue(new Decimal(0));
  });

  it('does not render the cash amount', () => {
    const { findByTestId } = render();
    expect(findByTestId('payable-now-cash-amount')).not.toExist();
  });

  it('renders an ADD button', () => {
    const { findByTestId } = render();
    expect(findByTestId('edit-cash-button')).toHaveText('ADD');
  });
});

describe('when cash amount is greater than zero', () => {
  it('renders the payable now cash amount', () => {
    const { find } = render();
    expect(find('Currency[data-testid="payable-now-cash-amount"]')).toHaveProp({
      amount: payableNowCashAmount,
      currency: 'AUD',
    });
  });

  it('renders a CHANGE button', () => {
    const { findByTestId } = render();
    expect(findByTestId('edit-cash-button')).toHaveText('CHANGE');
  });
});

describe('with a total payable after voucher of zero', () => {
  beforeEach(() => {
    getTotalPayableAfterCredits.mockReturnValue(new Decimal(0));
  });

  it('disables the button', () => {
    const { findByTestId } = render();
    expect(findByTestId('edit-cash-button')).toHaveProp({ disabled: true });
  });
});

describe('clicking on the Cash button', () => {
  it('renders the cash slider in a modal', () => {
    const { find, findByTestId } = render();
    findByTestId('edit-cash-button').simulate('click');
    expect(find('Modal').find('CashSlider')).toHaveProp({
      cash: {
        amount: payableNowCashAmount,
      },
    });
  });

  it('emits an interaction event', () => {
    const { findByTestId } = render();
    findByTestId('edit-cash-button').simulate('click');
    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Use Cash for Deposit Pop Up',
      value: 'Use Cash Opened',
    });
  });

  describe('clicking on the modal close button', () => {
    it('renders the cash slider in a modal', () => {
      const { find, findByTestId } = render();
      findByTestId('edit-cash-button').simulate('click');
      findByTestId('close-modal-button').simulate('click');
      expect(find('Modal')).not.toExist();
    });

    it('emits an interaction event', () => {
      const { findByTestId } = render();
      findByTestId('edit-cash-button').simulate('click');
      findByTestId('close-modal-button').simulate('click');
      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Use Cash for Deposit Pop Up',
        value: 'Use Cash Closed',
      });
    });

    describe('changing the cash amount with the slider and then closing the modal', () => {
      it('does not apply the new cash amount', () => {
        const { find, findByTestId, decorators } = render();
        findByTestId('edit-cash-button').simulate('click');

        act(() => {
          find('CashSlider')
            .props()
            .updateCash({
              amount: new Decimal(50),
            });
        });

        expect(decorators.store.dispatch).not.toHaveBeenCalled();

        findByTestId('close-modal-button').simulate('click');
        expect(decorators.store.dispatch).not.toHaveBeenCalled();
      });
    });
  });
});

describe('updating cash from the modal', () => {
  it('updates cash', () => {
    const { find, findByTestId, decorators } = render();

    findByTestId('edit-cash-button').simulate('click');

    act(() => {
      find('CashSlider')
        .props()
        .updateCash({
          amount: new Decimal(50),
        });
    });

    expect(decorators.store.dispatch).not.toHaveBeenCalled();

    findByTestId('apply-cash-button').simulate('click');
    expect(decorators.store.dispatch).toHaveBeenCalledWith(
      updatePayments({
        cash: {
          payableNow: {
            amount: new Decimal(50),
          },
        },
      }),
    );
  });

  it('emits interaction event', () => {
    const { find, findByTestId, decorators } = render();

    findByTestId('edit-cash-button').simulate('click');

    act(() => {
      find('CashSlider')
        .props()
        .updateCash({
          amount: new Decimal(50),
        });
    });

    expect(decorators.store.dispatch).not.toHaveBeenCalled();

    findByTestId('apply-cash-button').simulate('click');

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Use Cash for Deposit Pop Up',
      value: 'Use Cash Apply Button Selected',
    });
  });
});
