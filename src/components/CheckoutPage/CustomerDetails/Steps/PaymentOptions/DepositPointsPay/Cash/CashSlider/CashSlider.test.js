import React from 'react';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import Slider from 'rc-slider';
import { act } from 'react-dom/test-utils';
import { mountUtils } from 'test-utils';
import CashSlider from './CashSlider';
import { getPointsAmountInCash, getMinimumDepositAmount, getTravelPassAmount, getVoucherAmount } from 'store/checkout/checkoutSelectors';
import { getTotalPayableAtBooking } from 'store/quote/quoteSelectors';
import formatNumber from 'lib/formatters/formatNumber';
import { useDataLayer } from 'hooks/useDataLayer';

jest.mock('store/checkout/checkoutSelectors');
jest.mock('store/quote/quoteSelectors');
jest.mock('hooks/useDataLayer');
jest.mock('rc-slider', () => () => null);
jest.mock('lodash/debounce', () => (fn) => fn);

jest.useFakeTimers();

let baseProps;
let emitInteractionEvent;

const render = (props) => mountUtils(<CashSlider {...baseProps} {...props} />, { decorators: { store: true } });

const pointsAmountInCash = new Decimal(20);
const payableNowCashAmount = new Decimal(100);
const totalPayableAtBooking = new Decimal(200);
const minimumDepositAmount = new Decimal(50);
const travelPassAmount = new Decimal(15);
const voucherAmount = new Decimal(10);

const minCashAmount = minimumDepositAmount.minus(pointsAmountInCash).minus(travelPassAmount).minus(voucherAmount);
const maxCashAmount = totalPayableAtBooking.minus(pointsAmountInCash).minus(voucherAmount).minus(travelPassAmount);

beforeEach(() => {
  baseProps = {
    cash: {
      amount: payableNowCashAmount,
    },
    updateCash: jest.fn(),
  };

  getPointsAmountInCash.mockReturnValue(pointsAmountInCash);
  getTotalPayableAtBooking.mockReturnValue(totalPayableAtBooking);
  getMinimumDepositAmount.mockReturnValue(minimumDepositAmount);
  getTravelPassAmount.mockReturnValue(travelPassAmount);
  getVoucherAmount.mockReturnValue(voucherAmount);

  emitInteractionEvent = jest.fn();
  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

it('renders the Slider', () => {
  const { find } = render();
  expect(find(Slider)).toHaveProp({
    min: minCashAmount.toNumber(),
    max: maxCashAmount.toNumber(),
    defaultValue: payableNowCashAmount.toNumber(),
    step: 10,
  });
});

it('renders the formatted cash input', () => {
  const { findByTestId } = render();
  expect(findByTestId('cash-input')).toHaveProp({ value: '$100.00' });
});

it('renders the pay-later-amount', () => {
  const { find } = render();
  expect(find('Currency[data-testid="pay-later-amount"]')).toHaveProp({
    amount: totalPayableAtBooking.minus(payableNowCashAmount).minus(pointsAmountInCash).minus(voucherAmount).minus(travelPassAmount),
    currency: 'AUD',
  });
});

describe('when updating the cash value from the slider', () => {
  it('calls updateCash', () => {
    const { find } = render();
    act(() => {
      find(Slider).props().onChange(90);
    });
    expect(baseProps.updateCash).toHaveBeenCalledWith({ amount: new Decimal(90) });
  });

  it('emits user interaction', () => {
    const { find } = render();
    act(() => {
      find(Slider).props().onChange(90);
    });
    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Use Cash for Deposit Pop Up',
      value: 'Slider Selected',
    });
  });
});

describe('when entering a formatted cash amount', () => {
  describe('onBlur of the cash Input', () => {
    it('updates the cash', () => {
      const { findByTestId } = render();

      const cashInput = findByTestId('cash-input');
      const cashAmount = new Decimal(90);

      act(() => {
        cashInput.simulate('change', { target: { value: formatNumber({ number: cashAmount.toNumber(), decimalPlaces: 2 }) } });
      });
      act(() => {
        cashInput.simulate('blur');
      });

      expect(baseProps.updateCash).toHaveBeenCalledWith({
        amount: cashAmount,
      });
    });

    it('updates the key of the slider', () => {
      const { find, findByTestId, wrapper } = render();

      const cashInput = findByTestId('cash-input');
      const cashAmount = new Decimal(90);
      const initialSliderKey = find(Slider).key();

      act(() => {
        cashInput.simulate('change', { target: { value: formatNumber({ number: cashAmount.toNumber(), decimalPlaces: 2 }) } });
      });
      act(() => {
        cashInput.simulate('blur');
      });
      act(() => {
        jest.runAllTimers();
      });

      wrapper.update();
      expect(wrapper.find(Slider).key()).not.toEqual(initialSliderKey);
    });
  });

  describe('onKeyUp of the cash Input with enter key', () => {
    it('updates the cash', () => {
      const { findByTestId } = render();

      const cashInput = findByTestId('cash-input');
      const cashAmount = new Decimal(90);

      act(() => {
        cashInput.simulate('change', { target: { value: formatNumber({ number: cashAmount.toNumber(), decimalPlaces: 2 }) } });
      });
      act(() => {
        cashInput.simulate('keyUp', { keyCode: 13 });
      });

      expect(baseProps.updateCash).toHaveBeenCalledWith({
        amount: cashAmount,
      });
    });
  });

  describe('onKeyUp of the cash Input with other key', () => {
    it('does not update the cash', () => {
      const { findByTestId } = render();
      const cashInput = findByTestId('cash-input');

      act(() => {
        cashInput.simulate('keyUp', { keyCode: 1 });
      });

      expect(baseProps.updateCash).not.toHaveBeenCalled();
    });
  });

  describe('when the cash entered is less than the minimum amount', () => {
    it('updates the cash with the amount increased to the minimum cash amount', () => {
      const { findByTestId } = render();

      const cashInput = findByTestId('cash-input');

      act(() => {
        cashInput.simulate('change', { target: { value: formatNumber({ number: minCashAmount.minus(1).toNumber(), decimalPlaces: 2 }) } });
      });
      act(() => {
        cashInput.simulate('blur');
      });

      expect(baseProps.updateCash).toHaveBeenCalledWith({
        amount: minCashAmount,
      });
    });
  });

  describe('when the cash entered is greater than the maximum cash amount', () => {
    it('updates the cash with the amount decreased to the maximum cash amount', () => {
      const { findByTestId } = render();

      const cashInput = findByTestId('cash-input');

      act(() => {
        cashInput.simulate('change', {
          target: { value: formatNumber({ number: maxCashAmount.plus(1).toNumber(), decimalPlaces: 2 }) },
        });
      });
      act(() => {
        cashInput.simulate('blur');
      });

      expect(baseProps.updateCash).toHaveBeenCalledWith({
        amount: maxCashAmount,
      });
    });
  });
});
