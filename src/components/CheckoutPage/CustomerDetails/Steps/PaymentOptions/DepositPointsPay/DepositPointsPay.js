import React, { Fragment } from 'react';
import { useSelector } from 'react-redux';
import { format as formatDate } from 'date-fns';
import { Heading, Text, Box } from '@qga/roo-ui/components';
import Points from './Points';
import Cash from './Cash';
import BookingError from 'components/CheckoutPage/BookingError';
import { getPointsErrors } from 'store/booking/bookingSelectors';
import DepositPayLearnMore from 'components/CheckoutPage/CustomerDetails/Steps/PaymentOptions/DepositPayLearnMore';
import { getMinimumDepositAmount } from 'store/checkout/checkoutSelectors';
import { getPayableLaterDueDate } from 'store/quote/quoteSelectors';
import Currency from 'components/Currency';
import { DEPOSIT_PAY_PERCENTAGE, DISPLAY_DATE_FORMAT } from 'config';
import HotelsVoucher from 'components/CheckoutPage/CustomerDetails/Steps/PaymentOptions/HotelsVoucher';

const DepositPointsPay = () => {
  const pointsErrors = useSelector(getPointsErrors);
  const minDepositAmount = useSelector(getMinimumDepositAmount);
  const payableLaterDueDate = useSelector(getPayableLaterDueDate);

  return (
    <Fragment>
      <Heading.h3 fontSize="md">How would you like to pay for your deposit.</Heading.h3>
      <Text lineHeight="tight" color="greys.steel" fontSize="xs">
        <Text>A minimum </Text>
        <Text data-testid="min-deposit-percentage">{DEPOSIT_PAY_PERCENTAGE.toString()}% </Text>
        <Text>deposit of </Text>
        <Currency amount={minDepositAmount} currency="AUD" fontSize="sm" data-testid="min-deposit-amount" /> is required. Final payment
        charged on <Text data-testid="payable-later-due-date">{formatDate(payableLaterDueDate, DISPLAY_DATE_FORMAT)}</Text>{' '}
      </Text>
      <DepositPayLearnMore />
      <Box mt={4}>
        <HotelsVoucher mb={4} />
        <BookingError errors={pointsErrors} type="Qantas Points" />
        <Points mb={4} />
        <Cash mb={4} />
      </Box>
    </Fragment>
  );
};

export default DepositPointsPay;
