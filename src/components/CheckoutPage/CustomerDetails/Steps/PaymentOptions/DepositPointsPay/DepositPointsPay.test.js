import React from 'react';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import { mountUtils } from 'test-utils';
import DepositPointsPay from './DepositPointsPay';
import { getPointsErrors } from 'store/booking/bookingSelectors';
import { getMinimumDepositAmount } from 'store/checkout/checkoutSelectors';
import { getPayableLaterDueDate } from 'store/quote/quoteSelectors';
import { DEPOSIT_PAY_PERCENTAGE } from 'config';

jest.mock('store/booking/bookingSelectors');
jest.mock('store/checkout/checkoutSelectors');
jest.mock('store/quote/quoteSelectors');
jest.mock('store/ui/uiSelectors');

mountUtils.mockComponent('Cash');
mountUtils.mockComponent('Points');
mountUtils.mockComponent('BookingError');
mountUtils.mockComponent('TravelPass');
mountUtils.mockComponent('HotelsVoucher');

const pointsErrors = [{ code: 'code', message: 'message' }];
const minDepositAmount = new Decimal(100);
const payableLaterDueDate = new Date(2020, 10, 10);

const render = () => {
  return mountUtils(<DepositPointsPay />, { decorators: { store: true, theme: true } });
};

beforeEach(() => {
  getPointsErrors.mockReturnValue(pointsErrors);
  getMinimumDepositAmount.mockReturnValue(minDepositAmount);
  getPayableLaterDueDate.mockReturnValue(payableLaterDueDate);
});

it('renders the BookingError', () => {
  const { find } = render();
  expect(find('BookingError')).toExist();
});

it('renders the HotelsVoucher component', () => {
  const { find } = render();
  expect(find('HotelsVoucher')).toExist();
});

it('renders the Cash component', () => {
  const { find } = render();
  expect(find('Cash')).toExist();
});

it('renders the Points component', () => {
  const { find } = render();
  expect(find('Points')).toExist();
});

it('renders the minimum deposit percentage', () => {
  const { findByTestId } = render();
  expect(findByTestId('min-deposit-percentage').text()).toMatch(DEPOSIT_PAY_PERCENTAGE.toString());
});

it('renders the minimum deposit amount', () => {
  const { find } = render();
  expect(find('Currency[data-testid="min-deposit-amount"]')).toHaveProp({
    amount: minDepositAmount,
    currency: 'AUD',
  });
});

it('renders the payable later due date', () => {
  const { findByTestId } = render();
  expect(findByTestId('payable-later-due-date')).toHaveText('Tue 10 Nov, 2020');
});
