import React, { Fragment, useCallback, useEffect, useState, useMemo } from 'react';
import PropTypes from 'prop-types';
import { Flex, Box, Button, Heading, NakedButton, Text, Wrapper, Link } from '@qga/roo-ui/components';
import { useSelector, useDispatch } from 'react-redux';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import PointsSlider from './PointsSlider';
import Currency from 'components/Currency';
import { getPointsBalance } from 'store/user/userSelectors';
import { updatePayments } from 'store/checkout/checkoutActions';
import { useLoginUrl } from 'lib/qffAuth';
import usePointsRedemptionAvailable from 'components/CheckoutPage/CustomerDetails/Steps/PaymentOptions/hooks/usePointsRedemptionAvailable';
import { useDataLayer } from 'hooks/useDataLayer';
import Modal from 'components/Modal';
import { useModal } from 'lib/hooks';
import {
  getPointsAmount,
  getPointsAmountInCash,
  getMinimumDepositAmount,
  getPayableNowCashAmount,
  getTotalPayableAfterCredits,
} from 'store/checkout/checkoutSelectors';
import { MIN_POINTS_AMOUNT } from 'config';
import usePointsConverters from 'hooks/points/usePointsConverters';
import {
  PaymentMethodSummaryContainer,
  PaymentMethodSummaryButton,
} from 'components/CheckoutPage/CustomerDetails/Steps/PaymentOptions/primitives';
import { MinimumPointsMessages } from 'components/CheckoutPage/CustomerDetails/Steps/PaymentOptions/PaymentMethodSummary';
import formatNumber from 'lib/formatters/formatNumber';
import PointsLearnMore from '../../PointsLearnMore';
import { getPayWithToggleMessage } from 'store/campaign/campaignSelectors';
import { useIsAuthenticated } from 'lib/oauth';

const ModalFooter = ({ applyPointsFromModal, clearPoints }) => {
  return (
    <Fragment>
      <Box borderTop={1} borderColor="greys.porcelain" mt={'auto'}>
        <Flex px={6} py={4} justifyContent="space-between">
          <NakedButton onClick={clearPoints} color="brand.primary" data-testid="clear-points-button">
            <Text fontWeight="bold" letterSpacing="1px">
              CLEAR
            </Text>
          </NakedButton>
          <Button onClick={applyPointsFromModal} data-testid="apply-points-button" variant="primary">
            ADD POINTS
          </Button>
        </Flex>
      </Box>
    </Fragment>
  );
};

ModalFooter.propTypes = {
  applyPointsFromModal: PropTypes.func.isRequired,
  clearPoints: PropTypes.func.isRequired,
};

const Points = ({ ...rest }) => {
  const dispatch = useDispatch();
  const { openModal, closeModal, modalProps } = useModal();
  const { convertPointsToCash } = usePointsConverters();
  const isAuthenticated = useIsAuthenticated();
  const pointsBalance = useSelector(getPointsBalance);
  const pointsAmount = useSelector(getPointsAmount);
  const pointsAmountInCash = useSelector(getPointsAmountInCash);
  const payableNowCashAmount = useSelector(getPayableNowCashAmount);
  const minDepositAmount = useSelector(getMinimumDepositAmount);
  const totalPayableAfterCredits = useSelector(getTotalPayableAfterCredits);
  const payWithToggleMessage = useSelector(getPayWithToggleMessage);
  const hasPointsAmountGreaterThanZero = pointsAmount > 0;
  const [bufferedPoints, setBufferedPoints] = useState();
  const { isPointsPayAvailable } = usePointsRedemptionAvailable();
  const { emitInteractionEvent } = useDataLayer();
  const { loginUrl } = useLoginUrl();

  const defaultPoints = useMemo(() => {
    return { amount: pointsAmount, amountInCash: pointsAmountInCash };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pointsAmount.toNumber(), pointsAmountInCash.toNumber()]);

  useEffect(() => {
    setBufferedPoints(defaultPoints);
  }, [defaultPoints]);

  const updatePoints = useCallback(
    (points) => {
      let newPayableNowCashAmount = payableNowCashAmount;

      if (points.amountInCash.lessThan(minDepositAmount)) {
        newPayableNowCashAmount = Decimal.max(minDepositAmount.minus(points.amountInCash), payableNowCashAmount);
      }

      dispatch(updatePayments({ points, cash: { payableNow: { amount: newPayableNowCashAmount } } }));
      setBufferedPoints(points);
    },
    [dispatch, minDepositAmount, payableNowCashAmount],
  );

  const applyPointsFromModal = useCallback(() => {
    updatePoints({
      amount: bufferedPoints.amount,
      amountInCash: bufferedPoints.amountInCash,
    });

    emitInteractionEvent({
      type: 'Use Points for Deposit Pop Up',
      value: 'Use Points Apply Button Selected',
    });

    closeModal();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bufferedPoints, updatePoints, closeModal]);

  const editPoints = useCallback(() => {
    if (pointsAmount.isZero()) {
      const minPointsAmountInCash = convertPointsToCash({ points: MIN_POINTS_AMOUNT });

      setBufferedPoints({
        amount: MIN_POINTS_AMOUNT,
        amountInCash: minPointsAmountInCash,
      });
    }

    openModal();

    emitInteractionEvent({
      type: 'Use Points for Deposit Pop Up',
      value: 'Use Points Opened',
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [convertPointsToCash, emitInteractionEvent, openModal, pointsAmount.toNumber()]);

  const closePointsModal = useCallback(() => {
    closeModal();
    setBufferedPoints(defaultPoints);

    emitInteractionEvent({
      type: 'Use Points for Deposit Pop Up',
      value: 'Use Points Closed',
    });
  }, [closeModal, defaultPoints, emitInteractionEvent]);

  const clearPoints = useCallback(() => {
    updatePoints({
      amount: new Decimal(0),
      amountInCash: new Decimal(0),
    });
    closeModal();

    emitInteractionEvent({
      type: 'Use Points for Deposit Pop Up',
      value: 'Use Points Clear Points Link Selected',
    });
  }, [closeModal, emitInteractionEvent, updatePoints]);

  const borderRadius = isAuthenticated ? 'exageratedRoundTopOnly' : 'exagerated';

  return (
    <Wrapper {...rest}>
      <Box mb={4}>
        <Heading.h3 fontSize="md" data-testid="redeem-message">
          Redeem Qantas Points on your hotel booking. <PointsLearnMore mb={4} />
        </Heading.h3>
        {payWithToggleMessage && (
          <Text fontSize="sm" color="brand.primary" display="block" mb={2} data-testid="pay-with-toggle-message">
            {payWithToggleMessage}
          </Text>
        )}
      </Box>
      <PaymentMethodSummaryContainer borderRadius={borderRadius} border={1}>
        <Flex alignItems={['flex-start', 'center']} justifyContent="space-between" width="100%" flexDirection={['column', 'row']}>
          <Text fontWeight="bold" fontSize={['sm', 'base']}>
            Qantas Points + Pay
          </Text>
          <Box mr={[0, 4]}>
            {isAuthenticated && totalPayableAfterCredits.greaterThan(0) && (
              <Fragment>
                {isPointsPayAvailable && hasPointsAmountGreaterThanZero && (
                  <Currency amount={pointsAmount} currency="PTS" fontSize={['xs', 'base']} data-testid="points-amount" />
                )}
                <MinimumPointsMessages />
              </Fragment>
            )}
          </Box>
        </Flex>
        {isAuthenticated && (
          <PaymentMethodSummaryButton
            onClick={editPoints}
            disabled={!isPointsPayAvailable}
            variant="primary"
            data-testid="edit-points-button"
          >
            {hasPointsAmountGreaterThanZero ? 'CHANGE' : 'ADD'}
          </PaymentMethodSummaryButton>
        )}
        {!isAuthenticated && (
          <PaymentMethodSummaryButton as={Link} href={loginUrl} data-testid="login-button" variant="primary">
            LOGIN
          </PaymentMethodSummaryButton>
        )}
      </PaymentMethodSummaryContainer>
      {isAuthenticated && (
        <Box bg="greys.porcelain" px={4} py={2} border={1} borderColor="greys.alto" borderRadius="exageratedRoundBottomOnly" borderTop={0}>
          <Text fontSize="sm" data-testid="available-points">
            {formatNumber({ number: pointsBalance })} points available
          </Text>
        </Box>
      )}

      {modalProps.isOpen && (
        <Modal
          {...modalProps}
          onRequestClose={closePointsModal}
          title="Add Qantas Points"
          padding={0}
          footerComponent={() => <ModalFooter clearPoints={clearPoints} applyPointsFromModal={applyPointsFromModal} />}
        >
          <Box padding={[6, 8]}>
            <PointsSlider points={bufferedPoints} updatePoints={setBufferedPoints} />
          </Box>
        </Modal>
      )}
    </Wrapper>
  );
};

export default Points;
