import React from 'react';
import { act } from 'react-dom/test-utils';
import { mountUtils } from 'test-utils';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import Points from './Points';
import { getPointsErrors } from 'store/booking/bookingSelectors';
import {
  getPayableNowCashAmount,
  getTotalPayableAfterCredits,
  getMinimumDepositAmount,
  getPointsAmount,
  getPointsAmountInCash,
} from 'store/checkout/checkoutSelectors';
import { updatePayments } from 'store/checkout/checkoutActions';
import { getPointsBalance, getFirstName } from 'store/user/userSelectors';
import { useLoginUrl } from 'lib/qffAuth';
import { useIsAuthenticated } from 'lib/oauth';
import { useDataLayer } from 'hooks/useDataLayer';
import usePointsConverters from 'hooks/points/usePointsConverters';
import { MIN_POINTS_AMOUNT } from 'config';
import usePointsRedemptionAvailable from 'components/CheckoutPage/CustomerDetails/Steps/PaymentOptions/hooks/usePointsRedemptionAvailable';
import { getPayWithToggleMessage } from 'store/campaign/campaignSelectors';

jest.mock('store/user/userSelectors');
jest.mock('store/campaign/campaignSelectors');
jest.mock('store/checkout/checkoutSelectors');
jest.mock('store/booking/bookingSelectors');
jest.mock('lib/qffAuth');
jest.mock('lib/oauth');
jest.mock('store/pointsConversion/pointsConversionSelectors');
jest.mock('hooks/useDataLayer');
jest.mock('hooks/points/usePointsConverters');
jest.mock('components/CheckoutPage/CustomerDetails/Steps/PaymentOptions/hooks/usePointsRedemptionAvailable');
jest.mock('store/ui/uiSelectors');

mountUtils.mockComponent('PointsLearnMore');
mountUtils.mockComponent('PointsSlider');

let baseProps;
const isAuthenticated = true;
const pointsBalance = new Decimal(100000);
const totalPayableAfterCredits = new Decimal(100);
const pointsAmount = new Decimal(20000);
const pointsAmountInCash = new Decimal(20);
const payableNowCashAmount = new Decimal(50);
const minimumDepositAmount = new Decimal(20);
const firstName = 'Frank';
const pointsErrors = [{ code: 'code', message: 'message' }];
const isPointsPayAvailable = true;
const payWithToggleMessage = 'payWithToggleMessage';

const emitInteractionEvent = jest.fn();

const POINTS_CONVERSION_MULTIPLIER = 1000;
const convertPointsToCash = ({ points }) => new Decimal(points).dividedBy(POINTS_CONVERSION_MULTIPLIER);
const convertCashToPoints = ({ cash }) => new Decimal(cash).times(POINTS_CONVERSION_MULTIPLIER);

const render = (props) => mountUtils(<Points {...baseProps} {...props} />, { decorators: { store: true, theme: true } });

beforeEach(() => {
  baseProps = {
    updateFormData: jest.fn(),
  };

  useIsAuthenticated.mockReturnValue(isAuthenticated);
  getPointsBalance.mockReturnValue(pointsBalance);
  getTotalPayableAfterCredits.mockReturnValue(totalPayableAfterCredits);
  getPointsAmount.mockReturnValue(pointsAmount);
  getPointsAmountInCash.mockReturnValue(pointsAmountInCash);
  getPayableNowCashAmount.mockReturnValue(payableNowCashAmount);
  getPayWithToggleMessage.mockReturnValue(null);
  getMinimumDepositAmount.mockReturnValue(minimumDepositAmount);
  getFirstName.mockReturnValue(firstName);
  getPointsErrors.mockReturnValue(pointsErrors);
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  usePointsConverters.mockReturnValue({ convertCashToPoints, convertPointsToCash }); // enough to allow points payment
  usePointsRedemptionAvailable.mockReturnValue({
    isPointsPayAvailable,
  });
  useLoginUrl.mockReturnValue({ logoutUrl: '/hotels/auth/callback?state=/foo' });
});

afterEach(() => jest.clearAllMocks());

describe('when not authenticated', () => {
  beforeEach(() => {
    useIsAuthenticated.mockReturnValue(false);
  });

  it('renders the Login button component', () => {
    const { findByTestId } = render();
    expect(findByTestId('login-button')).toExist();
  });

  it('does not render the Points Button', () => {
    const { findByTestId } = render();
    expect(findByTestId('edit-points-button')).not.toExist();
  });

  it('does not render the available points amount', () => {
    const { findByTestId } = render();
    expect(findByTestId('available-points')).not.toExist();
  });
});

describe('when authenticated', () => {
  beforeEach(() => {
    useIsAuthenticated.mockReturnValue(true);
  });

  it('does not display the login button', () => {
    const { findByTestId } = render();
    expect(findByTestId('login-button')).not.toExist();
  });

  it('renders the MinimumPointsMessages component', () => {
    const { find } = render();
    expect(find('MinimumPointsMessages')).toExist();
  });

  it('renders the Points Button', () => {
    const { findByTestId } = render();
    expect(findByTestId('edit-points-button')).toExist();
  });

  it('renders the current points amount', () => {
    const { findByTestId } = render();
    expect(findByTestId('points-amount')).toHaveText('20,000PTS');
  });

  it('renders the available points amount', () => {
    const { findByTestId } = render();
    expect(findByTestId('available-points')).toHaveText('100,000 points available');
  });

  describe('when points amount selected is zero', () => {
    beforeEach(() => {
      getPointsAmount.mockReturnValue(new Decimal(0));
    });

    it('does not render the points amount', () => {
      const { findByTestId } = render();
      expect(findByTestId('points-amount')).not.toExist();
    });

    it('renders an ADD text box', () => {
      const { findByTestId } = render();
      expect(findByTestId('edit-points-button')).toHaveText('ADD');
    });
  });

  describe('when points amount selected is greater than zero', () => {
    it('renders the points amount', () => {
      const { find } = render();
      expect(find('Currency[data-testid="points-amount"]')).toHaveProp({
        amount: pointsAmount,
        currency: 'PTS',
      });
    });

    it('renders a CHANGE text box', () => {
      const { findByTestId } = render();
      expect(findByTestId('edit-points-button')).toHaveText('CHANGE');
    });
  });

  describe('clicking on the Points button', () => {
    it('renders the points slider in a modal', () => {
      const { find, findByTestId } = render();
      findByTestId('edit-points-button').simulate('click');
      expect(find('Modal').find('PointsSlider')).toHaveProp({
        points: { amount: pointsAmount, amountInCash: pointsAmountInCash },
      });
    });

    it('emits an interaction event', () => {
      const { findByTestId } = render();
      findByTestId('edit-points-button').simulate('click');
      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Use Points for Deposit Pop Up',
        value: 'Use Points Opened',
      });
    });

    describe('when the points amount is zero', () => {
      it('sets the initial points amount for the slider to the MIN_POINTS_AMOUNT', () => {
        getPointsAmount.mockReturnValue(new Decimal(0));
        const { find, findByTestId } = render();

        findByTestId('edit-points-button').simulate('click');
        expect(find('Modal').find('PointsSlider')).toHaveProp({
          points: { amount: MIN_POINTS_AMOUNT, amountInCash: convertPointsToCash({ points: MIN_POINTS_AMOUNT }) },
        });
      });
    });

    describe('closing the modal', () => {
      it('emits an interaction event', () => {
        const { findByTestId } = render();
        findByTestId('edit-points-button').simulate('click');
        findByTestId('close').simulate('click');

        expect(emitInteractionEvent).toHaveBeenCalledWith({
          type: 'Use Points for Deposit Pop Up',
          value: 'Use Points Closed',
        });
      });
    });
  });

  describe('updating points from the modal', () => {
    describe('applying points', () => {
      let find, findByTestId, decorators;

      beforeEach(() => {
        ({ find, findByTestId, decorators } = render());

        findByTestId('edit-points-button').simulate('click');

        act(() => {
          find('PointsSlider')
            .props()
            .updatePoints({
              amount: new Decimal(50000),
              amountInCash: new Decimal(50),
            });
        });

        expect(decorators.store.dispatch).not.toHaveBeenCalled();

        findByTestId('apply-points-button').simulate('click');
      });

      it('updates points and cash', () => {
        expect(decorators.store.dispatch).toHaveBeenCalledWith(
          updatePayments({
            cash: {
              payableNow: {
                amount: new Decimal(50),
              },
            },
            points: {
              amount: new Decimal(50000),
              amountInCash: new Decimal(50),
            },
          }),
        );
      });

      it('emits an interaction event', () => {
        expect(emitInteractionEvent).toHaveBeenCalledWith({
          type: 'Use Points for Deposit Pop Up',
          value: 'Use Points Apply Button Selected',
        });
      });
    });

    describe('clearing points', () => {
      let findByTestId, decorators;

      beforeEach(() => {
        ({ findByTestId, decorators } = render());
        decorators.store.dispatch.mockClear();
        findByTestId('edit-points-button').simulate('click');
        findByTestId('clear-points-button').simulate('click');
      });

      it('updates points to zero', () => {
        const { findByTestId, decorators } = render();
        decorators.store.dispatch.mockClear();
        findByTestId('edit-points-button').simulate('click');
        findByTestId('clear-points-button').simulate('click');

        expect(decorators.store.dispatch).toHaveBeenCalledWith(
          updatePayments({
            cash: {
              payableNow: {
                amount: new Decimal(50),
              },
            },
            points: {
              amount: new Decimal(0),
              amountInCash: new Decimal(0),
            },
          }),
        );
      });

      it('emits an interaction event', () => {
        expect(emitInteractionEvent).toHaveBeenCalledWith({
          type: 'Use Points for Deposit Pop Up',
          value: 'Use Points Clear Points Link Selected',
        });
      });
    });

    describe('with a points amount in cash that is less than the minimum deposit amount', () => {
      it('adjusts the payableNowCashAmount', () => {
        const minimumDepositAmount = new Decimal(110);
        getMinimumDepositAmount.mockReturnValue(minimumDepositAmount);

        const { find, findByTestId, decorators } = render();
        decorators.store.dispatch.mockClear();
        findByTestId('edit-points-button').simulate('click');

        act(() => {
          find('PointsSlider')
            .props()
            .updatePoints({
              amount: new Decimal(50000),
              amountInCash: new Decimal(50),
            });
        });

        expect(decorators.store.dispatch).not.toHaveBeenCalled();

        const pointsAmountInCash = new Decimal(50);

        findByTestId('apply-points-button').simulate('click');
        expect(decorators.store.dispatch).toHaveBeenCalledWith(
          updatePayments({
            cash: {
              payableNow: {
                amount: minimumDepositAmount.minus(pointsAmountInCash),
              },
            },
            points: {
              amount: new Decimal(50000),
              amountInCash: pointsAmountInCash,
            },
          }),
        );
      });
    });
  });

  describe('when isPointsPayAvailable is false', () => {
    const isPointsPayAvailable = false;

    beforeEach(() => {
      usePointsRedemptionAvailable.mockReturnValue({
        isPointsPayAvailable,
      });
    });

    describe('the edit points button', () => {
      it('isDisabled', () => {
        const { findByTestId } = render();
        expect(findByTestId('edit-points-button')).toHaveProp({
          disabled: true,
        });
      });

      it('does not render the Points or cash amounts', () => {
        const { findByTestId } = render();
        expect(findByTestId('points-amount')).not.toExist();
        expect(findByTestId('cash-amount')).not.toExist();
      });
    });
  });
});

describe('VPP messaging', () => {
  it('shows the redeem messaging', () => {
    const { findByTestId } = render();

    expect(findByTestId('redeem-message')).toIncludeText('Redeem Qantas Points on your hotel booking.');
  });

  it('renders PointsLearnMore', () => {
    const { find } = render();

    expect(find('PointsLearnMore')).toExist();
  });

  it('does NOT show the pay with toggle message', () => {
    const { findByTestId } = render();

    expect(findByTestId('pay-with-toggle-message')).not.toExist();
  });

  describe('when a pay with toggle message is present', () => {
    beforeEach(() => {
      getPayWithToggleMessage.mockReturnValue(payWithToggleMessage);
    });

    it('renders the pay with toggle message', () => {
      const { findByTestId } = render();

      expect(findByTestId('pay-with-toggle-message')).toHaveText(payWithToggleMessage);
    });
  });
});
