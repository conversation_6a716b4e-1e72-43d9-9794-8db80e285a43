import React, { useEffect, useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import debounce from 'lodash/debounce';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import Slider from 'rc-slider';
import { useSelector } from 'react-redux';
import { Box, Input, Text, Flex, Icon } from '@qga/roo-ui/components';
import usePointsConverters from 'hooks/points/usePointsConverters';
import formatNumber from 'lib/formatters/formatNumber';
import theme from 'lib/theme';
import { getPayableNowCashAmount, getVoucherAmount, getTravelPassAmount } from 'store/checkout/checkoutSelectors';
import { getTotalPayableAtBooking } from 'store/quote/quoteSelectors';
import { getPointsBalance } from 'store/user/userSelectors';
import { MIN_POINTS_AMOUNT } from 'config';
import Currency from 'components/Currency';
import { useDataLayer } from 'hooks/useDataLayer';
import { useSliderKey } from 'lib/hooks/slider';

const UPDATE_DEBOUNCE_MILLIS = 300;

const emitInteractionEventDebounced = debounce(({ emitInteractionEvent }) => {
  emitInteractionEvent({ type: 'Use Points for Deposit Pop Up', value: 'Slider Selected' });
}, UPDATE_DEBOUNCE_MILLIS);

const adjustPointsAmountWithinRange = ({ pointsAmount, minPointsAmount, maxPointsAmount }) => {
  return Decimal.min(Decimal.max(pointsAmount, minPointsAmount), maxPointsAmount);
};

const formattedStringToNumber = (formatted) => (formatted ? Number(formatted.replace(/(,|\$)/g, '')) : 0);
const maskPoints = (cash) => cash.replace(/[^0-9,]/g, '');
const formatPoints = (pointsAmount) => formatNumber({ number: pointsAmount, decimalPlaces: 0 });

const PointsSlider = ({ points, updatePoints }) => {
  const { convertPointsToCash, convertCashToPoints } = usePointsConverters();
  const { emitInteractionEvent } = useDataLayer();
  const pointsAmount = points?.amount;
  const pointsAmountInCash = convertPointsToCash({ points: pointsAmount });
  const pointsBalance = useSelector(getPointsBalance);
  const cashAmount = useSelector(getPayableNowCashAmount);
  const totalPayableAtBooking = useSelector(getTotalPayableAtBooking);
  const voucherAmount = useSelector(getVoucherAmount);
  const travelPassAmount = useSelector(getTravelPassAmount);
  const nonPointsPaymentsAmount = cashAmount.plus(voucherAmount).plus(travelPassAmount);
  const maxPointsAmount = convertCashToPoints({ cash: totalPayableAtBooking.minus(nonPointsPaymentsAmount) });
  const maxSpendableAmount = Decimal.min(pointsBalance, maxPointsAmount);
  const payableLaterCashAmount = totalPayableAtBooking.minus(nonPointsPaymentsAmount).minus(pointsAmountInCash);
  const [inputValue, setInputValue] = useState(formatPoints(pointsAmount));
  const [sliderValue, setSliderValue] = useState(pointsAmount.toNumber());
  const { sliderKey, regenerateSliderKey } = useSliderKey();

  const handlePointsChange = useCallback(
    (amount) => {
      const amountInCash = convertPointsToCash({ points: amount });
      const maxAmountInCast = convertPointsToCash({ points: maxSpendableAmount });

      if (amount.greaterThanOrEqualTo(maxSpendableAmount)) {
        setSliderValue(maxSpendableAmount.toNumber());
        setInputValue(formatPoints(maxSpendableAmount));
        updatePoints({ amount: maxSpendableAmount, amountInCash: maxAmountInCast });
        emitInteractionEventDebounced({ emitInteractionEvent });
      } else {
        setSliderValue(amount.toNumber());
        setInputValue(formatPoints(amount));
        updatePoints({ amount, amountInCash });
      }

      emitInteractionEventDebounced({ emitInteractionEvent });
    },
    [convertPointsToCash, emitInteractionEvent, maxSpendableAmount, updatePoints],
  );

  useEffect(() => {
    setInputValue(formatPoints(pointsAmount));
  }, [pointsAmount.toNumber()]); // eslint-disable-line react-hooks/exhaustive-deps

  const updatePointsAmountFromBuffer = useCallback(() => {
    const pointsAmount = inputValue ? new Decimal(formattedStringToNumber(inputValue)) : new Decimal(0);
    handlePointsChange(adjustPointsAmountWithinRange({ pointsAmount, minPointsAmount: MIN_POINTS_AMOUNT, maxPointsAmount }));
    regenerateSliderKey();
  }, [inputValue, handlePointsChange, maxPointsAmount, regenerateSliderKey]);

  const handleSliderChange = useCallback(
    (value) => {
      const pointsAmount = new Decimal(value);
      handlePointsChange(pointsAmount);
    },
    [handlePointsChange],
  );

  const handleInputChange = (e) => {
    const value = maskPoints(e.target.value);
    const pointsAmount = new Decimal(formattedStringToNumber(value));
    handlePointsChange(pointsAmount);
  };

  const handleInputKeyUp = useCallback((e) => e.keyCode === 13 && updatePointsAmountFromBuffer(), [updatePointsAmountFromBuffer]);

  return (
    <Box pt={[8, 4]} minHeight="200px">
      <Slider
        key={sliderKey}
        min={MIN_POINTS_AMOUNT.toNumber()}
        max={maxPointsAmount.toNumber()}
        value={sliderValue}
        onChange={handleSliderChange}
        step={1000}
        railStyle={theme.slider.rail}
        trackStyle={theme.slider.track}
        handleStyle={theme.slider.handle}
      />
      <Flex justifyContent="space-between" mt={8}>
        <Box>
          <Box mb={1}>Use now</Box>
          <Flex alignItems="center" mb={4}>
            <Box width="120px">
              <Input
                id="points"
                name="points"
                value={inputValue}
                onChange={handleInputChange}
                onBlur={updatePointsAmountFromBuffer}
                onKeyUp={handleInputKeyUp}
                data-testid="points-input"
                mb={0}
                px={2}
                py={1}
              />
            </Box>
            <Text ml={2}>pts</Text>
          </Flex>
          <Box fontSize="xs" mb="1">
            Minimum 5,000 points required.
          </Box>
          {pointsAmount.equals(maxSpendableAmount) && (
            <Box fontSize="xs" mb="1">
              You&apos;ve reached your total points balance.
            </Box>
          )}
        </Box>
        <Box>
          <Box mb={1} textAlign="right">
            <Icon name="depositPay" mr={2} size={22} />
            Pay later
          </Box>
          <Box pt={1} textAlign="right">
            <Currency amount={payableLaterCashAmount} currency="AUD" data-testid="pay-later-amount" />
          </Box>
        </Box>
      </Flex>
    </Box>
  );
};

PointsSlider.propTypes = {
  updatePoints: PropTypes.func.isRequired,
  points: PropTypes.shape({
    amount: PropTypes.instanceOf(Decimal).isRequired,
    amountInCash: PropTypes.instanceOf(Decimal).isRequired,
  }).isRequired,
};

export default PointsSlider;
