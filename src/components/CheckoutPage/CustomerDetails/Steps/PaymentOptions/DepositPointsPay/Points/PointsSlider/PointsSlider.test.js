import React from 'react';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import Slider from 'rc-slider';
import { act } from 'react-dom/test-utils';
import { mountUtils } from 'test-utils';
import PointsSlider from './PointsSlider';
import usePointsConverters from 'hooks/points/usePointsConverters';
import { MIN_POINTS_AMOUNT } from 'config';
import { getPayableNowCashAmount, getVoucherAmount, getTravelPassAmount } from 'store/checkout/checkoutSelectors';
import { getPointsBalance } from 'store/user/userSelectors';
import { getTotalPayableAtBooking } from 'store/quote/quoteSelectors';
import formatNumber from 'lib/formatters/formatNumber';
import { useDataLayer } from 'hooks/useDataLayer';

jest.mock('store/checkout/checkoutSelectors');
jest.mock('store/user/userSelectors');
jest.mock('store/quote/quoteSelectors');
jest.mock('hooks/points/usePointsConverters');
jest.mock('rc-slider', () => () => null);
jest.mock('lodash/debounce', () => (fn) => fn);
jest.mock('hooks/useDataLayer');

jest.useFakeTimers();

const POINTS_CONVERSION_MULTIPLIER = 1000;

let baseProps;

const render = (props) => mountUtils(<PointsSlider {...baseProps} {...props} />, { decorators: { store: true } });

const pointsAmount = new Decimal(10000);
const pointsAmountInCash = pointsAmount.dividedBy(POINTS_CONVERSION_MULTIPLIER);
const payableNowCashAmount = new Decimal(100);
const voucherAmount = new Decimal(10);
const travelPassAmount = new Decimal(10);
const totalPayableAtBooking = new Decimal(200);
const pointsBalance = new Decimal(10000);

const convertCashToPoints = ({ cash }) => new Decimal(cash).times(POINTS_CONVERSION_MULTIPLIER);
const convertPointsToCash = ({ points }) => new Decimal(points).dividedBy(POINTS_CONVERSION_MULTIPLIER);

let emitInteractionEvent;

beforeEach(() => {
  baseProps = {
    points: {
      amount: pointsAmount,
      amountInCash: pointsAmountInCash,
    },
    updatePoints: jest.fn(),
  };

  getPayableNowCashAmount.mockReturnValue(payableNowCashAmount);
  getTotalPayableAtBooking.mockReturnValue(totalPayableAtBooking);
  getVoucherAmount.mockReturnValue(voucherAmount);
  getTravelPassAmount.mockReturnValue(travelPassAmount);
  getPointsBalance.mockReturnValue(pointsBalance);

  usePointsConverters.mockReturnValue({
    convertCashToPoints,
    convertPointsToCash,
  });
  emitInteractionEvent = jest.fn();
  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

it('renders the Slider', () => {
  const { find } = render();
  expect(find(Slider)).toHaveProp({
    min: MIN_POINTS_AMOUNT.toNumber(),
    max: totalPayableAtBooking
      .minus(payableNowCashAmount)
      .minus(voucherAmount)
      .minus(travelPassAmount)
      .times(POINTS_CONVERSION_MULTIPLIER)
      .toNumber(),
    value: pointsAmount.toNumber(),
    step: 1000,
  });
});

it('renders the formatted points input', () => {
  const { findByTestId } = render();
  expect(findByTestId('points-input')).toHaveProp({ value: '10,000' });
});

it('renders the pay-later-amount', () => {
  const { find } = render();
  expect(find('Currency[data-testid="pay-later-amount"]')).toHaveProp({
    amount: totalPayableAtBooking
      .minus(payableNowCashAmount)
      .minus(voucherAmount)
      .minus(travelPassAmount)
      .minus(pointsAmount.dividedBy(POINTS_CONVERSION_MULTIPLIER)),
    currency: 'AUD',
  });
});

describe('when updating the points value from the slider', () => {
  it('calls updatePoints', () => {
    const { find } = render();
    act(() => {
      find(Slider).props().onChange(7000);
    });
    expect(baseProps.updatePoints).toHaveBeenCalledWith({ amount: new Decimal(7000), amountInCash: new Decimal(7) });
  });

  it('emits an interaction event', () => {
    const { find } = render();
    act(() => {
      find(Slider).props().onChange(7000);
    });
    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Use Points for Deposit Pop Up',
      value: 'Slider Selected',
    });
  });

  it('prevents spending more than the users points balance', () => {
    const { find } = render();
    act(() => {
      find(Slider).props().onChange(20000);
    });
    expect(baseProps.updatePoints).toHaveBeenCalledWith({
      amount: pointsBalance,
      amountInCash: convertPointsToCash({ points: pointsBalance.toNumber() }),
    });
  });
});

describe('when entering a formatted points amount', () => {
  describe('onBlur of the points Input', () => {
    it('updates the points', () => {
      const { findByTestId } = render();

      const pointsInput = findByTestId('points-input');
      const pointsAmount = new Decimal(7000);
      const pointsAmountInCash = pointsAmount.dividedBy(POINTS_CONVERSION_MULTIPLIER);
      act(() => {
        pointsInput.simulate('change', { target: { value: formatNumber({ number: pointsAmount, decimalPlaces: 0 }) } });
      });
      act(() => {
        pointsInput.simulate('blur');
      });

      expect(baseProps.updatePoints).toHaveBeenCalledWith({
        amount: pointsAmount,
        amountInCash: pointsAmountInCash,
      });
    });

    it('updates the key of the slider', () => {
      const { find, findByTestId, wrapper } = render();

      const pointsInput = findByTestId('points-input');
      const pointsAmount = new Decimal(7000);

      const initialSliderKey = find(Slider).key();

      act(() => {
        pointsInput.simulate('change', { target: { value: formatNumber({ number: pointsAmount, decimalPlaces: 0 }) } });
      });
      act(() => {
        pointsInput.simulate('blur');
      });
      act(() => {
        jest.runAllTimers();
      });

      wrapper.update();

      expect(wrapper.find(Slider).key()).not.toEqual(initialSliderKey);
    });
  });

  describe('onKeyUp of the points Input with enter key', () => {
    it('updates the points', () => {
      const { findByTestId } = render();

      const pointsInput = findByTestId('points-input');
      const pointsAmount = new Decimal(7000);
      const pointsAmountInCash = pointsAmount.dividedBy(POINTS_CONVERSION_MULTIPLIER);

      act(() => {
        pointsInput.simulate('change', { target: { value: formatNumber({ number: pointsAmount, decimalPlaces: 0 }) } });
      });
      act(() => {
        pointsInput.simulate('keyUp', { keyCode: 13 });
      });

      expect(baseProps.updatePoints).toHaveBeenCalledWith({
        amount: pointsAmount,
        amountInCash: pointsAmountInCash,
      });
    });
  });

  describe('onKeyUp of the points Input with other key', () => {
    it('does not update the points', () => {
      const { findByTestId } = render();
      const pointsInput = findByTestId('points-input');
      act(() => {
        pointsInput.simulate('keyUp', { keyCode: 1 });
      });

      expect(baseProps.updatePoints).not.toHaveBeenCalled();
    });
  });

  describe('when the points entered is less than the minimum amount', () => {
    it('updates the points with the amount increased to the minimum points amount', () => {
      const { findByTestId } = render();

      const pointsInput = findByTestId('points-input');
      const minimumPointsAmount = MIN_POINTS_AMOUNT;
      const minimumPointsAmountInCash = minimumPointsAmount.dividedBy(POINTS_CONVERSION_MULTIPLIER);

      act(() => {
        pointsInput.simulate('change', { target: { value: formatNumber({ number: 2000, decimalPlaces: 0 }) } });
      });
      act(() => {
        pointsInput.simulate('blur');
      });
      expect(baseProps.updatePoints).toHaveBeenCalledWith({
        amount: minimumPointsAmount,
        amountInCash: minimumPointsAmountInCash,
      });
    });
  });

  describe('when the points entered is greater than the maximum points amount', () => {
    it('updates the points with the amount decreased to the maximum points amount', () => {
      getPointsBalance.mockReturnValue(new Decimal(100000));

      const { findByTestId } = render();

      const pointsInput = findByTestId('points-input');
      const maximumPointsAmount = totalPayableAtBooking
        .minus(payableNowCashAmount)
        .minus(voucherAmount)
        .minus(travelPassAmount)
        .times(POINTS_CONVERSION_MULTIPLIER);

      const maximumPointsAmountInCash = maximumPointsAmount.dividedBy(POINTS_CONVERSION_MULTIPLIER);

      act(() => {
        pointsInput.simulate('change', {
          target: { value: formatNumber({ number: maximumPointsAmount.plus(1), decimalPlaces: 0 }) },
        });
      });
      act(() => {
        pointsInput.simulate('blur');
      });

      expect(baseProps.updatePoints).toHaveBeenCalledWith({
        amount: maximumPointsAmount,
        amountInCash: maximumPointsAmountInCash,
      });
    });
  });
});
