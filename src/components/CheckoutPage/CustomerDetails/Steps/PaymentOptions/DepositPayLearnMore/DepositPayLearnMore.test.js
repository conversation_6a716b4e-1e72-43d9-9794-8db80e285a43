import React from 'react';
import { mountUtils } from 'test-utils';
import LearnMore from './DepositPayLearnMore';

jest.mock('store/ui/uiSelectors');

const render = () => mountUtils(<LearnMore />, { decorators: { store: true, theme: true } });

describe('when the link is clicked', () => {
  it('opens the modal with the body text', () => {
    const { find, findByTestId } = render();

    find('TextButton').simulate('click');

    expect(findByTestId('modal-deposit-pay-learn-more-text')).toExist();
  });
});
