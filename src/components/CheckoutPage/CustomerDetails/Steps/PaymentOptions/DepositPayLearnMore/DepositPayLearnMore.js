import React, { Fragment, useCallback } from 'react';
import { Text, Box } from '@qga/roo-ui/components';
import TextButton from 'components/TextButton';
import { useModal } from 'lib/hooks';
import Modal from 'components/Modal';
import { useDataLayer } from 'hooks/useDataLayer';

const DepositPayLearnMore = () => {
  const { openModal, modalProps } = useModal();
  const { emitInteractionEvent } = useDataLayer();

  const handleOpenModal = useCallback(() => {
    openModal();
    emitInteractionEvent({
      type: 'Learn More Link',
      value: 'Deposit Up Front Selected',
    });
  }, [emitInteractionEvent, openModal]);

  return (
    <Fragment>
      <TextButton onClick={handleOpenModal} color="inherit" fontSize="sm">
        Learn more
      </TextButton>
      <Modal {...modalProps} title="Deposit Pay Terms &amp; Conditions">
        <Text display="block" mb="5" fontSize={['sm', 'base']} data-testid="modal-deposit-pay-learn-more-text">
          <Box>
            A minimum 20% deposit, payable using a payment card or using Qantas Points, is required to secure your booking. Deposit payment
            terms will only be permitted for bookings with a free cancellation option, within Australia. Final payment of the balance owing
            is payable 7 days prior to the end of the free cancellation window for the booking, and will be automatically charged to the
            card details provided at the time of booking. Where payment of the balance is not made within the required timeframe, or the
            payment card provided is declined, the booking will be cancelled, and the deposit will be refunded in full. Deposit Pay is
            available at the discretion of Qantas, and may be removed or revoked at any time. Payment of a deposit indicates acceptance of
            these conditions.
          </Box>
        </Text>
      </Modal>
    </Fragment>
  );
};

export default DepositPayLearnMore;
