import React from 'react';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import { mountUtils } from 'test-utils';
import usePointsRedemptionAvailable from 'components/CheckoutPage/CustomerDetails/Steps/PaymentOptions/hooks/usePointsRedemptionAvailable';
import MinimumPointsMessages from './MinimumPointsMessages';
import { getPointsAmount } from 'store/checkout/checkoutSelectors';

jest.mock('components/CheckoutPage/CustomerDetails/Steps/PaymentOptions/hooks/usePointsRedemptionAvailable');
jest.mock('store/checkout/checkoutSelectors');

const render = () => mountUtils(<MinimumPointsMessages />, { decorators: { store: true } });

beforeEach(() => {
  getPointsAmount.mockReturnValue(new Decimal(0));
});

describe('when isBalanceLessThanMinPoints is false', () => {
  it('does not display a message', () => {
    usePointsRedemptionAvailable.mockReturnValue({
      isBalanceLessThanMinPoints: false,
    });

    const { findByTestId } = render();

    expect(findByTestId('insufficient-points-balance-message')).not.toExist();
  });
});

describe('when isPayableLessThanMinPoints is false', () => {
  it('displays the correct message', () => {
    usePointsRedemptionAvailable.mockReturnValue({
      isPayableLessThanMinPoints: false,
    });

    const { findByTestId } = render();

    expect(findByTestId('insufficient-booking-points-amount-message')).not.toExist();
  });
});

describe('when isBalanceLessThanMinPoints is true', () => {
  it('displays the correct message', () => {
    usePointsRedemptionAvailable.mockReturnValue({
      isBalanceLessThanMinPoints: true,
      isPayableLessThanMinPoints: false,
    });

    const { findByTestId } = render();

    expect(findByTestId('insufficient-points-balance-message')).toExist();
    expect(findByTestId('insufficient-booking-points-amount-message')).not.toExist();
  });
});

describe('when isPayableLessThanMinPoints is true', () => {
  const isPayableLessThanMinPoints = true;
  const isBalanceLessThanMinPoints = false;

  it('displays the correct message', () => {
    usePointsRedemptionAvailable.mockReturnValue({
      isBalanceLessThanMinPoints,
      isPayableLessThanMinPoints,
    });

    const { findByTestId } = render();

    expect(findByTestId('insufficient-booking-points-amount-message')).toExist();
    expect(findByTestId('insufficient-points-balance-message')).not.toExist();
  });

  describe('when a points amount is allocated', () => {
    getPointsAmount.mockReturnValue(new Decimal(1));
    usePointsRedemptionAvailable.mockReturnValue({
      isBalanceLessThanMinPoints,
      isPayableLessThanMinPoints,
    });

    const { findByTestId } = render();

    expect(findByTestId('insufficient-booking-points-amount-message')).not.toExist();
    expect(findByTestId('insufficient-points-balance-message')).not.toExist();
  });

  describe('when isBalanceLessThanMinPoints is true', () => {
    const isBalanceLessThanMinPoints = true;

    it('displays the correct message', () => {
      usePointsRedemptionAvailable.mockReturnValue({
        isBalanceLessThanMinPoints,
        isPayableLessThanMinPoints,
      });

      const { findByTestId } = render();

      expect(findByTestId('insufficient-points-balance-message')).toExist();
      expect(findByTestId('insufficient-booking-points-amount-message')).not.toExist();
    });
  });
});
