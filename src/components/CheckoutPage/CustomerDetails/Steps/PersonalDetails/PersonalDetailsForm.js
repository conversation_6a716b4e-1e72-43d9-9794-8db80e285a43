import React, { useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { useForm } from 'react-hook-form';
import useFormPersist from 'react-hook-form-persist';
import { useSelector } from 'react-redux';
import styled from '@emotion/styled';
import isEmpty from 'lodash/isEmpty';
import { Box, Input, Text, Flex, Select } from '@qga/roo-ui/components';
import { StepForm } from 'components/StepWizard';
import { NAME_TITLES, EMAIL_REGEX, NAME_TEXT_REGEX, SAFE_TEXT_REGEX, CHECKOUT_FORM_STEPS } from 'lib/enums/checkout';
import { FieldLabel, FieldError } from '@qga/components';
import TextArea from 'components/TextArea';
import { getTitle, getFirstName, getLastName, getEmailAddress, getPhoneNumber } from 'store/user/userSelectors';
import { useDataLayer } from 'hooks/useDataLayer';
import { useIsAuthenticated } from 'lib/oauth';
import { getIsLoggedInFromStep } from 'components/CheckoutPage/CustomerDetails/sessionStorage';

const isFormPopulated = (formData) => !!(formData.title || formData.firstName || formData.lastName);

const StyledSelect = styled(Select)`
  ::-ms-expand {
    display: none;
  }
`;

const PersonalDetailsForm = ({ step }) => {
  const {
    register,
    formState: { errors },
    handleSubmit,
    setValue,
    watch,
    /* eslint-disable no-unused-vars */
    reset,
  } = useForm({ mode: 'onBlur' });
  const { formData, updateFormData } = step;
  const isAuthenticated = useIsAuthenticated();
  const title = useSelector(getTitle);
  const firstName = useSelector(getFirstName);
  const lastName = useSelector(getLastName);
  const emailAddress = useSelector(getEmailAddress);
  const phoneNumber = useSelector(getPhoneNumber);

  const isLoggedInFromStep = getIsLoggedInFromStep();

  const { emitInteractionEvent } = useDataLayer();
  const completeStep = useCallback(
    (data) => {
      if (!isEmpty(data.specialRequests)) {
        emitInteractionEvent({ type: 'Special Request Note', value: data.specialRequests });
      }
      emitInteractionEvent({ type: 'Continue Button', value: 'Your Details Button Selected' });
      step.completeStep(data);
    },
    [step, emitInteractionEvent],
  );

  const maskPhoneNumber = (value) => {
    setValue('phoneNumber', value.replace(/[^0-9+ ]/g, ''));
  };

  useFormPersist(CHECKOUT_FORM_STEPS.PERSONAL_DETAILS, { setValue, watch });

  useEffect((data) => {
    if (isLoggedInFromStep) {
      step.completeStep(data);
    }
  }, []); // eslint-disable-line

  useEffect(() => {
    if (isAuthenticated && !isFormPopulated(formData)) {
      updateFormData({ title, firstName, lastName, emailAddress, phoneNumber });
    }
  }, [firstName, formData, isAuthenticated, lastName, title, updateFormData, emailAddress, phoneNumber]);

  return (
    <StepForm {...step} completeStep={completeStep} handleSubmit={handleSubmit}>
      <Box mb={[4, 8]}>
        <Flex flexDirection={['column', 'row']}>
          <FieldLabel htmlFor="title" flex="0 0 20%" mb={[5, 3]} pr={[0, 4]}>
            Title
            <StyledSelect
              id="title"
              name="title"
              defaultValue={formData.title}
              {...register('title', { required: 'Please select title' })}
              error={!!errors.title}
              key={formData.title}
              color="greys.steel"
              lineHeight="loose"
              mb={[0, 3]}
            >
              <option value="">Select</option>
              {NAME_TITLES.map((title) => (
                <option key={title} value={title}>
                  {title}
                </option>
              ))}
            </StyledSelect>
            <FieldError error={errors.title} data-testid="title-error" />
          </FieldLabel>
          <FieldLabel htmlFor="firstName" flex="0 0 40%" mb={[5, 3]} pr={[0, 4]}>
            First name
            <Input
              id="firstName"
              name="firstName"
              defaultValue={formData.firstName}
              {...register('firstName', {
                required: 'Please enter your first name',
                pattern: {
                  value: NAME_TEXT_REGEX,
                  message: 'First name cannot contain numerical or non-English characters',
                },
              })}
              error={!!errors.firstName}
              color="greys.steel"
              lineHeight="loose"
              mb={[0, 3]}
            />
            <FieldError error={errors.firstName} data-testid="first-name-error" />
          </FieldLabel>
          <FieldLabel htmlFor="lastName" flex="1 1 auto" mb={[1, 3]}>
            Last name
            <Input
              id="lastName"
              name="lastName"
              defaultValue={formData.lastName}
              {...register('lastName', {
                required: 'Please enter your last name',
                pattern: {
                  value: NAME_TEXT_REGEX,
                  message: 'Last name cannot contain numerical or non-English characters',
                },
              })}
              error={!!errors.lastName}
              color="greys.steel"
              lineHeight="loose"
              mb={[0, 3]}
            />
            <FieldError error={errors.lastName} data-testid="last-name-error" />
          </FieldLabel>
        </Flex>
        <Text color="greys.dusty">You&apos;ll need to show ID that matches this name when you check in.</Text>
      </Box>
      <Flex flexDirection={['column', 'row']} mb={[5, 6]}>
        <FieldLabel htmlFor="emailAddress" flex="0 0 50%" mb={[5, 3]} pr={[0, 4]}>
          Email address
          <Input
            id="emailAddress"
            name="emailAddress"
            type="email"
            defaultValue={formData.emailAddress}
            {...register('emailAddress', {
              required: 'Please enter your email address',
              pattern: {
                value: EMAIL_REGEX,
                message: 'Please enter a valid email address',
              },
            })}
            error={!!errors.emailAddress}
            color="greys.steel"
            lineHeight="loose"
            mb={[1, 3]}
          />
          <FieldError error={errors.emailAddress} data-testid="email-address-error" />
          <Text color="greys.dusty">We&apos;ll send important information about your booking to this email address</Text>
        </FieldLabel>
        <FieldLabel htmlFor="phoneNumber" flex="1 1 auto" mb={[0, 3]}>
          Phone number
          <Input
            onChange={(e) => maskPhoneNumber(e.target.value)}
            id="phoneNumber"
            name="phoneNumber"
            defaultValue={formData.phoneNumber}
            {...register('phoneNumber', { required: 'Please enter your phone number' })}
            error={!!errors.phoneNumber}
            color="greys.steel"
            lineHeight="loose"
            mb={[1, 3]}
          />
          <FieldError error={errors.phoneNumber} data-testid="phone-number-error" />
          <Text color="greys.dusty">In case we need to contact you</Text>
        </FieldLabel>
      </Flex>
      <Box>
        <FieldLabel htmlFor="specialRequests" mb={[5, 3]}>
          Special requests for your stay <Text color="greys.dusty">(optional)</Text>
          <TextArea
            rows={5}
            maxLength={130}
            defaultValue={formData.specialRequests}
            error={!!errors.specialRequests}
            {...register('specialRequests', {
              maxLength: {
                value: 130,
                message: 'Special requests should be less than 130 characters',
              },
              pattern: {
                value: SAFE_TEXT_REGEX,
                message: 'Special requests cannot contain special characters',
              },
            })}
            id="specialRequests"
            name="specialRequests"
            color="greys.steel"
            lineHeight="loose"
            mb={[1, 3]}
          />
          <FieldError error={errors.specialRequests} data-testid="special-requests-error" />
        </FieldLabel>
        <Text color="greys.dusty">
          Eg. Non-smoking room, baby cot. Bed types are requests only and may not be honoured at the hotel if availability does not permit.
          Your hotel will try to fullfil these requests. Fees may apply.
        </Text>
      </Box>
    </StepForm>
  );
};

PersonalDetailsForm.propTypes = {
  step: PropTypes.shape({
    formData: PropTypes.object,
    updateFormData: PropTypes.func.isRequired,
    completeStep: PropTypes.func.isRequired,
  }),
};

PersonalDetailsForm.defaultProps = {
  step: {
    formData: {},
  },
};

export default PersonalDetailsForm;
