import React from 'react';
import PropTypes from 'prop-types';
import noop from 'lodash/noop';
import { useSelector } from 'react-redux';
import { Box } from '@qga/roo-ui/components';
import { StepHeader } from 'components/StepWizard';
import { useIsAuthenticated } from 'lib/oauth';
import PersonalDetailsForm from './PersonalDetailsForm';
import LoginPrompt from './LoginPrompt';
import PointsPrompt from './PointsPrompt';
import { ACCOUNT_MANAGEMENT_TYPES, QFF_ACCOUNT_MANAGEMENT } from 'config';
import { POINTS_REDEMPTION_ENABLED } from 'config/flags';
import { getIsClassic } from 'store/quote/quoteSelectors';

const PersonalDetails = ({ step, ...rest }) => {
  const { edit, hasState, stepNumber } = step;
  const isAuthenticated = useIsAuthenticated();
  const isAppWide = QFF_ACCOUNT_MANAGEMENT === ACCOUNT_MANAGEMENT_TYPES.APP_WIDE;
  const isClassic = useSelector(getIsClassic);

  return (
    <>
      {!isClassic && isAppWide && !isAuthenticated && <LoginPrompt />}
      {POINTS_REDEMPTION_ENABLED && isAppWide && isAuthenticated && <PointsPrompt />}

      <Box bg="white" borderRadius="default" boxShadow="hard" ref={step.containerRef} {...rest}>
        <StepHeader title="Your details" edit={edit} hasState={hasState} stepNumber={stepNumber} />
        <PersonalDetailsForm step={step} />
      </Box>
    </>
  );
};

PersonalDetails.propTypes = {
  step: PropTypes.shape({
    hasState: PropTypes.func,
    edit: PropTypes.func,
    stepNumber: PropTypes.number,
    containerRef: PropTypes.shape({ current: PropTypes.any }),
  }),
};

PersonalDetails.defaultProps = {
  step: {
    hasState: noop,
    edit: noop,
    containerRef: null,
  },
};

export default PersonalDetails;
