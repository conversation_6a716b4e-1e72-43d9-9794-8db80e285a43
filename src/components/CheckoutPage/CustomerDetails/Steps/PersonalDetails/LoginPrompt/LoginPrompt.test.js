import React from 'react';
import { mountUtils } from 'test-utils';
import LoginPrompt from './LoginPrompt';
import { useLoginUrl } from 'lib/qffAuth';
import { useDataLayer } from 'hooks/useDataLayer';

jest.mock('lib/qffAuth');
jest.mock('hooks/useDataLayer');
const emitInteractionEvent = jest.fn();

const render = () => mountUtils(<LoginPrompt />, { decorators: { store: true, theme: true } });

beforeEach(() => {
  emitInteractionEvent.mockClear();
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  useLoginUrl.mockReturnValue({ loginUrl: '/hotels/auth/callback?state=/foo' });
});

describe('login prompt details', () => {
  it('hides when POINTS_REDEMPTION_ENABLED = false', () => {
    const { findByTestId } = render();
    expect(findByTestId('login-prompt')).toEqual({});
  });
});
