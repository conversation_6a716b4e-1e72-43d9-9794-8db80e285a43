import React from 'react';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { Box, Text, Button, Flex, Image } from '@qga/roo-ui/components';
import { points } from '@qga/roo-ui/assets';
import { useLoginUrl } from 'lib/qffAuth';
import { useDataLayer } from 'hooks/useDataLayer';
import { POINTS_REDEMPTION_ENABLED } from 'config/flags';

const InfoBox = styled(Box)`
  background-color: ${themeGet('colors.white')};
  margin-bottom: ${themeGet('space.4')};
  border: ${themeGet('borders.1')};
  border-color: ${themeGet('colors.greys.alto')};
  padding: ${themeGet('space.6')} 0;
  color: ${themeGet('colors.greys.charcoal')};
  border-radius: ${themeGet('radii.default')};
`;

const LoginPrompt = () => {
  const { emitInteractionEvent } = useDataLayer();
  const { loginUrl } = useLoginUrl();

  const handleClick = async () => {
    emitInteractionEvent({ type: 'Personal Details Login Button', value: 'Personal Details Login Button Selected' });
  };

  return (
    <>
      {POINTS_REDEMPTION_ENABLED && (
        <InfoBox>
          <Flex flexDirection={['column', 'row']} alignItems="flex-start" justifyContent="flex-start" px={[2, 8]}>
            <Flex justifyContent="center" alignItems="flex-start" px={[2, 0]} pb={[6, 0]}>
              <Image m={0} width={['64px', '32px']} src={points} alt="information icon" />
            </Flex>
            <Flex flexDirection="column" px={[2, 4]}>
              <Text display="block" fontSize={['base', 'md']} data-testid="login-prompt">
                Log in to use Qantas points
              </Text>
              <Text display="block" fontSize={['base', 'md']} mb={4} color="greys.steel">
                Enjoy Qantas Frequent Flyer benefits or continue as a guest and pay with cash.
              </Text>
              <Button
                variant="primary"
                width={['100%', '114px']}
                href={loginUrl}
                onClick={handleClick}
                aria-label="Log in to your frequent flyer account"
              >
                Log In
              </Button>
            </Flex>
          </Flex>
        </InfoBox>
      )}
    </>
  );
};

export default LoginPrompt;
