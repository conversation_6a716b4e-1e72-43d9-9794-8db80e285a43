import React from 'react';
import { mountUtils } from 'test-utils';
import PersonalDetails from './PersonalDetails';
import { StepHeader } from 'components/StepWizard';
import PersonalDetailsForm from './PersonalDetailsForm';
import { getIsAuthenticated } from 'store/user/userSelectors';

import * as config from 'config';

jest.mock('components/StepWizard', () => ({ StepHeader: () => null }));
jest.mock('./PersonalDetailsForm', () => () => null);
jest.mock('store/user/userSelectors');
jest.mock('config');

const defaultProps = {
  step: {},
};

const render = (props) => mountUtils(<PersonalDetails {...defaultProps} {...props} />, { decorators: { store: true } });

beforeEach(() => {
  getIsAuthenticated.mockReturnValue(false);
  config.QFF_ACCOUNT_MANAGEMENT = jest.requireActual('config').ACCOUNT_MANAGEMENT_TYPES.APP_WIDE;
});

it('renders the StepHeader', () => {
  const { find } = render();
  expect(find(StepHeader)).toHaveProp({ ...defaultProps.step, title: 'Your details' });
});

it('renders the PersonalDetailsForm', () => {
  const { find } = render();
  expect(find(PersonalDetailsForm)).toHaveProp({ step: defaultProps.step });
});
