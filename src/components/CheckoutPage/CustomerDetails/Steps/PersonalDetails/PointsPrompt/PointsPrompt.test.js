import React from 'react';
import { mountUtils } from 'test-utils';
import PointsPrompt from './PointsPrompt';
import { useDataLayer } from 'hooks/useDataLayer';
import { getFirstName, getLastName, getPointsBalance } from 'store/user/userSelectors';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';

jest.mock('lib/qffAuth');
jest.mock('hooks/useDataLayer');
jest.mock('store/user/userSelectors');
jest.mock('config');
const emitInteractionEvent = jest.fn();

const render = () => mountUtils(<PointsPrompt />, { decorators: { store: true, theme: true } });

beforeEach(() => {
  emitInteractionEvent.mockClear();
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  getFirstName.mockReturnValue('firstName');
  getLastName.mockReturnValue('lastName');
  getPointsBalance.mockReturnValue(new Decimal(9000));
});

describe('renders the points prompt details', () => {
  it('renders the welcome text', () => {
    const { find } = render();
    expect(find('InfoBox')).toIncludeText('Available Qantas Points: 9,000 PTS');
  });

  it('renders the points balance text', () => {
    const { find } = render();
    expect(find('InfoBox')).toIncludeText('Available Qantas Points: 9,000 PTS');
  });

  it('renders the call-to-action', () => {
    const { find } = render();
    expect(find('UppercaseButton')).toIncludeText('Not You? Log Out');
  });
});
