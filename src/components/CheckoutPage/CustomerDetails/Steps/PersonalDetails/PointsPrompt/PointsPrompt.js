import React from 'react';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { Box, Text, NakedButton, Flex, Image, Icon } from '@qga/roo-ui/components';
import { iconUser } from '@qga/roo-ui/assets';
import { useSelector } from 'react-redux';
import { mediaQuery } from 'lib/styledSystem';
import { getFirstName, getLastName, getPointsBalance } from 'store/user/userSelectors';
import { useLogout } from 'lib/oauth';

const InfoBox = styled(Box)`
  background-color: ${themeGet('colors.white')};
  margin-bottom: ${themeGet('space.4')};
  border: ${themeGet('borders.1')};
  border-color: ${themeGet('colors.greys.alto')};
  padding: ${themeGet('space.6')} 0;
  color: ${themeGet('colors.greys.charcoal')};
  border-radius: ${themeGet('radii.default')};
`;

const UppercaseButton = styled(NakedButton)`
  text-transform: uppercase;
  color: ${themeGet('colors.ui.link')};
  font-weight: ${themeGet('fontWeights.bold')};
  letter-spacing: ${themeGet('letterSpacings.wide')};
`;

const LogOutSpan = styled('span')`
  color: ${themeGet('colors.greys.charcoal')};
`;

const PointsText = styled('strong')`
  display: block;

  ${mediaQuery.minWidth.md} {
    display: inline;
  }
`;

const PointsPrompt = () => {
  const firstName = useSelector(getFirstName);
  const lastName = useSelector(getLastName);
  const pointsBalance = useSelector(getPointsBalance);
  const displayPoints = new Intl.NumberFormat('en-AU').format(pointsBalance.toNumber());
  const { logout } = useLogout();

  return (
    <InfoBox>
      <Flex flexDirection={['column', 'row']} alignItems={['flex-start', 'center']} justifyContent="flex-start" px={[2, 8]}>
        <Flex justifyContent={['flex-start', 'center']} alignItems={['flex-start', 'center']} px={[2, 0]} pb={[6, 0]}>
          <Image m={0} width={['64px', '32px']} src={iconUser} alt="information icon" />
        </Flex>
        <Flex flexDirection={['column', 'row']} justifyContent="space-between" alignItems={['flex-start', 'center']} width="100%">
          <Flex flexDirection="column" px={[2, 4]}>
            <Text display="block" fontSize={['base', 'md']}>
              Welcome back {firstName} {lastName},
            </Text>
            <Text display="block" fontSize={['base', 'md']} mb={[4, 0]} color="greys.steel">
              Available Qantas Points: <PointsText>{displayPoints} PTS</PointsText>
            </Text>
          </Flex>
          <Flex alignSelf={['flex-start', 'center']} justifySelf="flex-end" px={[2, 0]}>
            <UppercaseButton onClick={logout} aria-label="Log out of your frequent flyer account">
              <LogOutSpan>Not You?</LogOutSpan> Log Out <Icon name="arrowForward" ml={2} mt={-1} />
            </UppercaseButton>
          </Flex>
        </Flex>
      </Flex>
    </InfoBox>
  );
};

export default PointsPrompt;
