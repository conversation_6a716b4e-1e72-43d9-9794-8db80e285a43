import React from 'react';
import { useForm } from 'react-hook-form';
import useFormPersist from 'react-hook-form-persist';
import { mountUtils } from 'test-utils';
import PersonalDetailsForm from './PersonalDetailsForm';
import { StepForm } from 'components/StepWizard';
import { EMAIL_REGEX, NAME_TEXT_REGEX, SAFE_TEXT_REGEX } from 'lib/enums/checkout';
import { getTitle, getFirstName, getLastName, getEmailAddress, getPhoneNumber } from 'store/user/userSelectors';
import { useIsAuthenticated } from 'lib/oauth';
import * as config from 'config';
import { getIsLoggedInFromStep } from 'components/CheckoutPage/CustomerDetails/sessionStorage';
import { FieldError } from '@qga/components';

jest.mock('react-hook-form');
jest.mock('react-hook-form-persist');
jest.mock('components/StepWizard', () => ({
  StepForm: ({ children }) => children,
}));
jest.mock('store/user/userSelectors');
jest.mock('lib/oauth');
jest.mock('config');
jest.mock('components/CheckoutPage/CustomerDetails/sessionStorage');

const formData = {};

const updateFormData = jest.fn();
const completeStep = jest.fn();
const setValue = jest.fn();
const watch = jest.fn();
const reset = jest.fn();

const defaultProps = {
  step: { hasState: () => true, edit: () => {}, formData, updateFormData, completeStep },
};

const render = (props) => mountUtils(<PersonalDetailsForm {...defaultProps} {...props} />, { decorators: { store: true } });

const handleSubmit = () => {};

const errors = {
  title: { message: 'title error' },
  firstName: { message: 'firstName error' },
  lastName: { message: 'lastName error' },
  emailAddress: { message: 'emailAddress error' },
  phoneNumber: { message: 'phoneNumber error' },
  specialRequests: { message: 'specialRequests error' },
};

let register;

beforeEach(() => {
  updateFormData.mockClear();
  register = jest.fn();
  useForm.mockReturnValue({ register, formState: { errors }, handleSubmit, setValue, watch, reset });
  useIsAuthenticated.mockReturnValue(false);
  config.QFF_ACCOUNT_MANAGEMENT = jest.requireActual('config').ACCOUNT_MANAGEMENT_TYPES.APP_WIDE;
});

it('renders the StepForm', () => {
  const { find } = render();
  expect(find(StepForm)).toHaveProp({ ...defaultProps.step, handleSubmit: expect.any(Function), completeStep: expect.any(Function) });
});

it('does not default the form data based on qff login', () => {
  render();
  expect(updateFormData).not.toHaveBeenCalled();
});

it('calls the useFormPersist hook with the expected params', () => {
  render();

  expect(useFormPersist).toHaveBeenCalledWith('personalDetailsForm', { setValue, watch });
});

describe('isLoggedInFromStep', () => {
  describe('when FALSE', () => {
    it('does not call the completeStep function', () => {
      render();

      expect(completeStep).not.toHaveBeenCalled();
    });
  });

  describe('when TRUE', () => {
    beforeEach(() => {
      getIsLoggedInFromStep.mockReturnValue(true);
    });

    it('calls the completeStep function', () => {
      render();

      expect(completeStep).toHaveBeenCalled();
    });
  });
});

describe('title', () => {
  it('renders the title Select', () => {
    const { find } = render();
    expect(find('StyledSelect[name="title"]').first()).toHaveProp({ defaultValue: formData.title, error: true });
  });

  it('registers the expected validation rule', () => {
    render();
    expect(register.mock.calls[0]).toMatchObject(['title', { required: 'Please select title' }]);
  });

  it('renders the title FieldError', async () => {
    const { find } = render();
    expect(find(FieldError).filterWhere((c) => c.prop('data-testid') === 'title-error')).toHaveProp({ error: errors.title });
  });
});

describe('firstName', () => {
  it('renders the firstName Input', () => {
    const { find } = render();
    expect(find('Input[name="firstName"]')).toHaveProp({ defaultValue: formData.firstName, error: true });
  });

  it('registers the expected validation rule', () => {
    render();
    expect(register.mock.calls[1]).toMatchObject([
      'firstName',
      {
        required: 'Please enter your first name',
        pattern: {
          value: NAME_TEXT_REGEX,
          message: 'First name cannot contain numerical or non-English characters',
        },
      },
    ]);
  });

  it('renders the firstName FieldError', async () => {
    const { find } = render();
    expect(find(FieldError).filterWhere((c) => c.prop('data-testid') === 'first-name-error')).toHaveProp({
      error: errors.firstName,
    });
  });
});

describe('lastName', () => {
  it('renders the lastName Input', () => {
    const { find } = render();
    expect(find('Input[name="lastName"]')).toHaveProp({ defaultValue: formData.lastName, error: true });
  });

  it('registers the expected validation rule', () => {
    render();
    expect(register.mock.calls[2]).toMatchObject([
      'lastName',
      {
        required: 'Please enter your last name',
        pattern: {
          value: NAME_TEXT_REGEX,
          message: 'Last name cannot contain numerical or non-English characters',
        },
      },
    ]);
  });

  it('renders the lastName FieldError', async () => {
    const { find } = render();
    expect(find(FieldError).filterWhere((c) => c.prop('data-testid') === 'last-name-error')).toHaveProp({
      error: errors.lastName,
    });
  });
});

describe('emailAddress', () => {
  it('renders the emailAddress Input', () => {
    const { find } = render();
    expect(find('Input[name="emailAddress"]')).toHaveProp({ defaultValue: formData.emailAddress, error: true });
  });

  it('registers the expected validation rule', () => {
    render();
    expect(register.mock.calls[3]).toMatchObject([
      'emailAddress',
      {
        pattern: {
          message: 'Please enter a valid email address',
          value: EMAIL_REGEX,
        },
        required: 'Please enter your email address',
      },
    ]);
  });

  it('renders the emailAddress FieldError', async () => {
    const { find } = render();
    expect(find(FieldError).filterWhere((c) => c.prop('data-testid') === 'email-address-error')).toHaveProp({
      error: errors.emailAddress,
    });
  });
});

describe('phoneNumber', () => {
  it('renders the phoneNumber Input', () => {
    const { find } = render();
    expect(find('Input[name="phoneNumber"]')).toHaveProp({ defaultValue: formData.phoneNumber, error: true });
  });

  it('registers the expected validation rule', () => {
    render();
    expect(register.mock.calls[4]).toMatchObject(['phoneNumber', { required: 'Please enter your phone number' }]);
  });

  it('renders the phoneNumber FieldError', async () => {
    const { find } = render();
    expect(find(FieldError).filterWhere((c) => c.prop('data-testid') === 'phone-number-error')).toHaveProp({
      error: errors.phoneNumber,
    });
  });

  it('rejects anything that is not a number, a space, or +', () => {
    const { find } = render();
    find('Input[name="phoneNumber"]').simulate('change', { target: { value: '+123b 456#a' } });
    expect(setValue).toHaveBeenCalledWith('phoneNumber', '+123 456');
  });
});

describe('specialRequests', () => {
  it('renders the specialRequests TextArea', () => {
    const { find } = render();
    expect(find('StyledTextarea[name="specialRequests"]')).toHaveProp({ defaultValue: formData.specialRequests });
  });

  it('registers the expected validation rule', () => {
    render();
    expect(register.mock.calls[5]).toMatchObject([
      'specialRequests',
      {
        maxLength: { message: 'Special requests should be less than 130 characters', value: 130 },
        pattern: { message: 'Special requests cannot contain special characters', value: SAFE_TEXT_REGEX },
      },
    ]);
  });

  it('renders the specialRequests FieldError', async () => {
    const { find } = render();
    expect(find(FieldError).filterWhere((c) => c.prop('data-testid') === 'special-requests-error')).toHaveProp({
      error: errors.specialRequests,
    });
  });
});

describe('when the user is authenticated', () => {
  beforeEach(() => {
    useIsAuthenticated.mockReturnValue(true);
    getTitle.mockReturnValue('mr');
    getFirstName.mockReturnValue('Alan');
    getLastName.mockReturnValue('Joyce');
    getEmailAddress.mockReturnValue('<EMAIL>');
    getPhoneNumber.mockReturnValue('0424123456');
  });

  it('defaults the form data with qff login details', () => {
    render();
    expect(updateFormData).toHaveBeenCalledWith({
      title: 'mr',
      firstName: 'Alan',
      lastName: 'Joyce',
      emailAddress: '<EMAIL>',
      phoneNumber: '0424123456',
    });
  });

  it('does NOT render the cta login prompt', () => {
    const { find } = render();
    expect(find('Login Prompt')).not.toExist();
  });

  it('renders the title select with the formData.title as the key so it can reinitialize if the title is set from the auth data', () => {
    const formData = { title: 'Mr' };
    const { find } = render({ step: { hasState: () => true, edit: () => {}, formData, updateFormData, completeStep } });
    expect(find('StyledSelect[name="title"]').first().key()).toEqual(formData.title);
  });
});

describe('special requests', () => {
  it('renders the special requests Input', () => {
    const { find } = render();
    expect(find('TextArea')).toHaveProp({ defaultValue: formData.specialRequests, error: true });
  });

  it('renders the specialRequests FieldError', async () => {
    const { find } = render();
    expect(find(FieldError).filterWhere((c) => c.prop('data-testid') === 'special-requests-error')).toHaveProp({
      error: errors.specialRequests,
    });
  });

  it('registers the expected validation rule', () => {
    render();
    expect(register.mock.calls[5]).toMatchObject([
      'specialRequests',
      {
        maxLength: { message: 'Special requests should be less than 130 characters', value: 130 },
        pattern: { message: 'Special requests cannot contain special characters', value: SAFE_TEXT_REGEX },
      },
    ]);
  });
});

describe('when QFF_ACCOUNT_MANAGEMENT is checkout only', () => {
  beforeEach(() => {
    config.QFF_ACCOUNT_MANAGEMENT = jest.requireActual('config').ACCOUNT_MANAGEMENT_TYPES.CHECKOUT_ONLY;
  });

  it('does NOT render the cta login prompt', () => {
    const { find } = render();
    expect(find('Login Prompt')).not.toExist();
  });
});
