import React from 'react';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import { mountUtils } from 'test-utils';
import PriceDetails from './PriceDetails';
import { getPointsAmount, getTravelPassAmount, getPayableNowCashAmount, getPayableLaterCashAmount } from 'store/checkout/checkoutSelectors';
import { getPayableLaterDueDate } from 'store/quote/quoteSelectors';

jest.mock('store/checkout/checkoutSelectors');
jest.mock('store/quote/quoteSelectors');

mountUtils.mockComponent('PaymentBreakdown');
mountUtils.mockComponent('PriceBreakdownModal');

const pointsAmount = new Decimal(1000);
const travelPassAmount = new Decimal(50);
const payableLaterDueDate = new Date(2020, 10, 10);
const payableNowCashAmount = new Decimal(100);
const payableLaterCashAmount = new Decimal(100);

const payableAtPropertyTotal = {
  amount: new Decimal(10),
  currency: 'AUD',
};
const property = {
  name: 'Test hotel',
};

const offer = {
  charges: {
    payableAtProperty: { total: payableAtPropertyTotal },
  },
};

const checkIn = new Date(2020, 9, 1);
const checkOut = new Date(2020, 9, 2);

const defaultProps = {
  property,
  offer,
  checkIn,
  checkOut,
};

const render = (props) => mountUtils(<PriceDetails {...defaultProps} {...props} />, { decorators: { store: true } });

beforeEach(() => {
  getPointsAmount.mockReturnValue(pointsAmount);
  getTravelPassAmount.mockReturnValue(travelPassAmount);
  getPayableNowCashAmount.mockReturnValue(payableNowCashAmount);
  getPayableLaterCashAmount.mockReturnValue(payableLaterCashAmount);

  getPayableLaterDueDate.mockReturnValue(payableLaterDueDate);
});

it('renders the PaymentBreakdown', () => {
  const { find } = render();
  expect(find('PaymentBreakdown')).toHaveProp({
    payableNowCashAmount,
    payableLaterCashAmount,
    payableLaterDueDate,
    pointsAmount,
    travelPassAmount,
    payableAtProperty: payableAtPropertyTotal,
  });
});

it('renders the PriceBreakdownModal', () => {
  const { find } = render();
  expect(find('PriceBreakdownModal')).toHaveProp({
    property,
    offer,
    checkIn,
    checkOut,
  });
});
