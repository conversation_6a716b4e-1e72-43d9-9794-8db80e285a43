import React from 'react';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import { mountUtils } from 'test-utils';
import CreditCard from './CreditCard';
import { getCreditCardErrors } from 'store/booking/bookingSelectors';
import { getPayableNowCashAmount, getPayableLaterCashAmount, getIsCreditCardValid } from 'store/checkout/checkoutSelectors';
import { getPayableLaterDueDate } from 'store/quote/quoteSelectors';

jest.mock('store/booking/bookingSelectors');
jest.mock('store/checkout/checkoutSelectors');
jest.mock('store/quote/quoteSelectors');

mountUtils.mockComponent('AdyenForm');
mountUtils.mockComponent('AdyenDetails');
mountUtils.mockComponent('BookingError');

const payableNowCashAmount = new Decimal(100);
const payableLaterCashAmount = new Decimal(100);
const payableLaterDueDate = new Date(2020, 9, 1);
const creditCardErrors = [{ error: true }];

const render = () =>
  mountUtils(<CreditCard payableNowCashAmount={payableNowCashAmount} />, {
    decorators: { store: true, theme: true },
  });

beforeEach(() => {
  getPayableNowCashAmount.mockReturnValue(payableNowCashAmount);
  getPayableLaterCashAmount.mockReturnValue(payableLaterCashAmount);
  getPayableLaterDueDate.mockReturnValue(payableLaterDueDate);
  getCreditCardErrors.mockReturnValue(creditCardErrors);
  getIsCreditCardValid.mockReturnValue(false);
});

it('renders the payableNowCashAmount', () => {
  const { find } = render();
  expect(find('Currency[data-testid="payable-now-cash-amount"]')).toHaveProp({
    amount: payableNowCashAmount,
    currency: 'AUD',
  });
});

it('renders the payableLaterCashAmount', () => {
  const { find } = render();
  expect(find('Currency[data-testid="payable-later-cash-amount"]')).toHaveProp({
    amount: payableLaterCashAmount,
    currency: 'AUD',
  });
});

it('renders the payableLaterDueDate', () => {
  const { findByTestId } = render();
  expect(findByTestId('payable-later-due-date')).toHaveText(' on Thu 1 Oct, 2020');
});

it('renders BookingError', () => {
  const { find } = render();
  expect(find('BookingError')).toHaveProp({
    errors: creditCardErrors,
    type: 'credit card',
  });
});

it('renders the AdyenForm', () => {
  const { find } = render();
  expect(find('AdyenForm')).toExist();
});

it('renders the credit card details instead of the form', () => {
  getIsCreditCardValid.mockReturnValue(true);
  const { find } = render();
  expect(find('AdyenForm')).not.toExist();
  expect(find('AdyenDetails')).toExist();
});

describe('when clicking on the edit button', () => {
  it('renders the credit card form  and hides the details', () => {
    getIsCreditCardValid.mockReturnValue(true);
    const { find, findByTestId } = render();
    findByTestId('edit-button-desktop').simulate('click');
    expect(find('AdyenForm')).toExist();
    expect(find('AdyenDetails')).not.toExist();
  });
});
