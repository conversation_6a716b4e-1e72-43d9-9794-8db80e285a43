import React from 'react';
import { mountUtils } from 'test-utils';
import _ from 'lodash';
import Loader from 'components/Loader';
import useAdyenCheckout from 'lib/hooks/useAdyenCheckout';
import AdyenFields from './AdyenFields';
import AdyenLoaderSkeleton from './AdyenLoaderSkeleton';

jest.mock('lib/hooks/useAdyenCheckout');
jest.mock(
  'components/Loader',
  () =>
    ({ children }) =>
      children,
);
jest.mock('store/ui/uiSelectors');
mountUtils.mockComponent('FieldError');

describe('AdyenFields', () => {
  const props = {
    onChange: jest.fn().mockName('onChange'),
  };
  const useAdyenCheckoutReturn = {
    isLoading: false,
    cardNumberProps: { 'data-cse': 'encryptedCardNumber', error: null, focused: false },
    expiryDateProps: { 'data-cse': 'encryptedExpiryDate', error: null, focused: false },
    securityCodeProps: { 'data-cse': 'encryptedSecurityCode', error: null, focused: false },
  };

  const render = () => mountUtils(<AdyenFields {...props} />, { decorators: { store: true, theme: true } });

  beforeEach(() => {
    jest.clearAllMocks();
    useAdyenCheckout.mockReturnValue(useAdyenCheckoutReturn);
  });

  it('calls the useAdyenCheckout hook with expected props', () => {
    render();
    expect(useAdyenCheckout.mock.calls).toMatchInlineSnapshot(`
      [
        [
          {
            "brands": [
              "mc",
              "visa",
              "amex",
            ],
            "containerElementId": "adyen-form",
            "onChange": [MockFunction onChange],
          },
        ],
      ]
    `);
  });

  it('wraps the credit card form in a Loader', () => {
    const { find } = render();
    const loaderNode = find(Loader);
    expect(loaderNode).toHaveProp({ isLoading: false, loaderComponent: AdyenLoaderSkeleton });
  });

  it('renders the form placeholders', () => {
    const { find } = render();
    const [{ containerElementId }] = useAdyenCheckout.mock.calls[0];
    expect(find(`[id="${containerElementId}"]`)).toExist();
    expect(find(`[data-cse="${useAdyenCheckoutReturn.cardNumberProps['data-cse']}"]`)).toExist();
    expect(find(`[data-cse="${useAdyenCheckoutReturn.expiryDateProps['data-cse']}"]`)).toExist();
    expect(find(`[data-cse="${useAdyenCheckoutReturn.securityCodeProps['data-cse']}"]`)).toExist();
  });

  describe('with focused fields', () => {
    describe.each`
      fieldId            | fieldProps
      ${'card-number'}   | ${'cardNumberProps'}
      ${'expiry-date'}   | ${'expiryDateProps'}
      ${'security-code'} | ${'securityCodeProps'}
    `('$fieldId', ({ fieldId, fieldProps }) => {
      it('renders the focus state', () => {
        useAdyenCheckout.mockReturnValue(
          _.merge({}, useAdyenCheckoutReturn, {
            [fieldProps]: { focused: true },
          }),
        );
        const { find } = render();
        expect(find(`[id="${fieldId}"]`).first()).toHaveProp({ focused: true });
      });

      it('renders the error state', () => {
        const error = { message: 'error message' };
        useAdyenCheckout.mockReturnValue(
          _.merge({}, useAdyenCheckoutReturn, {
            [fieldProps]: { error },
          }),
        );
        const { find } = render();
        expect(find(`[id="${fieldId}"]`).first()).toHaveProp({ error: expect.anything() });
        // findByTestId only works on DOM nodes
        expect(find(`[data-testid="${fieldId}-error"]`).first()).toHaveProp({ error });
      });
    });
  });
});
