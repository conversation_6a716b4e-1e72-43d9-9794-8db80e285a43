import React from 'react';
import { act } from 'react-dom/test-utils';
import { mountUtils } from 'test-utils';
import merge from 'lodash/merge';
import ConfirmAndPay from './ConfirmAndPay';

import { INITIAL, EDITING, COMPLETE } from 'components/StepWizard';
import { getRequiresCreditCardPayment, getIsCreditCardValid } from 'store/checkout/checkoutSelectors';
import { getStayDates, getQuote } from 'store/quote/quoteSelectors';
import { createBooking } from 'store/booking/bookingActions';
import { getIsCreating } from 'store/booking/bookingSelectors';
import { useDataLayer } from 'hooks/useDataLayer';

jest.mock('hooks/useDataLayer');
jest.mock('store/quote/quoteSelectors');
jest.mock('store/checkout/checkoutSelectors');
jest.mock('store/booking/bookingSelectors');
jest.mock('store/ui/uiSelectors');
jest.mock('hooks/useAddPaymentInfoEvent');

mountUtils.mockComponent('StepHeader');
mountUtils.mockComponent('BookingSummary');
mountUtils.mockComponent('BookingProgressDialog');
mountUtils.mockComponent('PriceDetails');
mountUtils.mockComponent('CreditCard');

const mockHasState =
  (expectedState) =>
  (...states) =>
    states.includes(expectedState);

const baseProps = {
  step: {
    updateFormData: jest.fn(),
    edit: jest.fn(),
    hasState: mockHasState(INITIAL),
    completeStep: jest.fn(),
  },
};
const emitInteractionEvent = jest.fn();

const render = (props) => mountUtils(<ConfirmAndPay {...merge({ ...baseProps }, props)} />, { decorators: { store: true, theme: true } });

const checkIn = new Date(2020, 9, 1);
const checkOut = new Date(2020, 9, 2);
const stayDates = { checkIn, checkOut };
const property = {};
const standardOffer = { type: 'standard', cancellationPolicy: { description: 'offer description' } };
const quoteWithStandardOffer = { property, offer: standardOffer };

beforeEach(() => {
  getRequiresCreditCardPayment.mockReturnValue(true);
  getStayDates.mockReturnValue(stayDates);
  getQuote.mockReturnValue(quoteWithStandardOffer);
  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

afterEach(() => {
  getIsCreating.mockReturnValue(false);
  emitInteractionEvent.mockClear();
});

[INITIAL, COMPLETE].forEach((state) => {
  describe(`with a state of ${state}`, () => {
    beforeEach(() => {
      getRequiresCreditCardPayment.mockReturnValue(false);
    });

    it('renders the StepHeader', () => {
      const { find } = render({ step: { hasState: mockHasState(state) } });
      expect(find('StepHeader')).toHaveProp({
        title: 'Confirm & pay',
        edit: baseProps.step.edit,
        hasState: baseProps.step.hasState,
        subTitle: null,
      });
    });

    it('does not render the review summary information', () => {
      const { findByTestId } = render({ step: { hasState: mockHasState(state) } });
      expect(findByTestId('review-summary')).not.toExist();
    });

    describe('when a credit card payment is required', () => {
      beforeEach(() => {
        getRequiresCreditCardPayment.mockReturnValue(true);
      });

      it('renders the StepHeader with CreditCardIcons', () => {
        const { find } = render({ step: { hasState: mockHasState(state) } });
        expect(find('StepHeader')).toHaveProp({
          title: 'Confirm & pay',
          edit: baseProps.step.edit,
          hasState: baseProps.step.hasState,
        });
        expect(find('StepHeader').props().subTitle.type.displayName).toEqual('CreditCardIcons');
      });
    });
  });
});

describe('with a state of EDITING', () => {
  it('renders the BookingSummary', () => {
    const { find } = render({ step: { hasState: mockHasState(EDITING) } });
    expect(find('BookingSummary')).toExist();
  });

  it('renders the PriceDetails', () => {
    const { find } = render({ step: { hasState: mockHasState(EDITING) } });
    expect(find('PriceDetails')).toHaveProp({
      checkIn,
      checkOut,
      property,
      offer: standardOffer,
    });
  });

  it('renders the payment button disabled', () => {
    const { findByTestId } = render({ step: { hasState: mockHasState(EDITING) } });
    expect(findByTestId('payment-button')).toHaveProp({ disabled: true });
  });

  describe('when a credit card payment is required', () => {
    beforeEach(() => {
      getRequiresCreditCardPayment.mockReturnValue(true);
    });

    it('renders the CreditCard form', () => {
      const { find } = render({ step: { hasState: mockHasState(EDITING) } });
      expect(find('CreditCard')).toExist();
    });
  });

  describe('when clicking the hotel cancellation policy textButton', () => {
    let cancellationPolicyDescription;

    beforeEach(() => {
      const { findByTestId } = render({ step: { hasState: mockHasState(EDITING) } });
      findByTestId('cancellation-policy-button').simulate('click');

      cancellationPolicyDescription = findByTestId('cancellation-policy-description');
    });

    it('opens the modal with the cancellation policy', () => {
      expect(cancellationPolicyDescription).toHaveText('Cancellation policy' + quoteWithStandardOffer.offer.cancellationPolicy.description);
    });

    it('emits a user interactive event', () => {
      expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Policy and Terms Links', value: 'Hotel Cancellation Policy Selected' });
    });
  });

  describe('when the terms and conditions are selected', () => {
    it('dispatches an event to the data layer', () => {
      const { wrapper, find } = render({ step: { hasState: mockHasState(EDITING) } });

      act(() => {
        find('Checkbox[name="acceptTerms"]').prop('onChange')();
      });
      wrapper.update();

      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Policy and Terms Links',
        value: 'Agree Opted In',
      });
    });

    describe('when the credit card is valid', () => {
      beforeEach(() => {
        getIsCreditCardValid.mockReturnValue(true);
      });

      it('enables the payment button', () => {
        const { wrapper, find, findByTestId } = render({ step: { hasState: mockHasState(EDITING) } });

        act(() => {
          find('Checkbox[name="acceptTerms"]').prop('onChange')();
        });
        wrapper.update();

        expect(findByTestId('payment-button')).toHaveProp({ disabled: false });
      });

      describe('when creating', () => {
        it('disables the payment button', () => {
          getIsCreating.mockReturnValue(true);

          const { wrapper, find, findByTestId } = render({ step: { hasState: mockHasState(EDITING) } });

          act(() => {
            find('Checkbox[name="acceptTerms"]').prop('onChange')();
          });
          wrapper.update();

          expect(findByTestId('payment-button')).toHaveProp({ disabled: true });
        });
      });

      describe('clicking the payment button', () => {
        it('dispatches the createBooking action', () => {
          const { wrapper, find, findByTestId, decorators } = render({ step: { hasState: mockHasState(EDITING) } });

          act(() => {
            find('Checkbox[name="acceptTerms"]').prop('onChange')();
          });
          wrapper.update();

          findByTestId('payment-button').simulate('click');

          expect(decorators.store.dispatch).toHaveBeenCalledWith(createBooking());
        });

        it('dispatches an event to the data layer', () => {
          const { wrapper, find, findByTestId } = render({ step: { hasState: mockHasState(EDITING) } });

          act(() => {
            find('Checkbox[name="acceptTerms"]').prop('onChange')();
          });
          wrapper.update();

          findByTestId('payment-button').simulate('click');
          expect(emitInteractionEvent).toHaveBeenCalledWith({
            type: 'Pay Now Button',
            value: 'Button Selected',
          });
        });
      });
    });
  });

  it('renders the BookingProgressDialog while the booking is being created', () => {
    getIsCreating.mockReturnValue(true);
    const { find } = render({ step: { hasState: mockHasState(EDITING) } });
    expect(find('BookingProgressDialog')).toExist();
  });

  it('does not render the BookingProgressDialog when the booking is not being created', () => {
    getIsCreating.mockReturnValue(false);
    const { find } = render({ step: { hasState: mockHasState(EDITING) } });
    expect(find('BookingProgressDialog')).not.toExist();
  });
});
