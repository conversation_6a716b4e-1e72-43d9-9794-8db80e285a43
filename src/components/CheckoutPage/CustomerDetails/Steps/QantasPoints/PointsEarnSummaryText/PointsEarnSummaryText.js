import React from 'react';
import PropTypes from 'prop-types';
import { Box, Flex, Text } from '@qga/roo-ui/components';
import PointsClubModal from 'components/PointsClubModal';
import PointsEarnDisplay from 'components/PointsEarnDisplay';
import PointsPerDollar from 'components/PointsPerDollar';

const PointsEarnSummaryText = ({ pointsEarned, pointsPerDollar, pointsClubLevel }) => {
  const totalPointsEarned = pointsEarned?.qffPoints?.total;

  return (
    <Box color="greys.charcoal">
      {totalPointsEarned === 0 && (
        <Flex flexDirection="row" flexWrap="wrap" justifyContent={['center', 'normal']} alignItems="center">
          You will not earn Qantas Points when using points
        </Flex>
      )}
      {totalPointsEarned > 0 && (
        <Flex flexDirection="row" flexWrap="wrap" justifyContent={['center', 'normal']} alignItems="center">
          You&apos;ll earn&nbsp;
          <PointsEarnDisplay
            total={pointsEarned.qffPoints.total}
            base={pointsEarned.qffPoints.base}
            fontSize={['sm', 'base']}
            data-testid="total-qff-points"
          />{' '}
          <Text fontWeight="bold" fontSize={['sm', 'base']}>
            &nbsp;PTS&nbsp;•&nbsp;
          </Text>
          <PointsPerDollar pointsPerDollar={pointsPerDollar} fontSize={['sm', 'base']} />
          &nbsp;on your booking
          {pointsClubLevel && (
            <>
              {` as a `}
              <PointsClubModal>
                <Text fontSize={['sm', 'base']} textDecoration="underline" data-testid="points-club-modal-link" lineHeight="tight">
                  &nbsp;Points Club&nbsp;
                </Text>
              </PointsClubModal>
              {` member,`}
            </>
          )}
          &nbsp;plus&nbsp;
          <PointsEarnDisplay
            total={pointsEarned.qbrPoints.total}
            fontSize={['sm', 'base']}
            data-testid="total-qbr-points"
            fontWeight="normal"
          />
          &nbsp;PTS for your business**.
        </Flex>
      )}
    </Box>
  );
};

PointsEarnSummaryText.propTypes = {
  pointsEarned: PropTypes.object.isRequired,
  pointsClubLevel: PropTypes.object,
  pointsPerDollar: PropTypes.number.isRequired,
};

PointsEarnSummaryText.defaultProps = {
  pointsClubLevel: {},
};
export default PointsEarnSummaryText;
