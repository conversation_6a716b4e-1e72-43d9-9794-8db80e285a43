import React from 'react';
import { mountUtils } from 'test-utils';
import PointsEarnSummaryText from './PointsEarnSummaryText';

mountUtils.mockComponent('PointsEarnDisplay');
mountUtils.mockComponent('PointsClubModal');
mountUtils.mockComponent('PointsPerDollar');

const pointsEarned = {
  qffPoints: { total: 1234 },
  qbrPoints: { total: 234 },
};

const pointsPerDollar = 3;
const pointsClubLevel = {};

const defaultProps = { pointsEarned, pointsClubLevel, pointsPerDollar };
const decorators = { helmet: true, router: true, store: true };
const render = (props = {}) =>
  mountUtils(<PointsEarnSummaryText {...defaultProps} {...props} />, {
    decorators,
  });

describe('<PointsEarnSummaryText />', () => {
  it('renders PointsEarnDisplay', () => {
    expect(render().find('PointsEarnDisplay')).toExist();
  });

  it('renders PointsClubModal', () => {
    expect(render().find('PointsClubModal')).toExist();
  });

  it('renders PointsPerDollar', () => {
    expect(render().find('PointsPerDollar')).toExist();
  });

  describe('when user is using points or offer is classic', () => {
    it('renders the appropriate message', () => {
      expect(
        render({
          pointsEarned: {
            qffPoints: { total: 0 },
          },
        }).find('Flex'),
      ).toHaveText('You will not earn Qantas Points when using points');
    });
  });
});
