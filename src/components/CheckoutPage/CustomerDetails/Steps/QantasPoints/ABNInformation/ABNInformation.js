import React, { Fragment, useCallback } from 'react';
import { Text, Link, Box } from '@qga/roo-ui/components';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import Modal from 'components/Modal';
import TextButton from 'components/TextButton';
import { useModal } from 'lib/hooks';
import { useDataLayer } from 'hooks/useDataLayer';
import { PRIVACY_AND_SECURITY_URL } from 'config';

const PrivacyPolicyLink = styled(Link)`
  color: ${themeGet('colors.red')};
  font-size: ${themeGet('fontSizes.base')};
  text-decoration: underline;
  padding-left: ${themeGet('space.1')};
`;

const ABNInformation = () => {
  const { openModal, modalProps } = useModal();
  const { emitInteractionEvent } = useDataLayer();
  const handleOnClick = useCallback(() => {
    openModal();
    emitInteractionEvent({ type: 'Business Rewards', value: 'Learn Link Selected' });
  }, [openModal, emitInteractionEvent]);

  return (
    <Fragment>
      <TextButton
        color="greys.steel"
        hoverColor="greys.steel"
        textDecoration="underline"
        onClick={handleOnClick}
        py={[0, 1]}
        data-testid="qantas-business-rewards-link"
        fontSize="sm"
      >
        Learn about Qantas Business Rewards
      </TextButton>
      {modalProps.isOpen && (
        <Modal {...modalProps} padding="0" title="Earn Qantas Points for your business">
          <Box p={6}>
            <Text display="block">
              Add your Qantas Business Rewards membership ABN number here to earn Qantas Points for your business on top of Qantas Points
              for the traveller on this stay.
            </Text>
            <Text display="block" pt={2} data-testid="privacy-policy-statement">
              By entering your ABN, admin users for this ABN&apos;s Qantas Business Rewards account will be able to view and modify your
              booking. See our
              <PrivacyPolicyLink
                pl={2}
                href={PRIVACY_AND_SECURITY_URL}
                rel="noopener noreferrer"
                target="_blank"
                aria-label="View privacy policy in a new tab"
                data-testid="privacy-policy-link"
              >
                privacy policy
              </PrivacyPolicyLink>
            </Text>
          </Box>
        </Modal>
      )}
    </Fragment>
  );
};

export default ABNInformation;
