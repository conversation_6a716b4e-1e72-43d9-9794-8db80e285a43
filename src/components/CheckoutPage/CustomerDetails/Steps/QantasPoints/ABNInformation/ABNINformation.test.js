import React from 'react';
import { mountUtils } from 'test-utils';
import ABNInformation from './ABNInformation';
import Modal from 'components/Modal';
import { useDataLayer } from 'hooks/useDataLayer';

jest.mock('hooks/useDataLayer');
jest.mock('store/ui/uiSelectors');

const decorators = { theme: true, store: true };
const render = () => mountUtils(<ABNInformation />, { decorators });
const emitInteractionEvent = jest.fn();

describe('<ABNInformation />', () => {
  describe('Clicking the Qantas Business Rewards Link', () => {
    beforeEach(() => {
      useDataLayer.mockReturnValue({ emitInteractionEvent });
    });

    it('renders the Modal with text and title', () => {
      const { find, findByTestId, wrapper, findByText } = render();
      findByText('Learn about Qantas Business Rewards').simulate('click');
      wrapper.update();

      expect(find(Modal)).toHaveProp({ title: 'Earn Qantas Points for your business' });
      expect(
        findByText(
          'Add your Qantas Business Rewards membership ABN number here to earn Qantas Points for your business on top of Qantas Points for the traveller on this stay.',
        ),
      ).toExist();

      expect(findByTestId('privacy-policy-statement')).toExist();
    });

    it('renders privacy policy link', () => {
      const { findByTestId, wrapper, findByText } = render();
      findByText('Learn about Qantas Business Rewards').simulate('click');
      wrapper.update();
      expect(findByTestId('privacy-policy-link')).toExist();
    });

    it('dispatches an event to the data layer', () => {
      const { findByText } = render();
      findByText('Learn about Qantas Business Rewards').simulate('click');
      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Business Rewards',
        value: 'Learn Link Selected',
      });
    });
  });
});
