import React from 'react';
import { mountUtils } from 'test-utils';
import QantasPoints from './QantasPoints';
import { StepHeader } from 'components/StepWizard';
import QantasPointsForm from './QantasPointsForm';

jest.mock('components/StepWizard', () => ({
  StepHeader: () => null,
}));

jest.mock('./QantasPointsForm', () => () => null);

const defaultProps = {
  step: {},
};

const render = (props) => mountUtils(<QantasPoints {...defaultProps} {...props} />);

it('renders the StepHeader', () => {
  const { find } = render();
  expect(find(StepHeader)).toHaveProp({ ...defaultProps.step, title: 'Earn Qantas Points' });
});

it('renders the QantasPointsForm', () => {
  const { find } = render();
  expect(find(QantasPointsForm)).toHaveProp({ step: defaultProps.step });
});
