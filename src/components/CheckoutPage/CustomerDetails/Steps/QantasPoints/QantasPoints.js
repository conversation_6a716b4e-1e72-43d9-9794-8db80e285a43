import React from 'react';
import PropTypes from 'prop-types';
import noop from 'lodash/noop';
import { Box } from '@qga/roo-ui/components';
import { StepHeader } from 'components/StepWizard';
import QantasPointsForm from './QantasPointsForm';

const QantasPoints = ({ step, ...rest }) => {
  const { edit, hasState, stepNumber } = step;

  return (
    <Box bg="white" borderRadius="default" boxShadow="hard" ref={step.containerRef} {...rest}>
      <StepHeader title="Earn Qantas Points" edit={edit} hasState={hasState} stepNumber={stepNumber} />
      <QantasPointsForm step={step} />
    </Box>
  );
};

QantasPoints.propTypes = {
  step: PropTypes.shape({
    hasState: PropTypes.func,
    edit: PropTypes.func,
    stepNumber: PropTypes.number,
    containerRef: PropTypes.shape({ current: PropTypes.any }),
  }),
};

QantasPoints.defaultProps = {
  step: {
    hasState: noop,
    edit: noop,
    containerRef: null,
  },
};

export default QantasPoints;
