import React from 'react';
import { useForm } from 'react-hook-form';
import useFormPersist from 'react-hook-form-persist';
import { pointsClubPlus, pointsClub } from '@qga/roo-ui/logos';
import { mountUtils } from 'test-utils';
import QantasPointsForm from './QantasPointsForm';
import { StepForm } from 'components/StepWizard';
import { QFF_OR_EMPTY_REGEX, ABN_OR_EMPTY_REGEX } from 'lib/enums/checkout';
import { useLoginUrl } from 'lib/qffAuth';
import { getMemberId, getPointsClubLevel } from 'store/user/userSelectors';
import { useIsAuthenticated } from 'lib/oauth';
import { POINTS_CLUB, POINTS_CLUB_PLUS } from 'lib/enums/pointsClub';
import { useDataLayer } from 'hooks/useDataLayer';
import useCheckoutPointsEarned from 'components/CheckoutPage/hooks/useCheckoutPointsEarned';
import { getQuote } from 'store/quote/quoteSelectors';
import { act } from 'react-test-renderer';
import { FieldError } from '@qga/components';

jest.mock('react-hook-form');
jest.mock('react-hook-form-persist');
jest.mock('components/StepWizard', () => ({
  StepForm: ({ children }) => children,
}));
jest.mock('store/user/userSelectors');
jest.mock('store/quote/quoteSelectors');
jest.mock('lib/qffAuth');
jest.mock('hooks/useDataLayer');
jest.mock('components/CheckoutPage/hooks/useCheckoutPointsEarned');
jest.mock('store/ui/uiSelectors');
jest.mock('lib/oauth');

mountUtils.mockComponent('ABNInformation');
mountUtils.mockComponent('QFFInformation');
mountUtils.mockComponent('PointsEarnSummaryText');

const formData = {};

const completeStep = jest.fn();
const updateFormData = jest.fn();
const setValue = jest.fn();
const watch = jest.fn();

const emitInteractionEvent = jest.fn();

const defaultProps = {
  step: { hasState: () => true, edit: () => {}, formData, updateFormData, completeStep },
};

const render = (props) => {
  return mountUtils(<QantasPointsForm {...defaultProps} {...props} />, {
    decorators: { store: true, theme: true, router: true },
  });
};

const handleSubmit = () => {};

const errors = {
  qffNumber: { message: 'qffNumber error' },
  abn: { message: 'abn error' },
};

const pointsEarned = {
  qffPoints: { total: 1234 },
  qbrPoints: { total: 234 },
};

const quote = {
  offer: {
    pointsEarned,
  },
};

let register;

beforeEach(() => {
  jest.clearAllMocks();
  updateFormData.mockClear();
  emitInteractionEvent.mockClear();
  useIsAuthenticated.mockReturnValue(false);
  register = jest.fn();
  useForm.mockReturnValue({ register, formState: { errors }, handleSubmit, setValue, watch });
  getQuote.mockReturnValue(quote);
  useCheckoutPointsEarned.mockReturnValue({
    pointsEarned,
  });
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  useLoginUrl.mockReturnValue({ loginUrl: '/hotels/auth/callback?state=/foo' });
});

it('renders the StepForm', () => {
  const { find } = render();
  expect(find(StepForm)).toHaveProp({ ...defaultProps.step, handleSubmit: expect.any(Function), completeStep: expect.any(Function) });
});

it('calls useCheckoutPointsEarned with the quote pointsEarned', () => {
  render();
  expect(useCheckoutPointsEarned).toHaveBeenCalledWith({ pointsEarned });
});

it('calls the useFormPersist hook with the expected params', () => {
  render();

  expect(useFormPersist).toHaveBeenCalledWith('qantasPointsForm', { setValue, watch }, { onDataRestored: expect.any(Function) });
});

describe('points earned', () => {
  it('should renders <PointsPerDollar>', () => {
    const { find } = render();
    expect(find('PointsEarnSummaryText')).toExist();
  });
});

describe('qffNumber', () => {
  it('renders the qffNumber Input', () => {
    const { find } = render();
    expect(find('Input[name="qffNumber"]')).toHaveProp({ defaultValue: formData.qffNumber, error: true });
  });

  it('registers the expected validation rule', () => {
    render();
    expect(register.mock.calls[0]).toMatchObject([
      'qffNumber',
      { pattern: { message: 'Please enter a valid Frequent Flyer Number', value: QFF_OR_EMPTY_REGEX } },
    ]);
  });

  it('renders the qffNumber FieldError', async () => {
    const { find } = render();
    expect(find(FieldError).filterWhere((c) => c.prop('data-testid') === 'qff-number-error')).toHaveProp({
      error: errors.qffNumber,
    });
  });

  it('renders the qff info link', async () => {
    const { find } = render();
    expect(find('QFFInformation')).toExist();
  });
});

describe('points club logged out UI', () => {
  it('does NOT render a login button when logged in', async () => {
    useIsAuthenticated.mockReturnValue(true);
    const { find } = render();
    const loginCTA = find('[data-testid="points-club-banner-login-cta"]');
    expect(loginCTA).not.toExist();
  });

  it('does render a login button when logged out', async () => {
    const { find } = render();
    const loginCTA = find('[data-testid="points-club-banner-login-cta"]');
    expect(loginCTA).toExist();
  });

  it('dispatches an event to the data layer when the login link is clicked', async () => {
    const { findByTestId } = render();

    const loginLink = findByTestId('points-club-banner-login-cta');
    loginLink.simulate('click');
    await flushPromises();

    expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Points Club Login Now Text', value: 'Login Now Link Selected' });
  });
});

describe('points club logged in UI', () => {
  const qffNumber = 'qffNumber';

  beforeEach(() => {
    useIsAuthenticated.mockReturnValue(true);
    getMemberId.mockReturnValue(qffNumber);
  });

  it('does not render points club specific images or text when logged out', () => {
    useIsAuthenticated.mockReturnValue(false);
    getPointsClubLevel.mockReturnValue(null);

    const { find } = render();
    expect(find('PointsClubImage')).not.toExist();
  });

  it('renders the points club logo', () => {
    getPointsClubLevel.mockReturnValue(POINTS_CLUB);

    const { find } = render();
    expect(find('PointsClubImage')).toHaveProp({ src: pointsClub, alt: 'Points Club Logo' });
  });

  it('renders the points club plus logo', () => {
    getPointsClubLevel.mockReturnValue(POINTS_CLUB_PLUS);

    const { find } = render();
    expect(find('PointsClubImage')).toHaveProp({ src: pointsClubPlus, alt: 'Points Club Logo' });
  });

  it('renders the points club tooltip of the QFF number is changed', () => {
    getPointsClubLevel.mockReturnValue(POINTS_CLUB_PLUS);

    const { find, wrapper } = render();
    find('Input[name="qffNumber"]').simulate('change', { target: { value: 'qffNumber1' } });
    wrapper.update();
    expect(find('[data-testid="qff-points-club-notice"]').first()).toHaveText(
      'Please note, only Qantas Points Club members are eligible for Points Club bonus points.',
    );
  });

  it('hides the points club tooltip of the QFF number is restored', () => {
    getPointsClubLevel.mockReturnValue(POINTS_CLUB_PLUS);

    const { find, wrapper } = render();
    find('Input[name="qffNumber"]').simulate('change', { target: { value: 'qffNumber1' } });
    wrapper.update();
    expect(find('[data-testid="qff-points-club-notice"]')).toExist();
    find('Input[name="qffNumber"]').simulate('change', { target: { value: qffNumber } });
    wrapper.update();
    expect(find('[data-testid="qff-points-club-notice"]')).not.toExist();
  });
});

describe('abn', () => {
  it('renders the abn Input', () => {
    const { find } = render();
    expect(find('Input[name="abn"]')).toHaveProp({ defaultValue: formData.abn, error: true });
  });

  it('registers the expected validation rule', () => {
    render();
    expect(register.mock.calls[1]).toMatchObject([
      'abn',
      { pattern: { message: 'Please enter a valid 11-digit ABN', value: ABN_OR_EMPTY_REGEX } },
    ]);
  });

  it('renders the abn FieldError', async () => {
    const { find } = render();
    expect(find(FieldError).filterWhere((c) => c.prop('data-testid') === 'abn-error')).toHaveProp({
      error: errors.abn,
    });
  });

  it('renders the abn info link', async () => {
    const { find } = render();
    expect(find('ABNInformation')).toExist();
  });
});

describe('when the user is authenticated', () => {
  const qffNumber = 'qffNumber';

  beforeEach(() => {
    useIsAuthenticated.mockReturnValue(true);
    getMemberId.mockReturnValue(qffNumber);
  });

  describe('when no qffNumber has been provided', () => {
    it('defaults the form data with qff login details', () => {
      render();
      act(() => {
        const { onDataRestored } = useFormPersist.mock.calls[0][2];

        onDataRestored({ qffNumber: '' });
      });

      expect(setValue).toHaveBeenCalledWith('qffNumber', qffNumber);
    });
  });

  describe('when a qffNumber has previously been provided', () => {
    it('defaults the form data with qff login details', () => {
      render();
      act(() => {
        const { onDataRestored } = useFormPersist.mock.calls[0][2];

        onDataRestored({ qffNumber: '1234' });
      });

      expect(setValue).not.toHaveBeenCalledWith('qffNumber', qffNumber);
    });
  });
});
