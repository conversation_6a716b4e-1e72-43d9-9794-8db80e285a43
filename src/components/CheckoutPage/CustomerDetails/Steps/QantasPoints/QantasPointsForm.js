import styled from '@emotion/styled';
import React, { useCallback, useState } from 'react';
import PropTypes from 'prop-types';
import { useForm } from 'react-hook-form';
import useFormPersist from 'react-hook-form-persist';
import { useSelector } from 'react-redux';
import { Box, Input, Flex, Text, Image, Link } from '@qga/roo-ui/components';
import { pointsClub, pointsClubPlus, qantasQff } from '@qga/roo-ui/logos';
import { StepForm } from 'components/StepWizard';
import { QFF_OR_EMPTY_REGEX, ABN_OR_EMPTY_REGEX, CHECKOUT_FORM_STEPS } from 'lib/enums/checkout';
import { useLoginUrl } from 'lib/qffAuth';
import { FieldLabel, FieldError } from '@qga/components';
import TextButton from 'components/TextButton';
import { getMemberId, getPointsClubLevel } from 'store/user/userSelectors';
import { getQuote } from 'store/quote/quoteSelectors';
import ABNInformation from './ABNInformation';
import QFFInformation from './QFFInformation';
import { mediaQuery } from 'lib/styledSystem';
import { POINTS_CLUB_PLUS } from 'lib/enums/pointsClub';
import { themeGet } from 'styled-system';
import { useDataLayer } from 'hooks/useDataLayer';
import PointsEarnSummaryText from './PointsEarnSummaryText/PointsEarnSummaryText';
import useCheckoutPointsEarned from 'components/CheckoutPage/hooks/useCheckoutPointsEarned';
import { POINTS_PER_DOLLAR_DEFAULT } from 'config';
import { useIsAuthenticated } from 'lib/oauth';

const TotalPointsContainer = styled(Flex)`
  padding-bottom: ${themeGet('space.4')};
  border-bottom: ${themeGet('borders.1')} ${themeGet('colors.greys.alto')};
  text-align: center;
  color: ${themeGet('colors.greys.charcoal')};
  flex-direction: column;
  border-radius: ${themeGet('radii.default')};
  ${mediaQuery.minWidth.md} {
    flex-direction: row;
    margin-bottom: ${themeGet('space.7')};
    padding: ${themeGet('space.3')} ${themeGet('space.5')};
    border: ${themeGet('borders.1')} ${themeGet('colors.greys.alto')};
  }
`;

const PointsClubImage = styled(Image)`
  margin: 0 0 ${themeGet('space.4')} ${themeGet('space.4')};
  padding-left: ${themeGet('space.4')};
  border-left: 1px solid ${themeGet('colors.greys.alto')};
  width: 125px;
  height: 54px;

  ${mediaQuery.minWidth.md} {
    margin-bottom: 0;
    width: 0;
  }
`;

const QantasPointsForm = ({ step }) => {
  const {
    register,
    formState: { errors },
    handleSubmit,
    setValue,
    watch,
  } = useForm({ mode: 'onBlur' });
  const { formData } = step;
  const isAuthenticated = useIsAuthenticated();
  const qffNumber = useSelector(getMemberId);
  const { offer } = useSelector(getQuote);
  const { pointsEarned } = useCheckoutPointsEarned({ pointsEarned: offer.pointsEarned });
  const pointsClubLevel = useSelector(getPointsClubLevel);
  const [isQffInputDirty, setQffInputDirty] = useState(false);
  const isPointsClubPlus = pointsClubLevel === POINTS_CLUB_PLUS;
  const { loginUrl } = useLoginUrl();

  const pointsPerDollar = pointsEarned?.maxQffEarnPpd ?? POINTS_PER_DOLLAR_DEFAULT;
  const { emitInteractionEvent } = useDataLayer();
  const completeStep = useCallback(
    (data) => {
      step.completeStep(data);
      emitInteractionEvent({ type: 'Continue Button', value: 'Earn Qantas Points Button Selected' });
      if (data.qffNumber) {
        emitInteractionEvent({ type: 'Frequent Flyer Number', value: 'Number Added' });
      }
      if (data.abn) {
        emitInteractionEvent({ type: 'Business Rewards', value: 'ABN Added' });
      }
    },
    [step, emitInteractionEvent],
  );

  const qffNumberChanged = useCallback(
    (event) => {
      if (pointsClubLevel) {
        setQffInputDirty(event.target.value !== qffNumber);
      }
    },
    [qffNumber, pointsClubLevel],
  );

  const showLoginModal = () => {
    emitInteractionEvent({ type: 'Points Club Login Now Text', value: 'Login Now Link Selected' });
  };

  const onDataRestored = ({ qffNumber: persistedQffNumber }) => {
    if (isAuthenticated && !persistedQffNumber && qffNumber) {
      setValue('qffNumber', qffNumber);
    }
  };

  useFormPersist(CHECKOUT_FORM_STEPS.QANTAS_POINTS, { setValue, watch }, { onDataRestored });

  return (
    <StepForm {...step} completeStep={completeStep} handleSubmit={handleSubmit}>
      <Box mb={[4, 8]}>
        <TotalPointsContainer data-testid="qff-and-abn-points" mx={[1, 0]}>
          <Flex flexWrap={['wrap', 'wrap', 'nowrap']} justifyContent={['center', 'center', 'normal']} alignItems="center" lineHeight="16px">
            <Image
              src={qantasQff}
              alt="Qantas Frequent Flyer Logo"
              mb={[4, 4, 0]}
              height={['32px', 'initial']}
              width={['125px', 'initial']}
            />
            {pointsClubLevel && <PointsClubImage src={isPointsClubPlus ? pointsClubPlus : pointsClub} alt="Points Club Logo" />}
            <Text fontSize={['sm', 'base']} textAlign="start" ml={[0, 9]} lineHeight="16px">
              <PointsEarnSummaryText pointsEarned={pointsEarned} pointsPerDollar={pointsPerDollar} />
            </Text>
          </Flex>
        </TotalPointsContainer>
        {!isAuthenticated && (
          <Box mt={3}>
            <Text fontSize="sm" color="greys.steel">
              Are you a Points Club member?
            </Text>
            &nbsp;
            <TextButton as={Link} href={loginUrl} onClick={showLoginModal} fontSize="sm" data-testid="points-club-banner-login-cta">
              Login now
            </TextButton>
            &nbsp;
            <Text fontSize="sm" color="greys.steel">
              to access more bonus points on your hotel booking.
            </Text>
          </Box>
        )}
        <Flex flexDirection={['column', 'row']} mt={[4, 7]}>
          <FieldLabel htmlFor="qffNumber" flex="1 1 0" mr={[0, 4]} mb={[5, 3]}>
            Qantas Frequent Flyer number <Text color="greys.dusty">(optional)</Text>
            <Box position="relative">
              <Input
                id="qffNumber"
                name="qffNumber"
                defaultValue={qffNumber}
                {...register('qffNumber', {
                  pattern: { value: QFF_OR_EMPTY_REGEX, message: 'Please enter a valid Frequent Flyer Number' },
                })}
                error={!!errors.qffNumber}
                onChange={qffNumberChanged}
                color="greys.steel"
                lineHeight="loose"
              />
            </Box>
            <FieldError error={errors.qffNumber} data-testid="qff-number-error" />
            {isQffInputDirty && (
              <Text color="greys.dusty" display="block" data-testid="qff-points-club-notice">
                Please note, only Qantas Points Club members are eligible for Points Club bonus points.
              </Text>
            )}
            <QFFInformation />
          </FieldLabel>
          <FieldLabel htmlFor="abn" flex="1 1 0" mb={[0, 3]}>
            ABN for business trips <Text color="greys.dusty">(optional)</Text>
            <Box position="relative">
              <Input
                id="abn"
                name="abn"
                defaultValue={formData.abn}
                {...register('abn', { pattern: { value: ABN_OR_EMPTY_REGEX, message: 'Please enter a valid 11-digit ABN' } })}
                error={!!errors.abn}
                color="greys.steel"
                lineHeight="loose"
              />
            </Box>
            <FieldError error={errors.abn} data-testid="abn-error" />
            <ABNInformation />
          </FieldLabel>
        </Flex>
      </Box>
    </StepForm>
  );
};

QantasPointsForm.propTypes = {
  step: PropTypes.shape({
    formData: PropTypes.object,
    updateFormData: PropTypes.func.isRequired,
    completeStep: PropTypes.func.isRequired,
  }),
};

QantasPointsForm.defaultProps = {
  step: {
    formData: {},
  },
};

export default QantasPointsForm;
