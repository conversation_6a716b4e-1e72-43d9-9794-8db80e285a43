import React, { useEffect, useCallback } from 'react';
import get from 'lodash/get';
import { useSelector, useDispatch } from 'react-redux';
import { Flex, Box, Button, Text } from '@qga/roo-ui/components';
import { useDialog } from 'components/Dialog';
import { getPriceIncreaseErrors, getProperty, getOfferCharges as getBookingCharges, getRoomType } from 'store/booking/bookingSelectors';
import { clearBooking } from 'store/booking/bookingActions';
import { getCharges as getQuoteCharges, getIsClassic } from 'store/quote/quoteSelectors';
import { clearQuote, createQuote } from 'store/quote/quoteActions';
import Currency from 'components/Currency';
import Image from 'components/Image/Image';
import ErrorHeading from 'components/CheckoutPage/BookingErrorDialogs/ErrorHeading';
import { useDataLayer } from 'hooks/useDataLayer';

const PriceIncreaseDialog = () => {
  const dispatch = useDispatch();
  const { emitInteractionEvent } = useDataLayer();
  const priceIncreaseErrors = useSelector(getPriceIncreaseErrors);
  const property = useSelector(getProperty);
  const roomType = useSelector(getRoomType);
  const bookingCharges = useSelector(getBookingCharges);
  const quoteCharges = useSelector(getQuoteCharges);
  const isClassic = useSelector(getIsClassic);
  const { Dialog, isOpen, openDialog, closeDialog } = useDialog();

  const imageSrc = get(property, 'mainImage.urlMedium');
  const bookingTotal = get(bookingCharges, 'payableAtBooking.total');
  const quoteTotal = get(quoteCharges, 'payableAtBooking.total');
  const roomTypeName = get(roomType, 'name');
  const hasErrors = !!priceIncreaseErrors.length;

  useEffect(() => {
    if (hasErrors) {
      openDialog();
      emitInteractionEvent({ type: 'Booking Errors', value: 'Price Increase Error Showed' });
    }
    if (!hasErrors) closeDialog();
  }, [isOpen, openDialog, closeDialog, hasErrors, emitInteractionEvent]);

  const handleContinueClick = useCallback(() => {
    //clearing the booking has the effect of closing the dialog as the errors will be also been cleared
    dispatch(clearBooking());
    dispatch(createQuote());

    emitInteractionEvent({ type: 'Booking Errors', value: 'Price Increase Accept Selected' });
  }, [dispatch, emitInteractionEvent]);

  const handleChangeClick = useCallback(() => {
    dispatch(clearBooking());
    dispatch(clearQuote());
    emitInteractionEvent({ type: 'Booking Errors', value: 'Price Increase Change Selected' });
    window.history.back();
  }, [dispatch, emitInteractionEvent]);

  if (!hasErrors) return null;

  // We don't get the classic price back from the failed booking in points. As this is an edge case and would happen
  // very rarely, downgrade the UX with less detail rather than add technical complexity.
  const message = isClassic
    ? 'The total price for your booking has changed. Please refresh price details or change your selection'
    : 'The total price for your booking has changed. Please review and accept the price update, or change your selection.';

  const continueButtonText = isClassic ? 'Refresh price' : 'Accept and continue';

  return (
    <Dialog>
      <ErrorHeading>Price Update Alert</ErrorHeading>
      <Box mb={[6, 8]}>
        <Text display="inline">{message}</Text>
      </Box>
      {!isClassic && (
        <Box border={1} borderRadius="default" borderColor="greys.alto" p={3} mb={8}>
          <Flex flexDirection={['column', 'row']}>
            <Box mr={[0, 4]} mb={[3, 0]}>
              <Image height={['72px', '100%']} width={['auto', '100px']} src={imageSrc} alt={roomTypeName} />
            </Box>
            <Box flex="1 1 auto">
              <Text as="p" mt={0} mb={2} fontSize={['base', 'md']} fontWeight="bold">
                {roomTypeName}
              </Text>

              <Box maxWidth="300px">
                <Flex alignContent="center" justifyContent="space-between">
                  <Text fontSize={['sm', 'base']} fontWeight="bold">
                    New booking total:
                  </Text>
                  <Currency
                    amount={bookingTotal.amount}
                    currency={bookingTotal.currency}
                    lineHeight="loose"
                    fontSize={['sm', 'base']}
                    fontWeight="bold"
                  />
                </Flex>
                <Flex alignContent="center" justifyContent="space-between">
                  <Text fontSize={['sm', 'base']}>Previous booking total:</Text>
                  <Currency amount={quoteTotal.amount} currency={quoteTotal.currency} lineHeight="loose" fontSize={['sm', 'base']} />
                </Flex>
              </Box>
            </Box>
          </Flex>
        </Box>
      )}
      <Button variant="primary" width={['100%', 'auto']} mr={[0, 2]} onClick={handleContinueClick}>
        {continueButtonText}
      </Button>
      <Button variant="inverted" width={['100%', 'auto']} onClick={handleChangeClick}>
        Change selection
      </Button>
    </Dialog>
  );
};

PriceIncreaseDialog.displayName = 'PriceIncreaseDialog';

export default PriceIncreaseDialog;
