import React from 'react';
import { mountUtils } from 'test-utils';
import PriceIncreaseDialog from './PriceIncreaseDialog';
import { getPriceIncreaseErrors, getProperty, getOfferCharges, getRoomType } from 'store/booking/bookingSelectors';
import { clearBooking } from 'store/booking/bookingActions';
import { clearQuote, createQuote } from 'store/quote/quoteActions';
import { getCharges, getIsClassic } from 'store/quote/quoteSelectors';
import { useDialog } from 'components/Dialog';
import { useDataLayer } from 'hooks/useDataLayer';

jest.mock('store/booking/bookingSelectors');
jest.mock('store/quote/quoteSelectors');
jest.mock('components/Dialog');
jest.mock('hooks/useDataLayer');

mountUtils.mockComponent('Image');

const render = () => mountUtils(<PriceIncreaseDialog />, { decorators: { store: true, theme: true } });

const property = { mainImage: { urlMedium: 'imageUrl' } };
const roomType = { name: 'Room Name' };
const bookingCharges = { payableAtBooking: { total: { amount: '379.00', currency: 'AUD' } } };
const quoteCharges = { payableAtBooking: { total: { amount: '349.00', currency: 'AUD' } } };

window.history.back = jest.fn();
window.scrollTo = jest.fn();

let Dialog;
let closeDialog;
let emitInteractionEvent;

beforeEach(() => {
  getProperty.mockReturnValue(property);
  getRoomType.mockReturnValue(roomType);
  getCharges.mockReturnValue(quoteCharges);
  getOfferCharges.mockReturnValue(bookingCharges);
  getIsClassic.mockReturnValue(false);

  Dialog = ({ children }) => children;
  closeDialog = jest.fn();

  useDialog.mockReturnValue({
    Dialog,
    openDialog: jest.fn(),
    closeDialog,
    isOpen: true,
  });

  emitInteractionEvent = jest.fn();
  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

afterEach(() => {
  jest.clearAllMocks();
});

describe('when there is a payment mismatch error', () => {
  const priceIncreaseErrors = [{ code: 'inventory_price_changed' }];
  const newBookingTotalText = 'New booking total:$379.00AUD';
  const previousBookingTotalText = 'Previous booking total:$349.00AUD';

  beforeEach(() => {
    getPriceIncreaseErrors.mockReturnValue(priceIncreaseErrors);
  });

  it('shows the correct title and message', () => {
    const { findByText } = render();
    expect(findByText('Price Update Alert')).toExist();
    expect(
      findByText('The total price for your booking has changed. Please review and accept the price update, or change your selection.'),
    ).toExist();
  });

  it('shows the room and prices', () => {
    const { wrapper, findByText } = render();
    expect(findByText(roomType.name)).toExist();
    expect(wrapper).toIncludeText(newBookingTotalText);
    expect(wrapper).toIncludeText(previousBookingTotalText);
  });

  it('renders the correct image', () => {
    const { find } = render();
    expect(find('Image')).toHaveProp({ src: property.mainImage.urlMedium });
  });

  it('emits an interaction event', () => {
    render();
    expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Booking Errors', value: 'Price Increase Error Showed' });
  });

  describe('when "Accept and continue" is clicked', () => {
    let findByText, decorators;

    beforeEach(() => {
      ({ findByText, decorators } = render());
      findByText('Accept and continue').simulate('click');
    });

    it('creates a new quote', () => {
      expect(decorators.store.dispatch).toHaveBeenCalledWith(createQuote());
    });

    it('clears the booking', () => {
      expect(decorators.store.dispatch).toHaveBeenCalledWith(clearBooking());
    });

    it('emits an interaction event', () => {
      expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Booking Errors', value: 'Price Increase Accept Selected' });
    });
  });

  describe('clicking "Change selection"', () => {
    let findByText, decorators;

    beforeEach(() => {
      ({ findByText, decorators } = render());
      findByText('Change selection').simulate('click');
    });

    it('clears the quote', () => {
      expect(decorators.store.dispatch).toHaveBeenCalledWith(clearQuote());
    });

    it('clears the booking', () => {
      expect(decorators.store.dispatch).toHaveBeenCalledWith(clearBooking());
    });

    it('navigates to the previous page', () => {
      expect(window.history.back).toHaveBeenCalled();
    });

    it('emits an interaction event', () => {
      expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Booking Errors', value: 'Price Increase Change Selected' });
    });
  });

  describe('with a classic booking', () => {
    beforeEach(() => {
      getIsClassic.mockReturnValue(true);
    });

    it('renders the correct title and message', () => {
      const { findByText } = render();
      expect(findByText('Price Update Alert')).toExist();
      expect(findByText('The total price for your booking has changed. Please refresh price details or change your selection')).toExist();
    });

    it('does not render the room or prices', () => {
      const { wrapper, findByText } = render();
      expect(findByText(roomType.name)).not.toExist();
      expect(wrapper).not.toIncludeText(newBookingTotalText);
      expect(wrapper).not.toIncludeText(previousBookingTotalText);
    });

    describe('when "Refresh price" is clicked', () => {
      let findByText, decorators;

      beforeEach(() => {
        ({ findByText, decorators } = render());
        findByText('Refresh price').simulate('click');
      });

      it('creates a new quote', () => {
        expect(decorators.store.dispatch).toHaveBeenCalledWith(createQuote());
      });

      it('clears the booking', () => {
        expect(decorators.store.dispatch).toHaveBeenCalledWith(clearBooking());
      });

      it('emits an interaction event', () => {
        expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Booking Errors', value: 'Price Increase Accept Selected' });
      });
    });
  });
});

describe('with no errors', () => {
  beforeEach(() => {
    getPriceIncreaseErrors.mockReturnValue([]);
  });
  it('renders nothing', () => {
    const { wrapper } = render();
    expect(wrapper).toBeEmptyRender();
  });
});
