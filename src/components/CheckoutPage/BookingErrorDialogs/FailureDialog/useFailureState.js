import React, { useEffect, useState, Fragment } from 'react';
import { useSelector } from 'react-redux';
import { Text, Box } from '@qga/roo-ui/components';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import AppLink from 'components/AppLink';
import { BOOKING_STATES } from 'lib/enums/booking';
import { ContactLinks, CallCenterHours } from 'components/CheckoutPage/BookingErrorDialogs/textSnippets';
import { getBookingReference, getBookingState, getGenericPaymentErrors, getNonSpecificErrors } from 'store/booking/bookingSelectors';

const LinkText = styled(Box)`
  text-decoration: underline;
  font-size: ${themeGet('fontSizes.sm')};
  color: ${themeGet('colors.greys.charcoal')};
`;

const errorData = {
  blockedBookingError: {
    isDismissible: false,
    analyticsData: { type: 'Booking Errors', value: 'Partially Completed Error Showed' },
    heading: 'Sorry, something went wrong.',
    body: (
      <Fragment>
        <Text as="p">You&apos;re booking is partially completed.</Text>
        <Text as="p">
          To continue with your booking, <ContactLinks />. <CallCenterHours />.
        </Text>
      </Fragment>
    ),
  },
  pollingError: {
    isDismissible: false,
    analyticsData: { type: 'Booking Errors', value: 'Longer Than Usual Error Showed' },
    heading: 'Sorry, something went wrong.',
    body: (
      <Fragment>
        <Text as="p">Your booking is taking longer than usual to process and has not yet been completed.</Text>
        <Text as="p">
          To continue with your booking, <ContactLinks />. <CallCenterHours />
        </Text>
      </Fragment>
    ),
  },
  // Note: The generic failure code indicates that bookings platform did not proceed far enough to
  // process individual payment types, it may suggest fraud
  genericPaymentError: {
    isDismissible: true,
    analyticsData: { type: 'Booking Errors', value: 'Unable To Process Error Showed' },
    heading: "Sorry, we couldn't complete your booking at this time.",
    body: (
      <Fragment>
        <Text as="p"> We are unable to process your payment, and your booking has not been completed.</Text>
        <Text as="p">
          If you need further assistance, please visit our &nbsp;
          <AppLink to="/contact-us">
            <LinkText> contact us page.</LinkText>
          </AppLink>
        </Text>
      </Fragment>
    ),
  },
  nonSpecificError: {
    isDismissible: true,
    analyticsData: { type: 'Booking Errors', value: 'Unexpected Site Error Showed' },
    heading: 'Sorry, something went wrong.',
    body: (
      <Fragment>
        <Text as="p">There was an unexpected site error and payment has not been processed.</Text>
        <Text as="p">To continue with your booking, please try again.</Text>
        <Text as="p">
          If you need further assistance, <ContactLinks />. <CallCenterHours />
        </Text>
      </Fragment>
    ),
  },
};

export const useFailureState = () => {
  const bookingReference = useSelector(getBookingReference);
  const bookingState = useSelector(getBookingState);
  const genericPaymentErrors = useSelector(getGenericPaymentErrors);
  const nonSpecificErrors = useSelector(getNonSpecificErrors);
  const [failureState, setFailureState] = useState(null);

  useEffect(() => {
    let state = null;

    if (bookingState === BOOKING_STATES.BLOCKED) {
      state = { ...errorData.blockedBookingError, bookingReference };
    } else if (bookingState === BOOKING_STATES.POLLING_FAILED) {
      state = { ...errorData.pollingError, bookingReference };
    } else if (bookingState === BOOKING_STATES.FAILED && genericPaymentErrors.length > 0) {
      state = { ...errorData.genericPaymentError, bookingReference };
    } else if (bookingState === BOOKING_STATES.FAILED && nonSpecificErrors.length > 0) {
      state = { ...errorData.nonSpecificError, bookingReference };
    }

    setFailureState(state);
  }, [bookingReference, bookingState, genericPaymentErrors.length, nonSpecificErrors.length]);

  return failureState;
};
