// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import { renderHook } from 'test-utils';
import { getIsPointsPay } from 'store/ui/uiSelectors';
import { getPointsAmountInCash } from 'store/checkout/checkoutSelectors';
import { getTotalPayableAtBooking } from 'store/quote/quoteSelectors';
import * as config from 'config';

jest.mock('config');
jest.mock('store/ui/uiSelectors');
jest.mock('store/checkout/checkoutSelectors');
jest.mock('store/quote/quoteSelectors');
jest.mock('lodash/debounce', () => (fn) => fn);

let useCheckoutPointsEarned;

const initialPointsEarned = {
  maxQffEarnPpd: 3,
  maxQbrEarnPpd: 3,
  qffPoints: {
    base: 450,
    total: 450,
    qffPointsClub: 0,
  },
  qbrPoints: {
    total: 450,
  },
  propertyPpd: 3,
};

const render = () => {
  return renderHook(() => useCheckoutPointsEarned({ pointsEarned: initialPointsEarned }), { store: true });
};

const pointsAmountInCash = new Decimal(100);
const totalPayableAtBooking = new Decimal(250);

describe('with POINTS_EARN_ENABLED on', () => {
  beforeEach(() => {
    Object.assign(config, jest.requireActual('config'));
    config.POINTS_EARN_ENABLED = true;
    useCheckoutPointsEarned = jest.requireActual('./useCheckoutPointsEarned').default;

    getIsPointsPay.mockReturnValue(true);
    getPointsAmountInCash.mockReturnValue(pointsAmountInCash);
    getTotalPayableAtBooking.mockReturnValue(totalPayableAtBooking);
  });

  it('returns a minimal version of the provided pointsEarned', () => {
    const { result } = render();

    expect(result.current.pointsEarned).toEqual({
      qffPoints: {
        base: 450,
        total: 450,
      },
      qbrPoints: {
        total: 450,
      },
      propertyPpd: 3,
      maxQffEarnPpd: 3,
    });
  });

  describe('on change to the points amount in cash', () => {
    it('recalculates based on total cash amount and ppd multipliers', () => {
      const { result, rerender, store } = render();

      getPointsAmountInCash.mockReturnValue(new Decimal(150));
      store.triggerUpdate();
      rerender();

      expect(result.current.pointsEarned).toEqual({
        maxQffEarnPpd: 3,
        qffPoints: {
          base: 300,
          total: 300,
        },
        qbrPoints: {
          total: 300,
        },
      });
    });
  });
});
