import React from 'react';
import { mountUtils } from 'test-utils';
import BackToPropertyLink from './BackToPropertyLink';
import { useDataLayer } from 'hooks/useDataLayer';
import { getIsMobileApp } from 'store/ui/uiSelectors';
import { getQueryParams } from 'store/router/routerSelectors';

const emitInteractionEvent = jest.fn();

jest.mock('hooks/useDataLayer', () => ({ useDataLayer: jest.fn() }));
jest.mock('store/ui/uiSelectors');
jest.mock('store/router/routerSelectors');

describe('BackToPropertyLink', () => {
  const render = () => mountUtils(<BackToPropertyLink />, { decorators: { theme: true, store: true } });

  beforeEach(() => {
    jest.resetAllMocks();
    useDataLayer.mockReturnValue({ emitInteractionEvent });
    getQueryParams.mockReturnValue({ propertyId: 1234, adults: 2 });
  });

  describe('when mobile', () => {
    describe('is web app', () => {
      beforeEach(() => {
        getIsMobileApp.mockReturnValue(false);
      });

      it('renders the back button', () => {
        const { findByTestId } = render();

        const backButton = findByTestId('mobile-back-link');
        expect(backButton).toExist();
      });

      it('navigates back when the button is clicked', () => {
        const { find } = render();

        expect(find('NextLink').first()).toHaveProp({ href: '/properties/1234?adults=2' });
      });

      it('dispatches event tracking when clicked', () => {
        const { findByTestId } = render();

        const backButton = findByTestId('mobile-back-link');
        backButton.simulate('click');

        expect(emitInteractionEvent).toHaveBeenCalledWith({
          type: 'Change Your Selection',
          value: 'Link Clicked',
        });
      });

      it('has the correct text', () => {
        const { findByTestId } = render();
        const mobilePageBlock = findByTestId('mobile-page-block');

        expect(mobilePageBlock).toHaveText('Review & pay');
      });
    });

    describe('is mobile app', () => {
      beforeEach(() => {
        getIsMobileApp.mockReturnValue(true);
      });
      it('does not render the back button', () => {
        const { findByTestId } = render();

        const backButton = findByTestId('mobile-back-link');
        expect(backButton).not.toExist();
      });
    });
  });

  describe('when desktop', () => {
    it('renders the back button', () => {
      const { find } = render();

      const backButton = find('BackLink');
      expect(backButton).toExist();
    });

    it('navigates back when the button is clicked', () => {
      const { find } = render();

      expect(find('NextLink').first()).toHaveProp({ href: '/properties/1234?adults=2' });
    });

    it('dispatches event tracking when clicked', () => {
      const { find } = render();

      const backButton = find('a[data-testid="back-link"]').first();
      backButton.simulate('click');

      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Change Your Selection',
        value: 'Link Clicked',
      });
    });

    it('has the correct text', () => {
      const { find } = render();
      const desktopPageBlock = find('BackLink [data-testid="not-phone-label"]').first();

      expect(desktopPageBlock).toHaveText('Change your selection');
    });
  });
});
