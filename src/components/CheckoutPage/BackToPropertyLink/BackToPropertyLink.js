import React, { useCallback } from 'react';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { useSelector } from 'react-redux';
import { Icon, Text, Link } from '@qga/roo-ui/components';
import MobileAppBoundary from 'components/MobileAppBoundary';
import { useDataLayer } from 'hooks/useDataLayer';
import PageBlock from 'components/PageBlock';
import BackLink from 'components/BackLink';
import { getQueryParams } from 'store/router/routerSelectors';
import NextLink from 'next/link';
import stringifyQueryValues from 'lib/search/stringifyQueryValues';
import omit from 'lodash/omit';

const MobileBackLink = styled(Link)`
  color: ${themeGet('colors.white')};
  width: 100%;
  display: flex;
`;

const BackToPropertyLink = () => {
  const { propertyId, ...query } = useSelector(getQueryParams);
  const { emitInteractionEvent } = useDataLayer();
  const propertyQueryString = stringifyQueryValues(omit(query, ['offerId', 'roomTypeId']));
  const backToPropertyUrl = `/properties/${propertyId}?${propertyQueryString}`;

  const handleOnClick = useCallback(() => {
    emitInteractionEvent({ type: 'Change Your Selection', value: 'Link Clicked' });
  }, [emitInteractionEvent]);

  return (
    <MobileAppBoundary>
      <PageBlock py={4} display={['none', 'block']} printable={false} data-testid="desktop-page-block">
        <BackLink to={backToPropertyUrl} label="Change your selection" onClick={handleOnClick} />
      </PageBlock>
      <PageBlock display={['block', 'none']} py={4} backgroundColor="greys.charcoal" printable={false} data-testid="mobile-page-block">
        <NextLink href={backToPropertyUrl} passHref>
          <MobileBackLink aria-label="Change your selection" onClick={handleOnClick} data-testid="mobile-back-link">
            <Icon name="chevronLeft" size={24} />
            <Text mx="auto" pr={9}>
              Review & pay
            </Text>
          </MobileBackLink>
        </NextLink>
      </PageBlock>
    </MobileAppBoundary>
  );
};

export default BackToPropertyLink;
