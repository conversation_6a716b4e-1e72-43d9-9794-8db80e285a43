import React from 'react';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import { mountUtils } from 'test-utils';
import ClassicMessaging from './ClassicMessaging';
import { getPointsBalance } from 'store/user/userSelectors';
import { useIsAuthenticated, useLogout } from 'lib/oauth';
import { getPointsAmount } from 'store/checkout/checkoutSelectors';

jest.mock('store/user/userSelectors');
jest.mock('store/checkout/checkoutSelectors');
jest.mock('lib/oauth');

mountUtils.mockComponent('InsufficientPoints');
mountUtils.mockComponent('WelcomeMessage');
mountUtils.mockComponent('LoginPrompt');

const quote = {
  property: {
    name: 'Test property',
  },
  roomType: {
    name: 'Bedroom',
    mainImage: {
      urlMedium: 'medium',
      urlLarge: 'large',
      caption: 'caption',
    },
  },
};

const mockLogout = jest.fn();

const render = () => mountUtils(<ClassicMessaging quote={quote} />, { decorators: { store: true, theme: true } });

describe('when viewing a classic offer', () => {
  beforeEach(() => {
    useLogout.mockReturnValue({ logout: mockLogout });
  });

  describe('when authenticated with insufficient points', () => {
    beforeEach(() => {
      useIsAuthenticated.mockReturnValue(true);
      getPointsBalance.mockReturnValue(new Decimal(0));
      getPointsAmount.mockReturnValue(new Decimal(1000));
    });

    it('renders the InsufficientPoints component', () => {
      const { find } = render();
      expect(find('InsufficientPoints')).toExist();
      expect(find('WelcomeMessage')).not.toExist();
      expect(find('LoginPrompt')).not.toExist();
    });
  });

  describe('when authenticated with sufficient points', () => {
    beforeEach(() => {
      useIsAuthenticated.mockReturnValue(true);
      getPointsBalance.mockReturnValue(new Decimal(10000));
      getPointsAmount.mockReturnValue(new Decimal(1000));
    });

    it('renders the WelcomeMessage component', () => {
      const { find } = render();
      expect(find('InsufficientPoints')).not.toExist();
      expect(find('LoginMessaging')).toExist();
      expect(find('LoginPrompt')).not.toExist();
    });
  });

  describe('when not authenticated', () => {
    beforeEach(() => {
      useIsAuthenticated.mockReturnValue(false);
    });

    it('renders the LoginPrompt component', () => {
      const { find } = render();
      expect(find('InsufficientPoints')).not.toExist();
      expect(find('WelcomeMessage')).not.toExist();
      expect(find('LoginPrompt')).toExist();
    });
  });
});
