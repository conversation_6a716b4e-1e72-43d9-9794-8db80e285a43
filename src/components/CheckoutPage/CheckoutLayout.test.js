import React from 'react';
import { useRouter } from 'next/router';
import { mountUtils } from 'test-utils';
import CheckoutLayout from './CheckoutLayout';
import CheckoutHelmet from './CheckoutHelmet';
import { getQuote, getIsAvailable, getIsLoading } from 'store/quote/quoteSelectors';

jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));
jest.mock('store/quote/quoteSelectors');
jest.mock('./CheckoutHelmet', () => () => null);
jest.mock('store/ui/uiSelectors');
mountUtils.mockComponent('CustomerDetails');
mountUtils.mockComponent('QuoteDetails');
mountUtils.mockComponent('ClassicMessaging');
mountUtils.mockComponent('QuoteUnavailable');
mountUtils.mockComponent('FailureDialog');
mountUtils.mockComponent('PriceIncreaseDialog');
mountUtils.mockComponent('InventoryUnavailableDialog');
mountUtils.mockComponent('WelcomeMessage');
mountUtils.mockComponent('MobileAppLeftNavigationIcon');

const mockRouter = {
  query: {
    ss_action: 'rebook',
  },
};

const render = () => mountUtils(<CheckoutLayout />, { decorators: { theme: true, store: true } });

const quote = 'quote';

beforeEach(() => {
  useRouter.mockReturnValue(mockRouter);
});

it('renders the CheckoutHelmet', () => {
  const { find } = render();
  expect(find(CheckoutHelmet)).toExist();
});

it('renders the MobileAppLeftNavigationIcon', () => {
  const { find } = render();
  expect(find('MobileAppLeftNavigationIcon')).toHaveProp({ iconName: 'back' });
});

it('renders the WelcomeMessage', () => {
  const { find } = render();

  expect(find('WelcomeMessage')).toExist();
});

it('renders the FailureDialog', () => {
  const { find } = render();

  expect(find('FailureDialog')).toExist();
});

it('renders the InventoryUnavailableDialog', () => {
  const { find } = render();

  expect(find('InventoryUnavailableDialog')).toExist();
});

it('renders the PriceIncreaseDialog', () => {
  const { find } = render();

  expect(find('PriceIncreaseDialog')).toExist();
});

describe('when the quote is present', () => {
  beforeEach(() => {
    getQuote.mockReturnValue(quote);
  });

  describe('when the quote is present and has a status of available', () => {
    beforeEach(() => {
      getIsAvailable.mockReturnValue(true);
    });

    it('renders the heading', () => {
      const { findByTestId } = render();
      expect(findByTestId('heading')).toExist();
    });

    it('renders the CustomerDetails', () => {
      const { find } = render();
      expect(find('CustomerDetails')).toHaveProp({ quote });
    });

    it('renders the QuoteDetails', () => {
      const { find } = render();
      expect(find('QuoteDetails')).toExist();
    });

    it('does not render QuoteUnavailable', () => {
      const { find } = render();
      expect(find('QuoteUnavailable')).not.toExist();
    });
  });

  describe('when the quote is present and has a status of unavailable', () => {
    beforeEach(() => {
      getIsAvailable.mockReturnValue(false);
    });

    it('does not render the CustomerDetails', () => {
      const { find } = render();
      expect(find('CustomerDetails')).not.toExist();
    });

    it('renders the QuoteDetails', () => {
      const { find } = render();
      expect(find('QuoteDetails')).toExist();
    });

    it('renders the QuoteUnavailable', () => {
      const { find } = render();
      expect(find('QuoteUnavailable')).toExist();
    });

    describe('when the quote is loading', () => {
      beforeEach(() => {
        getIsLoading.mockReturnValue(true);
      });

      it('does not render the QuoteUnavailable', () => {
        const { find } = render();
        expect(find('QuoteUnavailable')).not.toExist();
      });
    });
  });

  describe('when the quote is for a classic offer', () => {
    const classicQuote = { offer: { type: 'classic' } };
    beforeEach(() => {
      getIsAvailable.mockReturnValue(true);
      getQuote.mockReturnValue(classicQuote);
    });

    it('renders the ClassicMessaging', () => {
      const { find } = render();
      expect(find('ClassicMessaging')).toExist();
      expect(find('ClassicMessaging')).toHaveProp({ quote: classicQuote });
    });
  });

  describe('when the quote is NOT for a classic offer', () => {
    beforeEach(() => {
      getIsAvailable.mockReturnValue(true);
      getQuote.mockReturnValue(quote);
    });

    it('does NOT render the ClassicMessaging', () => {
      const { find } = render();
      expect(find('ClassicMessaging')).not.toExist();
    });
  });
});

describe('when the quote is not available, and the quote is not loading', () => {
  beforeEach(() => {
    getIsAvailable.mockReturnValue(false);
    getIsLoading.mockReturnValue(false);
    getQuote.mockReturnValue(null);
  });

  it('renders QuoteUnavailable', () => {
    const { find } = render();
    expect(find('QuoteUnavailable')).toExist();
  });

  it('does not render CustomerDetails', () => {
    const { find } = render();
    expect(find('CustomerDetails')).not.toExist();
  });
});
