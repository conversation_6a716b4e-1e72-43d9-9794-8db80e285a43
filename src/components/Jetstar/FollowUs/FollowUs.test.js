import React from 'react';
import { mountUtils } from 'test-utils';
import FollowUs, { SOCIAL_LINKS } from './FollowUs';
import { useDataLayer } from 'hooks/useDataLayer';

jest.mock('hooks/useDataLayer');
const emitInteractionEvent = jest.fn();

const decorators = { theme: true };
const render = () => mountUtils(<FollowUs />, { decorators });

describe('<FollowUs />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    useDataLayer.mockReturnValue({ emitInteractionEvent });
  });

  SOCIAL_LINKS.forEach(({ id, url }) => {
    it(`renders the ${id} link`, () => {
      const { findByTestId } = render();
      expect(findByTestId(`follow-${id}-link`)).toHaveProp({
        href: url,
        'aria-label': `Follow us on ${id}`,
      });
    });

    it(`tracks when the ${id} link is clicked`, () => {
      const { findByTestId } = render();
      findByTestId(`follow-${id}-link`).simulate('click');
      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Footer Navigation',
        value: `Follow on ${id} Selected`,
      });
    });
  });

  it('renders the links for the app store', () => {
    const { findByTestId } = render();
    expect(findByTestId('app-store-link')).toHaveProp({
      href: 'https://apps.apple.com/au/app/jetstar/id821234247',
      'aria-label': 'Download the Jetstar app in the App Store',
    });
  });

  it('tracks when the app store link is clicked', () => {
    const { findByTestId } = render();
    findByTestId('app-store-link').simulate('click');
    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Footer Navigation',
      value: 'Download app on App Store Selected',
    });
  });

  it('renders the links for google play', () => {
    const { findByTestId } = render();
    expect(findByTestId('google-play-link')).toHaveProp({
      href: 'https://play.google.com/store/apps/details?id=com.ink.jetstar.mobile.app',
      'aria-label': 'Download the Jetstar app in Google Play',
    });
  });

  it('tracks when the google play link is clicked', () => {
    const { findByTestId } = render();
    findByTestId('google-play-link').simulate('click');
    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Footer Navigation',
      value: 'Download app on Google Play Selected',
    });
  });
});
