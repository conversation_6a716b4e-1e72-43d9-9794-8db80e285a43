import React from 'react';
import { mountUtils } from 'test-utils';
import ContactUsLayout from './ContactUsLayout';
import { HOTELS_BRAND_NAME, BRAND_NAME } from 'config';

mountUtils.mockComponent('Header');
mountUtils.mockComponent('RecommendedLinks');
mountUtils.mockComponent('FaqNavBar');

const decorators = { helmet: true, router: true, store: true, theme: true };
const render = (props) => mountUtils(<ContactUsLayout {...props} />, { decorators });

describe('<ContactUsLayout />', () => {
  it('uses helmet to set document title', () => {
    const { find } = render();

    expect(find('title')).toHaveText(`Contact us | ${HOTELS_BRAND_NAME} Australia`);
    expect(find('meta[name="hotels-booking-stage"]').prop('content')).toEqual('contact-us');
    expect(find('meta[name="robots"]').prop('content')).toEqual('index, follow');
    expect(find('meta[name="description"]').prop('content')).toEqual(`Contact us | ${HOTELS_BRAND_NAME} Australia`);
  });

  it('renders <Header />', () => {
    const { find } = render();

    expect(find('Header')).toExist();
    expect(find('Header').first()).toHaveProp({
      title: `${BRAND_NAME} contact us`,
      description: "Here's how to get in contact with us, online or via our contact centre.",
    });
  });

  it('renders <RecommendedLinks />', () => {
    const { find } = render();

    expect(find('RecommendedLinks')).toExist();
  });
});
