import React, { useCallback } from 'react';
import { CONTACT_US_PAGE_META } from 'lib/enums/contactUs';
import styled from '@emotion/styled';
import AppLink from 'components/AppLink';
import Header from './Header';
import RecommendedLinks from './RecommendedLinks';
import { Box, Link, Text } from '@qga/roo-ui/components';
import { themeGet } from 'styled-system';
import FAQsLinks from './FAQsLinks';
import { BRAND_NAME, HOTELS_BRAND_NAME, HOTELS_URL, FAQS } from 'config';
import Head from 'next/head';
import { useMount } from 'react-use';
import { fetchFaqs } from 'store/faqs/faqActions';
import { useDispatch } from 'react-redux';
import { useDataLayer } from 'hooks/useDataLayer';
import PageBlock from 'components/PageBlock';

const HeaderText = styled(Text)`
  display: block;
  font-size: ${themeGet('fontSizes.md')};
  font-weight: ${themeGet('fontWeights.bold')};
  margin-bottom: ${themeGet('space.3')};
`;

const ContactUsLayout = () => {
  const dispatch = useDispatch();
  const { emitInteractionEvent } = useDataLayer();

  const handleFAQLinkClick = useCallback(() => {
    emitInteractionEvent({ type: 'FAQs Link', value: 'Link Selected' });
  }, [emitInteractionEvent]);

  useMount(() => {
    dispatch(fetchFaqs());
  });

  return (
    <>
      <Head>
        <title>Contact us | {HOTELS_BRAND_NAME} Australia</title>
        <link rel="canonical" href={`${HOTELS_URL}/contact-us`} />
        {CONTACT_US_PAGE_META.map(({ name, content }) => (
          <meta key={name} name={name} content={content} />
        ))}
      </Head>
      <Header
        title={`${BRAND_NAME} contact us`}
        description="Here's how to get in contact with us, online or via our contact centre."
        py={5}
      />
      <PageBlock bg="white" px={[4, 6, 0]} data-print-style="page-block">
        <Box borderBottom={1} borderColor="greys.porcelain" pb={10}>
          <HeaderText data-testid="review-FAQs">
            Please review the{' '}
            <Link as={AppLink} to={FAQS} data-testid="faqs-link" onClick={handleFAQLinkClick}>
              FAQs
            </Link>{' '}
            before getting in touch with our team.
          </HeaderText>
        </Box>
      </PageBlock>
      <RecommendedLinks />
      <FAQsLinks />
    </>
  );
};

export default ContactUsLayout;
