import React from 'react';
import { useForm } from 'react-hook-form';
import { useDataLayer } from 'hooks/useDataLayer';
import { mountUtils } from 'test-utils';
import { createEnquiry } from 'store/enquiry/enquiryActions';
import EnquiryForm from './EnquiryForm';
import useRecaptcha from './useRecaptcha';

jest.mock('./useRecaptcha');
jest.mock('react-hook-form');
jest.mock('hooks/useDataLayer');
const emitInteractionEvent = jest.fn();

const reactRedux = require('react-redux');
const dispatchMock = jest.fn();
reactRedux.useDispatch = jest.fn(() => dispatchMock);

mountUtils.mockComponent('PersonalDetails');
mountUtils.mockComponent('EnquiryDetails');
mountUtils.mockComponent('MessageBox');
mountUtils.mockComponent('ProgressDialog');

const decorators = { store: true, theme: true };
const render = () => mountUtils(<EnquiryForm />, { decorators });

const errors = {};
const formData = { data: 'data' };
const getToken = jest.fn().mockImplementation(() => Promise.resolve('recaptchaToken'));
const handleSubmit = jest.fn();
const register = jest.fn();
const setValue = jest.fn();
const unregister = jest.fn();
const watch = jest.fn();
const RecaptchaBranding = jest.fn().mockImplementation(() => <div id="recaptcha-branding" />);

describe('EnquiryForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    useDataLayer.mockReturnValue({ emitInteractionEvent });
    useForm.mockReturnValue({ formState: { errors }, handleSubmit, register, setValue, unregister, watch });
    useRecaptcha.mockReturnValue({ getToken, RecaptchaBranding });
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });

  it('renders the enquiry form', () => {
    const { findByTestId } = render();

    expect(findByTestId('new-enquiry-form')).toExist();
  });

  it('renders <PersonalDetails /> with the correct props', () => {
    const { find } = render();
    expect(find('PersonalDetails')).toHaveProp({
      errors,
      register,
      setValue,
    });
  });

  it('renders <EnquiryDetails /> with the correct props', () => {
    const { find } = render();
    expect(find('EnquiryDetails')).toHaveProp({
      errors,
      register,
      watch,
      unregister,
    });
  });

  it('renders <MessageBox /> with the correct props', () => {
    const { find } = render();
    expect(find('MessageBox')).toHaveProp({
      errors,
      register,
    });
  });

  it('renders the submit button', () => {
    const { findByTestId } = render();

    expect(findByTestId('submit-button').text()).toEqual('Send');
  });

  it('calls the useRecaptcha hook', () => {
    render();

    expect(useRecaptcha).toHaveBeenCalled();
  });

  it('renders <RecaptchaBranding />', () => {
    const { find } = render();

    expect(find('div[id="recaptcha-branding"]')).toExist();
  });

  it('renders <ProgressDialog />', () => {
    const { find } = render();

    expect(find('ProgressDialog')).toExist();
  });

  describe('when submitting the form', () => {
    beforeEach(() => {
      handleSubmit.mockImplementation((callback) => callback(formData));

      const { wrapper } = render();

      wrapper.find('Button[type="submit"]').simulate('click');
      wrapper.update();
    });

    it('fetches a recaptcha token', () => {
      expect(getToken).toHaveBeenCalledTimes(1);
    });

    it('dispatches the createEnquiry action with the form data and recaptcha token', () => {
      const expected = createEnquiry({
        ...formData,
        recaptchaToken: 'recaptchaToken',
      });

      expect(dispatchMock).toHaveBeenCalledWith(expected);
    });

    it('emits an event to the data layer', () => {
      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Submit Button',
        value: 'Button Selected',
      });
    });
  });
});
