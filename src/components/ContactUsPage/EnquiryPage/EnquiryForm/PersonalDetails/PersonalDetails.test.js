import React from 'react';
import { mountUtils } from 'test-utils';
import { EMAIL_REGEX } from 'lib/enums/checkout';
import PersonalDetails from './PersonalDetails';
import { getTitle, getFirstName, getLastName, getEmailAddress, getPhoneNumber } from 'store/user/userSelectors';
import { FieldError } from '@qga/components';

jest.mock('store/user/userSelectors');

const errors = {
  title: { message: 'title error' },
  firstName: { message: 'firstName error' },
  lastName: { message: 'lastName error' },
  emailAddress: { message: 'emailAddress error' },
  phoneNumber: { message: 'phoneNumber error' },
};

const decorators = { router: true, store: true, theme: true };
const props = {
  errors,
  handleSubmit: jest.fn(),
  register: jest.fn(),
  setValue: jest.fn(),
  watch: jest.fn(),
};

const render = () => mountUtils(<PersonalDetails {...props} />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
  getTitle.mockReturnValue('Mr');
  getFirstName.mockReturnValue('FirstName');
  getLastName.mockReturnValue('LastName');
  getEmailAddress.mockReturnValue('<EMAIL>');
  getPhoneNumber.mockReturnValue('1234567890');
});

describe('title', () => {
  it('renders the field', () => {
    const { find } = render();

    expect(find('[name="title"]').first()).toHaveProp({
      id: 'title',
      error: true,
    });
  });

  it('registers the field with the validation rule', () => {
    render();

    expect(props.register.mock.calls[0]).toMatchObject(['title', { required: 'Please select title' }]);
  });

  it('renders the field error', () => {
    const { find } = render();

    expect(find(FieldError).filterWhere((c) => c.prop('data-testid') === 'title-error')).toHaveProp({
      error: errors.title,
    });
  });

  it('pre-populates the title', () => {
    const { find } = render();

    expect(find('[name="title"]').first()).toHaveProp({
      id: 'title',
      defaultValue: 'Mr',
    });
  });
});

describe('firstName', () => {
  it('renders the field', () => {
    const { find } = render();

    expect(find('[name="firstName"]').first()).toHaveProp({
      id: 'firstName',
      error: true,
    });
  });

  it('registers the field with the validation rule', () => {
    render();

    expect(props.register.mock.calls[1]).toMatchObject([
      'firstName',
      { maxLength: { message: 'First name should be less than 80 characters', value: 80 }, required: 'Please enter your first name' },
    ]);
  });

  it('renders the field error', () => {
    const { find } = render();

    expect(find(FieldError).filterWhere((c) => c.prop('data-testid') === 'first-name-error')).toHaveProp({
      error: errors.firstName,
    });
  });

  it('pre-populates the first name', () => {
    const { find } = render();

    expect(find('[name="firstName"]').first()).toHaveProp({
      id: 'firstName',
      defaultValue: 'FirstName',
    });
  });
});

describe('lastName', () => {
  it('renders the field', () => {
    const { find } = render();

    expect(find('[name="lastName"]').first()).toHaveProp({
      id: 'lastName',
      error: true,
    });
  });

  it('registers the field with the validation rule', () => {
    render();

    expect(props.register.mock.calls[2]).toMatchObject(['lastName', { required: 'Please enter your last name' }]);
  });

  it('renders the field error', () => {
    const { find } = render();

    expect(find(FieldError).filterWhere((c) => c.prop('data-testid') === 'last-name-error')).toHaveProp({
      error: errors.lastName,
    });
  });

  it('pre-populates the last name', () => {
    const { find } = render();

    expect(find('[name="lastName"]').first()).toHaveProp({
      id: 'lastName',
      defaultValue: 'LastName',
    });
  });
});

describe('emailAddress', () => {
  it('renders the field', () => {
    const { find } = render();

    expect(find('[name="emailAddress"]').first()).toHaveProp({
      id: 'emailAddress',
      error: true,
    });
  });

  it('registers the field with the validation rule', () => {
    render();

    expect(props.register.mock.calls[3]).toMatchObject([
      'emailAddress',
      {
        maxLength: { message: 'email should be less than 255 characters', value: 255 },
        pattern: {
          message: 'Please enter a valid email address',
          value: EMAIL_REGEX,
        },
        required: 'Please enter your email address',
      },
    ]);
  });

  it('renders the field error', () => {
    const { find } = render();

    expect(find(FieldError).filterWhere((c) => c.prop('data-testid') === 'email-address-error')).toHaveProp({
      error: errors.emailAddress,
    });
  });

  it('pre-populates the email address', () => {
    const { find } = render();

    expect(find('[name="emailAddress"]').first()).toHaveProp({
      id: 'emailAddress',
      defaultValue: '<EMAIL>',
    });
  });
});

describe('phoneNumber', () => {
  it('renders the field', () => {
    const { find } = render();

    expect(find('[name="phoneNumber"]').first()).toHaveProp({
      id: 'phoneNumber',
      error: true,
    });
  });

  it('registers the field with the validation rule', () => {
    render();

    expect(props.register.mock.calls[4]).toMatchObject([
      'phoneNumber',
      { maxLength: { message: 'Phone number should be less than 40 digits', value: 40 }, required: 'Please enter your phone number' },
    ]);
  });

  it('rejects anything that is not a number, a space, or +', () => {
    const { find } = render();
    find('Input[name="phoneNumber"]').simulate('change', { target: { value: '+123b 456#a' } });

    expect(props.setValue).toHaveBeenCalledWith('phoneNumber', '+123 456');
  });

  it('renders the field error', () => {
    const { find } = render();

    expect(find(FieldError).filterWhere((c) => c.prop('data-testid') === 'phone-number-error')).toHaveProp({
      error: errors.phoneNumber,
    });
  });

  it('pre-populates the phone number', () => {
    const { find } = render();

    expect(find('[name="phoneNumber"]').first()).toHaveProp({
      id: 'phoneNumber',
      defaultValue: '1234567890',
    });
  });
});
