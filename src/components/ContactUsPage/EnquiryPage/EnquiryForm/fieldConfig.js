import { EMAIL_REGEX } from 'lib/enums/checkout';

const fieldConfig = {
  title: { required: 'Please select title' },
  firstName: {
    required: 'Please enter your first name',
    maxLength: {
      message: 'First name should be less than 80 characters',
      value: 80,
    },
  },
  lastName: { required: 'Please enter your last name' },
  email: {
    required: 'Please enter your email address',
    pattern: {
      value: EMAIL_REGEX,
      message: 'Please enter a valid email address',
    },
    maxLength: {
      message: 'email should be less than 255 characters',
      value: 255,
    },
  },
  phone: {
    required: 'Please enter your phone number',
    maxLength: {
      message: 'Phone number should be less than 40 digits',
      value: 40,
    },
  },
  qffNumber: {
    maxLength: {
      message: 'QFF number should be of 20 digits',
      value: 20,
    },
  },
  bookingReference: {
    required: 'Please enter your booking reference',
    maxLength: {
      message: 'Booking reference should be a maximum of 10 characters',
      value: 10,
    },
  },
  enquiryType: {
    required: 'Please select an enquiry type',
  },
  airbnbBookingReference: {
    required: 'Please enter your Airbnb booking reference',
    maxLength: {
      message: 'Airbnb Booking reference should be a maximum of 20 characters',
      value: 20,
    },
  },
  airbnbBookingCost: {
    required: 'Please enter your Airbnb booking cost',
    maxLength: {
      message: 'Airbnb booking cost should be less than 10 digits',
      value: 10,
    },
  },
};

export default fieldConfig;
