import React, { useCallback } from 'react';
import styled from '@emotion/styled';
import PropTypes from 'prop-types';
import { themeGet } from 'styled-system';
import { Box, Link } from '@qga/roo-ui/components';
import { BRAND_SELF_SERVICE_URL } from 'config';
import { useDataLayer } from 'hooks/useDataLayer';

const LinkText = styled(Box)`
  text-decoration: underline;
  font-size: ${themeGet('fontSizes.base')};
`;

const SelfServiceLink = ({ text, eventType }) => {
  const { emitInteractionEvent } = useDataLayer();

  const handleClick = useCallback(() => {
    emitInteractionEvent({ type: `${eventType} Booking Link`, value: 'Link Selected' });
  }, [emitInteractionEvent, eventType]);

  return (
    <Link href={BRAND_SELF_SERVICE_URL} onClick={handleClick} data-testid="self-service-link">
      <LinkText>{text}</LinkText>
    </Link>
  );
};

SelfServiceLink.propTypes = {
  text: PropTypes.string.isRequired,
  eventType: PropTypes.string.isRequired,
};

export default SelfServiceLink;
