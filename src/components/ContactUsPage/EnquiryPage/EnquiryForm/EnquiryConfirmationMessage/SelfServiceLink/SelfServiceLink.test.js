import React from 'react';
import { mountUtils } from 'test-utils';
import { BRAND_SELF_SERVICE_URL } from 'config';
import SelfServiceLink from './SelfServiceLink';
import { useDataLayer } from 'hooks/useDataLayer';

let props;

jest.mock('store/enquiry/enquirySelectors');
jest.mock('hooks/useDataLayer');
const emitInteractionEvent = jest.fn();

const render = () => mountUtils(<SelfServiceLink {...props} />);

describe('Self service link', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    useDataLayer.mockReturnValue({ emitInteractionEvent });

    props = {
      text: 'Link text',
      eventType: 'View',
    };
  });

  it('has the correct href', () => {
    const { findByTestId } = render();
    const selfServiceLink = findByTestId('self-service-link');

    expect(selfServiceLink).toHaveProp({ href: BRAND_SELF_SERVICE_URL });
  });

  it('emits an event to the data layer when clicked', () => {
    const { findByTestId } = render();
    const selfServiceLink = findByTestId('self-service-link');

    selfServiceLink.simulate('click');

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: `${props.eventType} Booking Link`,
      value: 'Link Selected',
    });
  });
});
