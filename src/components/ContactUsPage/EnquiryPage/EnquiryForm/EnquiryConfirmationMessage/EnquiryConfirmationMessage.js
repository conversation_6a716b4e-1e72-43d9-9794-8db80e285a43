import React, { useEffect } from 'react';
import styled from '@emotion/styled';
import { useSelector } from 'react-redux';
import { themeGet } from 'styled-system';
import { scrollTop } from 'lib/browser';
import { Icon, Flex, Box } from '@qga/roo-ui/components';
import PageBlock from 'components/PageBlock';
import FaqNavBar from 'components/FAQsPage/FaqNavBar';
import { getCaseNumber } from 'store/enquiry/enquirySelectors';
import { useDataLayer } from 'hooks/useDataLayer';
import SelfServiceLink from './SelfServiceLink';
import AppLink from 'components/AppLink';
import { SELF_SERVICE_ENABLED } from 'config';

const BoxWithMargin = styled(Box)`
  margin-top: ${themeGet('space.2')};
`;

const Title = styled(Box)`
  margin: 0 0 ${themeGet('space.2')} ${themeGet('space.2')};
  font-size: ${themeGet('fontSizes.lg')};
  font-weight: 'bold';
`;

const LinkText = styled(Box)`
  text-decoration: underline;
  font-size: ${themeGet('fontSizes.base')};
`;

const EnquiryConfirmationMessage = () => {
  const { emitInteractionEvent } = useDataLayer();
  const caseNumber = useSelector(getCaseNumber);

  useEffect(() => {
    scrollTop();
  }, []);

  const handleBackClick = () => {
    emitInteractionEvent({ type: 'Back To Contact Us Link', value: 'Link Selected' }, []);
  };

  return (
    <PageBlock py={10} px={[4, 6, 0]} data-print-style="page-block">
      <Flex>
        <Icon name="checkCircle" color="green" size={32} />
        <Title fontWeight="bold" data-testid="title">
          Thanks for getting in touch
        </Title>
      </Flex>
      <Box mt={5} width={[1, 0.75, 0.75]}>
        {caseNumber && <BoxWithMargin data-testid="case-number-message">Your support reference number is {caseNumber}.</BoxWithMargin>}
        <br />
        <BoxWithMargin>We have sent a confirmation of your request to your email address and will get back to you soon.</BoxWithMargin>
        <br />
        {SELF_SERVICE_ENABLED && (
          <BoxWithMargin>
            In the meantime, you can <SelfServiceLink text="view booking information" eventType="View" data-testid="view-booking-link" />,{' '}
            <SelfServiceLink text="change" eventType="Change" data-testid="change-booking-link" /> or{' '}
            <SelfServiceLink text="cancel" eventType="Cancel" data-testid="cancel-booking-link" /> your booking online.
          </BoxWithMargin>
        )}
        <br />
      </Box>

      <AppLink to="/contact-us" onClick={handleBackClick} data-testid="back-link">
        <LinkText>Back to Contact Us</LinkText>
      </AppLink>
      <Flex flexDirection="column" mt={20} mb={5}>
        <Title fontWeight="bold" data-testid="FAQs-links-title">
          Browse our popular FAQs topics
        </Title>
        <FaqNavBar />
      </Flex>
    </PageBlock>
  );
};

export default EnquiryConfirmationMessage;
