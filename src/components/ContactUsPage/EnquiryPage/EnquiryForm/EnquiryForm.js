import React, { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useForm } from 'react-hook-form';
import { Button } from '@qga/roo-ui/components';
import { createEnquiry } from 'store/enquiry/enquiryActions';
import { getStatus } from 'store/enquiry/enquirySelectors';
import Wrapper from './Wrapper/Wrapper';
import PersonalDetails from './PersonalDetails';
import EnquiryDetails from './EnquiryDetails';
import MessageBox from './MessageBox';
import useRecaptcha from './useRecaptcha';
import { useDataLayer } from 'hooks/useDataLayer';
import { EMAIL_ENQUIRY_STATUS } from 'lib/enums/contactUs';
import ProgressDialog from './ProgressDialog';

// Note: This is needed for the enquiryType dropdown, as it does not render
// the correct conditional fields without having an initial/default value
const EnquiryForm = () => {
  const {
    formState: { errors },
    handleSubmit,
    register,
    setValue,
    unregister,
    watch,
  } = useForm();
  const { emitInteractionEvent } = useDataLayer();

  const dispatch = useDispatch();
  const formStatus = useSelector(getStatus);
  const isSubmitting = formStatus === EMAIL_ENQUIRY_STATUS.SUBMITTING;
  const { getToken, RecaptchaBranding } = useRecaptcha();

  const onSubmit = useCallback(
    (formData) => {
      async function processForm() {
        const recaptchaToken = await getToken();

        dispatch(
          createEnquiry({
            ...formData,
            recaptchaToken,
          }),
        );
      }

      processForm();
      emitInteractionEvent({ type: 'Submit Button', value: 'Button Selected' });
    },
    [dispatch, emitInteractionEvent, getToken],
  );

  return (
    <Wrapper>
      <ProgressDialog isSubmitting={isSubmitting} />
      <form data-testid="new-enquiry-form" onSubmit={handleSubmit(onSubmit)}>
        <PersonalDetails errors={errors} register={register} setValue={setValue} />
        <EnquiryDetails errors={errors} register={register} setValue={setValue} unregister={unregister} watch={watch} />
        <MessageBox errors={errors} register={register} />

        <Button type="submit" variant="primary" width={['100%', 'auto']} display="block" mb={4} data-testid="submit-button">
          Send
        </Button>
        <RecaptchaBranding color="greys.alto" fontSize="xs" />
      </form>
    </Wrapper>
  );
};

export default EnquiryForm;
