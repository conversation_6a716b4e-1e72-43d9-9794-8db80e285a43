import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import map from 'lodash/map';
import { Flex, Input, Select, Text } from '@qga/roo-ui/components';
import { EMAIL_ENQUIRY_TYPES } from 'lib/enums/contactUs';
import AirbnbDetails from './AirbnbDetails';
import { FieldError, FieldLabel } from '@qga/components';
import { useDataLayer } from 'hooks/useDataLayer';
import { getQueryParams } from 'store/router/routerSelectors';
import { getMemberId } from 'store/user/userSelectors';
import fieldConfig from '../fieldConfig';
import { ACCOUNT_MANAGEMENT_TYPES, QFF_ACCOUNT_MANAGEMENT } from 'config';

const bookingEnquiryTypes = ['amendment', 'cancellation', 'existingReservation'];

const EnquiryDetails = ({ errors, register, setValue, unregister, watch }) => {
  const qffNumber = useSelector(getMemberId);
  const { enquiryType: defaultSelectValue = '' } = useSelector(getQueryParams);
  const { emitInteractionEvent } = useDataLayer();
  const enquiryType = watch('enquiryType');

  const isAirbnbEnquiry = enquiryType === 'airbnb';
  const isSupplierEnquiry = enquiryType === 'supplier';
  const isBookingEnquiry = bookingEnquiryTypes.includes(enquiryType);

  useEffect(() => {
    if (!enquiryType) return;

    const capitalizedEnquiryType = enquiryType.replace(/^./, (str) => str.toUpperCase());
    emitInteractionEvent({ type: 'Help Topic Dropdown', value: `${capitalizedEnquiryType} Enquiry Selected` });
  }, [emitInteractionEvent, enquiryType]);

  return (
    <Flex flexDirection="column" mb={4} width={[1, 2 / 3]}>
      {QFF_ACCOUNT_MANAGEMENT === ACCOUNT_MANAGEMENT_TYPES.APP_WIDE && !isSupplierEnquiry && (
        <FieldLabel htmlFor="qffNumber" mb={2}>
          Your Qantas Frequent Flyer number
          <Input
            id="qffNumber"
            name="qffNumber"
            type="text"
            defaultValue={qffNumber}
            {...register('qffNumber', fieldConfig.qffNumber)}
            error={!!errors.qffNumber}
            mb={0}
          />
          <FieldError error={errors.qffNumber} data-testid="qff-number-error" />
          <Text color="greys.dusty">Optional</Text>
        </FieldLabel>
      )}

      <FieldLabel htmlFor="enquiryType" mb={2}>
        Enquiry Type
        <Select
          id="enquiryType"
          name="enquiryType"
          defaultValue={defaultSelectValue}
          {...register('enquiryType', fieldConfig.enquiryType)}
          error={!!errors.enquiryType}
        >
          <option disabled value="">
            Please select an enquiry type
          </option>
          {map(EMAIL_ENQUIRY_TYPES, (type, key) => (
            <option key={key} value={key}>
              {type.label}
            </option>
          ))}
        </Select>
        <FieldError error={errors.enquiryType} data-testid="enquiry-type-error" />
      </FieldLabel>

      {isAirbnbEnquiry && <AirbnbDetails errors={errors} register={register} setValue={setValue} unregister={unregister} />}

      {isBookingEnquiry && (
        <FieldLabel htmlFor="bookingReference" flex="1 1 auto">
          Booking reference
          <Input
            id="bookingReference"
            name="bookingReference"
            {...register('bookingReference', fieldConfig.bookingReference)}
            error={!!errors.bookingReference}
          />
          <FieldError error={errors.bookingReference} data-testid="booking-reference-error" />
        </FieldLabel>
      )}
    </Flex>
  );
};

EnquiryDetails.propTypes = {
  errors: PropTypes.object,
  register: PropTypes.func.isRequired,
  watch: PropTypes.func.isRequired,
  setValue: PropTypes.func.isRequired,
  unregister: PropTypes.func.isRequired,
};

EnquiryDetails.defaultProps = {
  errors: {},
};

export default EnquiryDetails;
