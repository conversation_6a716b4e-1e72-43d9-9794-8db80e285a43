import { format, addYears } from 'date-fns';
import PropTypes from 'prop-types';
import React, { Fragment, useCallback, useState } from 'react';
import { useUnmount, useUpdateEffect } from 'react-use';
import { ButtonGroup, Box, Input } from '@qga/roo-ui/components';
import DatePicker from 'components/DatePicker';
import { FieldError, FieldLabel } from '@qga/components';
import fieldConfig from '../../fieldConfig';

const dateFormat = 'yyyy-MM-dd';
export const datePickerConfig = {
  anchorX: 'left',
  labelOptions: {
    hidden: true,
  },
  minDate: addYears(new Date(), -1),
  submitOnChange: false,
  viewThreshold: 0,
};

export const existingBookingOptions = [
  { label: 'No', value: 'no' },
  { label: 'Yes', value: 'yes' },
];

const AirbnbDetails = ({ errors, register, setValue, unregister }) => {
  const [existingBooking, setExistingBooking] = useState('no');
  const [selectedDates, setSelectedDates] = useState({});
  const hasExistingBooking = existingBooking === 'yes';

  useUpdateEffect(() => {
    if (hasExistingBooking) {
      register({ name: 'abnbCheckIn' }, { required: 'Please enter your check-in date' });
      register({ name: 'abnbCheckOut' }, { required: 'Please enter your check-out date' });
    }

    if (!hasExistingBooking) {
      unregister('abnbCheckIn');
      unregister('abnbCheckOut');
    }
  }, [hasExistingBooking, register, unregister]);

  useUnmount(() => {
    unregister('abnbCheckIn');
    unregister('abnbCheckOut');
  }, [unregister]);

  const onChangeDates = useCallback(
    ({ checkIn, checkOut }) => {
      if (checkIn) {
        setValue('abnbCheckIn', format(checkIn, dateFormat));
      }

      if (checkOut) {
        setValue('abnbCheckOut', format(checkOut, dateFormat));
      }
      setSelectedDates({ startDate: checkIn, endDate: checkOut });
    },
    [setValue],
  );

  return (
    <Fragment>
      <Box>
        <FieldLabel htmlFor="existingBooking" mb={4}>
          Do you have an existing Airbnb booking?
          <ButtonGroup
            id="existingBooking"
            name="existingBooking"
            options={existingBookingOptions}
            onChange={setExistingBooking}
            value={existingBooking}
            data-testid="abnb-existing-booking-button"
          />
        </FieldLabel>
      </Box>
      {hasExistingBooking && (
        <Fragment>
          <FieldLabel htmlFor="abnbBookingReference" mb={4}>
            Airbnb Booking Number
            <Input
              id="abnbBookingReference"
              name="abnbBookingReference"
              type="text"
              {...register('abnbBookingReference', fieldConfig.airbnbBookingReference)}
              error={!!errors.abnbBookingReference}
              mb={0}
            />
            <FieldError error={errors.abnbBookingReference} data-testid="abnb-booking-reference-error" />
          </FieldLabel>

          <FieldLabel htmlFor="abnbStayDates">
            Stay dates
            <DatePicker
              id="stayDates"
              name="stayDates"
              data-testid="stay-dates"
              mb={4}
              updateQuery={onChangeDates}
              selectedDates={selectedDates}
              error={!!errors.abnbCheckIn || !!errors.abnbCheckOut}
              {...datePickerConfig}
            />
            <FieldError error={errors.abnbCheckIn} data-testid="abnb-check-in-error" />
            <FieldError error={errors.abnbCheckOut} data-testid="abnb-check-out-error" />
          </FieldLabel>

          <FieldLabel htmlFor="abnbBookingCost" mb={4}>
            Booking cost
            <Input
              id="abnbBookingCost"
              name="abnbBookingCost"
              type="text"
              {...register('abnbBookingCost', fieldConfig.airbnbBookingCost)}
              error={!!errors.abnbBookingCost}
              mb={0}
            />
            <FieldError error={errors.abnbBookingCost} data-testid="abnb-booking-cost-error" />
          </FieldLabel>
        </Fragment>
      )}
    </Fragment>
  );
};

AirbnbDetails.propTypes = {
  errors: PropTypes.object,
  register: PropTypes.func.isRequired,
  setValue: PropTypes.func.isRequired,
  unregister: PropTypes.func.isRequired,
};
AirbnbDetails.defaultProps = {
  errors: {},
};

export default AirbnbDetails;
