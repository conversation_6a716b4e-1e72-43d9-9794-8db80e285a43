import React from 'react';
import { act } from 'react-dom/test-utils';
import { mountUtils } from 'test-utils';
import AirbnbDetails, { datePickerConfig, existingBookingOptions } from './AirbnbDetails';
import { FieldError } from '@qga/components';

jest.mock('store/ui/uiSelectors');

const errors = {
  abnbBookingReference: { message: 'airbnb booking reference error' },
  abnbCheckIn: { message: 'airbnb check in error' },
  abnbCheckOut: { message: 'airbnb check out error' },
  abnbBookingCost: { message: 'airbnb booking cost error' },
};

const registerRef = React.createRef();

const decorators = { router: true, store: true, theme: true };
const props = {
  errors,
  handleSubmit: jest.fn(),
  register: jest.fn().mockReturnValue(registerRef),
  setValue: jest.fn(),
  watch: jest.fn(),
  unregister: jest.fn(),
};

const render = () => mountUtils(<AirbnbDetails {...props} />, { decorators });
const renderWithBooking = () => {
  const { find, wrapper } = render();
  const button = find('ButtonGroup[data-testid="abnb-existing-booking-button"]');

  act(() => {
    button.prop('onChange')('yes');
  });

  wrapper.update();
  return wrapper;
};

beforeEach(() => {
  jest.clearAllMocks();
});

it('shows the existing booking button group', () => {
  const { find } = render();

  expect(find('ButtonGroup[name="existingBooking"]')).toHaveProp({
    id: 'existingBooking',
    options: existingBookingOptions,
  });
});

describe('with no existing booking', () => {
  it('does NOT show the airbnb booking reference field', () => {
    const { find } = render();

    expect(find('Input[name="abnbBookingReference"]')).not.toExist();
  });

  it('does NOT show the airbnb stay dates field', () => {
    const { find } = render();

    expect(find('DatePicker[name="stayDates"]')).not.toExist();
  });

  it('does NOT show the airbnb booking cost field', () => {
    const { find } = render();

    expect(find('Input[name="abnbBookingCost"]')).not.toExist();
  });
});

describe('with an existing booking', () => {
  describe('abnbBookingReference', () => {
    it('renders the field', () => {
      const wrapper = renderWithBooking();

      expect(wrapper.find('Input[name="abnbBookingReference"]')).toHaveProp({
        id: 'abnbBookingReference',
        type: 'text',
        error: true,
      });
    });

    it('registers the field with the validation rule', () => {
      renderWithBooking();

      expect(props.register.mock.calls[0]).toMatchObject([
        'abnbBookingReference',
        {
          maxLength: { message: 'Airbnb Booking reference should be a maximum of 20 characters', value: 20 },
          required: 'Please enter your Airbnb booking reference',
        },
      ]);
    });

    it('renders the field error', () => {
      const wrapper = renderWithBooking();

      expect(wrapper.find(FieldError).filterWhere((c) => c.prop('data-testid') === 'abnb-booking-reference-error')).toHaveProp({
        error: errors.abnbBookingReference,
      });
    });
  });

  describe('stayDates', () => {
    it('registers the checkIn date', () => {
      renderWithBooking();

      expect(props.register.mock.calls[2]).toMatchObject([{ name: 'abnbCheckIn' }, { required: 'Please enter your check-in date' }]);
    });

    it('registers the checkOut date', () => {
      renderWithBooking();

      expect(props.register.mock.calls[3]).toMatchObject([{ name: 'abnbCheckOut' }, { required: 'Please enter your check-out date' }]);
    });

    it('shows the DatePicker component', () => {
      const wrapper = renderWithBooking();

      expect(wrapper.find('DatePicker[name="stayDates"]')).toHaveProp({
        id: 'stayDates',
        ...datePickerConfig,
      });
    });

    it('shows checkIn date errors', () => {
      const wrapper = renderWithBooking();

      expect(wrapper.find(FieldError).filterWhere((c) => c.prop('data-testid') === 'abnb-check-in-error')).toHaveProp({
        error: errors.abnbCheckIn,
      });
    });

    it('shows checkOut date errors', () => {
      const wrapper = renderWithBooking();

      expect(wrapper.find(FieldError).filterWhere((c) => c.prop('data-testid') === 'abnb-check-out-error')).toHaveProp({
        error: errors.abnbCheckOut,
      });
    });

    describe('when changing the stay dates', () => {
      it('sets the checkIn date in the `dd/mm/yyyy` format', () => {
        const checkIn = new Date(2020, 11, 5);
        const wrapper = renderWithBooking();

        act(() => {
          wrapper.find('DatePicker[name="stayDates"]').prop('updateQuery')({ checkIn });
          wrapper.update();
        });

        expect(props.setValue).toHaveBeenCalledWith('abnbCheckIn', '2020-12-05');
      });

      it('sets the checkOut date in the `dd/mm/yyyy` format', () => {
        const checkOut = new Date(2020, 11, 7);
        const wrapper = renderWithBooking();

        act(() => {
          wrapper.find('DatePicker[name="stayDates"]').prop('updateQuery')({ checkOut });
          wrapper.update();
        });

        expect(props.setValue).toHaveBeenCalledWith('abnbCheckOut', '2020-12-07');
      });
    });

    describe('when unmounting', () => {
      it('unregisters the checkIn date', () => {
        const wrapper = renderWithBooking();
        act(() => {
          wrapper.unmount();
        });

        expect(props.unregister).toHaveBeenCalledWith('abnbCheckIn');
      });

      it('unregisters the checkOut date', () => {
        const wrapper = renderWithBooking();
        act(() => {
          wrapper.unmount();
        });

        expect(props.unregister).toHaveBeenCalledWith('abnbCheckOut');
      });
    });
  });

  describe('abnbBookingCost', () => {
    it('renders the field', () => {
      const wrapper = renderWithBooking();

      expect(wrapper.find('Input[name="abnbBookingCost"]')).toHaveProp({
        id: 'abnbBookingCost',
        error: true,
        type: 'text',
      });
    });

    it('registers the field with the validation rule', () => {
      renderWithBooking();

      expect(props.register.mock.calls[1]).toMatchObject([
        'abnbBookingCost',
        {
          maxLength: { message: 'Airbnb booking cost should be less than 10 digits', value: 10 },
          required: 'Please enter your Airbnb booking cost',
        },
      ]);
    });

    it('renders the field error', () => {
      const wrapper = renderWithBooking();

      expect(wrapper.find(FieldError).filterWhere((c) => c.prop('data-testid') === 'abnb-booking-cost-error')).toHaveProp({
        error: errors.abnbBookingCost,
      });
    });
  });
});
