import React from 'react';
import { mountUtils, resetConfigModule } from 'test-utils';
import EnquiryDetails from './EnquiryDetails';
import { useDataLayer } from 'hooks/useDataLayer';
import { getQueryParams } from 'store/router/routerSelectors';
import { getMemberId } from 'store/user/userSelectors';
import * as config from 'config';
import { FieldError } from '@qga/components';

jest.mock('config');

jest.mock('store/user/userSelectors');
jest.mock('store/router/routerSelectors');
jest.mock('store/enquiry/enquirySelectors');
jest.mock('hooks/useDataLayer');
const emitInteractionEvent = jest.fn();

const errors = {
  enquiryType: { message: 'enquiry type error' },
  bookingReference: { message: 'booking reference error' },
  qffNumber: { message: 'qffNumber error' },
};

const decorators = { router: true, store: true, theme: true };
const props = {
  errors,
  handleSubmit: jest.fn(),
  register: jest.fn(),
  setValue: jest.fn(),
  watch: jest.fn(),
  unregister: jest.fn(),
};

const render = () => mountUtils(<EnquiryDetails {...props} />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  getQueryParams.mockReturnValue({ enquiryType: undefined });
  getMemberId.mockReturnValue('**********');

  resetConfigModule(config);
  config.QFF_ACCOUNT_MANAGEMENT = config.ACCOUNT_MANAGEMENT_TYPES.APP_WIDE;
});

describe('enquiry type dropdown', () => {
  beforeEach(() => {
    props.watch.mockReturnValue('cancellation');
  });

  it('renders the field', () => {
    const { find } = render();

    expect(find('Select[name="enquiryType"]')).toHaveProp({
      id: 'enquiryType',
      error: true,
    });
  });

  it('registers the field with the validation rule', () => {
    render();

    expect(props.register.mock.calls[0]).toMatchObject([
      'qffNumber',
      { maxLength: { message: 'QFF number should be of 20 digits', value: 20 } },
    ]);
  });

  it('emits an event to the data layer when selected', () => {
    const { find } = render();
    const filedSelected = find('Select[name="enquiryType"]');

    filedSelected.simulate('click');

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Help Topic Dropdown',
      value: 'Cancellation Enquiry Selected',
    });
  });

  it('renders the field error', () => {
    const { find } = render();

    expect(find(FieldError).filterWhere((c) => c.prop('data-testid') === 'enquiry-type-error')).toHaveProp({
      error: errors.enquiryType,
    });
  });

  it('sets the default enquiry type value', () => {
    getQueryParams.mockReturnValue({ enquiryType: 'supplier' });
    const { find } = render();

    expect(find('Select[name="enquiryType"]')).toHaveProp({
      id: 'enquiryType',
      defaultValue: 'supplier',
    });
  });
});

describe('on load', () => {
  beforeEach(() => {
    props.watch.mockReturnValue(null);
  });

  it('sets the defaultValue to an empty string', () => {
    const { find } = render();

    expect(find('Select[name="enquiryType"]').prop('defaultValue')).toEqual('');
  });

  it('does NOT emit an event on page render', () => {
    expect(emitInteractionEvent).not.toHaveBeenCalled();
  });
});

describe('with a QH booking related enquiry', () => {
  beforeEach(() => {
    props.watch.mockReturnValue('cancellation');
  });

  it('shows the bookingReference field', () => {
    const { find } = render();

    expect(find('Input[name="bookingReference"]')).toHaveProp({
      id: 'bookingReference',
      error: true,
    });
  });

  it('registers the bookingReference field with the validation rule', () => {
    render();

    expect(props.register.mock.calls[2]).toMatchObject([
      'bookingReference',
      {
        maxLength: { message: 'Booking reference should be a maximum of 10 characters', value: 10 },
        required: 'Please enter your booking reference',
      },
    ]);
  });

  it('renders the bookingReference field error', () => {
    const { find } = render();

    expect(find(FieldError).filterWhere((c) => c.prop('data-testid') === 'booking-reference-error')).toHaveProp({
      error: errors.bookingReference,
    });
  });

  it('does NOT show the AirbnbDetails component', () => {
    const { find } = render();

    expect(find('AirbnbDetails')).not.toExist();
  });
});

describe('with a QH (non-booking) related enquiry', () => {
  beforeEach(() => {
    props.watch.mockReturnValue('general');
  });

  it('does NOT show the bookingReference field', () => {
    const { find } = render();

    expect(find('Input[name="bookingReference"]')).not.toExist();
  });

  it('does NOT show the AirbnbDetails component', () => {
    const { find } = render();

    expect(find('AirbnbDetails')).not.toExist();
  });
});

describe('with an Airbnb-related enquiry', () => {
  beforeEach(() => {
    props.watch.mockReturnValue('airbnb');
  });

  it('shows the AirbnbDetails component', () => {
    const { find } = render();

    expect(find('AirbnbDetails')).toExist();
  });

  it('does NOT show the bookingReference field', () => {
    const { find } = render();

    expect(find('Input[name="bookingReference"]')).not.toExist();
  });
});

describe('qffNumber', () => {
  describe('with app with account management', () => {
    beforeEach(() => {
      config.QFF_ACCOUNT_MANAGEMENT = config.ACCOUNT_MANAGEMENT_TYPES.APP_WIDE;
    });

    it('renders the field', () => {
      const { find } = render();

      expect(find('[name="qffNumber"]').first()).toHaveProp({
        id: 'qffNumber',
        error: true,
      });
    });

    it('registers the field with the validation rule', () => {
      render();

      expect(props.register.mock.calls[0]).toMatchObject([
        'qffNumber',
        { maxLength: { message: 'QFF number should be of 20 digits', value: 20 } },
      ]);
    });

    it('renders the field error', () => {
      const { find } = render();

      expect(find(FieldError).filterWhere((c) => c.prop('data-testid') === 'qff-number-error')).toHaveProp({
        error: errors.qffNumber,
      });
    });

    it('pre-populates the QFF number', () => {
      const { find } = render();

      expect(find('[name="qffNumber"]').first()).toHaveProp({
        id: 'qffNumber',
        defaultValue: '**********',
      });
    });
  });

  describe('with checkout only account management', () => {
    beforeEach(() => {
      config.QFF_ACCOUNT_MANAGEMENT = config.ACCOUNT_MANAGEMENT_TYPES.CHECKOUT_ONLY;
    });

    it('does not render the field', () => {
      const { find } = render();

      expect(find('[name="qffNumber"]')).not.toExist();
    });
  });
});
