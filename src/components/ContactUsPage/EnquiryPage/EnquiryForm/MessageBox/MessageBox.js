import PropTypes from 'prop-types';
import React from 'react';
import { Box } from '@qga/roo-ui/components';
import { FieldLabel, FieldError } from '@qga/components';
import TextArea from 'components/TextArea';

const MessageBox = ({ errors, register }) => (
  <Box>
    <FieldLabel htmlFor="message">
      Your message
      <TextArea maxLength={1000} rows={5} {...register('message')} id="message" name="message" error={!!errors.message} />
      <FieldError error={errors.message} data-testid="message-error" />
    </FieldLabel>
  </Box>
);

MessageBox.propTypes = {
  errors: PropTypes.object,
  register: PropTypes.func.isRequired,
};

MessageBox.defaultProps = {
  errors: {},
};

export default MessageBox;
