import React, { Fragment } from 'react';
import Head from 'next/head';
import { useSelector } from 'react-redux';
import { CONTACT_US_PAGE_META, EMAIL_BREADCRUMBS, EMAIL_ENQUIRY_STATUS } from 'lib/enums/contactUs';
import Breadcrumbs from 'components/Breadcrumbs';
import PageBlock from 'components/PageBlock';
import EnquiryForm from './EnquiryForm';
import Header from 'components/ContactUsPage/Header';
import ErrorBanner from './EnquiryForm/ErrorBanner';
import EnquiryConfirmationMessage from './EnquiryForm/EnquiryConfirmationMessage';
import { getStatus } from 'store/enquiry/enquirySelectors';
import { HOTELS_BRAND_NAME } from 'config';

const EnquiryLayout = () => {
  const queryStatus = useSelector(getStatus);
  const isComplete = queryStatus === EMAIL_ENQUIRY_STATUS.COMPLETE;
  const hasError = queryStatus === EMAIL_ENQUIRY_STATUS.ERROR;

  return (
    <Fragment>
      <Head>
        <title>Contact us | {HOTELS_BRAND_NAME} Australia</title>
        {CONTACT_US_PAGE_META.map(({ name, content }) => (
          <meta key={name} name={name} content={content} />
        ))}
      </Head>
      <PageBlock bg="white" py={10} px={[4, 6, 0]} data-print-style="page-block">
        <Breadcrumbs context="Contact Us" crumbs={EMAIL_BREADCRUMBS} />
      </PageBlock>
      <Header
        pb={10}
        title="Email us"
        description={`Get in touch with our team if you have questions about your ${HOTELS_BRAND_NAME} booking, would like to give feedback about your experience and more.`}
      />

      {hasError && <ErrorBanner />}
      {isComplete ? <EnquiryConfirmationMessage /> : <EnquiryForm />}
    </Fragment>
  );
};

export default EnquiryLayout;
