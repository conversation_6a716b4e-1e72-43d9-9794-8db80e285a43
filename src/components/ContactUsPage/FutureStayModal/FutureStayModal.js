import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { Box, NakedButton, Text } from '@qga/roo-ui/components';
import Modal from 'components/Modal';
import { useModal } from 'lib/hooks';
import { mediaQuery } from 'lib/styledSystem';
import { useDataLayer } from 'hooks/useDataLayer';

const StyledText = styled(Text)`
  display: block;
  font-size: ${themeGet('fontSizes.sm')};
  ${mediaQuery.minWidth.md} {
    font-size: ${themeGet('fontSizes.base')};
  }
`;

const FutureStayModal = ({ children, ...props }) => {
  const { openModal, closeModal, modalProps } = useModal();
  const { emitInteractionEvent } = useDataLayer();

  const openModalHandler = useCallback(() => {
    emitInteractionEvent({ type: 'Future Stay Find Out More Pop Up', value: 'Link Selected' });
    openModal();
  }, [emitInteractionEvent, openModal]);

  const closeModalHandler = useCallback(() => {
    emitInteractionEvent({ type: 'Future Stay Find Out More Pop Up', value: 'Dismiss Selected' });
    closeModal();
  }, [emitInteractionEvent, closeModal]);

  return (
    <Box display="inline" {...props}>
      <NakedButton onClick={openModalHandler} data-testid="future-stay-open-modal-button">
        {children}
      </NakedButton>
      <Modal {...modalProps} onRequestClose={closeModalHandler} padding={[5, 12]} title="Bookings Beyond 31 May 2020">
        <Box data-testid="future-stay-modal-content">
          <StyledText mb={6}>
            If you have a booking beyond 31 May 2020 that you wish to amend to a later date of travel, please manage your booking online or
            chat to us via live chat.
          </StyledText>

          <StyledText mb={6}>
            If you wish to check if you’re eligible for a refund, or take advantage of our credit voucher offer, (where you could receive
            1,000 Bonus Qantas Points), please contact us via the form and we will get to your enquiry as soon as all imminent enquiries are
            addressed.
          </StyledText>

          <StyledText>We appreciate your patience at this time.</StyledText>
        </Box>
      </Modal>
    </Box>
  );
};

FutureStayModal.propTypes = {
  children: PropTypes.node.isRequired,
};

export default FutureStayModal;
