import React from 'react';
import { mountUtils } from 'test-utils';
import InternationalHotelsModal from './InternationalHotelsModal';
import { useDataLayer } from 'hooks/useDataLayer';

jest.mock('hooks/useDataLayer');
jest.mock('store/ui/uiSelectors');
const emitInteractionEvent = jest.fn();

const render = (props) =>
  mountUtils(
    <InternationalHotelsModal {...props}>
      <div data-testid="modal-cta">Click me</div>
    </InternationalHotelsModal>,
    { decorators: { theme: true, store: true } },
  );

const renderModal = (props) => {
  const { wrapper, findByTestId } = render(props);
  findByTestId('intl-hotels-open-modal-button').first().simulate('click');
  wrapper.update();
  return wrapper.find('Modal');
};

beforeEach(() => {
  jest.clearAllMocks();

  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

it('renders the CTA for the modal', () => {
  const { findByTestId } = render();
  const cta = findByTestId('modal-cta');
  expect(cta).toExist();
  expect(cta.text()).toEqual('Click me');
});

it('renders the modal header', () => {
  const wrapper = renderModal();

  expect(wrapper.find('[data-testid="modal-header"]').first().text()).toMatch(/hotels outside of australia/i);
});

it('renders the modal content', () => {
  const wrapper = renderModal();

  const content = wrapper.find('[data-testid="intl-hotels-modal-content"]');

  expect(content).toExist();
  expect(content.first().text()).toMatch(/outside of australia/i);
});

it('emits an event to the data layer when the modal is opened', () => {
  renderModal();

  expect(emitInteractionEvent).toHaveBeenCalledWith({
    type: 'International Hotels Find Out More Pop Up',
    value: 'Link Selected',
  });
});

it('emits an event to the data layer when the modal is dismissed', () => {
  const modal = renderModal();

  const closeButton = modal.find('[data-testid="close"]');
  closeButton.first().simulate('click');

  expect(emitInteractionEvent).toHaveBeenCalledWith({
    type: 'International Hotels Find Out More Pop Up',
    value: 'Dismiss Selected',
  });
});
