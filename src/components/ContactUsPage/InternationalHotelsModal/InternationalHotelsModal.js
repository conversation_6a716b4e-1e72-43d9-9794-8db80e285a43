import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { Box, NakedButton, Text } from '@qga/roo-ui/components';
import Modal from 'components/Modal';
import { useModal } from 'lib/hooks';
import { mediaQuery } from 'lib/styledSystem';
import { useDataLayer } from 'hooks/useDataLayer';
import { HOTELS_BRAND_NAME } from 'config';

const StyledText = styled(Text)`
  display: block;
  font-size: ${themeGet('fontSizes.sm')};
  ${mediaQuery.minWidth.md} {
    font-size: ${themeGet('fontSizes.base')};
  }
`;

const InternationalHotelsModal = ({ children, ...props }) => {
  const { openModal, closeModal, modalProps } = useModal();
  const { emitInteractionEvent } = useDataLayer();

  const openModalHandler = useCallback(() => {
    emitInteractionEvent({ type: 'International Hotels Find Out More Pop Up', value: 'Link Selected' });
    openModal();
  }, [emitInteractionEvent, openModal]);

  const closeModalHandler = useCallback(() => {
    emitInteractionEvent({ type: 'International Hotels Find Out More Pop Up', value: 'Dismiss Selected' });
    closeModal();
  }, [emitInteractionEvent, closeModal]);

  return (
    <Box display="inline" {...props}>
      <NakedButton onClick={openModalHandler} data-testid="intl-hotels-open-modal-button">
        {children}
      </NakedButton>
      <Modal {...modalProps} onRequestClose={closeModalHandler} padding={[5, 12]} title="Hotels outside of Australia">
        <Box data-testid="intl-hotels-modal-content">
          <StyledText mb={6}>
            If you have a booking that is outside of Australia made before 19 March 2020, and your stay dates are between 20 March and 31
            May 2020, you will be eligible for a credit voucher for the full amount of your booking. Plus, you’ll earn 1,000 bonus Qantas
            Points.
          </StyledText>

          <StyledText mb={6}>
            Alternatively, if you’d prefer the money to be refunded, we can arrange for the full amount to be returned via the method used
            to pay for the original booking. There are no bonus Qantas Points available with this option.
          </StyledText>

          <StyledText>
            For all other international bookings that had stay dates before 20 March 2020, please contact {HOTELS_BRAND_NAME} Customer
            Service team to discuss the applicable hotel’s cancellation or change policy.
          </StyledText>
        </Box>
      </Modal>
    </Box>
  );
};

InternationalHotelsModal.propTypes = {
  children: PropTypes.node.isRequired,
};

export default InternationalHotelsModal;
