import React from 'react';
import { headset } from '@qga/roo-ui/assets';
import { mountUtils } from 'test-utils';
import CallUsCard from './CallUsCard';
import { DOMESTIC_PHONE_NUMBER, INTERNATIONAL_PHONE_NUMBER, PHONE_SUPPORT_OPERATING_TIMES } from 'config';

const decorators = { router: true, theme: true };
const render = () => mountUtils(<CallUsCard />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
});

it('shows the card heading ', () => {
  const { find } = render();

  expect(find('Card[data-testid="call-us"]')).toHaveProp({
    id: 'call-us',
    imageAltText: 'Headset icon',
    imageSrc: headset,
    title: 'Call our customer services team',
  });
});

it('shows the contact center opening hours', () => {
  const { findByTestId } = render();

  expect(findByTestId('phone-support-opening-hours').text()).toMatch(PHONE_SUPPORT_OPERATING_TIMES);
});

it('shows the domestic phone number for desktop', () => {
  const { findByTestId } = render();

  expect(findByTestId('domestic-number-desktop').text()).toEqual(DOMESTIC_PHONE_NUMBER);
  expect(findByTestId('domestic-number-desktop')).toHaveProp({
    href: `tel:${DOMESTIC_PHONE_NUMBER}`,
  });
});

it('shows the domestic phone number for mobile', () => {
  const { findByTestId } = render();

  expect(findByTestId('domestic-number-mobile').text()).toEqual(DOMESTIC_PHONE_NUMBER);
  expect(findByTestId('domestic-number-mobile')).toHaveProp({
    href: `tel:${DOMESTIC_PHONE_NUMBER}`,
  });
});

it('shows the international phone number', () => {
  const { findByTestId } = render();

  expect(findByTestId('intl-number').text()).toEqual(INTERNATIONAL_PHONE_NUMBER);
  expect(findByTestId('intl-number')).toHaveProp({
    href: `tel:${INTERNATIONAL_PHONE_NUMBER}`,
  });
});

it('shows the contact centre busy messaging', () => {
  const { findByTestId } = render();

  expect(findByTestId('contact-centre-busy-messaging').text()).toMatch(
    /strongly encourage only those customers staying within the next 7 days/i,
  );
});

it('does NOT show a link to send an email', () => {
  const { findByTestId } = render();

  expect(findByTestId('email-link')).not.toExist();
});
