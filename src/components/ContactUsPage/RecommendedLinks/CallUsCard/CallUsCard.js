import React from 'react';
import { headset } from '@qga/roo-ui/assets';
import { Flex, Hide, Link, OutlineButton, Text } from '@qga/roo-ui/components';
import Card from 'components/ContactUsPage/RecommendedLinks/Card';
import { DOMESTIC_PHONE_NUMBER, INTERNATIONAL_PHONE_NUMBER, PHONE_SUPPORT_OPERATING_TIMES } from 'config';

const CallUsCard = () => (
  <Card id="call-us" imageAltText="Headset icon" imageSrc={headset} title="Call our customer services team" mb={12} data-testid="call-us">
    <Text display="block" fontSize="sm" fontWeight="bold" data-testid="contact-centre-busy-messaging" mb={4}>
      We strongly encourage only those customers staying within the next 7 days to contact us if needed.
    </Text>

    <Text display="block" fontSize="base" data-testid="phone-support-opening-hours">
      {PHONE_SUPPORT_OPERATING_TIMES}
    </Text>
    <Text display="block" fontSize="base" mb={4}>
      Monday to Sunday
    </Text>

    <Hide xs>
      <Text
        as={Link}
        color="greys.charcoal"
        display="block"
        fontSize="lg"
        href={`tel:${DOMESTIC_PHONE_NUMBER}`}
        data-testid="domestic-number-desktop"
      >
        {DOMESTIC_PHONE_NUMBER}
      </Text>
    </Hide>

    <Hide sm md lg>
      <OutlineButton href={`tel:${DOMESTIC_PHONE_NUMBER}`} variant="primary" width={1} mb={2} data-testid="domestic-number-mobile">
        {DOMESTIC_PHONE_NUMBER}
      </OutlineButton>
    </Hide>

    <Flex alignItems="center" justifyContent={['center', 'flex-start']}>
      <Text display="inline" fontSize="base">
        Outside Australia:&nbsp;
      </Text>
      <Link color="greys.charcoal" href={`tel:${INTERNATIONAL_PHONE_NUMBER}`} data-testid="intl-number">
        {INTERNATIONAL_PHONE_NUMBER}
      </Link>
    </Flex>
  </Card>
);

export default CallUsCard;
