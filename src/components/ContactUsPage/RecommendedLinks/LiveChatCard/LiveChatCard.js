import PropTypes from 'prop-types';
import React, { useCallback } from 'react';
import { browser } from '@qga/roo-ui/assets';
import { Flex, Icon, Text } from '@qga/roo-ui/components';
import Card from 'components/ContactUsPage/RecommendedLinks/Card';
import ResponsiveButtonLink from 'components/ContactUsPage/ResponsiveButtonLink';
import useLiveAgent from './useLiveAgent';
import { LIVE_CHAT_OPERATING_TIMES, HOTELS_BRAND_NAME } from 'config';
import { useDataLayer } from 'hooks/useDataLayer';

const LiveChatBadge = ({ color, text, ...props }) => (
  <Flex alignItems="center" backgroundColor="greys.porcelain" borderRadius={3} width="fit-content" mb={4} px={4} py={2} {...props}>
    <Icon name="circle" color={color} size={14} />
    <Text fontSize="fontSizes.xs" ml={2}>
      {text}
    </Text>
  </Flex>
);

LiveChatBadge.propTypes = {
  color: PropTypes.string.isRequired,
  text: PropTypes.string.isRequired,
};

const LiveChatCard = () => {
  const { emitInteractionEvent } = useDataLayer();

  const liveChatOnlineElement = 'liveChatOnline';
  const liveChatOfflineElement = 'liveChatOffline';

  const { startChat, isOpen } = useLiveAgent({ liveChatOnlineElement, liveChatOfflineElement });

  const callCentreOpen = isOpen();

  const handleStartChatLinkClick = useCallback(() => {
    emitInteractionEvent({ type: 'Chat Online Now Link', value: 'Link Selected' });
    startChat();
  }, [emitInteractionEvent, startChat]);

  return (
    <Card id="live-chat" imageAltText="Browser icon" imageSrc={browser} title="Chat to our team online" mb={12} data-testid="live-chat">
      <Text display="block" fontSize="sm" fontWeight="bold" data-testid="contact-centre-busy-messaging" mb={4}>
        We strongly encourage only those customers staying within the next 7 days to contact us on Live Chat if needed.
      </Text>

      <Text display="block" fontSize="base" mb={4} data-testid="live-chat-directions">
        Need help with your {HOTELS_BRAND_NAME} booking? Chat with a member of our customer service team.
      </Text>

      {/* If updating chat times remember to update the useLiveAgent chat times also. */}
      <Text display="block" fontSize="md" data-testid="live-chat-opening-hours">
        {LIVE_CHAT_OPERATING_TIMES}
      </Text>
      <Text display="block" fontSize="base" mb={5} data-testid="opening-days">
        Monday to Friday
      </Text>

      <div id="liveChatOnline" style={{ display: 'none' }}>
        <LiveChatBadge color="green" text="Live chat available" data-testid="live-chat-available-badge" />
        <ResponsiveButtonLink onClick={handleStartChatLinkClick} text="Chat online now" data-testid="live-chat-link" />
      </div>

      {callCentreOpen ? (
        <div id="liveChatOffline" style={{ display: 'none' }}>
          <LiveChatBadge color="brand.primary" text="Live chat busy" data-testid="live-chat-busy-badge" />
          <Text data-testid="live-chat-busy-message">
            Our Live Chat team are currently attending to other customers.&nbsp;&nbsp;Please try again later.
          </Text>
        </div>
      ) : (
        <div id="liveChatOffline" style={{ display: 'none' }}>
          <LiveChatBadge color="greys.steel" text="Live chat unavailable" data-testid="live-chat-unavailable-badge" />
          <Text data-testid="live-chat-unavailable-message">
            Our Live Chat team is currently unavailable.&nbsp;&nbsp;Please try again later.
          </Text>
        </div>
      )}
    </Card>
  );
};

export default LiveChatCard;
