import React from 'react';
import { browser } from '@qga/roo-ui/assets';
import { mountUtils } from 'test-utils';
import LiveChatCard from './LiveChatCard';
import useLiveAgent from './useLiveAgent';
import * as config from 'config';

jest.mock('config');
jest.mock('./useLiveAgent');

const mockLiveAgent = {
  isOpen: jest.fn(),
  startChat: jest.fn(),
};

const decorators = { router: true, store: true, theme: true };
const render = () => mountUtils(<LiveChatCard />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
  config.HOTELS_BRAND_NAME = 'BRAND HOTELS';
  useLiveAgent.mockReturnValue(mockLiveAgent);
});

it('shows the card heading ', () => {
  const { find } = render();

  expect(find('Card[data-testid="live-chat"]')).toHaveProp({
    id: 'live-chat',
    imageAltText: 'Browser icon',
    imageSrc: browser,
    title: 'Chat to our team online',
  });
});

it('shows the directions for using live chat', () => {
  const { findByTestId } = render();

  expect(findByTestId('live-chat-directions')).toHaveText(
    'Need help with your BRAND HOTELS booking? Chat with a member of our customer service team.',
  );
});

it('shows the opening hours', () => {
  const { findByTestId } = render();

  expect(findByTestId('live-chat-opening-hours')).toHaveText(config.LIVE_CHAT_OPERATING_TIMES);
});

it('shows the opening days', () => {
  const { findByTestId } = render();

  expect(findByTestId('opening-days')).toHaveText('Monday to Friday');
});

describe('when the contact centre is open', () => {
  beforeEach(() => {
    mockLiveAgent.isOpen.mockReturnValue(true);
  });

  it('renders `liveChatOnline` with the correct styles', () => {
    const { find } = render();

    expect(find('#liveChatOnline')).toHaveProp({
      style: {
        display: 'none',
      },
    });
  });

  it('renders live chat available badge', () => {
    const { findByTestId } = render();

    expect(findByTestId('live-chat-available-badge')).toExist();
  });

  it('renders live chat link', () => {
    const { findByTestId } = render();

    expect(findByTestId('live-chat-link')).toExist();
  });
});

describe('when the contact centre is busy', () => {
  beforeEach(() => {
    mockLiveAgent.isOpen.mockReturnValue(true);
  });

  it('renders `liveChatOffline` with the correct styles', () => {
    const { find } = render();

    expect(find('#liveChatOffline')).toHaveProp({
      style: {
        display: 'none',
      },
    });
  });

  it('renders live chat busy badge', () => {
    const { findByTestId } = render();

    expect(findByTestId('live-chat-busy-badge')).toExist();
  });

  it('renders live chat busy messaging', () => {
    const { findByTestId } = render();

    expect(findByTestId('live-chat-busy-message').text()).toMatch(/Our Live Chat team are currently attending to other customers/);
  });
});

describe('when the contact centre is closed', () => {
  beforeEach(() => {
    mockLiveAgent.isOpen.mockReturnValue(false);
  });

  it('renders `liveChatOffline` with the correct styles', () => {
    const { find } = render();

    expect(find('#liveChatOffline')).toHaveProp({
      style: {
        display: 'none',
      },
    });
  });

  it('renders live chat unavailable badge', () => {
    const { findByTestId } = render();

    expect(findByTestId('live-chat-unavailable-badge')).toExist();
  });

  it('renders live chat unavailable messaging', () => {
    const { findByTestId } = render();

    expect(findByTestId('live-chat-unavailable-message').text()).toMatch(/Our Live Chat team is currently unavailable/);
  });
});
