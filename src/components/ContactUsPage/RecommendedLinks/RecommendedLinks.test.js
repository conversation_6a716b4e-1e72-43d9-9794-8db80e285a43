import React from 'react';
import { mountUtils } from 'test-utils';
import RecommendedLinks from './RecommendedLinks';
import { baggage, laptop, mail } from '@qga/roo-ui/assets';
import { useDataLayer } from 'hooks/useDataLayer';
import * as config from 'config';

jest.mock('config');
jest.mock('hooks/useDataLayer');

mountUtils.mockComponent('LiveChatCard');
mountUtils.mockComponent('CallUsCard');
mountUtils.mockComponent('PartnerServicesCard');

const decorators = { router: true, store: true, theme: true };
const emitInteractionEvent = jest.fn();

const render = () => mountUtils(<RecommendedLinks />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
  Object.assign(config, jest.requireActual('config'));
  config.SELF_SERVICE_ENABLED = true;
  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

it('shows the heading', () => {
  const { findByTestId } = render();

  expect(findByTestId('recommended-links-heading').text()).toMatch(/here to help/i);
});

describe('manage your booking', () => {
  it('shows the card heading ', () => {
    const { find } = render();

    expect(find('Card[data-testid="manage-your-booking"]')).toHaveProp({
      id: 'change-booking',
      imageAltText: 'Baggage icon',
      imageSrc: baggage,
      title: 'Change your booking',
    });
  });

  it('renders a description of self service', () => {
    const { findByTestId } = render();
    expect(findByTestId('self-service-text')).toHaveText(
      'You can do most things online. Avoid waiting on hold, here are some recommended links:',
    );
  });

  it('shows a link to self service', () => {
    const { find } = render();

    expect(find('ResponsiveButtonLink[data-testid="manage-your-booking-link"]')).toHaveProp({
      href: `${config.BRAND_SELF_SERVICE_URL}?source=QH-contact-us-page`,
      text: 'Manage your booking',
    });
  });

  describe('when clicking the link to self service', () => {
    it('dispatches an event to the data layer', () => {
      const { find } = render();

      find('ResponsiveButtonLink[data-testid="manage-your-booking-link"]').find('Link').simulate('click');

      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Manage Your Booking Link',
        value: 'Link Selected',
      });
    });
  });
});

describe('cancel your booking', () => {
  it('shows the card heading ', () => {
    const { find } = render();

    expect(find('Card[data-testid="cancel-your-booking"]')).toHaveProp({
      id: 'cancel-booking',
      imageAltText: 'Laptop icon',
      imageSrc: laptop,
      title: 'Cancel your booking',
    });
  });

  it('shows a link to self service', () => {
    const { find } = render();

    expect(find('ResponsiveButtonLink[data-testid="cancel-your-booking-link"]')).toHaveProp({
      href: `${config.BRAND_SELF_SERVICE_URL}?source=QH-contact-us-page`,
      text: 'Cancel your booking',
    });
  });

  describe('when clicking the link to self service', () => {
    it('dispatches an event to the data layer', () => {
      const { find } = render();

      find('ResponsiveButtonLink[data-testid="cancel-your-booking-link"]').find('Link').simulate('click');

      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Cancel Your Booking Link',
        value: 'Link Selected',
      });
    });
  });
});

describe('find your booking', () => {
  it('shows the card heading ', () => {
    const { find } = render();

    expect(find('Card[data-testid="find-your-booking"]')).toHaveProp({
      imageAltText: 'Baggage icon',
      imageSrc: baggage,
      title: 'Find information about your booking',
    });
  });

  it('shows a link to self service', () => {
    const { find } = render();

    expect(find('ResponsiveButtonLink[data-testid="view-your-booking-link"]')).toHaveProp({
      href: `${config.BRAND_SELF_SERVICE_URL}?source=QH-contact-us-page`,
      text: 'View your booking',
    });
  });

  describe('when clicking the link to self service', () => {
    it('dispatches an event to the data layer', () => {
      const { find } = render();

      find('ResponsiveButtonLink[data-testid="view-your-booking-link"]').find('Link').simulate('click');

      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'View Your Booking Link',
        value: 'Link Selected',
      });
    });
  });
});

describe('live chat', () => {
  it('shows the <LiveChatCard /> component', () => {
    const { find } = render();

    expect(find('LiveChatCard')).toExist();
  });
});

describe('call us', () => {
  it('shows the <CallUsCard /> component', () => {
    const { find } = render();

    expect(find('CallUsCard')).toExist();
  });
});

describe('email support', () => {
  it('shows the card heading', () => {
    const { find } = render();

    expect(find('Card[data-testid="email-support"]')).toHaveProp({
      imageAltText: 'Mail icon',
      imageSrc: mail,
      title: 'Email us',
    });
  });

  it('shows a link to QH support', () => {
    const { find } = render();

    expect(find('ResponsiveButtonLink[data-testid="email-support-link"]').first()).toHaveProp({
      to: config.CONTACT_US_FORM,
      text: 'Email us',
    });
  });

  describe('when clicking the email support link', () => {
    it('dispatches an event to the data layer', () => {
      const { find } = render();

      find('ResponsiveButtonLink[data-testid="email-support-link"]').find('Link').first().simulate('click');

      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Email Us Link',
        value: 'Link Selected',
      });
    });
  });
});

describe('partner services', () => {
  it('shows the <PartnerServicesCard /> component', () => {
    const { find } = render();

    expect(find('PartnerServicesCard')).toExist();
  });
});

describe('when SELF_SERVICE_ENABLED is false', () => {
  beforeEach(() => {
    config.SELF_SERVICE_ENABLED = false;
  });

  it('renders a description of self service', () => {
    const { findByTestId } = render();
    expect(findByTestId('self-service-text')).toHaveText('Here are some recommended information and links:');
  });

  it('does NOT show a link to self service', () => {
    const { findByTestId } = render();
    expect(findByTestId('manage-your-booking')).not.toExist();
  });

  it('does NOT show a link to cancel your booking', () => {
    const { findByTestId } = render();
    expect(findByTestId('cancel-your-booking')).not.toExist();
  });

  it('does NOT show a link to find your booking', () => {
    const { findByTestId } = render();
    expect(findByTestId('find-your-booking')).not.toExist();
  });
});
