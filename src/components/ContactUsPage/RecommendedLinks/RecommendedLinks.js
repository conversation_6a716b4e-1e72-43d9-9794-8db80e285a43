import React, { Fragment, useCallback } from 'react';
import { Flex, Heading, Text } from '@qga/roo-ui/components';
import { baggage, laptop, mail } from '@qga/roo-ui/assets';
import Card from './Card';
import LiveChatCard from './LiveChatCard';
import ResponsiveButtonLink from 'components/ContactUsPage/ResponsiveButtonLink';
import PageBlock from 'components/PageBlock';
import { BRAND_NAME, CONTACT_US_FORM, BRAND_SELF_SERVICE_URL, SELF_SERVICE_ENABLED } from 'config';
import { useDataLayer } from 'hooks/useDataLayer';
import CallUsCard from './CallUsCard';
import PartnerServicesCard from './PartnerServicesCard';
import AppLink from 'components/AppLink';

const SelfServiceText = 'You can do most things online. Avoid waiting on hold, here are some recommended links:';

const NoSelfServiceText = 'Here are some recommended information and links:';

const RecommendedLinks = () => {
  const { emitInteractionEvent } = useDataLayer();

  const selfServiceLink = `${BRAND_SELF_SERVICE_URL}?source=QH-contact-us-page`;

  const handleManageBookingLinkClick = useCallback(() => {
    emitInteractionEvent({ type: 'Manage Your Booking Link', value: 'Link Selected' });
  }, [emitInteractionEvent]);

  const handleCancelBookingLinkClick = useCallback(() => {
    emitInteractionEvent({ type: 'Cancel Your Booking Link', value: 'Link Selected' });
  }, [emitInteractionEvent]);

  const handleViewBookingLinkClick = useCallback(() => {
    emitInteractionEvent({ type: 'View Your Booking Link', value: 'Link Selected' });
  }, [emitInteractionEvent]);

  const handleEmailUsLinkClick = useCallback(() => {
    emitInteractionEvent({ type: 'Email Us Link', value: 'Link Selected' });
  }, [emitInteractionEvent]);

  return (
    <PageBlock bg="white" py={10} px={[4, 6, 0]}>
      <Heading.h2 data-testid="recommended-links-heading">We are here to help</Heading.h2>
      <Text display="block" fontSize="base" mb={12} data-testid="self-service-text">
        {SELF_SERVICE_ENABLED ? SelfServiceText : NoSelfServiceText}
      </Text>
      <Flex flexWrap="wrap">
        {SELF_SERVICE_ENABLED && (
          <Fragment>
            <Card
              id="change-booking"
              imageAltText="Baggage icon"
              imageSrc={baggage}
              title="Change your booking"
              mb={16}
              data-testid="manage-your-booking"
            >
              <Text display="block" fontSize="base" mb={5}>
                Need to make a change to your hotel booking? You can now view and print your reservation, or extend or shorten your stay
                online.
              </Text>
              <ResponsiveButtonLink
                href={selfServiceLink}
                onClick={handleManageBookingLinkClick}
                text="Manage your booking"
                data-testid="manage-your-booking-link"
              />
            </Card>

            <Card
              id="cancel-booking"
              imageAltText="Laptop icon"
              imageSrc={laptop}
              title="Cancel your booking"
              mb={16}
              data-testid="cancel-your-booking"
            >
              <Text display="block" fontSize="base" mb={5}>
                Need to cancel your hotel booking? Now available online.
              </Text>
              <ResponsiveButtonLink
                href={selfServiceLink}
                onClick={handleCancelBookingLinkClick}
                text="Cancel your booking"
                data-testid="cancel-your-booking-link"
              />
            </Card>
          </Fragment>
        )}
        <LiveChatCard />
        <CallUsCard />

        <Card id="email" imageAltText="Mail icon" imageSrc={mail} title="Email us" mb={16} data-testid="email-support">
          <Text display="block" fontSize="base" mb={5}>
            Get in touch with our team if you have questions about your {BRAND_NAME} Hotels booking, would like to give feedback about your
            experience and more.
          </Text>
          <ResponsiveButtonLink
            as={AppLink}
            to={CONTACT_US_FORM}
            onClick={handleEmailUsLinkClick}
            text="Email us"
            data-testid="email-support-link"
          />
        </Card>

        {SELF_SERVICE_ENABLED && (
          <Card
            imageAltText="Baggage icon"
            imageSrc={baggage}
            title="Find information about your booking"
            mb={16}
            data-testid="find-your-booking"
          >
            <Text display="block" fontSize="base" mb={5}>
              View your reservation details, print your booking confirmation, find directions to your hotel and more.
            </Text>
            <ResponsiveButtonLink
              href={selfServiceLink}
              onClick={handleViewBookingLinkClick}
              text="View your booking"
              data-testid="view-your-booking-link"
            />
          </Card>
        )}

        <PartnerServicesCard />
      </Flex>
    </PageBlock>
  );
};

export default RecommendedLinks;
