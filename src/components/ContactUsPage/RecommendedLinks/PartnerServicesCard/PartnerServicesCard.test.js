import React from 'react';
import { headset } from '@qga/roo-ui/assets';
import { mountUtils } from 'test-utils';
import PartnerServicesCard from './PartnerServicesCard';
import { useDataLayer } from 'hooks/useDataLayer';
import * as config from 'config';

jest.mock('config');
jest.mock('hooks/useDataLayer');

const decorators = { router: true, store: true, theme: true };
const emitInteractionEvent = jest.fn();

const render = () => mountUtils(<PartnerServicesCard />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  Object.assign(config, jest.requireActual('config'));
  config.BRAND_NAME = 'BRAND';
});

it('shows the card heading ', () => {
  const { find } = render();

  expect(find('Card[data-testid="partner-services"]')).toHaveProp({
    id: 'partner-services',
    imageAltText: 'Headset icon',
    imageSrc: headset,
    title: 'Qantas Group Accommodation partner services',
  });
});

it('shows a link to send an email', () => {
  const { find } = render();

  expect(find('ResponsiveButtonLink[data-testid="partner-services-email-link"]').prop('to')).toEqual(
    `${config.CONTACT_US_FORM}?enquiryType=supplier`,
  );
});

it('shows the partner services description', () => {
  const { findByTestId } = render();

  expect(findByTestId('partner-services-description')).toHaveText(
    'Contact us for support with the supplier extranet, reservations, or if you would like to become part of the network of hotels on the BRAND website.',
  );
});

describe('when clicking the email support link', () => {
  it('dispatches an event to the data layer', () => {
    const { find } = render();

    find('ResponsiveButtonLink[data-testid="partner-services-email-link"]').find('Link').first().simulate('click');

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Partner Services Email Us Link',
      value: 'Link Selected',
    });
  });
});

it('shows the contact center opening hours', () => {
  const { findByTestId } = render();

  expect(findByTestId('partner-services-opening-hours').text()).toMatch(config.PHONE_SUPPORT_OPERATING_TIMES);
});

it('shows the partner services phone number for desktop', () => {
  const { findByTestId } = render();

  expect(findByTestId('partner-services-number-desktop').text()).toEqual(config.PARTNER_SERVICES_PHONE_NUMBER);
  expect(findByTestId('partner-services-number-desktop')).toHaveProp({
    href: `tel:${config.PARTNER_SERVICES_PHONE_NUMBER}`,
  });
});

it('shows the partner services phone number for mobile', () => {
  const { findByTestId } = render();

  expect(findByTestId('partner-services-number-mobile').text()).toEqual(config.PARTNER_SERVICES_PHONE_NUMBER);
  expect(findByTestId('partner-services-number-mobile')).toHaveProp({
    href: `tel:${config.PARTNER_SERVICES_PHONE_NUMBER}`,
  });
});
