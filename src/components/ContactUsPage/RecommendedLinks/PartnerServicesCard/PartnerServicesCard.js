import React, { useCallback } from 'react';
import { headset } from '@qga/roo-ui/assets';
import { Hide, OutlineButton, Text, Box, Link } from '@qga/roo-ui/components';
import Card from 'components/ContactUsPage/RecommendedLinks/Card';
import ResponsiveButtonLink from 'components/ContactUsPage/ResponsiveButtonLink';
import { useDataLayer } from 'hooks/useDataLayer';
import { CONTACT_US_FORM, PARTNER_SERVICES_PHONE_NUMBER, PHONE_SUPPORT_OPERATING_TIMES, BRAND_NAME } from 'config';
import AppLink from 'components/AppLink';

const PartnerServicesCard = () => {
  const { emitInteractionEvent } = useDataLayer();

  const handleEmailLinkClick = useCallback(() => {
    emitInteractionEvent({ type: 'Partner Services Email Us Link', value: 'Link Selected' });
  }, [emitInteractionEvent]);

  return (
    <Card
      id="partner-services"
      imageAltText="Headset icon"
      imageSrc={headset}
      title="Qantas Group Accommodation partner services"
      mb={[12, 0]}
      data-testid="partner-services"
    >
      <Text display="block" fontSize="base" mb={5} data-testid="partner-services-description">
        Contact us for support with the supplier extranet, reservations, or if you would like to become part of the network of hotels on the{' '}
        {BRAND_NAME} website.
      </Text>

      <ResponsiveButtonLink
        as={AppLink}
        to={`${CONTACT_US_FORM}?enquiryType=supplier`}
        onClick={handleEmailLinkClick}
        text="Email us"
        data-testid="partner-services-email-link"
      />

      <Box mt={7}>
        <Text display="block" fontSize="base" data-testid="partner-services-opening-hours">
          {PHONE_SUPPORT_OPERATING_TIMES}
        </Text>
        <Text display="block" fontSize="base" mb={4}>
          Monday to Sunday
        </Text>

        <Hide xs>
          <Link
            href={`tel:${PARTNER_SERVICES_PHONE_NUMBER}`}
            color="greys.charcoal"
            display="block"
            fontSize="lg"
            data-testid="partner-services-number-desktop"
          >
            {PARTNER_SERVICES_PHONE_NUMBER}
          </Link>
        </Hide>

        <Hide sm md lg>
          <OutlineButton
            href={`tel:${PARTNER_SERVICES_PHONE_NUMBER}`}
            variant="primary"
            width={1}
            mb={2}
            data-testid="partner-services-number-mobile"
          >
            {PARTNER_SERVICES_PHONE_NUMBER}
          </OutlineButton>
        </Hide>
      </Box>
    </Card>
  );
};

export default PartnerServicesCard;
