import React from 'react';
import { mountUtils } from 'test-utils';
import MultiRoomMessage from './MultiRoomMessage';
import { MULTI_ROOM_BOOKING_PROMPT_MESSAGE, REQUEST_CALLBACK_PHONE_NUMBER } from 'config';
import { setRequestCallbackModalOpen } from 'store/property/propertyActions';
import { useDataLayer } from 'hooks/useDataLayer';

jest.mock('hooks/useDataLayer');

const decorators = { store: true, theme: true, router: true };
const emitInteractionEvent = jest.fn();
window.open = jest.fn();

useDataLayer.mockReturnValue({ emitInteractionEvent });
const render = () => mountUtils(<MultiRoomMessage />, { decorators });

describe('when icon is present', () => {
  it('renders the Icon', () => {
    const { find } = render();
    expect(find('Icon')).toHaveProp({
      name: 'error',
    });
  });

  describe('when group booking for more than 3 people', () => {
    it('renders the expected text', () => {
      const { findByTestId } = render();
      expect(findByTestId('multi-room-prompt-message')).toIncludeText(MULTI_ROOM_BOOKING_PROMPT_MESSAGE);
    });
  });

  it('emits an interaction event for opening the modal', () => {
    const { findByTestId } = render();
    findByTestId('request-callback-cta').simulate('click');
    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Request Callback Modal',
      value: 'Request Form Opened',
      customAttributes: {
        user_event_value: 'MultiRoom Message',
      },
    });
  });

  it('emits an interaction event for clicking the phone number', () => {
    const { findByTestId } = render();
    findByTestId('request-callback-number').simulate('click');
    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Request Callback Modal',
      value: 'Call Centre Phone Number Selected',
      customAttributes: {
        user_event_value: 'MultiRoom Message',
      },
    });
  });

  it('renders the request callback button', () => {
    const { findByTestId } = render({ isOpen: true });
    expect(findByTestId('request-callback-cta')).toExist();
  });

  it('renders the expected call back phone number', () => {
    const { findByTestId } = render();
    expect(findByTestId('request-callback-number')).toHaveText(REQUEST_CALLBACK_PHONE_NUMBER);
  });

  it('open the request callback modal when clicked', () => {
    const { findByTestId, decorators } = render();
    findByTestId('request-callback-cta').simulate('click');
    expect(decorators.store.dispatch).toHaveBeenCalledWith(setRequestCallbackModalOpen(true));
  });
});
