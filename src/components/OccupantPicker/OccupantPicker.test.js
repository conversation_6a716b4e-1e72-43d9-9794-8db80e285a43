import React from 'react';
import { act } from 'react-dom/test-utils';
import OccupantPicker from './OccupantPicker';
import { mountUtils, runAllTimers } from 'test-utils';
import ResponsiveModal from 'components/ResponsiveModal';

jest.useFakeTimers();
jest.mock('store/ui/uiSelectors');

const updateQuery = jest.fn();

const decorators = { store: true, theme: true };
const render = ({ adults, children, infants }) =>
  mountUtils(<OccupantPicker occupants={{ adults, children, infants }} updateQuery={updateQuery} viewThreshold={0.75} />, { decorators });

beforeEach(() => {
  jest.resetAllMocks();
});

describe('initial rendering', () => {
  it('sets the title on the ResponsiveModal', () => {
    const { find } = render({ adults: 1 });
    expect(find(ResponsiveModal).props().title).toEqual('Select guests');
  });

  it('sets the submit button text on the Footer', () => {
    const { find } = render({ adults: 1 });
    find('input').first().simulate('focus');
    expect(find('Footer').props().buttonText).toEqual('Confirm');
  });
});

describe('summary', () => {
  describe('with a single guest', () => {
    it('renders as a singular guest', () => {
      const { find } = render({ adults: 1 });
      expect(find('input').first().props().value).toEqual('1 guest');
    });
  });

  describe('with multiple guests', () => {
    it('renders with pluralized guests', () => {
      const { find } = render({ adults: 1, children: 1, infants: 1 });
      expect(find('input').first().props().value).toEqual('3 guests');
    });
  });

  describe('on phone', () => {
    describe('with a single guest', () => {
      it('renders as a singular guest', () => {
        const { find } = render({ adults: 1 });
        expect(find('input').last().props().value).toEqual('1 guest');
      });
    });

    describe('with multiple guests', () => {
      it('renders with pluralized guests', () => {
        const { find } = render({ adults: 1, children: 1, infants: 1 });
        expect(find('input').last().props().value).toEqual('3 guests');
      });
    });
  });
});

describe('dropdown', () => {
  it('opens when focusing on the phone input', () => {
    const { find } = render({ adults: 1 });
    expect(find('Dropdown')).not.toExist();
    find('input').first().simulate('focus');

    expect(find('Dropdown')).toExist();
  });

  it('opens when focusing on the non-phone input', () => {
    const { find, findByTestId } = render({ adults: 1 });
    expect(find('Dropdown')).not.toExist();
    findByTestId('occupant-picker-input').simulate('focus');

    expect(find('Dropdown')).toExist();
  });

  it('renders the skip to content button', () => {
    const { find } = render({ adults: 1 });
    find('input').first().simulate('focus');

    expect(find('SkipToContentButton')).toExist();
  });

  it('renders the number of adults', () => {
    const { find } = render({ adults: 1 });
    find('input').first().simulate('focus');

    expect(find('div[data-testid="occupant-count"]').at(0).text()).toEqual('1');
  });

  it('renders the number of children', () => {
    const { find } = render({ children: 1 });
    find('input').first().simulate('focus');

    expect(find('div[data-testid="occupant-count"]').at(1).text()).toEqual('1');
  });

  it('renders the number of infants', () => {
    const { find } = render({ infants: 1 });
    find('input').first().simulate('focus');

    expect(find('div[data-testid="occupant-count"]').at(2).text()).toEqual('1');
  });

  describe('editing occupants', () => {
    const occupantIndexMap = {
      adults: 0,
      children: 1,
      infants: 2,
    };

    ['adults', 'children', 'infants'].forEach((occupantType) => {
      it(`can increment and decrement ${occupantType}`, () => {
        const { find } = render({ adults: 5, children: 5, infants: 5 });
        find('input').first().simulate('focus');
        const occupantRow = find('Occupant').at(occupantIndexMap[occupantType]);
        occupantRow.find('button[data-testid="decrement"]').simulate('click');
        expect(occupantRow.find('div[data-testid="occupant-count"]').text()).toEqual('4');

        occupantRow.find('button[data-testid="increment"]').simulate('click');
        expect(occupantRow.find('div[data-testid="occupant-count"]').text()).toEqual('5');
      });

      it(`allows a maximum of 9 ${occupantType}`, () => {
        const { find } = render({ adults: 9, children: 9, infants: 9 });
        find('input').first().simulate('focus');
        const occupantRow = find('Occupant').at(occupantIndexMap[occupantType]);
        expect(occupantRow.find('button[data-testid="increment"]').props()['disabled']).toBe(true);
      });
    });

    ['children', 'infants'].forEach((occupantType) => {
      it(`allows a minimum of 0 ${occupantType}`, () => {
        const { find } = render({ children: 0, infants: 0 });
        find('input').first().simulate('focus');
        const occupantRow = find('Occupant').at(occupantIndexMap[occupantType]);
        expect(occupantRow.find('button[data-testid="decrement"]').props()['disabled']).toBe(true);
      });
    });

    it('allows a minimum of 1 adult', () => {
      const { find } = render({ adults: 1 });
      find('input').first().simulate('focus');
      const occupantRow = find('Occupant').at(occupantIndexMap['adults']);
      expect(occupantRow.find('button[data-testid="decrement"]').props()['disabled']).toBe(true);
    });

    it('updates the summary when updating occupants', () => {
      const { find } = render({ adults: 1 });
      expect(find('input').first().props().value).toEqual('1 guest');

      expect(find('input').last().props().value).toEqual('1 guest');

      find('input').first().simulate('focus');
      const occupantRow = find('Occupant').at(occupantIndexMap['adults']);
      occupantRow.find('button[data-testid="increment"]').simulate('click');

      expect(find('input').first().props().value).toEqual('2 guests');

      expect(find('input').last().props().value).toEqual('2 guests');
    });

    it('passes the viewThreshold to the Dropdown component', () => {
      const { find } = render({ adults: 1 });
      find('input').first().simulate('focus');
      expect(find('Dropdown')).toHaveProp({ viewThreshold: 0.75 });
    });

    describe('when closing with the close button', () => {
      let wrapper;
      beforeEach(async () => {
        wrapper = render({ adults: 1, children: 0, infants: 0 }).wrapper;
        wrapper.find('input').first().simulate('focus');
        act(() => {
          wrapper.find('[data-testid="occupant-picker-adults"]').find('button[data-testid="increment"]').simulate('click');
        });
        wrapper.update();
        expect(wrapper.find('Dropdown')).toExist();
        await act(async () => {
          wrapper.find('button[data-testid="close-modal"]').simulate('click');
          jest.runAllTimers();
        });
        wrapper.update();
      });

      it('closes the dropdown when closing the modal', () => {
        expect(wrapper.find('Dropdown')).not.toExist();
      });

      it('does not update the query when closing the modal', () => {
        expect(updateQuery).not.toHaveBeenCalled();
      });
    });

    describe('when occupants have changed', () => {
      let wrapper;

      beforeEach(() => {
        wrapper = render({ adults: 1, children: 1, infants: 1 }).wrapper;
        wrapper.find('input').first().simulate('focus');
        const occupantRow = wrapper.find('Occupant').at(0);
        occupantRow.find('button[data-testid="increment"]').simulate('click');
      });

      it('updates the query string when clicking "apply"', async () => {
        wrapper.find('button[data-testid="apply-button"]').simulate('click');
        runAllTimers();
        await flushPromises();
        expect(updateQuery).toHaveBeenCalledWith({ adults: 2, children: 1, infants: 1 });
      });

      it('updates the query string when clicking "done"', async () => {
        wrapper.find('button[data-testid="done-button"]').simulate('click');
        runAllTimers();
        await flushPromises();
        expect(updateQuery).toHaveBeenCalledWith({ adults: 2, children: 1, infants: 1 });
      });
    });

    describe('when occupants have not changed', () => {
      let wrapper;

      beforeEach(() => {
        wrapper = render({ adults: 1, children: 1, infants: 1 }).wrapper;
        wrapper.find('input').first().simulate('focus');
      });

      it('does not update the query string when clicking "done"', () => {
        wrapper.find('button[data-testid="done-button"]').simulate('click');
        expect(updateQuery).not.toHaveBeenCalled();
      });

      it('does not update the query string when clicking "apply"', () => {
        wrapper.find('button[data-testid="apply-button"]').simulate('click');
        expect(updateQuery).not.toHaveBeenCalled();
      });
    });
  });
});

describe('when the occupants update from state', () => {
  it('shows the correct guests count', () => {
    const { find, wrapper } = render({ occupants: { adults: 2, children: 0, infants: 0 } });

    act(() => {
      wrapper.setProps({ occupants: { adults: 1, children: 0, infants: 0 } });
    });
    wrapper.update();

    expect(find('input').first().props().value).toEqual('1 guest');
  });
});
