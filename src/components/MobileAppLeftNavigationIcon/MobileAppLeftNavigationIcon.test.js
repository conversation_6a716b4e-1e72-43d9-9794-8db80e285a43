import React from 'react';
import { mountUtils } from 'test-utils';
import { getIsMobileApp } from 'store/ui/uiSelectors';
import MobileAppLeftNavigationIcon from './MobileAppLeftNavigationIcon';
import { updateLeftNavigationIcon } from 'lib/qta/qta';

jest.mock('store/ui/uiSelectors');
jest.mock('lib/qta/qta');

const decorators = { store: true };
const props = { iconName: 'back' };
const render = () => mountUtils(<MobileAppLeftNavigationIcon {...props} />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
});

describe('when isMobileApp is true', () => {
  beforeEach(() => {
    getIsMobileApp.mockReturnValue(true);
  });
  it('notifies the Qantas Travel App which left navigation icon to use', () => {
    render();
    expect(updateLeftNavigationIcon).toHaveBeenCalledWith('back');
  });
});

describe('when isMobileApp is false', () => {
  beforeEach(() => {
    getIsMobileApp.mockReturnValue(false);
  });
  it('does NOT notify the Qantas Travel App which left navigation icon to use', () => {
    render();
    expect(updateLeftNavigationIcon).not.toHaveBeenCalled();
  });
});
