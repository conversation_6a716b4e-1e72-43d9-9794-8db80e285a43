import { useEffect } from 'react';
import PropTypes from 'prop-types';
import { getIsMobileApp } from 'store/ui/uiSelectors';
import { useSelector } from 'react-redux';
import { updateLeftNavigationIcon } from 'lib/qta/qta';

const MobileAppLeftNavigationIcon = ({ iconName }) => {
  const isMobileApp = useSelector(getIsMobileApp);

  useEffect(() => {
    if (isMobileApp) {
      updateLeftNavigationIcon(iconName);
    }
  });

  return null;
};

MobileAppLeftNavigationIcon.propTypes = {
  iconName: PropTypes.string.isRequired,
};

export default MobileAppLeftNavigationIcon;
