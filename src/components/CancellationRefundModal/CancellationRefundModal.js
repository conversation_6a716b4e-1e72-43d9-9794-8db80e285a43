import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { Icon, Text, Heading, Box } from '@qga/roo-ui/components';
import Markup from 'components/Markup';
import { useModal } from 'lib/hooks';
import { useDataLayer } from 'hooks/useDataLayer';
import Modal from 'components/Modal';
import CancellationRefundSummary, { resolveFirstCancellationWindowWithPenalty } from 'components/CancellationRefundSummary';

const CancellationRefundModal = ({ cancellationPolicy, fontSize, hideBeforeDate, hideWhenNonRefundable, ...rest }) => {
  const { openModal, modalProps } = useModal();
  const { emitInteractionEvent } = useDataLayer();

  const { isNonrefundable, description: cancellationDescription, cancellationWindows } = cancellationPolicy;
  const { formattedBeforeDate } = resolveFirstCancellationWindowWithPenalty(cancellationWindows) ?? {};

  const handleOnClick = useCallback(() => {
    openModal();
    emitInteractionEvent({ type: 'Summary View', value: 'Cancellation Policy Selected' });
  }, [emitInteractionEvent, openModal]);

  return (
    <Box {...rest}>
      <CancellationRefundSummary
        cancellationPolicy={cancellationPolicy}
        fontSize={fontSize}
        hideBeforeDate={hideBeforeDate}
        hideWhenNonRefundable={hideWhenNonRefundable}
        handleOnClick={handleOnClick}
      />

      <Modal {...modalProps} title="Cancellation details">
        <Text display="block" mb="5" fontSize="sm" data-testid="modal-cancellation-text">
          <Heading.h4 fontSize="base">Cancellation policy</Heading.h4>
          {isNonrefundable ? (
            <Box mb="6" fontWeight="bold" color="greys.steel">
              Non-refundable
            </Box>
          ) : (
            <Box mb="6">
              <Icon name="freeCancellation" size={[18, 22]} color="green" mr={2} />
              <Text fontWeight="bold" color="green" fontSize="sm">
                Free cancellation{' '}
              </Text>
              {formattedBeforeDate && `before ${formattedBeforeDate}`}
            </Box>
          )}
          <Markup content={cancellationDescription} />
        </Text>
      </Modal>
    </Box>
  );
};

CancellationRefundModal.propTypes = {
  cancellationPolicy: PropTypes.shape({
    isNonrefundable: PropTypes.bool.isRequired,
    description: PropTypes.string.isRequired,
    cancellationWindows: PropTypes.array.isRequired,
  }).isRequired,
  fontSize: PropTypes.oneOfType([PropTypes.string, PropTypes.array]),
  hideBeforeDate: PropTypes.bool,
  hideWhenNonRefundable: PropTypes.bool,
};

CancellationRefundModal.defaultProps = {
  fontSize: 'base',
  hideBeforeDate: false,
  hideWhenNonRefundable: false,
};

export default CancellationRefundModal;
