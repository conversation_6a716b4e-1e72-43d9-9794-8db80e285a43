import React from 'react';
import { mountUtils } from 'test-utils';
import PopularDestinationFooter from './PopularDestinationFooter';
import { LinkOptions, LinkList } from './types';
import { Masonry } from 'react-masonry';

jest.mock('react-masonry', () => ({
  Masonry: jest.fn(),
}));

const WA_LINKS: LinkOptions[] = [
  {
    children: 'Perth & Surrounds',
    testId: 'perth-wa-destinations-link',
    href: `/search/list?location=perth`,
    trackingName: 'Perth Destination',
  },
  {
    children: 'Broome',
    testId: 'broome-wa-destinations-link',
    href: `/search/list?location=broome`,
    trackingName: 'Broome Destination',
  },
];

const links: LinkList[] = [
  {
    title: 'Western Australia',
    linkList: WA_LINKS,
  },
];
const render = () => mountUtils(<PopularDestinationFooter links={links} />, { decorators: { store: true, router: true, theme: true } });

describe('<PopularDestinationFooter />', () => {
  it('renders the Popular Destination Footer title', () => {
    Masonry.mockImplementation(({ children }) => <div>{children}</div>);
    const { findByTestId } = render();
    expect(findByTestId('popular-destination-title')).toHaveText('Find accommodation in popular destinations');
  });
});
