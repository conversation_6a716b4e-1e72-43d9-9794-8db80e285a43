import { css } from '@emotion/core';
import styled from '@emotion/styled';
import startCase from 'lodash/startCase';
import { rem } from 'polished';
import React, { Fragment, useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Heading, Flex, Hide, Icon, Text, Container, NakedButton } from '@qga/roo-ui/components';
import { themeGet } from 'styled-system';
import { PROMO_AREA_STATES } from 'lib/enums/search';
import { mediaQuery } from 'lib/styledSystem';
import { setComponentState } from 'store/promoArea/promoAreaActions';
import { getComponentState, getIsLoading, getHasResults } from 'store/promoArea/promoAreaSelectors';
import CollapsedMessaging from './CollapsedMessaging';
import PromoNav from './PromoNav';
import PromoResults from './PromoResults';
import FadeInWrapper from 'components/FadeInWrapper';
import { useDataLayer } from 'hooks/useDataLayer';

const Wrapper = styled(Flex)`
  background-color: ${themeGet('colors.snow')};
  opacity: 0;
  max-height: 0;
  display: none;

  ${(props) =>
    !props.isHidden &&
    mediaQuery.minWidth.md &&
    css`
      display: block;
      opacity: 1;
    `}

  ${(props) =>
    !props.isExpanded &&
    css`
      max-height: ${rem('96px')};
    `};

  ${(props) =>
    props.isExpanded &&
    css`
      max-height: ${rem('400px')};
    `};
`;

const PromoArea = () => {
  const [isHidden, setHidden] = useState(false);
  const state = useSelector(getComponentState);
  const hasResults = useSelector(getHasResults);
  const isLoading = useSelector(getIsLoading);
  const dispatch = useDispatch();
  const { emitInteractionEvent } = useDataLayer();

  const isExpanded = state === PROMO_AREA_STATES.EXPANDED;

  useEffect(() => {
    if (!isLoading) {
      setHidden(!hasResults);
    }
  }, [isHidden, isLoading, hasResults]);

  const onToggleMenu = useCallback(() => {
    const nextState = isExpanded ? PROMO_AREA_STATES.COLLAPSED : PROMO_AREA_STATES.EXPANDED;
    dispatch(setComponentState(nextState));

    emitInteractionEvent({ type: 'Promo Area', value: startCase(`Section ${nextState}`) });
  }, [dispatch, emitInteractionEvent, isExpanded]);

  return (
    <Wrapper isExpanded={isExpanded} isHidden={isHidden}>
      <Container pt={[2, 2, 8]} pb={[0, 0, 8]} px={0}>
        <Hide md lg>
          <FadeInWrapper isVisible={isExpanded}>
            <Flex alignItems="center" width="100%" display="flex" justifyContent="space-between">
              <Heading.h2 data-testid="deals-heading-h3" color="greys.charcoal" display="block" fontWeight="bold" mb={0} ml={[3, 3, 0]}>
                Latest Deals
              </Heading.h2>
              <PromoNav />
            </Flex>
          </FadeInWrapper>
        </Hide>
        <Hide xs sm>
          <Flex alignItems="center">
            <Heading.h2
              data-testid="deals-heading-h2"
              color="greys.charcoal"
              display="block"
              fontSize="lg"
              fontWeight="normal"
              mb={0}
              mr={4}
              ml={4}
            >
              Latest Deals
            </Heading.h2>
            <FadeInWrapper isVisible={!isExpanded}>
              <CollapsedMessaging onToggleMenu={onToggleMenu} />
            </FadeInWrapper>
            <Flex flex="1 1 auto" justifyContent="flex-end">
              <NakedButton onClick={onToggleMenu} aria-label={`${isExpanded ? 'Hide' : 'Show'} Deals`}>
                {isExpanded ? (
                  <Fragment>
                    <Text fontSize="base">Hide</Text>
                    <Icon name="expandLess" size={18} />
                  </Fragment>
                ) : (
                  <Fragment>
                    <Text fontSize="base">Show</Text>
                    <Icon name="expandMore" size={18} />
                  </Fragment>
                )}
              </NakedButton>
            </Flex>
          </Flex>
        </Hide>

        <FadeInWrapper isVisible={isExpanded}>
          <Hide xs sm>
            <PromoNav />
          </Hide>
        </FadeInWrapper>
        <PromoResults />
      </Container>
    </Wrapper>
  );
};

export default PromoArea;
