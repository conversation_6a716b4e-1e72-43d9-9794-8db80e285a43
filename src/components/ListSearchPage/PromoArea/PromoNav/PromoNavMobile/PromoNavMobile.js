import React, { useCallback } from 'react';
import find from 'lodash/find';
import styled from '@emotion/styled';
import PropTypes from 'prop-types';
import isObject from 'lodash/isObject';
import { Flex, Select } from '@qga/roo-ui/components';
import { themeGet } from 'styled-system';

const getName = (option) => (isObject(option) ? option.name : option);
const getCode = (option) => (isObject(option) ? option.code : option);

const RooFlex = styled(Flex)`
  color: ${themeGet('colors.brand.primary')};
`;

const RooSelect = styled(Select)`
  text-align: right;
  border-radius: ${themeGet('radii.default')};
  border: none;
  background-color: transparent;
  cursor: pointer;
  ::-ms-expand {
    display: none;
`;

const PromoNavMobile = ({ activeCategory, categories, onChangeActiveCategory }) => {
  const onChange = useCallback(
    (activeCategory) => {
      if (activeCategory) {
        activeCategory = find(categories, ['code', activeCategory.target.value]);
        onChangeActiveCategory(activeCategory);
      }
    },
    [categories, onChangeActiveCategory],
  );

  return (
    <RooFlex>
      <RooSelect isActive={activeCategory} value={getCode(activeCategory)} onChange={onChange} mb={[0, 0]}>
        {categories.map((option) => (
          <option key={getCode(option)} value={getCode(option)}>
            {getName(option)}
          </option>
        ))}
      </RooSelect>
    </RooFlex>
  );
};

PromoNavMobile.propTypes = {
  activeCategory: PropTypes.object,
  categories: PropTypes.arrayOf(PropTypes.shape({ name: PropTypes.string, code: PropTypes.string })),
  onChangeActiveCategory: PropTypes.func,
  value: PropTypes.string,
};

PromoNavMobile.defaultProps = {
  activeCategory: {},
  categories: [],
  value: '',
};

PromoNavMobile.displayName = 'PromoNavMobile';

export default PromoNavMobile;
