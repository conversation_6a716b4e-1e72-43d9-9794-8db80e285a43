import React, { Fragment, useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Hide } from '@qga/roo-ui/components';
import { useDataLayer } from 'hooks/useDataLayer';
import { setActiveCategory } from 'store/promoArea/promoAreaActions';
import { getActiveCategory, getCategories, getIsLoading } from 'store/promoArea/promoAreaSelectors';
import startCase from 'lodash/startCase';
import PromoNavMobile from './PromoNavMobile';
import { SkeletonNavMobile, SkeletonNavDesktop } from 'components/ListSearchPage/PromoArea/primitives';
import PromoNavDesktop from './PromoNavDesktop';

const PromoNav = React.memo(() => {
  const dispatch = useDispatch();
  const { emitInteractionEvent } = useDataLayer();

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const categories = useSelector(getCategories) || [];
  const isLoading = useSelector(getIsLoading);
  const activeCategory = useSelector(getActiveCategory);

  const isVisible = categories.length > 1;

  useEffect(() => {
    const activeCategory = categories[0];

    if (activeCategory) {
      dispatch(setActiveCategory(activeCategory));

      emitInteractionEvent({
        type: 'Promo Area',
        value: startCase(`${activeCategory.name} displayed`),
      });
    }
  }, [categories, dispatch, emitInteractionEvent]);

  const onChangeActiveCategory = useCallback(
    (activeCategory) => {
      if (activeCategory) {
        dispatch(setActiveCategory(activeCategory));

        emitInteractionEvent({
          type: 'Promo Area',
          value: startCase(`${activeCategory.name} selected`),
        });
      }
    },
    [dispatch, emitInteractionEvent],
  );

  return (
    <Fragment>
      <Hide xs sm>
        {isLoading && <SkeletonNavDesktop />}
        {!isLoading && isVisible && (
          <PromoNavDesktop categories={categories} activeCategory={activeCategory} onChangeActiveCategory={onChangeActiveCategory} />
        )}
      </Hide>
      <Hide md lg>
        {isLoading && <SkeletonNavMobile />}
        {!isLoading && isVisible && (
          <PromoNavMobile categories={categories} activeCategory={activeCategory} onChangeActiveCategory={onChangeActiveCategory} />
        )}
      </Hide>
    </Fragment>
  );
});

PromoNav.displayName = 'PromoNav';

export default PromoNav;
