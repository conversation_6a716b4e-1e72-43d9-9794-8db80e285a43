import React from 'react';
import { mountUtils } from 'test-utils';
import PromoArea from './PromoArea';
import { setComponentState } from 'store/promoArea/promoAreaActions';
import { getComponentState, getHasResults, getIsLoading } from 'store/promoArea/promoAreaSelectors';
import { PROMO_AREA_STATES } from 'lib/enums/search';
import { useDataLayer } from 'hooks/useDataLayer';
import { useBreakpoints } from 'hooks/useBreakpoints';

jest.mock('store/promoArea/promoAreaSelectors');
jest.mock('hooks/useBreakpoints');
jest.mock('hooks/useDataLayer');

mountUtils.mockComponent('CollapsedMessaging');
mountUtils.mockComponent('PromoNav');
mountUtils.mockComponent('PromoResults');

const emitInteractionEvent = jest.fn();
const defaultProps = {
  onToggleMenu: jest.fn(),
};

const render = (props = {}) => mountUtils(<PromoArea {...defaultProps} {...props} />, { decorators: { store: true, theme: true } });

beforeEach(() => {
  jest.clearAllMocks();
  getHasResults.mockReturnValue(true);
  getIsLoading.mockReturnValue(false);
  getComponentState.mockReturnValue(PROMO_AREA_STATES.EXPANDED);
  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

const itRendersThePromoArea = () => {
  it('shows the desktop version heading', () => {
    const { find } = render();

    expect(find('Heading[data-testid="deals-heading-h2"]')).toHaveText('Latest Deals');
  });

  it('shows the mobile version heading', () => {
    const { find } = render();

    expect(find('Heading[data-testid="deals-heading-h3"]')).toHaveText('Latest Deals');
  });

  describe('when deals are available', () => {
    it('the promo area is visible', () => {
      const { find } = render();

      expect(find('Wrapper')).toHaveProp({ isHidden: false });
    });

    describe('and the promo area is collapsed', () => {
      beforeEach(() => {
        getComponentState.mockReturnValue(PROMO_AREA_STATES.COLLAPSED);
      });

      it('shows the collapsed messaging', () => {
        const { find } = render();

        expect(find('CollapsedMessaging')).toExist();
      });

      it('shows the expandMore icon', () => {
        const { find } = render();

        expect(find('Icon')).toHaveProp({ name: 'expandMore' });
      });

      it('shows the button text', () => {
        const { find } = render();

        expect(find('Text')).toHaveText('Show');
      });

      describe('when clicking the button', () => {
        it('expands the menu', () => {
          const { decorators, find } = render();

          find('NakedButton').simulate('click');
          expect(decorators.store.dispatch).toHaveBeenCalledWith(setComponentState(PROMO_AREA_STATES.EXPANDED));
        });

        it('emits an event to the data layer', () => {
          const { find } = render();

          find('NakedButton').simulate('click');

          expect(emitInteractionEvent).toHaveBeenCalledWith({
            type: 'Promo Area',
            value: 'Section Expanded',
          });
        });
      });
    });

    describe('and the promo area is expanded', () => {
      it('renders <PromoNav />', () => {
        const { find } = render();

        expect(find('PromoNav')).toExist();
      });

      it('renders <PromoResults />', () => {
        const { find } = render();

        expect(find('PromoResults')).toExist();
      });

      it('shows the expandLess icon', () => {
        const { find } = render();

        expect(find('Icon')).toHaveProp({ name: 'expandLess' });
      });

      it('shows the button text', () => {
        const { find } = render();

        expect(find('Text')).toHaveText('Hide');
      });

      describe('when clicking the button', () => {
        it('collapses the menu', () => {
          const { decorators, find } = render();
          find('NakedButton').simulate('click');
          expect(decorators.store.dispatch).toHaveBeenCalledWith(setComponentState(PROMO_AREA_STATES.COLLAPSED));
        });

        it('emits an event to the data layer', () => {
          const { find } = render();

          find('NakedButton').simulate('click');

          expect(emitInteractionEvent).toHaveBeenCalledWith({
            type: 'Promo Area',
            value: 'Section Collapsed',
          });
        });
      });
    });
  });

  describe('when no deals are available', () => {
    beforeEach(() => {
      getHasResults.mockReturnValue(false);
    });

    it('the promo area is hidden', () => {
      const { find } = render();
      expect(find('Wrapper')).toHaveProp({ isHidden: true });
    });
  });
};

describe('when rendering for desktop users', () => {
  beforeEach(() => {
    useBreakpoints.mockReturnValue({
      isLessThanBreakpoint: () => false,
    });
  });

  itRendersThePromoArea();
});
