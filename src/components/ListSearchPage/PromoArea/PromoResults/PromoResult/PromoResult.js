import PropTypes from 'prop-types';
import React, { useCallback, useEffect } from 'react';
import { Flex } from '@qga/roo-ui/components';
import { useInView } from 'react-intersection-observer';
import PropertyCard from 'components/PropertyCard';
import { themeGet } from 'styled-system';
import { useDataLayer } from 'hooks/useDataLayer';
import { usePersonalisation } from 'hooks/usePersonalisation';

const PromoResult = ({ id: propertyId, index, threshold, offerId, onScrollIntoView, ...rest }) => {
  const { emitInteractionEvent } = useDataLayer();
  const personalisation = usePersonalisation();
  const [ref, inView] = useInView({ root: null, threshold: threshold });

  const handlePropertyLinkOnClick = useCallback(() => {
    const cardNumber = index + 1;
    const customAttributes = { user_event_value: propertyId };
    emitInteractionEvent({ type: 'Promo Area', value: `Card ${cardNumber} Selected`, customAttributes });
    personalisation.trackPromoClick(propertyId);
  }, [personalisation, emitInteractionEvent, index, propertyId]);

  useEffect(() => {
    if (inView) {
      onScrollIntoView(index);
    }
  }, [inView, onScrollIntoView, index]);

  return (
    <Flex ref={ref} pl={[3, 3, 0]} box-shadow={themeGet('shadows.heavy')} border-bottom={themeGet('space.4')}>
      <PropertyCard {...rest} id={propertyId} featuredOfferId={offerId} inline onClick={handlePropertyLinkOnClick} />
    </Flex>
  );
};

PromoResult.propTypes = {
  id: PropTypes.string.isRequired,
  index: PropTypes.number.isRequired,
  offerId: PropTypes.string.isRequired,
  threshold: PropTypes.number,
  onScrollIntoView: PropTypes.func,
};

export default PromoResult;
