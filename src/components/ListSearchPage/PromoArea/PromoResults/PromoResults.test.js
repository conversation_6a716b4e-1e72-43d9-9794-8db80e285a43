import React from 'react';
import { mountUtils } from 'test-utils';
import { useDataLayer } from 'hooks/useDataLayer';
import { getActiveCategory, getIsLoading, getResultsForActiveCategory, getGa4PromoResults } from 'store/promoArea/promoAreaSelectors';
import { act } from 'react-dom/test-utils';
import { useDispatch } from 'react-redux';
import PromoResults from './PromoResults';
import { getSearchQuery } from 'store/search/searchSelectors';
import { getPropertyLocation } from 'store/property/propertySelectors';
import { emitPromoAreaGa4Results } from 'store/promoArea/promoAreaActions';
import { getPointsConversion } from 'store/pointsBurnTiers/pointsBurnSelectors';

jest.mock('store/promoArea/promoAreaSelectors');
jest.mock('hooks/useDataLayer');
jest.mock('store/search/searchSelectors');
jest.mock('store/property/propertySelectors');
jest.mock('store/pointsBurnTiers/pointsBurnSelectors');
mountUtils.mockComponent('SkeletonCard');
mountUtils.mockComponent('PromoResult');
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: jest.fn(),
}));

const emitInteractionEvent = jest.fn();
const dispatch = jest.fn();

const mockCategories = [
  { code: 'classic_rewards', name: 'classic Rewards' },
  { code: 'chain_promotions', name: 'Chain promotions' },
];

const regionName = 'Melbourne, VIC, Australia';
const payWith = { payWith: 'cash' };
const query = { location: regionName, payWith: 'cash' };
const ga4Results = [
  {
    property: { id: '124', name: 'hotel' },
    roomType: { name: 'room' },
    offer: {
      charges: {
        total: {
          amount: '300.00',
          currency: 'AUD',
        },
        totalCash: {
          amount: '0',
          currency: 'AUD',
        },
      },
    },
  },
  {
    offer: {
      charges: {
        total: {
          amount: '400.00',
          currency: 'AUD',
        },
        totalCash: {
          amount: '0',
          currency: 'AUD',
        },
      },
    },
    roomType: {
      name: 'room name 2',
    },
    property: {
      id: '1235',
      name: 'hotel name 2',
      category: 'hotels',
      hasOffer: true,
    },
  },
];

const pointsConversion = {
  levels: [
    { min: 0, max: 150, rate: 0.00824 },
    { min: 150, max: 400, rate: 0.00834 },
    { min: 400, max: 650, rate: 0.00848 },
    { min: 650, max: 900, rate: 0.00875 },
    { min: 900, max: null, rate: 0.00931 },
  ],
  name: 'VERSION11',
};

const payload = {
  results: ga4Results,
  query: query,
  listName: 'Promo Area Search Page',
  category: 'jetstar',
  type: 'list',
  currency: 'AUD',
  pointsConversion: pointsConversion,
};

const render = () => mountUtils(<PromoResults />, { decorators: { store: true, theme: true } });

beforeEach(() => {
  jest.clearAllMocks();
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  getResultsForActiveCategory.mockReturnValue([{ id: 'property1' }, { id: 'property2' }, { id: 'property3' }]);
  getGa4PromoResults.mockReturnValue(ga4Results);
  getPropertyLocation.mockReturnValue(regionName);
  getSearchQuery.mockReturnValue(payWith);
  getPointsConversion.mockReturnValue(pointsConversion);
});

describe('when loading', () => {
  beforeEach(() => {
    getIsLoading.mockReturnValue(true);
    getActiveCategory.mockReturnValue(mockCategories[0]);
    useDispatch.mockReturnValue(dispatch);
  });

  it('renders the skeleton results', () => {
    const { find } = render();

    expect(find('SkeletonResults')).toExist();
    expect(find('SkeletonNavDots')).toExist();
  });
});

describe('when not loading', () => {
  beforeEach(() => {
    getIsLoading.mockReturnValue(false);
  });

  it('renders <PromoResult /> for each result', () => {
    const { find } = render();

    const cards = find('PromoResult');

    expect(cards).toHaveLength(3);
    expect(cards.at(0)).toHaveProp({ id: 'property1' });
    expect(cards.at(1)).toHaveProp({ id: 'property2' });
    expect(cards.at(2)).toHaveProp({ id: 'property3' });
  });

  it('dispatches emitPromoAreaGa4Results', () => {
    const { find } = render();

    act(() => {
      find('PromoResult').at(0).prop('onScrollIntoView')(0);
    });

    expect(dispatch).toHaveBeenCalledWith(emitPromoAreaGa4Results(payload));
  });

  it('emits an event to the datalayer when property cards are scrolled', () => {
    const { find } = render();

    act(() => {
      find('PromoResult').at(0).prop('onScrollIntoView')(0);
    });

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Mobile Promo Area',
      value: 'Scrolled To Card 1',
    });
  });
});
