import React, { useCallback, useEffect, useRef, useState } from 'react';
import styled from '@emotion/styled';
import { isEmpty, startCase } from 'lodash';
import { useDispatch, useSelector } from 'react-redux';
import { Flex, Hide } from '@qga/roo-ui/components';
import { themeGet } from 'styled-system';
import { mediaQuery } from 'lib/styledSystem';
import { useBreakpoints } from 'hooks/useBreakpoints';
import {
  getActiveCategory,
  getComponentState,
  getIsLoading,
  getResultsForActiveCategory,
  getGa4PromoResults,
} from 'store/promoArea/promoAreaSelectors';
import PromoResult from './PromoResult';
import { useDataLayer } from 'hooks/useDataLayer';
import { SkeletonNavDots, SkeletonResults } from 'components/ListSearchPage/PromoArea/primitives';
import FadeInWrapper from 'components/FadeInWrapper';
import HorizontalDotNavigation from 'components/HorizontalDotNavigation';
import { PROMO_AREA_STATES } from 'lib/enums/search';
import { emitPromoAreaGa4Results } from 'store/promoArea/promoAreaActions';
import { getSearchQuery } from 'store/search/searchSelectors';
import { getPropertyLocation } from 'store/property/propertySelectors';
import { getPointsConversion } from 'store/pointsBurnTiers/pointsBurnSelectors';

export const Grid = styled(Flex)`
  width: 100%;
  height: 240px;
  padding-right: ${themeGet('space.4')};
  overflow-x: auto;
  justify-content: space-between;
  scroll-snap-type: x mandatory;
  ::-webkit-scrollbar {
    display: none;
  }
  scrollbar-width: none;
  > * {
    width: 90%;
    max-height: 95%;
    flex: 0 0 auto;
    margin: 0 auto;
    scroll-snap-align: start;
  }

  ${mediaQuery.minWidth.sm} {
    > * {
      width: 90%;
      margin-left: ${themeGet('space.3')};
    }
  }
  ${mediaQuery.minWidth.md} {
    padding-right: 0px;
    overflow-x: hidden;
    > * {
      width: 32%;
      margin-right: ${themeGet('space.5')};
      margin-left: ${themeGet('space.3')};
      flex: 1 1 auto;
    }
  }
`;

const PromoResults = () => {
  const results = useSelector(getResultsForActiveCategory);
  const isLoading = useSelector(getIsLoading);
  const state = useSelector(getComponentState);
  const isExpanded = state === PROMO_AREA_STATES.EXPANDED;
  const { isLessThanBreakpoint } = useBreakpoints();

  const dispatch = useDispatch();
  const ga4Results = useSelector(getGa4PromoResults);
  const searchQuery = useSelector(getSearchQuery);
  const payWith = searchQuery?.payWith;
  const searchLocation = searchQuery?.location;
  const location = useSelector(getPropertyLocation);

  const { emitInteractionEvent } = useDataLayer();

  const activeCategory = useSelector(getActiveCategory);

  // State for total number of dots
  const resultCount = results.length || 0;
  // State for the current position of the dots
  const [dotPosition, setDotPosition] = useState(0);

  // ref to reset property cards on change of category
  const gridRef = useRef(null);

  useEffect(() => {
    gridRef.current.scrollLeft = 0;
  }, [results]);

  const onScrollIntoView = useCallback(
    (index) => {
      if (activeCategory && isLessThanBreakpoint(1)) {
        setDotPosition(index);

        emitInteractionEvent({
          type: 'Mobile Promo Area',
          value: startCase(`Scrolled To Card ${index + 1}`),
        });
      }
    },
    [activeCategory, emitInteractionEvent, isLessThanBreakpoint],
  );

  const pointsConversion = useSelector(getPointsConversion);

  useEffect(() => {
    const hasLevels = !!pointsConversion?.levels?.length;
    const query = { payWith: payWith, location: location || searchLocation };
    if (!isEmpty(ga4Results) && hasLevels) {
      dispatch(
        emitPromoAreaGa4Results({
          results: ga4Results,
          query: query,
          listName: 'Promo Area Search Page',
          category: 'jetstar',
          type: 'list',
          currency: 'AUD',
          pointsConversion: pointsConversion,
        }),
      );
    }
  }, [
    activeCategory,
    dispatch,
    emitInteractionEvent,
    ga4Results,
    isLessThanBreakpoint,
    location,
    payWith,
    pointsConversion,
    pointsConversion?.levels?.length,
    searchLocation,
  ]);

  return (
    <FadeInWrapper isVisible={isExpanded}>
      <Grid ref={gridRef}>
        {isLoading ? (
          <SkeletonResults />
        ) : (
          results.map((result, index) => (
            <PromoResult {...result} key={`promo-deal-${index}`} index={index} threshold={0.75} onScrollIntoView={onScrollIntoView} />
          ))
        )}
      </Grid>
      <Hide md lg>
        {isLoading ? <SkeletonNavDots /> : <HorizontalDotNavigation position={dotPosition} total={resultCount} />}
      </Hide>
    </FadeInWrapper>
  );
};

export default PromoResults;
