import React from 'react';
import { mountUtils } from 'test-utils';
import { routeToProperty, updateQuery } from 'store/search/searchActions';
import { getQueryLocation, getQueryCheckIn, getQueryCheckOut, getQueryOccupants } from 'store/router/routerSelectors';
import SearchControls from './SearchControls';

const location = 'Melbourne';
const occupants = {
  adults: 1,
  children: 2,
  infants: 3,
};
const checkIn = '2028-01-01';
const checkOut = '2028-01-09';

jest.mock('store/router/routerSelectors');

mountUtils.mockComponent('LocationAutocompleter');
mountUtils.mockComponent('OccupantPicker');
mountUtils.mockComponent('AvailabilityDatePicker');
mountUtils.mockComponent('Filters');

const render = () => mountUtils(<SearchControls />, { decorators: { store: true, router: true } });

beforeEach(() => {
  jest.clearAllMocks();

  getQueryLocation.mockReturnValue(location);
  getQueryCheckIn.mockReturnValue(checkIn);
  getQueryCheckOut.mockReturnValue(checkOut);
  getQueryOccupants.mockReturnValue(occupants);
});

describe('<LocationAutocompleter />', () => {
  it('renders the LocationAutocompleter', () => {
    expect(render().find('LocationAutocompleter')).toHaveProp({
      locationName: location,
    });
  });

  it('updateQuery dispatches expected action', () => {
    const queryPayload = { query: true };
    const { find, decorators } = render();
    find('LocationAutocompleter').prop('updateQuery')(queryPayload);
    expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery(queryPayload));
  });

  it('routeToProperty dispatches expected action', () => {
    const id = '000';
    const { find, decorators } = render();
    find('LocationAutocompleter').prop('routeToProperty')({ id });
    expect(decorators.store.dispatch).toHaveBeenCalledWith(routeToProperty({ id }));
  });
});

it('renders the OccupantPicker', () => {
  const { find, decorators } = render();

  expect(find('OccupantPicker')).toHaveProp({ occupants });

  const queryPayload = { query: true };
  find('OccupantPicker').prop('updateQuery')(queryPayload);
  expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery(queryPayload));
});

it('renders the AvailabilityDatePicker', () => {
  const { find, decorators } = render();

  expect(find('AvailabilityDatePicker')).toHaveProp({
    anchorX: 'right',
    selectedDates: { startDate: checkIn, endDate: checkOut },
  });

  const queryPayload = { query: true };
  find('AvailabilityDatePicker').prop('updateQuery')(queryPayload);
  expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery(queryPayload));
});

it('renders the Filters', () => {
  const { find } = render();
  expect(find('Filters')).toHaveProp({
    showSortingOptions: true,
    title: 'Filter properties',
  });
});
