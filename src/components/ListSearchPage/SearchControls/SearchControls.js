import React, { useCallback, useState } from 'react';
import PropTypes from 'prop-types';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { Container, Box, Hide, Flex } from '@qga/roo-ui/components';
import { useDispatch, useSelector } from 'react-redux';
import LocationAutocompleter from 'components/LocationAutocompleter';
import OccupantPicker from 'components/OccupantPicker';
import { getQueryLocation, getQueryCheckIn, getQueryCheckOut, getQueryOccupants } from 'store/router/routerSelectors';
import { routeToProperty, updateQuery } from 'store/search/searchActions';
import FixedSearchControls from 'components/FixedSearchControls';
import AvailabilityDatePicker from 'components/AvailabilityDatePicker';

const labelOptions = {};

const SearchControlsWrapper = styled(Box)`
  z-index: ${themeGet('zIndices.searchControls')};
  position: relative;
`;

const ConnectedOccupantPicker = ({ onUpdateQuery }) => {
  const occupants = useSelector(getQueryOccupants);

  return (
    <OccupantPicker
      occupants={occupants}
      labelOptions={labelOptions}
      updateQuery={onUpdateQuery}
      viewThreshold={0}
      verboseInMobile={true}
    />
  );
};

ConnectedOccupantPicker.propTypes = {
  onUpdateQuery: PropTypes.func.isRequired,
};

const ConnectedLocationAutocompleter = ({ onUpdateQuery }) => {
  const location = useSelector(getQueryLocation);
  const dispatch = useDispatch();
  const onRouteToProperty = useCallback((payload) => dispatch(routeToProperty(payload)), [dispatch]);

  return (
    <LocationAutocompleter
      locationName={location}
      labelOptions={labelOptions}
      updateQuery={onUpdateQuery}
      routeToProperty={onRouteToProperty}
    />
  );
};

ConnectedLocationAutocompleter.propTypes = {
  onUpdateQuery: PropTypes.func.isRequired,
};

const ConnectedDatePicker = ({ onUpdateQuery }) => {
  const checkInDate = useSelector(getQueryCheckIn);
  const checkOutDate = useSelector(getQueryCheckOut);
  const [selectedDates, setselectedDates] = useState({ startDate: checkInDate, endDate: checkOutDate });

  const onClickOutside = useCallback(() => {
    setselectedDates({ startDate: undefined, endDate: undefined });
  }, [setselectedDates]);

  return (
    <AvailabilityDatePicker
      selectedDates={selectedDates}
      labelOptions={labelOptions}
      backgroundColor="white"
      clearSelectedDates={onClickOutside}
      updateQuery={onUpdateQuery}
      anchorX="right"
      isLimited={true}
    />
  );
};

ConnectedDatePicker.propTypes = {
  onUpdateQuery: PropTypes.func.isRequired,
};

const SearchControls = () => {
  const dispatch = useDispatch();
  const onUpdateQuery = useCallback((payload) => dispatch(updateQuery(payload)), [dispatch]);

  return (
    <SearchControlsWrapper bg="white" py={5}>
      <Container>
        <Flex flexWrap={['wrap', 'nowrap']} position="relative" style={{ gap: '.75rem' }}>
          <Box flexGrow={[1, 4, 6]} flexBasis={['100%', 0]}>
            <ConnectedLocationAutocompleter onUpdateQuery={onUpdateQuery} />
          </Box>
          <Box flexGrow={[1, 6, 4]} flexBasis={['calc(60% - .75rem)', 0]} data-testid="stay-date-picker">
            <ConnectedDatePicker onUpdateQuery={onUpdateQuery} />
          </Box>
          <Box flexGrow={[1, 2]} flexBasis={['40%', 0]}>
            <ConnectedOccupantPicker onUpdateQuery={onUpdateQuery} />
          </Box>
        </Flex>
        <Hide md lg>
          <FixedSearchControls isListSearch />
        </Hide>
      </Container>
    </SearchControlsWrapper>
  );
};

export default SearchControls;
