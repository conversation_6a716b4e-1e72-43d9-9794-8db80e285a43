import React from 'react';
import { mountUtils } from 'test-utils';
import { getQueryPayWith, getQueryMinPrice, getQueryMaxPrice } from 'store/router/routerSelectors';
import PayWith from './PayWith';
import { updateQuery } from 'store/search/searchActions';
import * as config from 'config';

jest.mock('config');
jest.mock('store/router/routerSelectors');

mountUtils.mockComponent('PayWithButtonGroup');

const props = {
  name: 'name',
};

const render = () => mountUtils(<PayWith {...props} />, { decorators: { store: true, theme: true } });

beforeEach(() => {
  config.PAYWITH_TOGGLE_ENABLED = true;

  getQueryMinPrice.mockReturnValue(100);
  getQueryMaxPrice.mockReturnValue(200);
  getQueryPayWith.mockReturnValue('points');
});

it('renders PayWithButtonGroup with expected props', () => {
  expect(render().find('PayWithButtonGroup')).toHaveProp({
    minPrice: 100,
    maxPrice: 200,
    payWith: 'points',
    name: props.name,
  });
});

it('renders the PayWithToggleMessage', () => {
  const { find } = render();
  expect(find('PayWithToggleMessage')).toHaveProp({
    pointerDirection: 'bottom',
  });
});

it('renders the minimum points text', () => {
  const { findByTestId } = render();
  expect(findByTestId('minimum-points')).toHaveText('Minimum 5,000 PTS required');
});

it('wraps updateQuery in dispatch', () => {
  const query = { checkIn: '2020-10-01' };
  const { find, decorators } = render();
  find('PayWithButtonGroup').prop('updateQuery')(query);
  expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery(query));
});

describe('when `PAYWITH_TOGGLE_ENABLED` is false', () => {
  beforeEach(() => {
    config.PAYWITH_TOGGLE_ENABLED = false;
  });

  it('does NOT render the PayWithButtonGroup', () => {
    const { find } = render();
    expect(find('PayWithButtonGroup')).not.toExist();
  });

  it('does NOT render the PayWithToggleMessage', () => {
    const { find } = render();
    expect(find('PayWithToggleMessage')).not.toExist();
  });

  it('does NOT render the minimum points text', () => {
    const { findByTestId } = render();
    expect(findByTestId('minimum-points')).not.toExist();
  });
});
