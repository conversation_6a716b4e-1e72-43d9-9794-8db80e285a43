import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { useSelector, useDispatch } from 'react-redux';
import PayWithButtonGroup from 'components/PayWithButtonGroup';
import { getQueryPayWith, getQueryMinPrice, getQueryMaxPrice } from 'store/router/routerSelectors';
import { updateQuery as updateQueryAction } from 'store/search/searchActions';
import { Flex, Text } from '@qga/roo-ui/components';
import { PAYWITH_TOGGLE_ENABLED } from 'config';
import PayWithToggleMessage from 'components/PayWithToggleMessage';
import { useBreakpoints } from 'hooks/useBreakpoints';

const PayWith = ({ name, ...rest }) => {
  const payWith = useSelector(getQueryPayWith);
  const minPrice = useSelector(getQueryMinPrice);
  const maxPrice = useSelector(getQueryMaxPrice);
  const dispatch = useDispatch();
  const { isLessThanBreakpoint } = useBreakpoints();
  const isMobile = isLessThanBreakpoint(1);
  const pointerPosition = isMobile ? '80%' : '90%';

  const updateQuery = useCallback(
    (query) => {
      dispatch(updateQueryAction(query));
    },
    [dispatch],
  );

  if (!PAYWITH_TOGGLE_ENABLED) return null;

  return (
    <Flex alignItems="flex-end" flexDirection="column" ml="auto" p={[2, 0]} position="relative" width={['100%', 'inherit']}>
      <PayWithToggleMessage pointerDirection="bottom" pointerPosition={pointerPosition} right="0px" mb={3} />
      <PayWithButtonGroup {...rest} name={name} payWith={payWith} minPrice={minPrice} maxPrice={maxPrice} updateQuery={updateQuery} />
      <Text color="greys.steel" mt={2} display="block" data-testid="minimum-points">
        Minimum 5,000 PTS required
      </Text>
    </Flex>
  );
};

PayWith.propTypes = {
  name: PropTypes.string.isRequired,
};

export default PayWith;
