import React, { Fragment } from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import { Box, Hide, LoadingIndicator, Flex } from '@qga/roo-ui/components';
import SearchResult from 'components/ListSearchPage/SearchResult';
import Pagination from 'components/Pagination';
import ResultLoader from 'components/Loader/ResultLoader';
import NoResultsMessage from 'components/ListSearchPage/NoResultsMessage';
import { LIST_SEARCH_LIMIT } from 'config';
import LoaderSkeletonCard from 'components/ListSearchPage/LoaderSkeletonCard';
import { getResults, getResultCount, getIsLoading, getHasError } from 'store/search/searchSelectors';

// pulled out and memoised to prevent re-render when loading state changes.
const SearchResults = React.memo(({ results }) => {
  return results.map((result, index) => <SearchResult key={index} index={index} result={result} />);
});

SearchResults.displayName = 'SearchResults';

SearchResults.propTypes = {
  results: PropTypes.array.isRequired,
};

const SearchList = React.memo(() => {
  const results = useSelector(getResults);
  const resultCount = useSelector(getResultCount);
  const isLoading = useSelector(getIsLoading);
  const hasError = useSelector(getHasError);

  return (
    <Fragment>
      {isLoading && (
        <Hide md lg>
          <Box mt={4} mb={8} data-testid="phone-loader">
            <LoadingIndicator />
          </Box>
        </Hide>
      )}
      <ResultLoader isLoading={isLoading} skeletonResultCount={LIST_SEARCH_LIMIT} skeletonCardComponent={LoaderSkeletonCard}>
        <SearchResults results={results} />
      </ResultLoader>
      {!isLoading && !hasError && resultCount === 0 && <NoResultsMessage />}
      {resultCount > 0 && (
        <Flex justifyContent={['center', 'space-between', 'space-between']} pb={8}>
          <Pagination total={resultCount} pageSize={LIST_SEARCH_LIMIT} />
        </Flex>
      )}
    </Fragment>
  );
});

SearchList.displayName = 'SearchList';

export default SearchList;
