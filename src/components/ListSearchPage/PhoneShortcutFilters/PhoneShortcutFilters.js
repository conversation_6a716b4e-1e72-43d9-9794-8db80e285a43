import React, { useCallback } from 'react';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { useSelector, useDispatch } from 'react-redux';
import { Flex, Text, Hide } from '@qga/roo-ui/components';
import capitalize from 'lodash/capitalize';
import { format } from 'lib/date';
import { getResultCount } from 'store/search/searchSelectors';
import { getFullKnownQuery } from 'store/router/routerSelectors';
import { updateQuery } from 'store/search/searchActions';
import PillButton from 'components/PillButton';
import { useDataLayer } from 'hooks/useDataLayer';
import SortBySelector from './SortBySelector';
import PayWith from 'components/ListSearchPage/PayWith';

export const FILTER_NAMES = Object.freeze({
  minTripadvisorRating: 'Trip Advisor Rating',
  minStarRating: 'Hotel Rating',
  freeCancellation: 'Free Cancellation',
});

const DATE_FORMAT = 'd MMM';

const PhoneShortcutFiltersContainer = styled(Hide)`
  display: flex;
  flex-direction: column;
  background-color: ${themeGet('colors.white')};
  padding: ${themeGet('space.4')} 0;
  margin-bottom: ${themeGet('space.4')};
  border-bottom: ${themeGet('borders.1')} ${themeGet('colors.greys.alto')};
  position: relative;
`;
PhoneShortcutFiltersContainer.displayName = 'PhoneShortcutFiltersContainer';

const ScrollableList = styled(Flex)`
  flex-direction: row;
  padding-top: ${themeGet('space.3')};
  padding-left: ${themeGet('space.3')};
  overflow: scroll;

  &:after {
    content: '';
    display: block;
    padding-left: ${themeGet('space.3')};
  }

  -ms-overflow-style: none; /* Internet Explorer 10+ */
  scrollbar-width: none; /* Firefox */

  &::-webkit-scrollbar {
    display: none; /* Safari and Chrome */
  }
`;
ScrollableList.displayName = 'ScrollableList';

const PhoneShortcutFilters = React.memo(() => {
  const query = useSelector(getFullKnownQuery);
  const resultsCount = useSelector(getResultCount);
  const { emitInteractionEvent } = useDataLayer();
  const dispatch = useDispatch();
  const { checkIn, checkOut, location, adults, children, minTripadvisorRating, minStarRating, freeCancellation, facilities } = query;
  const formattedCheckIn = checkIn ? format(checkIn, DATE_FORMAT) : '';
  const formattedCheckOut = checkOut ? format(checkOut, DATE_FORMAT) : '';
  const totalGuests = (adults || 0) + (children || 0);

  const onUpdateQuery = useCallback(
    (updatedQuery) => {
      dispatch(updateQuery(updatedQuery));
    },
    [dispatch],
  );

  const toggleFacilityFilter = useCallback(
    (selectedFacility) => {
      const isSelected = facilities.includes(selectedFacility);
      const updatedFacilities = isSelected
        ? facilities.filter((facility) => facility !== selectedFacility)
        : facilities.concat([selectedFacility]);
      onUpdateQuery({ facilities: updatedFacilities });
      emitInteractionEvent({
        type: 'Mobile Filters Buttons',
        value: `${capitalize(selectedFacility)} ${isSelected ? 'Removed' : 'Selected'}`,
      });
    },
    [onUpdateQuery, facilities, emitInteractionEvent],
  );

  const toggleQueryFilter = useCallback(
    (filters) => {
      // Destructure a filter key-value pair (eg: minStarRating: '4') and update the query based on if the filter is selected or not
      Object.entries(filters).forEach(([queryKey, value]) => {
        const isSelected = query[queryKey] === value;
        const updateValue = isSelected ? undefined : value;
        onUpdateQuery({ [queryKey]: updateValue });
        const filterName = FILTER_NAMES[queryKey];

        if (filterName) {
          emitInteractionEvent({
            type: 'Mobile Filters Buttons',
            value: `${filterName} ${isSelected ? 'Removed' : 'Selected'}`,
          });
        }
      });
    },
    [onUpdateQuery, query, emitInteractionEvent],
  );

  return (
    <>
      <PhoneShortcutFiltersContainer sm md lg mb={4}>
        <Text pl={4} fontSize="md" fontWeight="bold" data-testid="mobile-filters-title">
          Hotels in {location}
        </Text>
        <Text pl={4} data-testid="mobile-filters-description">
          {resultsCount} properties • {formattedCheckIn} - {formattedCheckOut} • {totalGuests} guests
        </Text>
        <ScrollableList data-testid="mobile-filters-list" pb={1}>
          <SortBySelector onChange={onUpdateQuery} />
          <PillButton onClick={() => toggleFacilityFilter('parking')} selected={facilities.includes('parking')}>
            <Text fontSize="xs">Parking Included</Text>
          </PillButton>
          <PillButton onClick={() => toggleQueryFilter({ minTripadvisorRating: '4' })} selected={minTripadvisorRating === '4'}>
            <Text fontSize="xs">Very Good 4+</Text>
          </PillButton>
          <PillButton onClick={() => toggleFacilityFilter('breakfast')} selected={facilities.includes('breakfast')}>
            <Text fontSize="xs">Breakfast Included</Text>
          </PillButton>
          <PillButton onClick={() => toggleQueryFilter({ freeCancellation: true })} selected={freeCancellation}>
            <Text fontSize="xs">Free Cancellation</Text>
          </PillButton>
          <PillButton onClick={() => toggleFacilityFilter('pool')} selected={facilities.includes('pool')}>
            <Text fontSize="xs">Swimming Pool</Text>
          </PillButton>
          <PillButton onClick={() => toggleQueryFilter({ minStarRating: '4' })} selected={minStarRating === '4'}>
            <Text fontSize="xs">4+ stars</Text>
          </PillButton>
        </ScrollableList>
      </PhoneShortcutFiltersContainer>
      <Hide sm md lg>
        <PayWith name="payWith" />
      </Hide>
    </>
  );
});
PhoneShortcutFilters.displayName = 'PhoneShortcutFilters';

export default PhoneShortcutFilters;
