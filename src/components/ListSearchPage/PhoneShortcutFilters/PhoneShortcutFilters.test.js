import React from 'react';
import capitalize from 'lodash/capitalize';
import PhoneShortcutFilters from './PhoneShortcutFilters';
import { mountUtils } from 'test-utils';
import { getResultCount } from 'store/search/searchSelectors';
import { getFullKnownQuery } from 'store/router/routerSelectors';
import { updateQuery } from 'store/search/searchActions';
import { useDataLayer } from 'hooks/useDataLayer';

export const FILTER_NAMES = Object.freeze({
  minTripadvisorRating: 'Trip Advisor Rating',
  minStarRating: 'Hotel Rating',
  freeCancellation: 'Free Cancellation',
});

jest.mock('hooks/useDataLayer');
jest.mock('store/search/searchSelectors');
jest.mock('store/router/routerSelectors');

mountUtils.mockComponent('PayWith');
mountUtils.mockComponent('SortBySelector');

const decorators = { store: true };
const render = () => mountUtils(<PhoneShortcutFilters />, { decorators });

const defaultQuery = {
  checkIn: new Date('2018-07-28'),
  checkOut: new Date('2018-07-29'),
  location: 'Melbourne',
  adults: 2,
  children: 0,
  minTripadvisorRating: undefined,
  minStarRating: undefined,
  freeCancellation: undefined,
  facilities: ['wifi', 'shuttle'],
};

const filterPills = [
  { value: 'parking', text: 'Parking Included', type: 'facility' },
  { value: 'minTripadvisorRating', text: 'Very Good 4+', type: 'query', default: '4' },
  { value: 'breakfast', text: 'Breakfast Included', type: 'facility' },
  { value: 'freeCancellation', text: 'Free Cancellation', type: 'query', default: true },
  { value: 'pool', text: 'Swimming Pool', type: 'facility' },
  { value: 'minStarRating', text: '4+ stars', type: 'query', default: '4' },
];

let emitInteractionEvent;

beforeEach(() => {
  getFullKnownQuery.mockReturnValue(defaultQuery);
  getResultCount.mockReturnValue(100);
  emitInteractionEvent = jest.fn();
  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

it('renders the filter copy', () => {
  const { findByTestId } = render();
  expect(findByTestId('mobile-filters-title')).toHaveText('Hotels in Melbourne');
  expect(findByTestId('mobile-filters-description')).toHaveText('100 properties • 28 Jul - 29 Jul • 2 guests');
});

it('renders the PayWith', () => {
  const { find } = render();
  expect(find('PayWith')).toExist();
});

it('renders the SortBySelector', () => {
  const { find } = render();
  expect(find('SortBySelector')).toExist();
});

it('renders the filter buttons', () => {
  const { find } = render();
  find('PillButton').forEach((element, index) => {
    expect(element).toHaveText(filterPills[index].text);
  });
});

describe('clicking on the facility filter', () => {
  filterPills.forEach((filter, index) => {
    if (filter.type === 'facility') {
      it(`adds the ${filter.value} filter`, () => {
        const { find, decorators } = render();
        find('PillButton button').at(index).simulate('click');

        expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery({ facilities: [...defaultQuery.facilities, filter.value] }));
        expect(emitInteractionEvent).toHaveBeenCalledWith({
          type: 'Mobile Filters Buttons',
          value: `${capitalize(filter.value)} Selected`,
        });
      });

      it(`removes the ${filter.value} filter`, () => {
        getFullKnownQuery.mockReturnValue({ ...defaultQuery, facilities: [...defaultQuery.facilities, filter.value] });

        const { find, decorators } = render();
        find('PillButton button').at(index).simulate('click');
        expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery({ facilities: defaultQuery.facilities }));
        expect(emitInteractionEvent).toHaveBeenCalledWith({
          type: 'Mobile Filters Buttons',
          value: `${capitalize(filter.value)} Removed`,
        });
      });
    }
  });
});

describe('clicking on the query filter', () => {
  filterPills.forEach((filter, index) => {
    if (filter.type === 'query') {
      it(`adds ${filter.value} to the query`, () => {
        const { find, decorators } = render();
        find('PillButton button').at(index).simulate('click');

        expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery({ [filter.value]: filter.default }));
        expect(emitInteractionEvent).toHaveBeenCalledWith({
          type: 'Mobile Filters Buttons',
          value: `${FILTER_NAMES[filter.value]} Selected`,
        });
      });

      it(`removes ${filter.value} from the query`, () => {
        getFullKnownQuery.mockReturnValue({ ...defaultQuery, [filter.value]: filter.default });

        const { find, decorators } = render();
        find('PillButton button').at(index).simulate('click');
        expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery({ [filter.value]: undefined }));
        expect(emitInteractionEvent).toHaveBeenCalledWith({
          type: 'Mobile Filters Buttons',
          value: `${FILTER_NAMES[filter.value]} Removed`,
        });
      });
    }
  });
});
