import React from 'react';
import styled from '@emotion/styled';
import { useDispatch, useSelector } from 'react-redux';
import Select from 'components/Select';
import { Label, Flex } from '@qga/roo-ui/components';
import { themeGet } from 'styled-system';
import { SORT_SELECT_OPTIONS } from 'lib/enums/search';
import { getQuerySortBy } from 'store/router/routerSelectors';
import { updateQuery } from 'store/search/searchActions';

const StyledSelect = styled(Select)`
  border-radius: ${themeGet('radii.default')};
  border: ${themeGet('borders.2')} ${themeGet('colors.greys.alto')};
  background-color: ${themeGet('colors.white')};
  cursor: pointer;
  ::-ms-expand {
    display: none;
  }

  &:focus {
    box-shadow: 0 0 0 1pt ${themeGet('colors.ui.linkFocus')};
  }
`;

const SortSelect = React.memo(() => {
  const sortBy = useSelector(getQuerySortBy);
  const dispatch = useDispatch();

  const onChange = ({ target: { value } }) => {
    dispatch(updateQuery({ sortBy: value }));
  };

  return (
    <Flex mb={8}>
      <Label hidden>Sort by:</Label>
      <StyledSelect
        value={sortBy}
        options={SORT_SELECT_OPTIONS}
        onChange={onChange}
        aria-label="Sort search results"
        data-testid="sort-select"
      />
    </Flex>
  );
});

SortSelect.displayName = 'SortSelect';

export default SortSelect;
