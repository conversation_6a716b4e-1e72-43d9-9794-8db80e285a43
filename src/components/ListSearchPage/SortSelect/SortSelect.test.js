import React from 'react';
import { mountUtils } from 'test-utils';
import SortSelect from './SortSelect';
import { SORT_BY } from 'lib/enums/search';
import { updateQuery } from 'store/search/searchActions';
import { getQuerySortBy } from 'store/router/routerSelectors';

jest.mock('store/router/routerSelectors');

const render = () => mountUtils(<SortSelect />, { decorators: { theme: true, store: true } });

const sortBy = 'popularity';

beforeEach(() => {
  jest.clearAllMocks();
  getQuerySortBy.mockReturnValue(sortBy);
});

it('renders the options', () => {
  const { find } = render();

  expect(find('select')).toHaveProp({ value: sortBy });

  expect(find('option')).toHaveLength(5);

  const promotionOption = find('option').at(0);
  expect(promotionOption.text()).toEqual(SORT_BY.PROMOTION.name);
  expect(promotionOption.props().value).toEqual(SORT_BY.PROMOTION.code);

  const popularityOption = find('option').at(1);
  expect(popularityOption.text()).toEqual(SORT_BY.POPULARITY.name);
  expect(popularityOption.props().value).toEqual(SORT_BY.POPULARITY.code);

  const rateDescOption = find('option').at(2);
  expect(rateDescOption.text()).toEqual(SORT_BY.PRICE_DESC.name);
  expect(rateDescOption.props().value).toEqual(SORT_BY.PRICE_DESC.code);

  const rateAscOption = find('option').at(3);
  expect(rateAscOption.text()).toEqual(SORT_BY.PRICE_ASC.name);
  expect(rateAscOption.props().value).toEqual(SORT_BY.PRICE_ASC.code);

  const minTripadvisorRatingOption = find('option').at(4);
  expect(minTripadvisorRatingOption.text()).toEqual(SORT_BY.TRIPADVISOR.name);
  expect(minTripadvisorRatingOption.props().value).toEqual(SORT_BY.TRIPADVISOR.code);
});

it('updates the query params when a new option is selected', () => {
  const { find, decorators } = render();

  find('select').simulate('change', { target: { value: 'price_asc' } });
  expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery({ sortBy: 'price_asc' }));
});
