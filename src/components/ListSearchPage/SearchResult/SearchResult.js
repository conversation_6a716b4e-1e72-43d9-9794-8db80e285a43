import React, { Fragment, useRef } from 'react';
import styled from '@emotion/styled';
import PropTypes from 'prop-types';
import { themeGet } from 'styled-system';
import { Card } from '@qga/roo-ui/components';
import isNil from 'lodash/isNil';
import SearchResultHighlighter from 'components/ListSearchPage/SearchResultHighlighter';
import SearchResultDetail from './SearchResultDetail';
import { useSelector } from 'react-redux';
import { getQueryFeaturedPropertyId, getQueryUtmSource, getQueryUtmCampaign, getQueryLocation } from 'store/router/routerSelectors';
import ExpandedClickableArea from 'components/ExpandedClickableArea';
import { getCampaignDefaultSash } from 'store/campaign/campaignSelectors';
import useViewPromotionEvent from 'hooks/useViewPromotionEvent';
import useSelectPromotionEvent from 'hooks/useSelectPromotionEvent';
import useSelectItemEvent from 'hooks/useSelectItemEvent';

const StyledCard = styled(Card)`
  padding: 0;
  position: relative;
  min-height: ${themeGet('imageSizes.searchThumbnailNotPhone.height')};
  box-shadow: ${themeGet('shadows.heavy')};
  transition: all 0.1s linear;
  &:hover {
    box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.1);
  }
`;

const isHighlighted = (featuredId, propertyId) => {
  if ([featuredId, propertyId].some(isNil)) return false;
  return Number(featuredId) === Number(propertyId);
};

const SearchResult = React.memo(({ result, index }) => {
  const ref = useRef(null);
  const location = useSelector(getQueryLocation);
  const featuredPropertyId = useSelector(getQueryFeaturedPropertyId);
  const utmCampaign = useSelector(getQueryUtmCampaign);
  const utmSource = useSelector(getQueryUtmSource);

  const highlighted = isHighlighted(featuredPropertyId, result.property.id);
  const Highlight = highlighted ? SearchResultHighlighter : Fragment;
  const referrerProp = highlighted ? { referrer: utmCampaign || utmSource } : {};

  const isClassic = result.offer.type === 'classic';
  const globalCampaignDefaultSash = useSelector(getCampaignDefaultSash);
  const useGlobalCampaignDefaultSash = globalCampaignDefaultSash && !isClassic;
  const promotionName = useGlobalCampaignDefaultSash ? globalCampaignDefaultSash : result.offer.promotion?.name;
  const promotion = { name: promotionName, slot: 'search_result' };
  const { fireSelectPromotionEvent } = useSelectPromotionEvent({ promotion });
  const { fireSelectItemEvent } = useSelectItemEvent();
  useViewPromotionEvent({ ref, promotion });

  const handleClick = () => {
    fireSelectPromotionEvent();
    fireSelectItemEvent({
      listName: `Hotels in ${location}`,
      type: 'list',
      property: result.property,
      offer: result.offer,
      roomType: result.roomType,
    });
  };

  return (
    <StyledCard data-testid="search-result" mb={[5, 6]} ref={ref} onClick={handleClick}>
      <Highlight {...referrerProp}>
        <ExpandedClickableArea position="relative">
          <SearchResultDetail result={result} cardIndex={index} />
        </ExpandedClickableArea>
      </Highlight>
    </StyledCard>
  );
});

SearchResult.propTypes = {
  index: PropTypes.number.isRequired,
  result: PropTypes.shape({
    property: PropTypes.object.isRequired,
    offer: PropTypes.object.isRequired,
    roomType: PropTypes.object.isRequired,
  }).isRequired,
};

export default SearchResult;
