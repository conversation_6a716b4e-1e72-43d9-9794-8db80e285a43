import React from 'react';
import PromotionalSash from 'components/PromotionalSash';
import Currency from 'components/Currency';
import { mountUtils } from 'test-utils';
import { useDataLayer } from 'hooks/useDataLayer';
import SearchResultDetail from './SearchResultDetail';
import { getQueryCheckIn, getQueryCheckOut } from 'store/router/routerSelectors';
import { getCountry } from 'store/search/searchSelectors';

jest.mock('config', () => ({
  SEARCH_DATE_FORMAT: 'YYYY-MM-DD',
  POINTS_EARN_ENABLED: true,
}));

jest.mock('hooks/useRatingTooltip');
jest.mock('store/router/routerSelectors');
jest.mock('hooks/useDataLayer');
jest.mock('store/search/searchSelectors');
jest.mock('store/ui/uiSelectors');

mountUtils.mockComponent('Image');
mountUtils.mockComponent('PromotionalSash');
mountUtils.mockComponent('TripAdvisorRating');
mountUtils.mockComponent('Inclusions');
mountUtils.mockComponent('PointsEarnSummary');
mountUtils.mockComponent('CancellationRefundSummary');
mountUtils.mockComponent('PriceBeforeDiscount');
mountUtils.mockComponent('PointsPerDollar');

let result;
const checkIn = new Date(2020, 10, 10);
const checkOut = new Date(2020, 10, 11);
const cardIndex = 19;
const emitInteractionEvent = jest.fn();
const country = 'Australia';
const limit = 40;

beforeEach(() => {
  jest.clearAllMocks();
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  getQueryCheckIn.mockReturnValue(checkIn);
  getQueryCheckOut.mockReturnValue(checkOut);
  getCountry.mockReturnValue(country);

  result = {
    offer: {
      charges: {
        total: {
          amount: '200',
          currency: 'AUD',
        },
      },
      cancellationPolicy: {
        isNonrefundable: true,
        description: 'sorry no refunds',
        cancellationWindows: [
          {
            currency: 'AUD',
            endTime: '2020-05-09T14:00:00+10:00',
            formattedBeforeDate: 'Thu 9 Apr, 2020',
            nights: '1',
            startTime: '2020-04-09T14:00:00+10:00',
          },
        ],
      },
      promotion: {
        campaignCode: '',
      },
      inclusions: [],
      pointsEarned: {
        qffPoints: {
          total: 1200,
        },
        maxQffEarnPpd: 3,
      },
    },
    roomType: { name: 'Superior Room', maxOccupantCount: 2 },
    property: {
      id: '111',
      name: 'Hilton',
      rating: '3.5',
      ratingType: 'AAA',
      mainImage: {
        urlMedium: 'med-image.jpg',
        urlLarge: 'large-image.jpg',
      },
      address: {
        suburb: 'Bangkok',
      },
    },
  };
});

const render = () =>
  mountUtils(<SearchResultDetail result={result} cardIndex={cardIndex} />, {
    decorators: { store: true, theme: true, router: true },
  });

describe('campaign messages', () => {
  describe('Currency is cash', () => {
    it('should contains <PointsPerDollar>', () => {
      const { find } = render();
      expect(find('PointsPerDollar')).toExist();
    });
  });
  describe('Currency is not cash', () => {
    beforeEach(() => {
      result.offer.type = 'special';
      result.offer.charges.total.currency = 'PTS';
    });

    it('renders the component with the correct props', () => {
      expect(render().find('CampaignPriceMessage')).toHaveProp({
        currency: 'PTS',
        offerType: 'special',
        country,
      });
    });
  });
});

describe('property image', () => {
  it('has the expected props', () => {
    expect(render().find('Image')).toHaveProp({
      src: result.property.mainImage.urlMedium,
      srcSet: `${result.property.mainImage.urlLarge} 2x, ${result.property.mainImage.urlMedium} 1x`,
    });
  });
});

describe('The Property details', () => {
  it('renders the property suburb', () => {
    expect(render().find('[data-testid="property-suburb"]').children().at(0)).toHaveText('Bangkok');
  });

  it('renders the property name', () => {
    expect(render().find('[data-testid="property-name"]').children().at(0)).toHaveText('Hilton');
  });
});

describe('The Room details', () => {
  it('renders the room name and occupant count', () => {
    expect(render().find('[data-testid="room-details"]').children().at(0)).toHaveText(
      `${result.roomType.name}  Sleeps ${result.roomType.maxOccupantCount}`,
    );
  });
});

describe('The Offer details', () => {
  it('renders the number of nights including a currency', () => {
    expect(render().find('[data-testid="number-of-nights"]').children().at(0)).toHaveText('1 night from (AUD)');
  });

  describe('PriceBeforeDiscount', () => {
    describe('when the offer does not include totalBeforeDiscount', () => {
      it('does not render the PriceBeforeDiscount', () => {
        result.offer.charges.totalBeforeDiscount = null;
        expect(render().find('PriceBeforeDiscount')).not.toExist();
      });
    });

    describe('when the offer includes totalBeforeDiscount', () => {
      describe('when totalBeforeDiscount equals total', () => {
        it('does not render the PriceBeforeDiscount', () => {
          result.offer.charges.totalBeforeDiscount = { ...result.offer.charges.total };
          expect(render().find('PriceBeforeDiscount')).not.toExist();
        });
      });

      describe('when totalBeforeDiscount does not equal total price', () => {
        beforeEach(() => {
          result.offer.charges.totalBeforeDiscount = { amount: '300', currency: 'AUD' };
        });

        it('renders the PriceBeforeDiscount', () => {
          expect(render().find('PriceBeforeDiscount')).toHaveProp({ total: result.offer.charges.totalBeforeDiscount });
        });
      });
    });
  });

  describe('total', () => {
    it('renders the total cost', () => {
      expect(render().find(Currency)).toHaveProp({
        amount: result.offer.charges.total.amount,
        currency: result.offer.charges.total.currency,
      });
    });

    it('does not render the * after the currency', () => {
      expect(render().findByTestId('asterisk')).not.toExist();
    });
  });

  describe('when currency is points', () => {
    it('does not render the currency in parenthesis', () => {
      result.offer.charges.total.currency = 'PTS';
      expect(render().find('Text[data-testid="number-of-nights"]').text()).not.toMatch(/\(*.\)/);
    });

    it('does render the * after the currency', () => {
      result.offer.charges.total.currency = 'PTS';
      expect(render().findByTestId('asterisk').text()).toEqual('*');
    });
  });

  describe('when currency is points and campaignCode contains classic', () => {
    it('renders a classic badge', () => {
      result.offer.charges.total.currency = 'PTS';
      result.offer.type = 'classic';
      expect(render().find('Icon[data-testid="classic-ribbon"]').exists()).toEqual(true);
    });
  });
});

describe('The PromotionalSash', () => {
  describe('with no offer.promotion', () => {
    beforeEach(() => {
      result.offer.promotion = null;
    });

    it('does not render any sashes', () => {
      expect(render().find(PromotionalSash).exists()).toEqual(false);
    });
  });

  describe('with offer.promotion', () => {
    beforeEach(() => {
      result.offer.promotion = {
        name: 'test promotion',
      };
    });

    it('renders sash on mobile with the correct sash', () => {
      expect(render().find('[data-testid="promotional-sash"]')).toHaveProp({ promotionName: result.offer.promotion.name });
    });
  });
});

describe('The Tripadvisor rating', () => {
  describe('when no customerRatings exist', () => {
    it('does not render the TripAdvisorRating', () => {
      expect(render().find('TripAdvisorRating')).not.toExist();
    });
  });

  describe('when the customerRatings exist', () => {
    it('renders the TripAdvisorRating', () => {
      result.property.customerRatings = [{ source: 'trip_advisor' }];
      expect(render().find('TripAdvisorRating').at(0)).toHaveProp({ rating: result.property.customerRatings[0] });
    });
  });
});

describe('Inclusions', () => {
  it('only renders if the Inclusions exist', () => {
    expect(render().find('Inclusions')).toHaveLength(0);
    result.offer.inclusions = [{ code: 'internet' }];
    expect(render().find('Inclusions')).toHaveLength(1);
  });
});

describe('The points earn message', () => {
  it('is rendered', () => {
    expect(render().find('PointsEarnSummary').exists()).toEqual(true);
  });
});

describe('The cancellation refund summary', () => {
  it('is rendered', () => {
    expect(render().find('CancellationRefundSummary').exists()).toEqual(true);
  });
});

describe('with a missing property image', () => {
  beforeEach(() => {
    result.property.mainImage = null;
  });

  it('has the expected src', () => {
    expect(render().find('Image').props().src).toBeUndefined();
  });

  it('has the expected srcSet', () => {
    expect(render().find('Image').props().srcSet).toBeUndefined();
  });
});

describe('when offer does not include points earn details', () => {
  beforeEach(() => {
    result.offer.pointsEarned = undefined;
  });

  it('is renders without error', () => {
    expect(render).not.toThrowError();
  });
});

describe('room name longer than 40 characters', () => {
  beforeEach(() => {
    result.roomType.name = 'Standard Queen Room (Emergency Accommodation Only) - No Housekeeping';
  });

  it('is shorten', () => {
    expect(render().find('[data-testid="room-details"]').children().at(0)).toHaveText(
      `${result.roomType.name.substring(0, limit - 1) + '...'}  Sleeps ${result.roomType.maxOccupantCount}`,
    );
  });
});
