import React from 'react';
import SearchResult from './SearchResult';
import { useDataLayer } from 'hooks/useDataLayer';
import { mountUtils } from 'test-utils';
import { getQueryFeaturedPropertyId, getQueryUtmSource, getQueryUtmCampaign, getQueryPage } from 'store/router/routerSelectors';
import { getPropertyLinkQueryString } from 'store/search/searchSelectors';

jest.mock('config', () => ({
  SEARCH_DATE_FORMAT: 'YYYY-MM-DD',
  LIST_SEARCH_LIMIT: 10,
}));
jest.mock('store/router/routerSelectors');
jest.mock('store/search/searchSelectors');
jest.mock('hooks/useDataLayer', () => ({ useDataLayer: jest.fn() }));

mountUtils.mockComponent('SearchResultDetail');

let result;
let propertyQueryString = 'propertyQueryString';
const index = 1;
const page = 2;
const emitInteractionEvent = jest.fn();

beforeEach(() => {
  getQueryPage.mockReturnValue(page);
  getPropertyLinkQueryString.mockReturnValue(propertyQueryString);

  useDataLayer.mockReturnValue({ emitInteractionEvent });

  result = {
    property: {
      id: '111',
      name: 'Hilton',
    },
    offer: {
      charges: {
        total: {
          amount: 100,
          currency: 'AUD',
        },
      },
    },
  };
});

afterEach(() => {
  getQueryUtmCampaign.mockReset();
  getQueryUtmSource.mockReset();
  getQueryFeaturedPropertyId.mockReset();
});

const decorators = { theme: true, router: true, store: true };
const render = () => mountUtils(<SearchResult result={result} index={index} />, { decorators });

describe('Metasearch highlighting', () => {
  describe('when the property is featured', () => {
    beforeEach(() => {
      getQueryFeaturedPropertyId.mockReturnValue(result.property.id);
    });

    it('renders the highlight component', () => {
      expect(render().find('SearchResultHighlighter')).toHaveLength(1);
    });

    describe('with a utmCampaign', () => {
      const utmCampaign = 'utmCampaign';

      beforeEach(() => {
        getQueryUtmCampaign.mockReturnValue(utmCampaign);
      });

      it('passes the referrer to the highlight', () => {
        const { find } = render();
        expect(find('SearchResultHighlighter')).toHaveProp({ referrer: utmCampaign });
      });
    });

    describe('with a utmSource', () => {
      const utmSource = 'utmSource';

      beforeEach(() => {
        getQueryUtmSource.mockReturnValue(utmSource);
      });

      it('passes the referrer to the highlight', () => {
        const { find } = render();
        expect(find('SearchResultHighlighter')).toHaveProp({ referrer: utmSource });
      });
    });
  });

  describe('when the property is not featured', () => {
    it('does not render the highlight component', () => {
      expect(render().find('SearchResultHighlighter')).not.toExist();
    });
  });
});

it('renders SearchResultDetail', () => {
  expect(render().find('SearchResultDetail')).toHaveProp({ result });
});
