import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { LIST_SEARCH_LIMIT } from 'config';
import { useSelector } from 'react-redux';
import { useDataLayer } from 'hooks/useDataLayer';
import { getPropertyLinkQueryString } from 'store/search/searchSelectors';
import { getQueryPage } from 'store/router/routerSelectors';
import AppLink from 'components/AppLink';
import { usePersonalisation } from 'hooks/usePersonalisation';
import { useBreakpoints } from 'hooks/useBreakpoints';
import { getIsMobileApp } from 'store/ui/uiSelectors';
import memoize from 'lodash/memoize';

const SearchResultLink = memoize(({ children, propertyId, cardIndex, ariaLabel }) => {
  const propertyQueryString = useSelector(getPropertyLinkQueryString);
  const paginationIndex = useSelector(getQueryPage);
  const { emitInteractionEvent } = useDataLayer();
  const personalisation = usePersonalisation();

  const { isLessThanBreakpoint } = useBreakpoints();
  const isMobileOrTablet = isLessThanBreakpoint(1);
  const isMobileApp = useSelector(getIsMobileApp);

  const shouldOpenInSameTab = isMobileOrTablet || isMobileApp;
  const linkTarget = shouldOpenInSameTab ? '_self' : '_blank';

  const propertyUrl = `/properties/${propertyId}?${propertyQueryString}`;

  const handlePropertyLinkOnClick = useCallback(() => {
    const cardsOnPrevPages = paginationIndex ? (paginationIndex - 1) * LIST_SEARCH_LIMIT : 0;
    const cardNumber = cardIndex + 1;
    const customAttributes = { user_event_value: propertyId };
    emitInteractionEvent({ type: 'Property Card', value: `Card ${cardsOnPrevPages + cardNumber} selected`, customAttributes });
    personalisation.trackSearchClick(propertyId);
  }, [personalisation, emitInteractionEvent, cardIndex, paginationIndex, propertyId]);

  return (
    <AppLink
      to={propertyUrl}
      onClick={handlePropertyLinkOnClick}
      aria-label={ariaLabel}
      data-gtmqueryselector="resultItem"
      data-expanded-clickable-area-target
      target={isMobileApp ? '_self' : linkTarget}
      rel="noopener noreferrer"
    >
      {children}
    </AppLink>
  );
});

SearchResultLink.propTypes = {
  children: PropTypes.node.isRequired,
  propertyId: PropTypes.string.isRequired,
  ariaLabel: PropTypes.string.isRequired,
  cardIndex: PropTypes.number.isRequired,
};

export default SearchResultLink;
