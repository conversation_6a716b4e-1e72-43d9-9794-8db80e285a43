import React from 'react';
import styled from '@emotion/styled';
import PropTypes from 'prop-types';
import { themeGet } from 'styled-system';
import { useTheme } from 'emotion-theming';
import { useSelector } from 'react-redux';
import { Flex, Box, Heading, StarRating, Text, Icon } from '@qga/roo-ui/components';
import get from 'lodash/get';
import isArray from 'lodash/isArray';
import find from 'lodash/find';
import Image from 'components/Image';
import TripAdvisorRating from 'components/TripAdvisorRating';
import Inclusions from 'components/ListSearchPage/Inclusions';
import Currency from 'components/Currency';
import PriceBeforeDiscount from 'components/PriceBeforeDiscount';
import Truncate from 'components/Truncate';
import PointsEarnSummary from 'components/PointsEarnSummary';
import PointsPerDollar from 'components/PointsPerDollar';
import PromotionalSash from 'components/PromotionalSash';
import { mediaQuery } from 'lib/styledSystem';
import CampaignPriceMessage from 'components/CampaignPriceMessage';
import { getQueryCheckIn, getQueryCheckOut } from 'store/router/routerSelectors';
import CancellationRefundSummary from 'components/CancellationRefundSummary/CancellationRefundSummary';
import { useBreakpoints } from 'hooks/useBreakpoints';
import { getNumberOfNights } from 'lib/date/numberOfNights';
import SearchResultLink from './SearchResultLink';
import { getCountry } from 'store/search/searchSelectors';
import { POINTS_PER_DOLLAR_DEFAULT, POINTS_EARN_ENABLED } from 'config';
import useRatingTooltip from 'hooks/useRatingTooltip';

const PromotionalSashContainer = styled(Box)`
  ${themeGet('card.searchResultCard.sashContainer')};
`;

const ResultDetailsContainer = styled(Flex)`
  padding: ${themeGet('space.3')} ${themeGet('space.1')} ${themeGet('space.0')} ${themeGet('space.3')};
  flex-direction: column;
  flex-basis: auto;

  ${mediaQuery.minWidth.sm} {
    padding: ${themeGet('space.8')} ${themeGet('space.1')} ${themeGet('space.5')} ${themeGet('space.5')};
    flex-basis: 300px;
  }

  ${mediaQuery.minWidth.md} {
    flex-basis: 350px;
  }
`;

const getTripAdvisorRating = (customerRatings) => {
  if (!isArray(customerRatings)) return null;
  return find(customerRatings, (rating) => rating.source === 'trip_advisor');
};

const truncateText = (text, limit) => {
  return text?.length > limit ? text.substring(0, limit - 1) + '...' : text;
};

const SearchResultDetail = React.memo(({ result, cardIndex }) => {
  const theme = useTheme();
  const { isLessThanBreakpoint } = useBreakpoints();
  const isMobile = isLessThanBreakpoint(0);
  const checkIn = useSelector(getQueryCheckIn);
  const checkOut = useSelector(getQueryCheckOut);
  const country = useSelector(getCountry);

  const { property, roomType, offer } = result;
  const { inclusions, charges, cancellationPolicy } = offer;
  const { rating, ratingType } = property;
  const ratingTooltip = useRatingTooltip({ ratingType });

  const tripAdvisorRatingItem = getTripAdvisorRating(property.customerRatings);
  const hasInclusions = inclusions.length > 0;
  const propertyImageSrcSet = property.mainImage
    ? `${get(property, 'mainImage.urlLarge')} 2x, ${get(property, 'mainImage.urlMedium')} 1x`
    : undefined;
  const imageAltTag = `${property.name} - ${get(property, 'address.suburb')} - ${get(property, 'address.country')}`;
  const total = charges.total;
  const totalBeforeDiscount = charges.totalBeforeDiscount;
  const hasDiscount = totalBeforeDiscount && totalBeforeDiscount.amount !== total.amount;
  const promotionSash = get(offer, 'promotion.name');
  const numberOfNights = getNumberOfNights({ checkIn, checkOut });
  const isClassic = offer.type === 'classic';
  const isCurrencyCash = total.currency !== 'PTS';
  const ariaCurrency = total.currency === 'PTS' ? 'points' : 'dollars';
  const pointsPerDollar = offer.pointsEarned?.maxQffEarnPpd ? offer.pointsEarned.maxQffEarnPpd : POINTS_PER_DOLLAR_DEFAULT;

  return (
    <Flex flex="1 1 auto" justifyContent="space-between" flexDirection="row">
      <Box flexBasis={theme.imageSizes.searchThumbnail.width}>
        <Image
          lazy
          width={theme.imageSizes.searchThumbnail.width}
          height="100%"
          src={get(property, 'mainImage.urlMedium')}
          srcSet={propertyImageSrcSet}
          alt={imageAltTag}
          borderRadius="defaultRoundLeftOnly"
        />
      </Box>

      <Flex flex="1 1 0px" flexDirection={['column', 'row']}>
        <ResultDetailsContainer>
          {promotionSash && (
            <PromotionalSashContainer>
              <PromotionalSash promotionName={promotionSash} data-testid="promotional-sash" />
            </PromotionalSashContainer>
          )}

          <SearchResultLink
            propertyId={property.id}
            cardIndex={cardIndex}
            ariaLabel={`${property.name} from ${parseInt(total.amount)} ${ariaCurrency}`}
          >
            <Truncate lines={2}>
              <Heading.h2 color="greys.charcoal" mb={0} fontSize={['sm', 'lg']} data-testid="property-name" lineHeight="normal">
                {property.name}
              </Heading.h2>
            </Truncate>
          </SearchResultLink>

          <Heading.h3 display={['none', 'block']} color="greys.dusty" fontSize="sm" mb={1} data-testid="property-suburb">
            {property?.address?.suburb}
          </Heading.h3>

          <Flex mt={1} alignItems="center" flexDirection="row">
            <StarRating
              rating={rating}
              ratingType={ratingType}
              size={12}
              tooltip={ratingTooltip?.tooltip}
              tooltipWrapper={{ pr: [6, 0] }}
            />
            {tripAdvisorRatingItem && <TripAdvisorRating rating={tripAdvisorRatingItem} small displayReviews={!isMobile} ml={[2, 4]} />}
          </Flex>
          <Box mt={1}>
            <Text color="greys.steel" fontSize="sm" data-testid="room-details">
              {truncateText(roomType.name, 40)} <Icon name="circle" size="4px" mx={1} mb={1} /> Sleeps {roomType.maxOccupantCount}
            </Text>
          </Box>
          {!isMobile && hasInclusions && <Inclusions inclusions={inclusions} mt="auto" alignItems="center" />}
        </ResultDetailsContainer>
        <Flex p={3} pl={[3, 5, 0]} pt={[1, 8, 8]} flex="1 1 auto" flexDirection="column" alignItems="flex-end">
          <Text display="block" color="greys.charcoal" lineHeight="30px" fontSize={['xs', 'sm']} data-testid="number-of-nights">
            {numberOfNights} from{isCurrencyCash && ` (${total.currency})`}
          </Text>
          <Flex alignItems="center">
            {isClassic && <Icon ml="-6px" name="ribbon" size={28} data-testid="classic-ribbon" color="greys.charcoal" />}
            <Currency
              amount={total.amount}
              currency={total.currency}
              roundToCeiling
              fontSize={['lg', '3xl']}
              hideCurrency={isCurrencyCash}
              color="greys.charcoal"
              fontWeight="bold"
            />
            {!isCurrencyCash && (
              <Text pt={[2, 4]} data-testid="asterisk">
                <sup>*</sup>
              </Text>
            )}
          </Flex>
          {hasDiscount && (
            <PriceBeforeDiscount
              total={totalBeforeDiscount}
              hideCurrency={isCurrencyCash}
              fontSize={['xs', 'sm']}
              lineHeight="0.6"
              roundToCeiling
              offerType={offer.type}
            />
          )}
          <CampaignPriceMessage currency={total.currency} offerType={offer.type} country={country} />
          <PointsEarnSummary {...offer.pointsEarned?.qffPoints} fontSize={['xs', 'sm']} display="block" mt={1} />
          {POINTS_EARN_ENABLED && isCurrencyCash && (
            <PointsPerDollar pointsPerDollar={pointsPerDollar} strikeThroughDefaultPoints={true} fontSize={['xs', 'sm']} display="block" />
          )}

          <CancellationRefundSummary
            cancellationPolicy={cancellationPolicy}
            fontSize={['xs', 'sm']}
            hideBeforeDate
            hideWhenNonRefundable
            display="block"
          />
        </Flex>
      </Flex>
    </Flex>
  );
});

SearchResultDetail.displayName = 'SearchResultDetail';

SearchResultDetail.propTypes = {
  result: PropTypes.shape({
    property: PropTypes.object.isRequired,
    offer: PropTypes.object.isRequired,
    roomType: PropTypes.object.isRequired,
  }).isRequired,
  cardIndex: PropTypes.number.isRequired,
};

export default SearchResultDetail;
