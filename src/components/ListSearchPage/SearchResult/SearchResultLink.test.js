import React from 'react';
import { createMockPersonalisationEventHandlers, mountUtils } from 'test-utils';
import { getPropertyLinkQueryString } from 'store/search/searchSelectors';
import { useDataLayer } from 'hooks/useDataLayer';
import SearchResultLink from './SearchResultLink';
import { usePersonalisation } from 'hooks/usePersonalisation';
import { useBreakpoints } from 'hooks/useBreakpoints';

jest.mock('hooks/useBreakpoints');
jest.mock('hooks/useDataLayer');
jest.mock('hooks/usePersonalisation');
jest.mock('store/search/searchSelectors');
jest.mock('store/user/userSelectors');
jest.mock('store/ui/uiSelectors');

const emitInteractionEvent = jest.fn();
const propertyQueryString =
  'adults=2&checkIn=2020-12-23&checkOut=2020-12-24&children=0&infants=0&location=Melbourne%2C VIC%2C Australia&page=1&payWith=points&sortBy=popularity';

const defaults = {
  children: 'Link',
  propertyId: '240240',
  cardIndex: 11,
  ariaLabel: 'an accessible description of the link',
};
const mockPerso = createMockPersonalisationEventHandlers();

const decorators = { store: true, router: `/search?${propertyQueryString}` };
const render = (props) => mountUtils(<SearchResultLink {...defaults} {...props} />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  usePersonalisation.mockReturnValue(mockPerso);
  getPropertyLinkQueryString.mockReturnValue(propertyQueryString);
  useBreakpoints.mockReturnValue({
    isLessThanBreakpoint: () => true,
  });
});

it('has the expected href', () => {
  const { find } = render();
  expect(find('AppLink')).toHaveProp({
    to: `/properties/240240?${propertyQueryString}`,
  });
});

it('has the expected aria-label', () => {
  const { find } = render();
  expect(find('AppLink')).toHaveProp({
    'aria-label': defaults.ariaLabel,
  });
});

it('has the expected data-gtmqueryselector', () => {
  const { find } = render();
  expect(find('AppLink')).toHaveProp({ 'data-gtmqueryselector': 'resultItem' });
});

it('emits an event when clicked', () => {
  const { find } = render();
  find('AppLink').simulate('click');

  expect(emitInteractionEvent).toHaveBeenCalledWith({
    type: 'Property Card',
    value: 'Card 12 selected',
    customAttributes: {
      user_event_value: defaults.propertyId,
    },
  });
});

it('emits perso event when clicked', () => {
  const { find } = render();
  find('AppLink').simulate('click');

  expect(mockPerso.trackSearchClick).toHaveBeenCalledWith(defaults.propertyId);
});

describe('property link', () => {
  describe('when user is on mobile or tablet', () => {
    beforeEach(() => {
      useBreakpoints.mockReturnValue({
        isLessThanBreakpoint: () => false,
      });
    });

    it('it opens in a new tab', () => {
      const { find } = render();
      expect(find('AppLink')).toHaveProp({
        target: '_blank',
      });
    });
  });

  describe('when user is on desktop', () => {
    beforeEach(() => {
      useBreakpoints.mockReturnValue({
        isLessThanBreakpoint: () => true,
      });
    });

    it('does not open in a new tab', () => {
      const { find } = render();
      expect(find('AppLink')).toHaveProp({
        target: '_self',
      });
    });
  });
});
