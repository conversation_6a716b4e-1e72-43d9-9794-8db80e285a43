import React from 'react';
import { mountUtils } from 'test-utils';
import Inclusions from './Inclusions';
import { useFeature } from '@optimizely/react-sdk';

jest.mock('@optimizely/react-sdk', () => ({
  useFeature: jest.fn(),
}));

const inclusions = [
  { code: 'club_access', description: 'free club access', icon: 'incJqTicket' },
  { code: 'internet', description: 'free internet', icon: 'incJqWifi' },
  { code: 'breakfast', description: 'breakfast for 2', icon: 'incJqBreakfast' },
  { code: 'parking', description: 'on-site parking', icon: 'incJqParking' },
];

const decorators = { theme: true };
const render = () => mountUtils(<Inclusions inclusions={inclusions} />, { decorators });

describe('The Inclusions', () => {
  describe('when useGlobalInclusions is false', () => {
    beforeEach(() => {
      useFeature.mockReturnValue([false]);
    });
    describe('when there are more than 3', () => {
      it('renders a maximum of 3', () => {
        const { find } = render();
        expect(find('Icon')).toHaveLength(3);
      });

      describe('The Title', () => {
        it('is added from the description', () => {
          const { findByTestId } = render();
          expect(findByTestId('inclusion').first()).toHaveProp({ title: 'free internet' });
          expect(findByTestId('inclusion').last()).toHaveProp({ title: 'on-site parking' });
        });
      });

      describe('The Icons', () => {
        it('only renders from the whitelist in order', () => {
          const { find } = render();
          expect(find('Icon').at(0)).toHaveProp({ name: 'wifi' });
          expect(find('Icon').at(1)).toHaveProp({ name: 'restaurant' });
          expect(find('Icon').at(2)).toHaveProp({ name: 'directionsCar' });
        });
      });
    });
  });

  describe('when useGlobalInclusions is true', () => {
    beforeEach(() => {
      useFeature.mockReturnValue([true]);
    });

    describe('when there are more than 3', () => {
      it('renders a maximum of 3', () => {
        const { find } = render();
        expect(find('Icon')).toHaveLength(3);
      });

      describe('The Title', () => {
        it('is added from the description', () => {
          const { findByTestId } = render();
          expect(findByTestId('inclusion').first()).toHaveProp({ title: 'free club access' });
          expect(findByTestId('inclusion').last()).toHaveProp({ title: 'breakfast for 2' });
        });
      });

      describe('The Icons', () => {
        it('it renders the icons in the order the inclusions are received', () => {
          const { find } = render();
          expect(find('Icon').at(0)).toHaveProp({ name: 'incJqTicket' });
          expect(find('Icon').at(1)).toHaveProp({ name: 'incJqWifi' });
          expect(find('Icon').at(2)).toHaveProp({ name: 'incJqBreakfast' });
        });
      });
    });
  });
});
