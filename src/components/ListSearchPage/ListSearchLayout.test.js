import React from 'react';
import ListSearchLayout from './ListSearchLayout';
import { mountUtils } from 'test-utils';
import LegacyBrowserBoundary from 'components/LegacyBrowserBoundary';

jest.mock('components/LegacyBrowserBoundary');
jest.mock('store/ui/uiSelectors');
jest.mock('hooks/useListSearchGa4Event');

mountUtils.mockComponent('SearchResultFetcher');
mountUtils.mockComponent('CampaignMessaging');
mountUtils.mockComponent('DynamicMessageBox');
mountUtils.mockComponent('Filters');
mountUtils.mockComponent('MapButton');
mountUtils.mockComponent('PayWith');
mountUtils.mockComponent('SearchControls');
mountUtils.mockComponent('SearchError');
mountUtils.mockComponent('SearchList');
mountUtils.mockComponent('SearchResultSummaryConnected');
mountUtils.mockComponent('SortSelect');
mountUtils.mockComponent('PromoArea');
mountUtils.mockComponent('WelcomeMessage');
mountUtils.mockComponent('MobileAppLeftNavigationIcon');
mountUtils.mockComponent('RequestCallbackModal');

beforeEach(() => {
  jest.clearAllMocks();
  LegacyBrowserBoundary.mockImplementation(({ children }) => children);
});

const render = (props) => mountUtils(<ListSearchLayout {...props} />, { decorators: { store: true, router: true, helmet: true } });

describe('ListSearchLayout', () => {
  it('renders SearchResultFetcher', () => {
    const { find } = render();
    expect(find('SearchResultFetcher')).toExist();
  });

  it('renders the MobileAppLeftNavigationIcon', () => {
    const { find } = render();
    expect(find('MobileAppLeftNavigationIcon')).toHaveProp({ iconName: 'close' });
  });

  it('renders the ListSearchMeta', () => {
    const { find } = render();
    expect(find('ListSearchMeta')).toExist();
  });

  it('renders page content', () => {
    const { find } = render();
    expect(find('MainContent')).toExist();
  });

  it('renders WelcomeMessage', () => {
    const { find } = render();
    expect(find('WelcomeMessage')).toExist();
  });

  it('renders SearchControls', () => {
    const { find } = render();
    expect(find('SearchControls')).toExist();
  });

  it('renders CampaignMessaging', () => {
    const { find } = render();
    expect(find('CampaignMessaging')).toExist();
  });

  it('renders PromoArea', () => {
    const { find } = render();

    expect(find('PromoArea')).toExist();
  });

  it('renders the skip to content button', () => {
    const { find } = render();
    expect(find('SkipToContentButton')).toExist();
  });

  it('renders the SearchResultSummary for desktop', () => {
    const { find } = render();
    expect(find('[data-testid="search-result-summary-desktop"]')).toExist();
  });

  it('renders the SortSelect', () => {
    const { find } = render();
    const SortSelects = find('SortSelect');
    expect(SortSelects).toExist();
  });

  it('renders the PayWith for desktop', () => {
    const { find } = render();
    expect(find('PayWith[name="payWith"]')).toExist();
  });

  it('renders the DynamicMessageBox', () => {
    const { find } = render();
    expect(find('DynamicMessageBox')).toExist();
  });

  it('renders the SidebarFilters', () => {
    const { find } = render();
    expect(find('Filters')).toExist();
  });

  it('renders the SearchError', () => {
    const { find } = render();
    expect(find('SearchError')).toExist();
  });

  it('renders SearchList', () => {
    const { find } = render();
    expect(find('SearchList')).toExist();
  });

  describe('maps button', () => {
    it('renders a map button', () => {
      const { find } = render();
      expect(find('MapButton')).toExist();
    });
  });
});

describe('RequestCallbackModal', () => {
  it('renders RequestCallbackModal', () => {
    const { find } = render();
    expect(find('RequestCallbackModal')).toHaveProp({ interactionType: 'Multiroom booking' });
  });
});
