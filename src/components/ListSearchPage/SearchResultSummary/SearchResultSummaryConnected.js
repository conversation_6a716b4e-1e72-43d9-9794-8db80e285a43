import React from 'react';
import { useSelector } from 'react-redux';
import { getResultCount, getTotalPropertyCount, getIsLoading } from 'store/search/searchSelectors';
import { getQueryLocation } from 'store/router/routerSelectors';
import { SearchResultSummary } from './SearchResultSummary';

export const SearchResultSummaryConnected = React.memo(() => {
  const resultCount = useSelector(getResultCount);
  const totalPropertyCount = useSelector(getTotalPropertyCount);
  const location = useSelector(getQueryLocation);
  const isLoading = useSelector(getIsLoading);

  return (
    <SearchResultSummary resultCount={resultCount} totalPropertyCount={totalPropertyCount} location={location} isLoading={isLoading} />
  );
});

SearchResultSummaryConnected.displayName = 'SearchResultSummaryConnected';

export default SearchResultSummaryConnected;
