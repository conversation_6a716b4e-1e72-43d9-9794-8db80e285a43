import React from 'react';
import { mountUtils } from 'test-utils';
import { SearchResultSummaryConnected } from './SearchResultSummaryConnected';
import { getResultCount, getTotalPropertyCount, getIsLoading } from 'store/search/searchSelectors';
import { getQueryLocation } from 'store/router/routerSelectors';

jest.mock('store/search/searchSelectors');
jest.mock('store/router/routerSelectors');

mountUtils.mockComponent('SearchResultSummary');

const render = () => mountUtils(<SearchResultSummaryConnected />, { decorators: { store: true } });

beforeEach(() => {
  jest.clearAllMocks();
});

it('renders with expected props', () => {
  getResultCount.mockReturnValue(10);
  getTotalPropertyCount.mockReturnValue(99);
  getQueryLocation.mockReturnValue('Melbourne');
  getIsLoading.mockReturnValue(false);

  const { find } = render();

  expect(find('SearchResultSummary')).toHaveProp({
    resultCount: 10,
    totalPropertyCount: 99,
    location: 'Melbourne',
    isLoading: false,
  });
});
