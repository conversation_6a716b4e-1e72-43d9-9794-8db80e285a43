import React from 'react';
import DynamicMessageBox from './DynamicMessageBox';
import { mountUtils, resetConfigModule } from 'test-utils';
import { getIsFlightBooker, getHasDismissedSearchMessaging } from 'store/user/userSelectors';
import { dismissSearchMessaging } from 'store/user/userActions';
import { useDataLayer } from 'hooks/useDataLayer';
import * as config from 'config';

jest.mock('config');
jest.mock('hooks/useDataLayer');
jest.mock('store/user/userSelectors', () => ({
  getIsFlightBooker: jest.fn(),
  getHasDismissedSearchMessaging: jest.fn(),
}));

const reactRedux = require('react-redux');
const dispatchMock = jest.fn();
reactRedux.useDispatch = jest.fn(() => dispatchMock);
const emitInteractionEvent = jest.fn();

const decorators = { theme: true, store: true };
const render = () => mountUtils(<DynamicMessageBox />, { decorators });

describe('<DynamicMessageBox />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    resetConfigModule(config);

    config.FLIGHT_BOOKER_MESSAGE_ENABLED = true;
    getIsFlightBooker.mockReturnValue(false);
    useDataLayer.mockReturnValue({ emitInteractionEvent });
    getHasDismissedSearchMessaging.mockReturnValue(true);
  });

  describe('when the banner has been dismissed before', () => {
    it('does not render the banner', () => {
      const { find } = render();

      expect(find('FlightBookerMessageBox')).not.toExist();
    });
  });

  describe("when the banner hasn't been dismissed before", () => {
    beforeEach(() => {
      getHasDismissedSearchMessaging.mockReturnValue(false);
    });

    describe('when the user is not a flightBooker', () => {
      it('does not render anything', () => {
        const { wrapper } = render();

        expect(wrapper).toBeEmptyRender();
      });
    });

    describe('when the user is a flightBooker', () => {
      beforeEach(() => {
        getIsFlightBooker.mockReturnValue(true);
      });

      it('renders the correct message', () => {
        const { find } = render();

        expect(find('FlightBookerMessageBox')).toExist();
      });

      describe('when FLIGHT_BOOKER_MESSAGE_ENABLED is off', () => {
        beforeEach(() => {
          config.FLIGHT_BOOKER_MESSAGE_ENABLED = false;
        });

        it('does not render a message', () => {
          const { find } = render();

          expect(find('FlightBookerMessageBox')).not.toExist();
        });
      });

      describe('when clicking the close icon', () => {
        it('dispatches the dismissSearchMessaging action', async () => {
          const { findByTestId } = render();
          findByTestId('close-flightbooker-banner').simulate('click');

          expect(dispatchMock).toHaveBeenCalledWith(dismissSearchMessaging());
        });
      });
    });
  });
});
