import React, { Fragment } from 'react';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { useUnmount } from 'react-use';
import { useDispatch } from 'react-redux';
import { Box, Container, Flex } from '@qga/roo-ui/components';
import SearchResultFetcher from 'components/SearchResultFetcher';
import PromoAreaDealsFetcher from 'components/PromoAreaDealsFetcher';
import CampaignMessaging from 'components/CampaignMessaging';
import MapButton from './Sidebar/MapButton';
import SkipToContentButton from 'components/SkipToContentButton';
import WelcomeMessage from 'components/WelcomeMessage';
import SearchControls from './SearchControls';
import { SearchResultSummaryConnected } from './SearchResultSummary';
import SortSelect from './SortSelect';
import SidebarFilters from './Sidebar/Filters';
import SidebarContainer from './Sidebar/SidebarContainer';
import SearchList from './SearchList';
import PayWith from './PayWith';
import SearchError from './SearchError';
import DynamicMessageBox from './Messaging/DynamicMessageBox';
import ListSearchMeta from './ListSearchMeta';
import PhoneShortcutFilters from './PhoneShortcutFilters';
import PromoArea from './PromoArea';
import LegacyBrowserBoundary from 'components/LegacyBrowserBoundary';
import MobileAppLeftNavigationIcon from 'components/MobileAppLeftNavigationIcon';
import { clearResults } from 'store/search/searchActions';
import RequestCallbackModal from '../RequestCallbackModal';
import useListSearchGa4Event from 'hooks/useListSearchGa4Event';

const CollapsibleBox = styled(Box)`
  flex-shrink: 0;
`;

const MainContent = styled(Box)`
  flex: 1;
  position: relative;
`;

const DisplaySidebarFilters = styled(Box)`
  position: absolute;
  top: 60px;
  left: 0;
  z-index: ${themeGet('zIndices.modalContent')};
  display: ${(props) => (props.isSidebarOpen ? 'block' : 'none')};
  width: ${themeGet('uiStructure.mediumScreenSidebar')};
`;

DisplaySidebarFilters.displayName = 'DisplaySidebarFilters';

MainContent.displayName = 'MainContent';

const ListSearchLayout = () => {
  const dispatch = useDispatch();

  useUnmount(() => {
    dispatch(clearResults());
  });

  useListSearchGa4Event();

  return (
    <Fragment>
      <RequestCallbackModal interactionType="Multiroom booking" />
      <SearchResultFetcher />
      <LegacyBrowserBoundary>
        <PromoAreaDealsFetcher />
      </LegacyBrowserBoundary>
      <MobileAppLeftNavigationIcon iconName="close" />
      <ListSearchMeta />
      <WelcomeMessage />
      <SearchControls />
      <CampaignMessaging />
      <LegacyBrowserBoundary>
        <PromoArea />
      </LegacyBrowserBoundary>
      <Container>
        <DynamicMessageBox />
      </Container>
      <div id="main-content"></div>
      <PhoneShortcutFilters />
      <Container display={['none', 'block']}>
        <SearchResultSummaryConnected data-testid="search-result-summary-desktop" />
      </Container>
      <Container>
        <Flex mt={[0, 4, 4]} flex="1 1 auto">
          <CollapsibleBox flexBasis={[0, 0, 400]}>
            <Box display={['none', 'none', 'block']}>
              <SidebarContainer position="relative">
                <SkipToContentButton as="a" href="#search-results">
                  Skip to content
                </SkipToContentButton>
                <MapButton />
                <SidebarFilters />
              </SidebarContainer>
            </Box>
          </CollapsibleBox>
          <div id="search-results" />
          <MainContent>
            <Flex alignItems={['flex-start', 'flex-end']} display={['none', 'flex']} mb={10}>
              <SortSelect />
              <PayWith name="payWith" />
            </Flex>
            <SearchError />
            <SearchList />
          </MainContent>
        </Flex>
      </Container>
    </Fragment>
  );
};

export default ListSearchLayout;
