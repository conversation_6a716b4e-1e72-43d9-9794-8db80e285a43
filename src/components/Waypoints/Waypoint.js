import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { Box } from '@qga/roo-ui/components';
import { useInView } from 'react-intersection-observer';

const Waypoint = ({ waypointId, onViewed }) => {
  const [ref, inView, entry] = useInView();

  useEffect(() => {
    if (inView && entry && window) {
      const pixelOffset = window.getComputedStyle(entry.target)?.top;
      onViewed({ waypointId, pixelOffset, active: true });
    }
  }, [inView, onViewed, waypointId, entry]);

  return <Box ref={ref} position="absolute" top={`${waypointId * 25}%`} />;
};

Waypoint.propTypes = {
  waypointId: PropTypes.number.isRequired,
  onViewed: PropTypes.func.isRequired,
};

export default Waypoint;
