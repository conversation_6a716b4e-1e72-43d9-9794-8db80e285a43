import React from 'react';
import { mocked, mountUtils } from 'test-utils';
import ClientsideReduxUpdater from './ClientsideReduxUpdater';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/router';
import { setIsMobileApp } from 'store/ui/uiActions';

jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: jest.fn(),
}));

const dispatch = jest.fn();

const decorators = { router: true, store: true };
const render = () => mountUtils(<ClientsideReduxUpdater />, { decorators });

describe('<ClientsideReduxUpdater />', () => {
  beforeEach(() => {
    mocked(useDispatch).mockReturnValue(dispatch);
  });

  it('updates setIsMobileApp() to true when clientType === qta', () => {
    mocked(useRouter).mockReturnValue({ query: { client: 'qta' } });
    render();
    expect(dispatch).toHaveBeenCalledWith(setIsMobileApp(true));
  });

  it('updates setIsMobileApp() to false when clientType !== qta', () => {
    mocked(useRouter).mockReturnValue({ query: {} });
    render();
    expect(dispatch).toHaveBeenCalledWith(setIsMobileApp(false));
  });
});
