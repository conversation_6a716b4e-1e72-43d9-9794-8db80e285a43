import React from 'react';
import { mountUtils } from 'test-utils';
import StepHeader from './StepHeader';
import { INITIAL, EDITING, COMPLETE } from 'components/StepWizard';
import { useDataLayer } from 'hooks/useDataLayer';

jest.mock('hooks/useDataLayer');

let edit = jest.fn();
const emitInteractionEvent = jest.fn();

const baseProps = { title: 'title', stepNumber: 1, edit };
const mockHasState =
  (expectedState) =>
  (...states) =>
    states.includes(expectedState);

const render = (props) => mountUtils(<StepHeader {...baseProps} {...props} />);

beforeEach(() => {
  edit.mockReset();
  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

it('displays the title', () => {
  const { findByTestId } = render({ hasState: mockHasState(INITIAL) });
  expect(findByTestId('title')).toHaveText(baseProps.title);
});

it('does not render the subTitle when not present', () => {
  const { findByTestId } = render({ hasState: mockHasState(INITIAL) });
  expect(findByTestId('sub-title')).not.toExist();
});

it('displays the subTitle when present', () => {
  const { findByTestId } = render({ subTitle: 'sub-title', hasState: mockHasState(INITIAL) });
  expect(findByTestId('sub-title')).toHaveText('sub-title');
});

[INITIAL, EDITING].forEach((state) => {
  describe(`when in ${state} state`, () => {
    it('does not display the checkCircle', () => {
      const { find } = render({ hasState: mockHasState(state) });
      expect(find('Icon[name="checkCircle"]')).not.toExist();
    });

    it('displays the step number', () => {
      const { findByTestId } = render({ hasState: mockHasState(state) });
      expect(findByTestId('step-number')).toHaveText(String(baseProps.stepNumber));
    });
  });
});

describe('when editing', () => {
  it('does not display the checkCircle', () => {
    const { find } = render({ hasState: mockHasState(EDITING) });
    expect(find('Icon[name="checkCircle"]')).not.toExist();
  });

  it('displays the step number', () => {
    const { findByTestId } = render({ hasState: mockHasState(EDITING) });
    expect(findByTestId('step-number')).toHaveText(String(baseProps.stepNumber));
  });
});

describe('when complete', () => {
  it('displays the checkCircle', () => {
    const { find } = render({ hasState: mockHasState(COMPLETE) });
    expect(find('Icon[name="checkCircle"]')).toExist();
  });

  it('does not display the step number', () => {
    const { findByTestId } = render({ hasState: mockHasState(COMPLETE) });
    expect(findByTestId('step-number')).not.toHaveText(String(baseProps.stepNumber));
  });

  it('displays the change button', () => {
    const { findByTestId } = render({ hasState: mockHasState(COMPLETE) });
    expect(findByTestId('change-button')).toExist();
  });

  describe('when clicking the change button', () => {
    it('calls edit on the step', () => {
      const { findByTestId } = render({ hasState: mockHasState(COMPLETE) });
      const changeButton = findByTestId('change-button');
      changeButton.simulate('click');
      expect(edit).toHaveBeenCalled();
    });

    it('dispatches an event to the data layer', () => {
      const { findByTestId } = render({ hasState: mockHasState(COMPLETE) });
      const changeButton = findByTestId('change-button');
      changeButton.simulate('click');
      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Change Button',
        value: 'title Button Selected',
      });
    });
  });
});
