/* eslint-disable react-hooks/rules-of-hooks,react-hooks/exhaustive-deps */
import React, { useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import findIndex from 'lodash/findIndex';
import compact from 'lodash/compact';

export const INITIAL = 'initial';
export const EDITING = 'editing';
export const COMPLETE = 'complete';

const StepWizard = ({ children, formData, updateFormData, collapsed }) => {
  const [steps, setSteps] = useState(
    React.Children.map(children, (_, index) => ({
      stepNumber: index + 1,
      state: index === 0 ? EDITING : INITIAL,
      containerRef: React.createRef(),
    })),
  );

  return React.Children.map(compact(children), (child, index) => {
    const step = {
      ...steps[index],
      state: collapsed ? INITIAL : steps[index].state,
    };

    const edit = useCallback(() => {
      const newSteps = [...steps];
      newSteps.forEach((step) => {
        if (step.state === EDITING) step.state = step.previousState;
      });
      newSteps[index] = {
        ...newSteps[index],
        state: EDITING,
        previousState: step.state,
      };
      setSteps(newSteps);

      const nextStepContainerElement = newSteps[index].containerRef.current;
      if (nextStepContainerElement)
        setTimeout(() => {
          // Give the render cycle some time to complete so that the scroll doesn't happen
          // before the nex step renders.
          // There will be a more reliable way to do this and we should
          // revisit once more of the steps are complete.
          nextStepContainerElement.scrollIntoView();
        }, 10);
    }, [index, step.state]);

    const getCurrentIndex = useCallback(() => findIndex(steps, (step) => step.state === EDITING));

    const getNextIndex = useCallback((currentIndex) => {
      const nextIndex = Math.min(currentIndex + 1, steps.length - 1);
      if (steps[nextIndex].state === COMPLETE) return getNextIndex(nextIndex);
      return nextIndex;
    });

    const getPrevIndex = useCallback((currentIndex) => {
      const prevIndex = Math.min(currentIndex - 1, steps.length - 1);
      return prevIndex;
    });

    const editNextStep = useCallback(() => {
      const currentIndex = getCurrentIndex();
      const nextIndex = getNextIndex(currentIndex);
      const newSteps = [...steps];

      newSteps[nextIndex] = { ...newSteps[nextIndex], state: EDITING, previousState: newSteps[nextIndex].state };
      setSteps(newSteps);

      const nextStepContainerElement = newSteps[getNextIndex(index)].containerRef.current;
      if (nextStepContainerElement)
        setTimeout(() => {
          // Give the render cycle some time to complete so that the scroll doesn't happen
          // before the nex step renders.
          // There will be a more reliable way to do this and we should
          // revisit once more of the steps are complete.
          nextStepContainerElement.scrollIntoView();
        }, 10);
    }, [getCurrentIndex, getNextIndex, index]);

    const editPrevStep = useCallback(() => {
      const currentIndex = getCurrentIndex();
      const prevIndex = getPrevIndex(currentIndex);
      const newSteps = [...steps];

      newSteps[currentIndex] = { ...newSteps[currentIndex], state: INITIAL, previousState: newSteps[currentIndex].state };
      newSteps[prevIndex] = { ...newSteps[prevIndex], state: EDITING, previousState: newSteps[prevIndex].state };
      setSteps(newSteps);

      const prevStepContainerElement = newSteps[getPrevIndex(index)].containerRef.current;
      if (prevStepContainerElement)
        setTimeout(() => {
          // Give the render cycle some time to complete so that the scroll doesn't happen
          // before the nex step renders.
          // There will be a more reliable way to do this and we should
          // revisit once more of the steps are complete.
          prevStepContainerElement.scrollIntoView();
        }, 10);
    }, [getCurrentIndex, getPrevIndex, index]);

    const completeStep = useCallback(
      (data) => {
        const newSteps = [...steps];
        updateFormData(data);
        newSteps[index].state = COMPLETE;
        setSteps(newSteps);
        editNextStep();
      },
      [editNextStep, index],
    );

    const hasState = useCallback((...states) => states.includes(step.state), [step.state]);

    const stepProps = {
      ...step,
      formData,
      completeStep,
      updateFormData,
      edit,
      editNextStep,
      editPrevStep,
      hasState,
    };

    return React.cloneElement(child, { step: stepProps });
  });
};

export default StepWizard;

StepWizard.propTypes = {
  formData: PropTypes.object.isRequired,
  updateFormData: PropTypes.func.isRequired,
};
