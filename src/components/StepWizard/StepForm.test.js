import React from 'react';
import { mountUtils } from 'test-utils';
import StepForm from './StepForm';
import { INITIAL, EDITING } from 'components/StepWizard';

const edit = jest.fn();
const completeStep = jest.fn();
const formData = { a: 1 };
const handleSubmit = (handler) => () => handler(formData);
const mockHasState =
  (expectedState) =>
  (...states) =>
    states.includes(expectedState);

jest.mock('components/CheckoutPage/CustomerDetails/sessionStorage');

const baseProps = { completeStep, handleSubmit };

const render = (props) =>
  mountUtils(
    <StepForm {...baseProps} {...props}>
      <div data-testid="child" />
    </StepForm>,
    { decorators: { store: true } },
  );

afterEach(() => {
  edit.mockReset();
  completeStep.mockReset();
});

describe('when editing', () => {
  it('renders children', () => {
    const { findByTestId } = render({ hasState: mockHasState(EDITING) });
    expect(findByTestId('child')).toExist();
  });

  it('the next-step button is of type "submit"', () => {
    const { findByTestId } = render({ hasState: mockHasState(EDITING) });
    expect(findByTestId('next-step-button')).toHaveProp({ type: 'submit' });
  });

  describe('onSubmit', () => {
    beforeEach(() => {
      const { find } = render({ hasState: mockHasState(EDITING) });
      find('form').props().onSubmit();
    });

    it('calls completeStep with formData', () => {
      expect(completeStep).toHaveBeenCalledWith({ a: 1 });
    });
  });
});

describe('when not in edit mode', () => {
  it('hides the form', () => {
    const { wrapper } = render({ hasState: mockHasState(INITIAL) });
    expect(wrapper).toHaveStyleRule('display', 'none');
  });
});
