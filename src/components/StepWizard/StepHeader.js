import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { Flex, Box, Heading, Text, Icon } from '@qga/roo-ui/components';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import TextButton from 'components/TextButton';
import { INITIAL, EDITING, COMPLETE } from 'components/StepWizard';
import { useDataLayer } from 'hooks/useDataLayer';

const ChangeButton = styled(TextButton)`
  text-transform: uppercase;
  line-height: 2;
  text-decoration: none;
  font-weight: ${themeGet('fontWeights.bold')};
`;

const StepHeader = ({ title, subTitle, stepNumber, edit, hasState }) => {
  const { emitInteractionEvent } = useDataLayer();
  const handleOnClick = useCallback(() => {
    edit(true);
    emitInteractionEvent({ type: 'Change Button', value: `${title} Button Selected` });
  }, [emitInteractionEvent, edit, title]);

  return (
    <Flex px={[4, 8]} py={4} borderBottom={1} borderColor="greys.alto">
      {hasState(INITIAL, EDITING) && (
        <Box borderRadius="50%" border={1} borderColor="greys.steel" textAlign="center" width="32px" height="32px" mr={[3, 5]}>
          <Text fontSize="sm" width="32px" lineHeight="32px" data-testid="step-number">
            {stepNumber}
          </Text>
        </Box>
      )}
      {hasState(COMPLETE) && (
        <Box mr={[3, 5]}>
          <Icon name="checkCircle" color="green" size={32} />
        </Box>
      )}
      <Flex justifyContent="space-between" flex="1 1 auto" alignItems="center">
        <Heading.h3 fontWeight="bold" fontSize="md" mb={0} lineHeight="32px" data-testid="title">
          {title}
        </Heading.h3>
        {subTitle && (
          <Box display={['none', 'block']} data-testid="sub-title">
            {subTitle}
          </Box>
        )}

        {hasState(COMPLETE) && (
          <Box>
            <ChangeButton onClick={handleOnClick} data-testid="change-button">
              Change
            </ChangeButton>
          </Box>
        )}
      </Flex>
    </Flex>
  );
};

StepHeader.propTypes = {
  title: PropTypes.string.isRequired,
  subTitle: PropTypes.oneOfType([PropTypes.string, PropTypes.element]),
  stepNumber: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  edit: PropTypes.func.isRequired,
  hasState: PropTypes.func.isRequired,
};

StepHeader.defaultProps = {
  subTitle: null,
};

export default StepHeader;
