import React from 'react';
import { mountUtils } from 'test-utils';
import StepWizard from './StepWizard';
import { INITIAL, EDITING, COMPLETE } from 'components/StepWizard';

// Note: this is written as one big integration test that navigates
// through a series of steps, exercising the various features of the wizard
// and confirming that the expected props are passed to each step

let formData;
let updateFormData = (newFormData) => (formData = { ...formData, ...newFormData });

const Step1 = ({ step }) => {
  const { editNextStep, completeStep } = step;
  const next = () => {
    completeStep({ step1: 'complete' });
    editNextStep();
  };
  return <button onClick={next} data-testid="complete-step" />;
};

const Step2 = ({ step }) => {
  const { edit, editNextStep, completeStep } = step;

  const skip = () => {
    completeStep({});
    editNextStep();
  };

  const next = () => {
    completeStep({ step2: 'complete' });
    editNextStep();
  };

  return (
    <div>
      <button onClick={skip} data-testid="skip-step" />
      <button onClick={edit} data-testid="edit-step" />
      <button onClick={next} data-testid="complete-step" />
    </div>
  );
};

const Step3 = () => null;

const IgnoredStep = () => null;

const render = (collapsed = false) =>
  mountUtils(
    <StepWizard formData={formData} updateFormData={updateFormData} collapsed={collapsed}>
      <Step1 foo="bar" />
      <Step2 biz="baz" />
      {false && <IgnoredStep />}
      <Step3 />
    </StepWizard>,
  );

const expectedFunctionProps = {
  completeStep: expect.any(Function),
  updateFormData: expect.any(Function),
  edit: expect.any(Function),
  editNextStep: expect.any(Function),
  editPrevStep: expect.any(Function),
  hasState: expect.any(Function),
};

let expectedStepProps;

beforeEach(() => {
  formData = {};
  expectedStepProps = {
    state: INITIAL,
    formData,
    containerRef: { current: null },
    ...expectedFunctionProps,
  };
});

describe('when in edit mode', () => {
  it('renders children with expected props skipping any ignored steps', () => {
    const { find } = render();

    expect(find(Step1)).toHaveProp({
      step: { ...expectedStepProps, state: EDITING, stepNumber: 1 },
      foo: 'bar',
    });

    expect(find(Step2)).toHaveProp({
      step: { ...expectedStepProps, stepNumber: 2 },
      biz: 'baz',
    });

    expect(find(Step3)).toHaveProp({
      step: { ...expectedStepProps, stepNumber: 3 },
    });

    expect(find(IgnoredStep)).not.toExist();
  });
});

describe('when collapsed', () => {
  it('none of the children will have the state EDITING', () => {
    const { find } = render(true);

    expect(find(Step1)).toHaveProp({
      step: { ...expectedStepProps, stepNumber: 1 },
      foo: 'bar',
    });

    expect(find(Step2)).toHaveProp({
      step: { ...expectedStepProps, stepNumber: 2 },
      biz: 'baz',
    });

    expect(find(Step3)).toHaveProp({
      step: { ...expectedStepProps, stepNumber: 3 },
    });

    expect(find(IgnoredStep)).not.toExist();
  });
});

describe('navigating the wizard', () => {
  let step1;
  let step2;
  let step3;

  it('sets the expected step props as each step is navigated', () => {
    const { find } = render();

    // Complete step 1 and proceed to step 2
    find(Step1).find('[data-testid="complete-step"]').simulate('click');

    step1 = find(Step1);
    step2 = find(Step2);
    step3 = find(Step3);

    expect(step1).toHaveProp({
      step: {
        ...expectedStepProps,
        stepNumber: 1,
        state: COMPLETE,
      },
    });

    expect(step2).toHaveProp({
      step: { ...expectedStepProps, stepNumber: 2, state: EDITING, previousState: INITIAL },
    });

    expect(step3).toHaveProp({
      step: { ...expectedStepProps, stepNumber: 3 },
    });

    expect(formData).toEqual({ step1: 'complete' });

    //Skip from step 2 to 3 without entering anything
    find(Step2).find('[data-testid="skip-step"]').simulate('click');

    step1 = find(Step1);
    step2 = find(Step2);
    step3 = find(Step3);

    expect(step1).toHaveProp({
      step: {
        ...expectedStepProps,
        stepNumber: 1,
        state: COMPLETE,
      },
    });

    expect(step2).toHaveProp({
      step: { ...expectedStepProps, stepNumber: 2, state: COMPLETE, previousState: INITIAL },
    });

    expect(step3).toHaveProp({
      step: { ...expectedStepProps, stepNumber: 3, state: EDITING, previousState: INITIAL },
    });

    expect(formData).toEqual({ step1: 'complete' });

    //Edit step 2
    find(Step2).find('[data-testid="edit-step"]').simulate('click');

    step1 = find(Step1);
    step2 = find(Step2);
    step3 = find(Step3);

    expect(step1).toHaveProp({
      step: {
        ...expectedStepProps,
        stepNumber: 1,
        state: COMPLETE,
      },
    });

    expect(step2).toHaveProp({
      step: { ...expectedStepProps, stepNumber: 2, state: EDITING, previousState: COMPLETE },
    });

    expect(step3).toHaveProp({
      step: { ...expectedStepProps, stepNumber: 3, previousState: INITIAL },
    });

    expect(formData).toEqual({ step1: 'complete' });

    //Complete step 2 and proceed to step 3

    find(Step2).find('[data-testid="complete-step"]').simulate('click');

    step1 = find(Step1);
    step2 = find(Step2);
    step3 = find(Step3);

    expect(step1).toHaveProp({
      step: {
        ...expectedStepProps,
        stepNumber: 1,
        state: COMPLETE,
      },
    });

    expect(step2).toHaveProp({
      step: {
        ...expectedStepProps,
        stepNumber: 2,
        state: COMPLETE,
        previousState: COMPLETE,
      },
    });

    expect(step3).toHaveProp({
      step: { ...expectedStepProps, stepNumber: 3, state: EDITING, previousState: INITIAL },
    });

    expect(formData).toEqual({ step1: 'complete', step2: 'complete' });
  });
});
