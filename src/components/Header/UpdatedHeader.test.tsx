import * as React from 'react';
import { render, within } from '@testing-library/react';
import { userEvent } from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { configureStore } from 'redux-mock-store';
import UpdatedHeader from './UpdatedHeader';
import { getQueryParams } from 'store/router/routerSelectors';
import { mocked } from 'test-utils';
import { useDataLayer } from 'hooks/useDataLayer';
import { HOTELS_URL } from 'config';

jest.mock('next/router', () => ({
  useRouter: jest.fn(() => ({
    isReady: jest.fn(),
    pathname: '/search/list',
  })),
}));
jest.mock('store/router/routerSelectors');
jest.mock('hooks/useDataLayer');

const emitInteractionEvent = jest.fn();
const mockStore = configureStore();
const mockedLocation = 'Melbourne';

const wrapper = (
  <Provider store={mockStore({})}>
    <UpdatedHeader />
  </Provider>
);

describe('UpdatedHeader', () => {
  beforeEach(() => {
    mocked(getQueryParams).mockReturnValue({ location: mockedLocation });
    mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
  });

  it('renders the jetstar logo in a link', () => {
    const { getByRole, getByAltText } = render(wrapper);

    const link = getByRole('link', { name: 'Jetstar logo' });
    expect(link).toBeInTheDocument();
    expect(link).toHaveAttribute('href', HOTELS_URL);

    const img = getByAltText('Jetstar logo');
    expect(img).toBeInTheDocument();
    expect(link).toContainElement(img);
  });

  it('renders all the links, in the right order, with the right labels', () => {
    const { queryAllByRole } = render(wrapper);

    const links = queryAllByRole('link');
    expect(links).toHaveLength(10);

    // skip 0 since it's the logo link
    expect(links[1]).toHaveAttribute('href', 'https://www.jetstar.com/au/en/flights');
    expect(links[1]).toHaveTextContent('Flights');
    expect(links[2]).toHaveAttribute('href', 'https://www.jetstar.com/au/en/holidays');
    expect(links[2]).toHaveTextContent('Holidays');
    expect(links[3]).toHaveAttribute('href', `/?location=${mockedLocation}`);
    expect(links[3]).toHaveTextContent('Hotels');
    expect(links[4]).toHaveAttribute('href', 'https://www.jetstar.com/au/en/car-hire');
    expect(links[4]).toHaveTextContent('Car Hire');
    expect(links[5]).toHaveAttribute('href', `/?location=${mockedLocation}`);
    expect(links[5]).toHaveTextContent('Hotels');
    expect(links[6]).toHaveAttribute('href', '/deals/sydney');
    expect(links[6]).toHaveTextContent('Deals');
    expect(links[7]).toHaveAttribute('href', '/?searchType=airbnb');
    expect(links[7]).toHaveTextContent('Airbnb');
    expect(links[8]).toHaveAttribute('href', '/faqs');
    expect(links[8]).toHaveTextContent('FAQs');
    expect(links[9]).toHaveAttribute('href', '/contact-us');
    expect(links[9]).toHaveTextContent('Contact us');
  });

  it('opens nav dropdown on click', async () => {
    const user = userEvent.setup();
    const { queryByRole } = render(wrapper);

    const trigger = queryByRole('button', { expanded: false });

    await user.click(trigger);

    expect(trigger).toHaveAttribute('aria-expanded', 'true');

    const content = document.getElementById(trigger.getAttribute('aria-controls'));
    expect(content).toBeInTheDocument();
  });

  it('when menu is opened, renders links in the right order with the right labels', async () => {
    const user = userEvent.setup();
    const { queryByRole } = render(wrapper);

    const trigger = queryByRole('button', { expanded: false });

    await user.click(trigger);

    const content = document.getElementById(trigger.getAttribute('aria-controls'));
    const links = within(content).getAllByRole('link');
    expect(links).toHaveLength(8);

    expect(links[0]).toHaveAttribute('href', 'https://www.jetstar.com/au/en/flights');
    expect(links[0]).toHaveTextContent('Flights');
    expect(links[1]).toHaveAttribute('href', 'https://www.jetstar.com/au/en/holidays');
    expect(links[1]).toHaveTextContent('Holidays');
    expect(links[2]).toHaveAttribute('href', `/?location=${mockedLocation}`);
    expect(links[2]).toHaveTextContent('Hotels');
    expect(links[3]).toHaveAttribute('href', '/deals/sydney');
    expect(links[3]).toHaveTextContent('Deals');
    expect(links[4]).toHaveAttribute('href', '/?searchType=airbnb');
    expect(links[4]).toHaveTextContent('Airbnb');
    expect(links[5]).toHaveAttribute('href', '/faqs');
    expect(links[5]).toHaveTextContent('FAQs');
    expect(links[6]).toHaveAttribute('href', '/contact-us');
    expect(links[6]).toHaveTextContent('Contact us');
    expect(links[7]).toHaveAttribute('href', 'https://www.jetstar.com/au/en/car-hire');
    expect(links[7]).toHaveTextContent('Car Hire');
  });

  it('when menu is opened, body scroll is locked', async () => {
    const user = userEvent.setup();
    const { queryByRole } = render(wrapper);

    const trigger = queryByRole('button', { expanded: false });

    await user.click(trigger);

    expect(document.body).toHaveStyle('overflow: hidden;');
  });

  // test passes but throws a console error 'Not implemented' as jsdom doesn't support navigation
  it('emits an event with the correct data when the Airbnb tab is clicked', async () => {
    const user = userEvent.setup();
    const { queryAllByRole } = render(wrapper);

    const links = queryAllByRole('link');

    await user.click(links[7]);

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Airbnb tab click',
      value: 'Airbnb tab clicked',
    });
  });

  it('when menu is opened, menu is navigatable with keyboard', async () => {
    const user = userEvent.setup();
    const { queryByRole } = render(wrapper);

    const trigger = queryByRole('button', { expanded: false });

    await user.click(trigger);

    const content = document.getElementById(trigger.getAttribute('aria-controls'));
    const links = within(content).getAllByRole('link');

    await user.keyboard('{Tab}');

    expect(links[0]).toHaveFocus();

    await user.keyboard('{Tab}');

    expect(links[1]).toHaveFocus();

    await user.keyboard('{Down}');

    expect(links[2]).toHaveFocus();

    await user.keyboard('{Up}');

    expect(links[1]).toHaveFocus();
  });

  // Potentially valuable tests but jsdom limitations with stylesheets and css-in-js solutions make them unusable for now
  // describe('when rendered on a mobile device', () => {
  //   it('renders navigation dropdown menu', () => {
  //     const { queryByRole } = render(
  //   <Provider store={mockStore({})}>
  //     <UpdatedHeader />
  //   </Provider>,
  // );
  //     const nav = queryByRole('navigation');
  //     expect(nav).toBeVisible();
  //   });
  // });

  // describe('when rendered on a desktop device', () => {
  //   it("doesn't render navigation dropdown menu", () => {
  //     const { queryByRole } = render(
  //   <Provider store={mockStore({})}>
  //     <UpdatedHeader />
  //   </Provider>,
  // );
  //     const nav = queryByRole('navigation', { hidden: true });
  //     expect(nav).not.toBeVisible();
  //   });
  // });
});
