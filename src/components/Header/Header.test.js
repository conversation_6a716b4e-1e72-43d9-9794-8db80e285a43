import React from 'react';
import { mountUtils } from 'test-utils';
import Header from './Header';
import {
  DESKTOP_NAVIGATION_MENU,
  PHONE_NAVIGATION_MENU,
  QFF_LINKS,
  SIMPLICITY_HEADER_ENABLED,
  QFF_ACCOUNT_MANAGEMENT,
  ACCOUNT_MANAGEMENT_TYPES,
  HEADER_LOGO,
} from 'config';
import { useDataLayer } from 'hooks/useDataLayer';
import { useBreakpoints } from 'hooks/useBreakpoints';
import { useRouter } from 'next/router';
import { Header as QGAHeader } from '@qga/components';

jest.mock('hooks/useBreakpoints');
jest.mock('hooks/useDataLayer');
jest.mock('store/router/routerSelectors');
jest.mock('store/ui/uiSelectors');
jest.mock('next/router', () => ({
  useRouter: jest.fn(() => ({
    push: jest.fn(),
    pathname: '/search/list',
  })),
}));

const emitInteractionEvent = jest.fn();

const decorators = { theme: true, router: true, store: true };
const render = () => mountUtils(<Header />, { decorators });

describe('<Header />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    useBreakpoints.mockReturnValue({ isLessThanBreakpoint: () => false });
    useDataLayer.mockReturnValue({ emitInteractionEvent });
  });

  it('renders the Header with props', () => {
    const { find } = render();
    expect(find(QGAHeader).last()).toHaveProp({
      desktopLinks: DESKTOP_NAVIGATION_MENU,
      phoneLinks: PHONE_NAVIGATION_MENU,
      qffLinks: QFF_LINKS,
      displayLoginTooltip: QFF_ACCOUNT_MANAGEMENT === ACCOUNT_MANAGEMENT_TYPES.APP_WIDE,
      qffLoginEnabled: QFF_ACCOUNT_MANAGEMENT === ACCOUNT_MANAGEMENT_TYPES.APP_WIDE,
      universalNavEnabled: SIMPLICITY_HEADER_ENABLED,
      navEnabled: true,
      ...HEADER_LOGO,
    });
  });

  it('renders the Logo with the correct alt text as for SEO optimisation', () => {
    const { findByTestId } = render();

    expect(findByTestId('header-logo-link').last().children()).toHaveProp({ alt: 'book hotels and accommodation' });
  });

  describe('when desktop it tracks', () => {
    it('the login click', () => {
      const { find } = render();
      find(QGAHeader).last().props().onClickLogin();

      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'QFF Login Button',
        value: 'Desktop Log In Button Selected',
      });
    });

    it('the logout click', () => {
      const { find } = render();
      find(QGAHeader).last().props().onClickLogout();

      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'QFF Logout Button',
        value: 'Desktop Log Out Button Selected',
      });
    });
  });

  describe('when mobile it tracks', () => {
    beforeEach(() => {
      useBreakpoints.mockReturnValue({ isLessThanBreakpoint: () => true });
    });

    it('the login click', () => {
      const { find } = render();
      find(QGAHeader).last().props().onClickLogin();

      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'QFF Login Button',
        value: 'Mobile Log In Button Selected',
      });
    });

    it('the logout click', () => {
      const { find } = render();
      find(QGAHeader).last().props().onClickLogout();

      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'QFF Logout Button',
        value: 'Mobile Log Out Button Selected',
      });
    });
  });
});

describe('<Header /> in search-map', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    useDataLayer.mockReturnValue({ emitInteractionEvent });
    useRouter.mockReturnValue({
      push: jest.fn(),
      pathname: '/search/map',
    });
  });

  it('does not display the login tooltip', () => {
    const { find } = render();
    expect(find(QGAHeader).last()).toHaveProp({
      displayLoginTooltip: false,
    });
  });
});
