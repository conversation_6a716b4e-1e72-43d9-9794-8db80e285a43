import React from 'react';
import { mountUtils } from 'test-utils';
import HorizontalDotNavigation from './HorizontalDotNavigation';
import theme from 'lib/theme';

const decorators = { store: true, theme: true };

const props = {
  position: 1,
  total: 3,
};

const render = () => mountUtils(<HorizontalDotNavigation {...props} />, { decorators });

describe('Horizontal Dot Navigation', () => {
  it('renders a dot for each property card', () => {
    const { findByTestId } = render();

    expect.assertions(props.total);
    Array.from({ length: props.total }).forEach((_, dotPosition) => {
      expect(findByTestId(`dot-${dotPosition}`)).toExist();
    });
  });

  it('highlights the dot with index equal to the position prop', () => {
    const { findByTestId } = render();

    expect(findByTestId('dot-1')).toHaveStyleRule('background-color', theme.colors.greys.dusty);
  });

  it('does not highlight dots with index not equal to the position prop', () => {
    const { findByTestId } = render();

    expect(findByTestId('dot-0')).toHaveStyleRule('background-color', theme.colors.greys.alto);
    expect(findByTestId('dot-2')).toHaveStyleRule('background-color', theme.colors.greys.alto);
  });
});
