import React, { Fragment } from 'react';
import { DESKTOP_MAP_SEARCH_LIMIT, MOBILE_MAP_SEARCH_LIMIT } from 'config';
import { useUnmount } from 'react-use';
import { useDispatch } from 'react-redux';
import SearchResultFetcher from 'components/SearchResultFetcher';
import { FixedPageWrapper, MapWrapper } from './primitives';
import MapSearchHelmet from './MapSearchHelmet';
import ResultsMap from './ResultsMap';
import { useBreakpoints } from 'hooks/useBreakpoints';
import { clearResults } from 'store/search/searchActions';
import useListSearchGa4Event from 'hooks/useListSearchGa4Event';

const MapSearch = () => {
  const { isLessThanBreakpoint } = useBreakpoints();
  const isMobileOrTablet = isLessThanBreakpoint(1);
  const dispatch = useDispatch();

  const searchLimit = isMobileOrTablet ? MOBILE_MAP_SEARCH_LIMIT : DESKTOP_MAP_SEARCH_LIMIT;

  useUnmount(() => {
    dispatch(clearResults());
  });

  useListSearchGa4Event();

  return (
    <Fragment>
      <MapSearchHelmet />
      <SearchResultFetcher limit={searchLimit} />
      <FixedPageWrapper>
        <MapWrapper>
          <ResultsMap />
        </MapWrapper>
      </FixedPageWrapper>
    </Fragment>
  );
};

export default MapSearch;
