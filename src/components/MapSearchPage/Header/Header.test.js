import React from 'react';
import { mountUtils } from 'test-utils';
import Header from './Header';
import { getIsMobileApp } from 'store/ui/uiSelectors';
import { getQueryString, getQueryLocation } from 'store/router/routerSelectors';
import BackLink from 'components/BackLink';
import { emitUserInteraction } from 'store/userEnvironment/userEnvironmentActions';

jest.mock('components/BackLink', () => () => null);
jest.mock('store/router/routerSelectors');
jest.mock('store/ui/uiSelectors');

describe('<Header />', () => {
  beforeEach(() => {
    getQueryLocation.mockReturnValue('Hobart');
    getQueryString.mockReturnValue('?sometext=true');
  });

  const decorators = { store: true, theme: true, router: true };
  const render = () => mountUtils(<Header />, { decorators });

  describe('BackLink', () => {
    it('passes the provided queryString to the back to location link', () => {
      const { wrapper } = render();
      expect(wrapper.find(BackLink)).toHaveProp({ to: '/search/list?sometext=true' });
    });

    it('omits lat/long parameters in the back to location link', () => {
      getQueryString.mockReturnValue('?neLat=12&neLng=12&swLat=12&swLng=12&location=Hobart');
      const { wrapper } = render();
      expect(wrapper.find(BackLink)).toHaveProp({ to: '/search/list?location=Hobart' });
    });

    it('passes a callback to the BackLink that logs a GA event', () => {
      const { find, decorators } = render();
      const { dispatch } = decorators.store;

      find(BackLink).invoke('onClick')();
      expect(dispatch).toHaveBeenCalledWith(expect.objectContaining({ type: emitUserInteraction.type }));
    });
  });

  describe('Mobile viewport BackLink', () => {
    describe('is web app', () => {
      beforeEach(() => {
        getIsMobileApp.mockReturnValue(false);
      });

      it('passes the provided queryString to the back to location link', () => {
        const { wrapper } = render();
        expect(wrapper.find('StyledBackLink')).toHaveProp({ to: '/search/list?sometext=true' });
      });

      it('omits lat/long parameters in the back to location link', () => {
        getQueryString.mockReturnValue('?neLat=12&neLng=12&swLat=12&swLng=12&location=Hobart');
        const { wrapper } = render();
        expect(wrapper.find('StyledBackLink')).toHaveProp({ to: '/search/list?location=Hobart' });
      });

      it('passes a callback to the BackLink that logs a GA event', () => {
        const { find, decorators } = render();
        const { dispatch } = decorators.store;

        find('StyledBackLink').invoke('onClick')();
        expect(dispatch).toHaveBeenCalledWith(expect.objectContaining({ type: emitUserInteraction.type }));
      });
    });
    describe('is mobile app', () => {
      beforeEach(() => {
        getIsMobileApp.mockReturnValue(true);
      });

      it('does not render the back link', () => {
        const { wrapper } = render();
        expect(wrapper.find('StyledBackLink')).not.toExist();
      });
    });
  });
});
