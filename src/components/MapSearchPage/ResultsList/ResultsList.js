import isEmpty from 'lodash/isEmpty';
import PropTypes from 'prop-types';
import React, { useCallback, useEffect, Fragment } from 'react';
import { useSelector } from 'react-redux';
import { Box, Flex } from '@qga/roo-ui/components';
import { getResultCount, getIsLoading, getResults } from 'store/search/searchSelectors';
import { getQueryLocation, getQueryPage } from 'store/router/routerSelectors';
import styled from '@emotion/styled';
import Result from './Result';
import Pagination from 'components/Pagination';
import PaginationSummary from 'components/Pagination/Summary';
import ResultLoader from 'components/Loader/ResultLoader';
import { PropertyCardSkeleton } from 'components/PropertyCard';
import ResultSummary from './ResultSummary';
import { DESKTOP_MAP_SEARCH_LIMIT } from 'config';
import { fixBodyScroll, unfixBodyScroll } from 'lib/browser';
import { useDataLayer } from 'hooks/useDataLayer';
import { usePersonalisation } from 'hooks/usePersonalisation';
import useSelectItemEvent from 'hooks/useSelectItemEvent';

const ListWrapper = styled(Box)`
  flex-grow: 1;
  overflow: auto;
  width: 100%;
`;

const ResultsList = React.memo(({ results = [], onResultHover }) => {
  const listRef = React.useRef();
  const resultCount = useSelector(getResultCount);
  const page = useSelector(getQueryPage) || 1;
  const isLoading = useSelector(getIsLoading);
  const searchResults = useSelector(getResults);
  const location = useSelector(getQueryLocation);

  const { emitInteractionEvent } = useDataLayer();
  const personalisation = usePersonalisation();
  const { fireSelectItemEvent } = useSelectItemEvent();

  const offset = DESKTOP_MAP_SEARCH_LIMIT * page - DESKTOP_MAP_SEARCH_LIMIT;
  const from = offset + 1;
  const to = from + DESKTOP_MAP_SEARCH_LIMIT - 1;

  useEffect(() => {
    if (!listRef.current?.scrollTo) return;

    listRef.current.scrollTo(0, 0);
  }, [listRef, results]);

  useEffect(() => {
    fixBodyScroll();
    return () => {
      unfixBodyScroll();
    };
  });

  const handleResultClick = useCallback(
    ({ index, id }) => {
      const cardsOnPrevPages = (page - 1) * DESKTOP_MAP_SEARCH_LIMIT;
      const cardNumber = cardsOnPrevPages + (index + 1);
      const customAttributes = { user_event_value: id };
      const result = searchResults[index];

      fireSelectItemEvent({
        listName: `Hotels in ${location}`,
        type: 'map',
        property: result.property,
        offer: result.offer,
        roomType: result.roomType,
      });

      emitInteractionEvent({ type: 'Property Card', value: `Property ${cardNumber} Selected`, customAttributes });
      personalisation.trackMapClick(id);
    },
    [page, searchResults, fireSelectItemEvent, location, emitInteractionEvent, personalisation],
  );

  if (!isLoading && isEmpty(results)) return null;

  return (
    <Flex
      backgroundColor="snow"
      boxShadow="hard"
      flexDirection="column"
      justifyContent="center"
      minWidth={500}
      width={500}
      data-testid="results-list-wrapper"
    >
      <Box p={4} borderBottom={1} borderColor="greys.alto">
        <ResultSummary isLoading={isLoading} />
      </Box>

      <ListWrapper ref={listRef} p={isLoading ? 4 : 0}>
        <ResultLoader isLoading={isLoading} skeletonResultCount={8} skeletonCardComponent={PropertyCardSkeleton}>
          {!isLoading && (
            <Fragment>
              <Box px={4} my={2}>
                <PaginationSummary from={from} to={to} total={resultCount} />
              </Box>
              {results.map((result, index) => (
                <Result
                  key={`result-${index}`}
                  property={result}
                  onHover={onResultHover}
                  onClick={() => handleResultClick({ index, id: result.id })}
                />
              ))}
              <Flex justifyContent="center" p={4}>
                <Box>
                  <Pagination flexDirection="column" pageSize={DESKTOP_MAP_SEARCH_LIMIT} total={resultCount} showSortOrder />
                </Box>
              </Flex>
            </Fragment>
          )}
        </ResultLoader>
      </ListWrapper>
    </Flex>
  );
});

ResultsList.displayName = 'ResultsList';

ResultsList.propTypes = {
  results: PropTypes.array,
  onResultHover: PropTypes.func.isRequired,
};

ResultsList.defaultProps = {
  results: [],
};

export default ResultsList;
