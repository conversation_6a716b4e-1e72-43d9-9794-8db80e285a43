import React from 'react';
import { act } from 'react-dom/test-utils';
import PropertyMarker from './PropertyMarker';
import { Marker, RedTriangle } from './primitives';
import { mountUtils } from 'test-utils';
import { getMapHoveredPropertyId, getMapActivePropertyId } from 'store/ui/uiSelectors';

mountUtils.mockComponent('Currency');
jest.mock('store/ui/uiSelectors');

const defaultProps = {
  map: jest.fn(),
  latitude: -37.671015156,
  longitude: 144.850230217,
  total: { amount: '99', currency: 'AUD' },
  onClick: jest.fn(),
  id: 'a',
  active: false,
  title: 'Novotel London West',
};

const decorators = { store: true, theme: true };
const render = (props) => mountUtils(<PropertyMarker {...defaultProps} {...props} />, { decorators });

describe('<PropertyMarker />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders <Wrapper /> with title', () => {
    const { find } = render();

    expect(find(Marker).prop('title')).toEqual(defaultProps.title);
  });

  it('renders <RedTriangle />', () => {
    const { find } = render();
    expect(find(RedTriangle).exists()).toBe(true);
  });

  describe('when currency is AUD', () => {
    it('renders <Currency /> with hideCurrency: true', () => {
      const audTotal = { ...defaultProps.total, currency: 'AUD' };
      const { find } = render({ total: audTotal });

      expect(find('Currency').props()).toEqual(expect.objectContaining({ hideCurrency: true }));
    });
  });

  describe('when currency is PTS', () => {
    it('renders <Currency /> with hideCurrency: false', () => {
      const pointsTotal = { ...defaultProps.total, currency: 'PTS' };
      const { find } = render({ total: pointsTotal });

      expect(find('Currency').props()).toEqual(expect.objectContaining({ hideCurrency: false }));
    });
  });

  describe('when the marker is active', () => {
    it('sets highlight on Marker and Triangle to true', () => {
      getMapActivePropertyId.mockReturnValue(defaultProps.id);
      const { find } = render({ active: true });

      expect(find(Marker).prop('highlight')).toBe(true);
      expect(find(RedTriangle).prop('highlight')).toBe(true);
    });
  });

  describe('when the marker is hovered', () => {
    it('sets highlight on Marker and Triangle to true', () => {
      getMapHoveredPropertyId.mockReturnValue(defaultProps.id);
      const { find, wrapper } = render();

      act(() => {
        find(Marker).simulate('mouseEnter');
      });

      wrapper.update();
      expect(find(Marker).prop('highlight')).toBe(true);
      expect(find(RedTriangle).prop('highlight')).toBe(true);
    });
  });

  describe('when clicked', () => {
    it('calls the onClick callback with id, latitude and longitude', () => {
      const { find, wrapper } = render();

      act(() => {
        find(Marker).simulate('click');
      });

      wrapper.update();
      expect(defaultProps.onClick).toHaveBeenCalledWith({ id: 'a', lat: -37.671015156, lng: 144.850230217 });
    });
  });
});
