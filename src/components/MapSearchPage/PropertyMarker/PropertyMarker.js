import React, { memo, useCallback } from 'react';
import { useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import { Marker, RedTriangle } from './primitives';
import Currency from 'components/Currency';
import { useHover } from 'hooks';
import { getMapHoveredPropertyId, getMapActivePropertyId } from 'store/ui/uiSelectors';

const PropertyMarker = ({ id, latitude, longitude, total, title, onClick }) => {
  const hoveredPropertyId = useSelector(getMapHoveredPropertyId);
  const activePropertyId = useSelector(getMapActivePropertyId);
  const [markerHovered, bind] = useHover();
  const isHovered = markerHovered || hoveredPropertyId === id;
  const isActive = activePropertyId === id;
  const highlight = isActive || isHovered;

  const handleClick = useCallback(
    (event) => {
      event.stopPropagation();
      onClick({ id, lat: latitude, lng: longitude });
    },
    [id, latitude, longitude, onClick],
  );

  return (
    <Marker active={isActive} onClick={handleClick} title={title} highlight={highlight} {...bind} data-testid="google-maps-property-marker">
      <Currency amount={total.amount} currency={total.currency} hideCurrency={total.currency === 'AUD'} roundToCeiling fontWeight="bold" />
      <RedTriangle highlight={highlight} />
    </Marker>
  );
};

PropertyMarker.propTypes = {
  latitude: PropTypes.number.isRequired,
  longitude: PropTypes.number.isRequired,
  total: PropTypes.shape({
    amount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    currency: PropTypes.string.isRequired,
  }).isRequired,
  onClick: PropTypes.func.isRequired,
  id: PropTypes.string.isRequired,
  active: PropTypes.bool.isRequired,
  title: PropTypes.string.isRequired,
  hoveredPropertyId: PropTypes.string,
};

PropertyMarker.defaultProps = {
  hoveredPropertyId: undefined,
};

export default memo(PropertyMarker);
