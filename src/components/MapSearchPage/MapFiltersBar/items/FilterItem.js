import React, { Fragment, useState, useCallback, useRef } from 'react';
import PropTypes from 'prop-types';
import styled from '@emotion/styled';
import { themeGet, display } from 'styled-system';
import { usePopper } from 'react-popper';
import { useClickAway } from 'react-use';
import { Flex, NakedButton, Text, Icon, Box } from '@qga/roo-ui/components';
import { mediaQuery } from 'lib/styledSystem';
import { useDataLayer } from 'hooks/useDataLayer';
import { useSelector } from 'react-redux';
import { getIsLoading } from 'store/search/searchSelectors';

const ItemButton = styled(NakedButton)`
  position: relative;
  padding: ${themeGet('space.1')} ${themeGet('space.2')};
  margin: ${themeGet('space.3')} ${themeGet('space.2')} ${themeGet('space.3')} 0;
  font-size: ${themeGet('fontSizes.base')};
  line-height: ${themeGet('lineHeights.normal')};
  border-radius: ${themeGet('radii.rounded')};
  border-color: ${(props) => (props.isOpen ? themeGet('colors.blue')(props) : themeGet('colors.greys.alto')(props))};
  background-color: ${(props) => (props.highlighted ? themeGet('colors.lightBlue')(props) : themeGet('colors.white')(props))};
  display: none;
  justify-content: center;
  white-space: nowrap;
  ${display};

  &:focus {
    outline-width: 0;
  }

  &:hover {
    border-color: ${themeGet('colors.blue')};
  }

  ${mediaQuery.minWidth.sm} {
    display: flex;
  }
`;

ItemButton.displayName = 'ItemButton';

const Footer = styled(Flex)`
  letter-spacing: 1.5px;
  position: sticky;
  bottom: 0;
  background-color: ${themeGet('colors.white')};
  justify-content: space-between;
  border-top: 2px solid ${themeGet('colors.greys.porcelain')};
  padding-top: ${themeGet('space.3')};
  margin-top: ${themeGet('space.3')};
`;

Footer.displayName = 'Footer';

const FooterButton = styled(NakedButton)`
  text-transform: uppercase;
  font-weight: ${themeGet('fontWeights.bold')};
  font-size: ${themeGet('fontSizes.sm')};
  cursor: pointer;
`;

const PopperContent = ({ children, close, buttonRef, ...rest }) => {
  const popperContentRef = useRef(null);

  useClickAway(popperContentRef, (e) => {
    if (!buttonRef.current.contains(e.target)) close();
  });

  return (
    <Box ref={popperContentRef} p={4} {...rest}>
      {children}
    </Box>
  );
};

PopperContent.propTypes = {
  children: PropTypes.node.isRequired,
  close: PropTypes.func.isRequired,
  buttonRef: PropTypes.shape({ current: PropTypes.instanceOf(PropTypes.elementType) }),
};

const FilterItem = ({ buttonText, isHighlighted, clearFilters, children }) => {
  const [isOpen, setOpen] = useState(false);
  const { emitInteractionEvent } = useDataLayer();
  const isLoading = useSelector(getIsLoading);
  const button = useRef(null);
  const [popperElement, setPopperElement] = useState(null);
  const { styles, attributes } = usePopper(button.current, popperElement);

  const openFilters = useCallback(() => {
    setOpen(true);
    emitInteractionEvent({ type: `${buttonText} Toggle`, value: 'Show Selected' });
  }, [buttonText, emitInteractionEvent]);

  const closeFilters = useCallback(() => {
    setOpen(false);
    emitInteractionEvent({ type: `${buttonText} Toggle`, value: 'Hide Selected' });
  }, [buttonText, emitInteractionEvent]);

  const toggleFilters = useCallback(() => {
    isOpen ? closeFilters() : openFilters();
  }, [isOpen, closeFilters, openFilters]);

  return (
    <Fragment>
      <ItemButton
        onClick={toggleFilters}
        highlighted={isHighlighted}
        data-testid="filter-button"
        isOpen={isOpen}
        ref={button}
        disabled={isLoading}
      >
        <Text>{buttonText}</Text>
        <Icon name={isOpen ? 'expandLess' : 'expandMore'} size={20} pt={1} />
      </ItemButton>

      {isOpen && (
        <Box
          aria-hidden="true"
          ref={setPopperElement}
          style={styles.popper}
          {...attributes.popper}
          data-testid="filter-popper"
          zIndex={100}
        >
          <PopperContent close={closeFilters} buttonRef={button} mt={6} bg="white">
            <Box minWidth={300} maxWidth={400} maxHeight={600} overflow="scroll">
              {children}
            </Box>
            <Footer>
              <FooterButton onClick={closeFilters} color="brand.primary" data-testid="done-button">
                done
              </FooterButton>
              <FooterButton onClick={clearFilters} color="greys.dusty" data-testid="clear-button">
                clear
              </FooterButton>
            </Footer>
          </PopperContent>
        </Box>
      )}
    </Fragment>
  );
};

FilterItem.propTypes = {
  buttonText: PropTypes.string.isRequired,
  isHighlighted: PropTypes.bool.isRequired,
  children: PropTypes.node.isRequired,
  clearFilters: PropTypes.func.isRequired,
};

export default FilterItem;
