import React, { useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { getQueryClassicRewards, getQueryDepositPay, getQueryFreeCancellation } from 'store/router/routerSelectors';
import { updateQuery as updateQueryAction } from 'store/search/searchActions';

import FilterItem from './FilterItem';
import PaymentPolicyFilter from 'components/Filters/Search/PaymentPolicyFilter';

const PaymentPolicyFilterItem = () => {
  const dispatch = useDispatch();
  const isDepositPay = useSelector(getQueryDepositPay);
  const isClassicRewards = useSelector(getQueryClassicRewards);
  const isFreeCancellation = useSelector(getQueryFreeCancellation);

  const updateQuery = useCallback(
    (query) => {
      dispatch(updateQueryAction(query));
    },
    [dispatch],
  );

  const clearFilters = useCallback(() => {
    updateQuery({ depositPay: undefined, freeCancellation: undefined, classicRewards: undefined });
  }, [updateQuery]);

  return (
    <FilterItem
      buttonText="Payment Options"
      isHighlighted={!!(isDepositPay || isFreeCancellation || isClassicRewards)}
      clearFilters={clearFilters}
    >
      <PaymentPolicyFilter onChange={updateQuery} />
    </FilterItem>
  );
};

export default PaymentPolicyFilterItem;
