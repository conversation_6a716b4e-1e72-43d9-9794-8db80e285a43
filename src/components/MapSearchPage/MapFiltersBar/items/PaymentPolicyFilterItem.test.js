import React from 'react';
import PaymentPolicyFilterItem from './PaymentPolicyFilterItem';
import FilterItem from './FilterItem';
import { mountUtils } from 'test-utils';
import { getQueryDepositPay, getQueryFreeCancellation, getQueryClassicRewards } from 'store/router/routerSelectors';
import { updateQuery } from 'store/search/searchActions';

jest.mock(
  './FilterItem',
  () =>
    ({ children }) =>
      children,
);
jest.mock('store/router/routerSelectors');

mountUtils.mockComponent('PaymentPolicyFilter');

const render = () => mountUtils(<PaymentPolicyFilterItem />, { decorators: { store: true } });

describe('FilterItem', () => {
  it('renders with the expected props', () => {
    const { find } = render();
    expect(find(FilterItem)).toHaveProp({
      buttonText: 'Payment Options',
      isHighlighted: false,
    });
  });

  describe('when depositPay is true', () => {
    it('renders as highlighted', () => {
      getQueryDepositPay.mockReturnValue(true);

      const { find } = render();
      expect(find(FilterItem)).toHaveProp({
        isHighlighted: true,
      });
    });
  });

  describe('when freeCancellation is true', () => {
    it('renders as highlighted', () => {
      getQueryFreeCancellation.mockReturnValue(true);

      const { find } = render();
      expect(find(FilterItem)).toHaveProp({
        isHighlighted: true,
      });
    });
  });

  describe('when maxPaymentPolicy is set', () => {
    it('renders as highlighted', () => {
      getQueryClassicRewards.mockReturnValue(true);

      const { find } = render();
      expect(find(FilterItem)).toHaveProp({
        isHighlighted: true,
      });
    });
  });

  describe('calling clearFilters', () => {
    it('dispatches updateQuery action with expected query', () => {
      const { find, decorators } = render();
      find(FilterItem).props().clearFilters();
      expect(decorators.store.dispatch).toHaveBeenCalledWith(
        updateQuery({ depositPay: undefined, freeCancellation: undefined, classicRewards: undefined }),
      );
    });
  });

  describe('PaymentPolicyFilter', () => {
    it('calls updateQuery action onChange', () => {
      const { find, decorators } = render();
      const query = { foo: 'bar' };
      find('PaymentPolicyFilter').props().onChange(query);
      expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery(query));
    });
  });
});
