import React from 'react';
import { act } from 'react-dom/test-utils';
import GoogleMapWithUI from './GoogleMapWithUI';
import { mountUtils } from 'test-utils';
import { getIsLoading } from 'store/search/searchSelectors';

mountUtils.mockComponent('MapStateIndicator');
mountUtils.mockComponent('GoogleMap');
mountUtils.mockComponent('FixedSearchControls');
jest.mock('store/search/searchSelectors');

const render = (props) =>
  mountUtils(
    <GoogleMapWithUI {...props}>
      <div data-testid="maps-child" />
    </GoogleMapWithUI>,
    { decorators: { store: true, theme: true, router: true } },
  );

describe('<GoogleMapWithUI />', () => {
  beforeEach(() => {
    getIsLoading.mockReturnValue(false);
  });

  it('renders <GoogleMap />', () => {
    const { find } = render();
    expect(find('GoogleMap')).toExist();
    expect(find('MapStateIndicator')).not.toExist();
    expect(find('FixedSearchControls')).not.toExist();
  });

  describe('<MapStateIndicator />', () => {
    const renderMapStateIndicator = (props) => {
      const { wrapper, find } = render(props);
      act(() => {
        find('GoogleMap').prop('onMapTilesLoaded')(true);
      });
      wrapper.update();
      return { wrapper, find };
    };

    it('renders the indicator with the correct props', () => {
      const { find } = renderMapStateIndicator();
      expect(find('MapStateIndicator')).toHaveProp({ isMapBoundsDirty: false, isLoading: false });
    });

    it('updates the indicator when the map is moved', () => {
      const { find, wrapper } = renderMapStateIndicator();
      expect(find('MapStateIndicator')).toHaveProp({ isMapBoundsDirty: false });
      act(() => {
        find('GoogleMap').prop('onMapMoved')();
      });
      wrapper.update();
      expect(find('MapStateIndicator')).toHaveProp({ isMapBoundsDirty: true });
    });

    it('updates the indicator when the map is required', () => {
      const { find, wrapper } = renderMapStateIndicator();
      act(() => {
        find('GoogleMap').prop('onMapMoved')();
      });
      wrapper.update();
      expect(find('MapStateIndicator')).toHaveProp({ isMapBoundsDirty: true });

      act(() => {
        find('MapStateIndicator').prop('onClick')();
      });
      wrapper.update();
      expect(find('MapStateIndicator')).toHaveProp({ isMapBoundsDirty: false });
    });
  });

  describe('<FixedSearchControls />', () => {
    const renderMapStateIndicator = (props) => {
      const { wrapper, find } = render(props);
      act(() => {
        find('GoogleMap').prop('onMapTilesLoaded')(true);
      });
      wrapper.update();
      return { wrapper, find };
    };
    it('are rendered with the right prop', () => {
      const { find } = renderMapStateIndicator();

      expect(find('FixedSearchControls')).toExist();
    });
  });
});
