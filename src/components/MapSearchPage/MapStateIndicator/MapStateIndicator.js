import React from 'react';
import PropTypes from 'prop-types';
import { themeGet } from 'styled-system';
import styled from '@emotion/styled';
import { Text, Flex, Box, NakedButton, LoadingIndicator } from '@qga/roo-ui/components';

const Wrapper = styled(Flex)`
  margin-bottom: ${themeGet('space.5')};
  flex-direction: column;
  justify-content: center;
  min-height: 40px;
  opacity: 0;
  {// eslint-disable-next-line prettier/prettier}
  transition:
    transform 300ms ease,
    opacity 300ms ease;
  &[data-show] {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
`;

const RedoSearchButton = styled(NakedButton)`
  height: 100%;
  min-height: 48px;
  border-radius: ${themeGet('radii.rounded')};
  border: 1px solid ${themeGet('colors.brand.primary')};
  padding: 0 ${themeGet('space.6')};
  background-color: ${themeGet('colors.white')};
  color: ${themeGet('colors.brand.primary')};
`;

const MapStateIndicator = ({ isMapBoundsDirty, isLoading, onClick }) => (
  <Wrapper data-testid="wrapper" data-show={isLoading || isMapBoundsDirty ? '' : undefined}>
    {isLoading && (
      <Box px={6}>
        <LoadingIndicator size={12} height="100%" />
      </Box>
    )}

    {!isLoading && isMapBoundsDirty && (
      <Flex>
        <RedoSearchButton onClick={onClick} data-testid="search_again">
          <Text fontSize="sm">Search this area</Text>
        </RedoSearchButton>
      </Flex>
    )}
  </Wrapper>
);

MapStateIndicator.propTypes = {
  isMapBoundsDirty: PropTypes.bool.isRequired,
  isLoading: PropTypes.bool.isRequired,
  onClick: PropTypes.func.isRequired,
};

export default MapStateIndicator;
