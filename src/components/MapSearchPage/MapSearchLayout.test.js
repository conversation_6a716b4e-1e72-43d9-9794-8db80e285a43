import React from 'react';
import MapSearchLayout from './MapSearchLayout';
import { DESKTOP_MAP_SEARCH_LIMIT, MOBILE_MAP_SEARCH_LIMIT } from 'config';
import { mountUtils } from 'test-utils';
import { useBreakpoints } from 'hooks/useBreakpoints';

jest.mock('hooks/useBreakpoints');
jest.mock('hooks/useListSearchGa4Event');

mountUtils.mockComponent('MapSearchHelmet');
mountUtils.mockComponent('ResultsMap');
mountUtils.mockComponent('SearchResultFetcher');

const decorators = { store: true, theme: true };
const render = () => mountUtils(<MapSearchLayout />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
  useBreakpoints.mockReturnValue({
    isLessThanBreakpoint: () => true,
  });
});

it('renders <MapSearchHelmet />', () => {
  const { find } = render();

  expect(find('MapSearchHelmet')).toExist();
});

it('renders <ResultsMap />', () => {
  const { find } = render();

  expect(find('ResultsMap')).toExist();
});

describe('mobile or tablet users', () => {
  it('renders <SearchResultFetcher /> with the mobile search limit', () => {
    const { find } = render();

    expect(find('SearchResultFetcher')).toHaveProp({
      limit: MOBILE_MAP_SEARCH_LIMIT,
    });
  });
});

describe('desktop users', () => {
  beforeEach(() => {
    useBreakpoints.mockReturnValue({
      isLessThanBreakpoint: () => false,
    });
  });

  it('renders <SearchResultFetcher /> with the desktop search limit', () => {
    const { find } = render();

    expect(find('SearchResultFetcher')).toHaveProp({
      limit: DESKTOP_MAP_SEARCH_LIMIT,
    });
  });
});
