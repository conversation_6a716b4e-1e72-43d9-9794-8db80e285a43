import React from 'react';
import { mountUtils } from 'test-utils';
import Header from './Header';

const transitionState = 'entered';
const cancelModal = jest.fn();

describe('with no title', () => {
  it('does not render title', () => {
    const wrapper = mountUtils(<Header transitionState={transitionState} cancelModal={cancelModal} />, {
      decorators: { store: true, theme: true },
    });
    expect(wrapper.find('div[data-testid="modal-title"]')).toHaveLength(0);
  });
});

describe('with title', () => {
  it('renders the title', () => {
    const title = 'some title';
    const wrapper = mountUtils(<Header title={title} transitionState={transitionState} cancelModal={cancelModal} />);

    expect(wrapper.find('div[data-testid="modal-title"]')).toHaveLength(1);
    expect(wrapper.find('div[data-testid="modal-title"]').text()).toEqual(title);
  });
});
