import React from 'react';
import { act } from 'react-dom/test-utils';
import { mountUtils, runAllTimers } from 'test-utils';
import ResponsiveModal from './ResponsiveModal';
import { isIOSDevice, fixBodyScroll, unfixBodyScroll, isPhone } from 'lib/browser';
import { getIsMobileApp } from 'store/ui/uiSelectors';

jest.useFakeTimers();
jest.mock('store/ui/uiSelectors');
jest.mock('lib/browser', () => ({
  isIOSDevice: jest.fn(),
  isPhone: jest.fn(),
  fixBodyScroll: jest.fn(),
  unfixBodyScroll: jest.fn(),
}));

window.scrollBy = jest.fn();

let submitCallback;
let blurCallback;
let cancelCallback;
let wrapper;

const decorators = { store: true, theme: true };

beforeEach(() => {
  jest.clearAllMocks();
  getIsMobileApp.mockReturnValue(false);
  isIOSDevice.mockReturnValue(false);
  isPhone.mockReturnValue(false);
  submitCallback = jest.fn();
  blurCallback = jest.fn();
  cancelCallback = jest.fn();
  wrapper = mountUtils(
    <ResponsiveModal onSubmit={submitCallback} onBlur={blurCallback} onCancel={cancelCallback}>
      {({ isOpen, openModal, cancelModal, submitModal, blurModal }) => (
        <div>
          <p data-testid="modal-is-open">{isOpen ? 'open' : 'closed'}</p>
          <button onClick={openModal} data-testid="open-modal-from-child" />
          <button onClick={submitModal} data-testid="submit-modal-from-child" />
          <button onClick={blurModal} data-testid="blur-modal-from-child" />
          <button onClick={cancelModal} data-testid="cancel-modal-from-child" />
        </div>
      )}
    </ResponsiveModal>,
    { decorators },
  ).wrapper;
});

it('initialises in the closed state', () => {
  expect(wrapper.find('ResponsiveContainer').props().isOpen).toBeFalsy();
  expect(wrapper.find('[data-testid="modal-is-open"]').text()).toEqual('closed');
});

it('does not call the blur callback when the instance is cancelled', () => {
  wrapper.find('[data-testid="open-modal-from-child"]').simulate('click');
  let subject = wrapper.find('Header').props();
  act(() => {
    subject.cancelModal();
    jest.runAllTimers();
  });
  wrapper.update();
  expect(blurCallback).not.toHaveBeenCalled();
});

it('opens when clicking on open action from child component', () => {
  wrapper.find('[data-testid="open-modal-from-child"]').simulate('click');
  expect(wrapper.find('ResponsiveContainer').props().transitionState).toEqual('entering');
  expect(wrapper.find('[data-testid="modal-is-open"]').text()).toEqual('open');
});

it('closes when clicking on submit action from child component', () => {
  wrapper.find('[data-testid="open-modal-from-child"]').simulate('click');
  wrapper.find('[data-testid="submit-modal-from-child"]').simulate('click');
  runAllTimers();
  wrapper.update();
  expect(wrapper.find('[data-testid="modal-is-open"]').text()).toEqual('closed');
});

it('calls the submit callback when clicking on submit action from child component', async () => {
  wrapper.find('[data-testid="open-modal-from-child"]').simulate('click');
  wrapper.find('[data-testid="submit-modal-from-child"]').simulate('click');
  runAllTimers();
  await flushPromises();
  expect(submitCallback).toHaveBeenCalledTimes(1);
});

it('closes when clicking on blur action from child component', () => {
  wrapper.find('[data-testid="open-modal-from-child"]').simulate('click');
  wrapper.find('[data-testid="cancel-modal-from-child"]').simulate('click');
  runAllTimers();
  wrapper.update();
  expect(wrapper.find('[data-testid="modal-is-open"]').text()).toEqual('closed');
});

it('calls the blur callback when clicking on blur action from child component', async () => {
  wrapper.find('[data-testid="open-modal-from-child"]').simulate('click');
  wrapper.find('[data-testid="blur-modal-from-child"]').simulate('click');
  runAllTimers();
  await flushPromises();
  expect(blurCallback).toHaveBeenCalledTimes(1);
});

it('closes when clicking on cancel action from child component', () => {
  wrapper.find('[data-testid="open-modal-from-child"]').simulate('click');
  wrapper.find('[data-testid="cancel-modal-from-child"]').simulate('click');
  runAllTimers();
  wrapper.update();
  expect(wrapper.find('[data-testid="modal-is-open"]').text()).toEqual('closed');
});

it('calls the cancel callback when clicking on cancel action from child component', async () => {
  wrapper.find('[data-testid="open-modal-from-child"]').simulate('click');
  wrapper.find('[data-testid="cancel-modal-from-child"]').simulate('click');
  runAllTimers();
  await flushPromises();
  expect(cancelCallback).toHaveBeenCalledTimes(1);
});

it('hides the header bar on non-mobile devices', () => {
  wrapper.find('[data-testid="open-modal-from-child"]').simulate('click');
  expect(wrapper.find('Hide').first().props().sm).toBeTruthy();
  expect(wrapper.find('Hide').first().props().md).toBeTruthy();
  expect(wrapper.find('Hide').first().props().lg).toBeTruthy();
});

describe('when on an iphone', () => {
  it('applies window scroll fix after opening', () => {
    isIOSDevice.mockReturnValue(true);
    isPhone.mockReturnValue(true);
    wrapper.find('[data-testid="open-modal-from-child"]').simulate('click');
    runAllTimers();
    wrapper.update();
    expect(window.scrollBy).toHaveBeenCalledWith(0, -100);
  });
});

describe('when on an iPad', () => {
  it('does not apply window scroll fix after opening', () => {
    isIOSDevice.mockReturnValue(true);
    wrapper.find('[data-testid="open-modal-from-child"]').simulate('click');
    runAllTimers();
    wrapper.update();
    expect(window.scrollBy).not.toHaveBeenCalled();
  });
});

describe('when not on an iphone', () => {
  it('does not apply window scroll fix after opening', () => {
    isPhone.mockReturnValue(true);
    wrapper.find('[data-testid="open-modal-from-child"]').simulate('click');
    runAllTimers();
    wrapper.update();
    expect(window.scrollBy).not.toHaveBeenCalled();
  });
});

describe('when not on a phone', () => {
  it('does not fix body scroll on open', () => {
    wrapper.find('[data-testid="open-modal-from-child"]').simulate('click');
    runAllTimers();
    wrapper.update();
    expect(fixBodyScroll).not.toHaveBeenCalled();
  });
});

describe('when on a phone', () => {
  beforeEach(() => {
    isPhone.mockImplementation(() => true);
  });

  afterEach(() => {
    isPhone.mockClear();
  });

  it('fixes body scroll on open', () => {
    wrapper.find('[data-testid="open-modal-from-child"]').simulate('click');
    runAllTimers();
    wrapper.update();
    expect(fixBodyScroll).toHaveBeenCalled();
  });

  it('unfixes body scroll on close', () => {
    wrapper.find('[data-testid="open-modal-from-child"]').simulate('click');
    wrapper.find('[data-testid="blur-modal-from-child"]').simulate('click');
    runAllTimers();
    wrapper.update();
    expect(unfixBodyScroll).toHaveBeenCalled();
  });

  it('unfixes body scroll on unmount', () => {
    wrapper.find('[data-testid="open-modal-from-child"]').simulate('click');
    wrapper.unmount();
    runAllTimers();
    expect(unfixBodyScroll).toHaveBeenCalled();
  });
});

describe('keyboard behaviour', () => {
  const ESC = 27;

  let outerNode;
  let wrapper;
  let cancelCallback;

  beforeEach(() => {
    outerNode = document.createElement('div');
    document.body.appendChild(outerNode);

    cancelCallback = jest.fn();

    wrapper = mountUtils(
      <ResponsiveModal onSubmit={submitCallback} onBlur={blurCallback} onCancel={cancelCallback}>
        {({ isOpen, openModal }) => (
          <div>
            <p data-testid="modal-is-open">{isOpen ? 'open' : 'closed'}</p>
            <button onClick={openModal} data-testid="open-modal-from-child" />
          </div>
        )}
      </ResponsiveModal>,
      { decorators },
    ).wrapper;
  });

  it('closes when ESC is pressed', async () => {
    wrapper.find('[data-testid="open-modal-from-child"]').simulate('click');
    act(() => {
      document.dispatchEvent(new KeyboardEvent('keydown', { keyCode: ESC, which: ESC, bubbles: true }));
      document.dispatchEvent(new KeyboardEvent('keyup', { keyCode: ESC, which: ESC, bubbles: true }));
      jest.runAllTimers();
    });

    wrapper.update();
    await flushPromises();

    expect(cancelCallback).toHaveBeenCalled();
  });
});

describe('Header', () => {
  it('closes when clicking on the close button, delaying final close state until animation is complete', () => {
    wrapper.find('[data-testid="open-modal-from-child"]').simulate('click');
    expect(wrapper.find('StyledNakedButton[data-testid="close-modal"]')).toHaveLength(1);
    wrapper.find('StyledNakedButton[data-testid="close-modal"]').simulate('click');
    runAllTimers();
    wrapper.update();
    expect(wrapper.find('[data-testid="modal-is-open"]').text()).toEqual('closed');
  });

  it('calls the onCancel callback when clicking the close button', async () => {
    wrapper.find('[data-testid="open-modal-from-child"]').simulate('click');
    wrapper.find('StyledNakedButton[data-testid="close-modal"]').simulate('click');
    runAllTimers();
    await flushPromises();
    expect(cancelCallback).toHaveBeenCalledTimes(1);
  });
});

describe('Footer', () => {
  const renderModalForFooter = ({ submitButtonText } = {}) =>
    mountUtils(
      <ResponsiveModal onSubmit={submitCallback} submitButtonText={submitButtonText}>
        {({ openModal }) => <button onClick={openModal} data-testid="open-modal-from-child" />}
      </ResponsiveModal>,
      { decorators },
    );

  describe('when onSubmit is null', () => {
    it('does not render', () => {
      submitCallback = undefined;
      const { wrapper } = renderModalForFooter();

      wrapper.find('[data-testid="open-modal-from-child"]').simulate('click');
      expect(wrapper.find('[data-testid="apply-button"]')).not.toExist();
    });

    describe('and submitButtonText is not null', () => {
      it('renders the component', () => {
        const { wrapper } = renderModalForFooter({ submitButtonText: 'Submit' });
        wrapper.find('[data-testid="open-modal-from-child"]').simulate('click');
        expect(wrapper.find('[data-testid="apply-button"]').exists()).toEqual(true);
      });
    });
  });

  it('closes the modal clicking the apply button', () => {
    wrapper.find('[data-testid="open-modal-from-child"]').simulate('click');
    expect(wrapper.find('StyledNakedButton[data-testid="close-modal"]')).toHaveLength(1);
    wrapper.find('button[data-testid="apply-button"]').simulate('click');
    runAllTimers();
    wrapper.update();
    expect(wrapper.find('[data-testid="modal-is-open"]').text()).toEqual('closed');
  });

  it('calls the onSubmit callback when clicking the apply button', async () => {
    wrapper.find('[data-testid="open-modal-from-child"]').simulate('click');
    wrapper.find('button[data-testid="apply-button"]').simulate('click');
    runAllTimers();
    await flushPromises();
    expect(submitCallback).toHaveBeenCalledTimes(1);
  });
});
