import React from 'react';
import { mocked, mountUtils } from 'test-utils';
import HomePageSearchPanel from './HomePageSearchPanel';
import { useDataLayer } from 'hooks/useDataLayer';
import { getHomePageBanner } from 'store/homePage/homePageSelectors';
import { useRouter } from 'next/router';
import { getIsMobileApp } from 'store/ui/uiSelectors';
// import { iconAirbnb } from '@qga/roo-ui/assets';

jest.mock('store/homePage/homePageSelectors');
jest.mock('hooks/useDataLayer');
jest.mock('store/ui/uiSelectors');
const mockRouter = {
  push: jest.fn(),
};
jest.mock('next/router', () => ({
  useRouter: jest.fn(() => mockRouter),
}));

mountUtils.mockComponent('HotelsTab');
mountUtils.mockComponent('AirbnbTab');

const mockBanner = {
  asset: {
    _ref: 'image-4bf004ffa6b38474026debfb71a8ef64571b11b7-1000x588-jpg',
    _type: 'reference',
  },
  caption: 'A picture of a crystal blue pool, surrounded by recliner chairs',
};

const emitInteractionEvent = jest.fn();

const decorators = { store: true, theme: true };
const render = () => mountUtils(<HomePageSearchPanel />, { decorators });

describe('<HomePageSearchPanel />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
    mocked(getHomePageBanner).mockReturnValue(mockBanner);
    mocked(useRouter).mockReturnValue(mockRouter);
    mocked(getIsMobileApp).mockReturnValue(false);
  });

  it('renders the background image', () => {
    const { find } = render();
    expect(find('HeroImage')).toHaveProp({
      src: 'https://cdn.sanity.io/images/abcdefghi/qh-test/4bf004ffa6b38474026debfb71a8ef64571b11b7-1000x588.jpg?w=2560&q=80&auto=format',
    });
  });

  it('renders the background image with the correct alt test', () => {
    const { find } = render();
    expect(find('HeroImage')).toHaveProp({
      alt: 'Book hotels and accommodation in Australia',
    });
  });

  // it('renders the default background image if not banner is present in redux', () => {
  //   mocked(getHomePageBanner).mockReturnValue(null);
  //   const { find } = render();
  //   expect(find('HeroImage')).toHaveProp({
  //     src: '/hotels/images/placeholder-desktop-image.jpg',
  //   });
  // });

  it('renders the tab list', () => {
    const { findByTestId } = render();
    expect(findByTestId('hotels-tab')).toHaveText('Hotels');
    expect(findByTestId('airbnb-tab')).toHaveText('Airbnb');
  });

  it('renders the hotels tab', () => {
    const { find } = render();
    expect(find('HotelsTab')).toExist();
  });

  // it('renders the airbnb tab', () => {
  //   const { find } = render();
  //   expect(find('AirbnbTab')).toExist();
  // });

  it('tracks when the hotels tab is clicked', () => {
    const { findByTestId } = render();
    findByTestId('hotels-tab').last().simulate('click');
    expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Search', value: 'Hotels Selected' });
  });

  it('tracks when the airbnb tab is clicked', () => {
    const { findByTestId } = render();
    findByTestId('airbnb-tab').last().simulate('click');
    expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Search', value: 'Hotels Airbnb Selected' });
  });
});
