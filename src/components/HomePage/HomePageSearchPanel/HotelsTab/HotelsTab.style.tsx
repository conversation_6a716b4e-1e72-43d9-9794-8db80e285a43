import { Flex, Button } from '@qga/roo-ui/components';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { FieldError } from '@qga/components';
import { rem } from 'polished';
import { mediaQuery } from 'lib/styledSystem';

export const InputErrorField = styled(FieldError)`
  padding: ${themeGet('space.2')} ${themeGet('space.4')};
  margin-top: ${themeGet('space.2')};

  color: ${themeGet('colors.greys.charcoal')};
  font-family: ${themeGet('fontFamily')};
  font-size: ${themeGet('fontSizes.sm')} !important;
  line-height: 1.5;
`;

export const PayWithWrapper = styled(Flex)`
  align-items: flex-end;
  flex-grow: 1;
  align-items: center;
  padding-right: 0;

  ${mediaQuery.minWidth.sm} {
    padding-right: ${rem('52px')};
  }

  ${mediaQuery.minWidth.md} {
    padding-right: 0;
  }
`;

export const SubmitButton = styled(Button)`
  &:hover {
    color: ${themeGet('colors.white')};
  }
`;
