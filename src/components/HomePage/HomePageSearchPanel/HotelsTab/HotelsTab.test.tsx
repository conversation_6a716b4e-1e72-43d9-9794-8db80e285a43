import React from 'react';
import { mocked, mountUtils } from 'test-utils';
import HotelsTab from './HotelsTab';
import { useDataLayer } from 'hooks/useDataLayer';
import { act } from 'react-dom/test-utils';
import { useRouter } from 'next/router';
import { registerGclid } from '../../sessionStorage';
import { isIOSDevice, isPhone } from 'lib/browser';
import { getIsMobileApp } from 'store/ui/uiSelectors';
import { updateQuery } from 'store/search/searchActions';

jest.mock('hooks/useDataLayer');
jest.mock('next/router');
jest.mock('components/HomePage/sessionStorage');
jest.mock('store/ui/uiSelectors');
jest.mock('lib/browser', () => ({
  isIOSDevice: jest.fn(),
  isPhone: jest.fn(),
}));
jest.mock('next/router', () => ({
  useRouter: jest.fn(() => ({
    isReady: true,
    push: jest.fn(),
    query: jest.fn(),
  })),
}));

mountUtils.mockComponent('LocationAutocompleter');
mountUtils.mockComponent('AvailabilityDatePicker');
mountUtils.mockComponent('OccupantPicker');
mountUtils.mockComponent('PayWithButtonGroup');
mountUtils.mockComponent('PayWithToggleMessage');

const location = 'Melbourne, VIC, Australia';
const checkInDate = '2022-05-29';
const checkOutDate = '2022-05-31';
const propertyName = 'the Hotel Windsor';

const emitInteractionEvent = jest.fn();

const updateLocationToMel = (find, wrapper) => {
  act(() => {
    find('LocationAutocompleter').prop('updateQuery')({ location: location });
  });
  wrapper.update();
};

const updateDates = (find, wrapper) => {
  act(() => {
    find('AvailabilityDatePicker').prop('updateQuery')({ checkIn: checkInDate, checkOut: checkOutDate });
  });
  wrapper.update();
};

const mockRouterQuery = {
  adults: 2,
  children: 0,
  infants: 0,
};

const decorators = { store: true, router: true, helmet: true };
const render = () => mountUtils(<HotelsTab />, { decorators });

describe('<HotelsTab />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (getIsMobileApp as jest.Mock).mockReturnValue(false);
    (isIOSDevice as jest.Mock).mockReturnValue(false);
    (isPhone as jest.Mock).mockReturnValue(false);
    mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });

    (useRouter as jest.Mock).mockReturnValue({
      isReady: true,
      push: jest.fn(),
      query: mockRouterQuery,
    });
  });

  it('renders the text heading', () => {
    const { find } = render();
    expect(find('Text').first()).toHaveText('Book Hotels & Accommodation');
  });

  it('renders the LocationAutocompleter', () => {
    const { find } = render();
    expect(find('LocationAutocompleter')).toExist();
  });

  it('renders the AvailabilityDatePicker', () => {
    const { find } = render();
    expect(find('AvailabilityDatePicker')).toExist();
  });

  it('renders the OccupantPicker', () => {
    const { find } = render();
    expect(find('LocationAutocompleter')).toExist();
  });

  it('renders the submit button', () => {
    const { find } = render();
    expect(find('SubmitButton')).toHaveProp({ to: undefined });
  });

  it('updates the search URL when the location changes to a geographic location', () => {
    const { find, wrapper } = render();

    updateLocationToMel(find, wrapper);
    updateDates(find, wrapper);

    expect(find('SubmitButton')).toHaveProp({
      to: '/search/list?adults=2&checkIn=2022-05-29&checkOut=2022-05-31&children=0&infants=0&location=Melbourne%2C%20VIC%2C%20Australia',
    });
    expect(find('LocationAutocompleter')).toHaveProp({ locationName: location });
  });

  it('updates the search URL when the location changes to a hotel name', () => {
    const { find, wrapper } = render();

    act(() => {
      find('LocationAutocompleter').prop('routeToProperty')({ propertyName: propertyName, id: 8421 });
    });
    wrapper.update();

    updateDates(find, wrapper);

    expect(find('SubmitButton')).toHaveProp({
      to: '/properties/8421?adults=2&checkIn=2022-05-29&checkOut=2022-05-31&children=0&infants=0',
    });
    expect(find('LocationAutocompleter')).toHaveProp({ locationName: propertyName });
  });

  it('updates the search URL when the check in and check out dates are set', () => {
    const { find, wrapper } = render();

    updateLocationToMel(find, wrapper);
    updateDates(find, wrapper);

    expect(find('SubmitButton')).toHaveProp({
      to: '/search/list?adults=2&checkIn=2022-05-29&checkOut=2022-05-31&children=0&infants=0&location=Melbourne%2C%20VIC%2C%20Australia',
    });
  });

  it('updates the search URL when the occupant picker is set', () => {
    const { find, wrapper } = render();

    updateLocationToMel(find, wrapper);
    updateDates(find, wrapper);

    act(() => {
      find('OccupantPicker').prop('updateQuery')({ children: 2 });
    });
    wrapper.update();

    expect(find('SubmitButton')).toHaveProp({
      to: '/search/list?adults=2&checkIn=2022-05-29&checkOut=2022-05-31&children=2&infants=0&location=Melbourne%2C%20VIC%2C%20Australia',
    });
  });

  // describe('when gclid param is present in the url', () => {
  //   beforeEach(() => {
  //     mocked(useRouter).mockReturnValue({
  //       query: { gclid: '1234' },
  //     });
  //   });

  //   it('appends it to the search URL', () => {
  //     const { find, wrapper } = render();

  //     updateLocationToMel(find, wrapper);
  //     updateDates(find, wrapper);

  //     expect(find('SubmitButton')).toHaveProp({
  //       to:
  //         '/search/list?adults=2&checkIn=2022-05-29&checkOut=2022-05-31&children=0&infants=0&location=Melbourne%2C%20VIC%2C%20Australia&gclid=1234',
  //     });
  //   });
  // });

  describe('errors', () => {
    describe('when a location and a date range are selected', () => {
      it('does NOT render any error', () => {
        const { find, findByTestId } = render();

        act(() => {
          find('LocationAutocompleter').prop('updateQuery')({ location: location });
          find('AvailabilityDatePicker').prop('updateQuery')({ checkIn: checkInDate, checkOut: checkOutDate });
          find('SubmitButton').simulate('click');
        });

        expect(findByTestId('dates-missing-error')).not.toExist();
        expect(findByTestId('missing-location-error')).not.toExist();
      });
    });

    describe('when the submitButton is clicked without entering any location or date', () => {
      it('renders both the dates and location missing errors', () => {
        const { find, findByTestId, wrapper } = render();

        act(() => {
          find('SubmitButton').simulate('click');
        });
        wrapper.update();

        expect(findByTestId('dates-missing-error')).not.toExist();
        expect(findByTestId('missing-location-error')).toExist();
      });

      it('when a location is submitted it removes the relative error', () => {
        const { find, findByTestId, wrapper } = render();

        act(() => {
          find('SubmitButton').simulate('click');
        });
        wrapper.update();

        expect(findByTestId('dates-missing-error')).not.toExist();
        expect(findByTestId('missing-location-error')).toExist();

        act(() => {
          find('LocationAutocompleter').prop('updateQuery')({ location: location });
        });
        wrapper.update();

        expect(findByTestId('dates-missing-error')).not.toExist();
        expect(findByTestId('missing-location-error')).not.toExist();
      });

      it.skip('when a date range is submitted it removes the relative error', () => {
        const { find, findByTestId, wrapper } = render();

        act(() => {
          find('SubmitButton').simulate('click');
        });
        wrapper.update();

        expect(findByTestId('dates-missing-error')).toExist();
        expect(findByTestId('missing-location-error')).toExist();

        act(() => {
          find('AvailabilityDatePicker').prop('updateQuery')({ checkIn: checkInDate, checkOut: checkOutDate });
        });
        wrapper.update();

        expect(findByTestId('dates-missing-error')).not.toExist();
        expect(findByTestId('missing-location-error')).toExist();
      });
    });

    it.skip('renders only the dates missing error if a location is selected before searching', () => {
      const { find, findByTestId, wrapper } = render();

      act(() => {
        find('LocationAutocompleter').prop('updateQuery')({ location: location });
      });
      wrapper.update();

      act(() => {
        find('SubmitButton').simulate('click');
      });
      wrapper.update();

      expect(findByTestId('dates-missing-error')).toExist();
      expect(findByTestId('missing-location-error')).not.toExist();
    });

    it('renders only the location missing error if a date range is selected before searching', () => {
      const { find, findByTestId, wrapper } = render();

      act(() => {
        find('AvailabilityDatePicker').prop('updateQuery')({ checkIn: checkInDate, checkOut: checkOutDate });
      });
      wrapper.update();

      act(() => {
        find('SubmitButton').simulate('click');
      });
      wrapper.update();

      expect(findByTestId('dates-missing-error')).not.toExist();
      expect(findByTestId('missing-location-error')).toExist();
    });
  });

  it('tracks when the user updates the location search', () => {
    const { find, wrapper } = render();

    act(() => {
      find('LocationAutocompleter').prop('updateQuery')({ location: location });
    });
    wrapper.update();

    expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Location Search', value: 'Melbourne, VIC, Australia Selected' });
  });

  it('tracks when the user updates the property search', () => {
    const { find, wrapper } = render();

    act(() => {
      find('LocationAutocompleter').prop('routeToProperty')({ propertyName: 'Hilton Sydney, NSW, Australia' });
    });
    wrapper.update();

    expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Property Search', value: 'Hilton Sydney, NSW, Australia Selected' });
  });

  it('tracks when the user updates the checkin and checkout dates', () => {
    const { find, wrapper } = render();

    act(() => {
      find('AvailabilityDatePicker').prop('updateQuery')({ checkIn: checkInDate, checkOut: checkOutDate });
    });
    wrapper.update();

    expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Date Calendar', value: 'Checkin Date Selected' });
    expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Date Calendar', value: 'Checkout Date Selected' });
  });

  it('tracks when the user updates the guest count', () => {
    const { find, wrapper } = render();

    act(() => {
      find('OccupantPicker').prop('updateQuery')({ adults: 1, children: 2, infants: 3 });
    });
    wrapper.update();

    expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Guests Dropdown', value: 'Guests Adults 1 Selected' });
    expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Guests Dropdown', value: 'Guests Children 2 Selected' });
    expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Guests Dropdown', value: 'Guests Infants 3 Selected' });
  });

  describe('when the gclid is present', () => {
    beforeEach(() => {
      (useRouter as jest.Mock).mockReturnValue({
        query: { adults: 2, gclid: '1234' },
      });
    });

    it('registers it to the sessionStorage', () => {
      render();

      expect(registerGclid).toHaveBeenCalled();
    });
  });

  describe('when a search is submitted', () => {
    it('dispatches an updateQuery action', () => {
      const { find, wrapper, decorators } = render();
      act(() => {
        find('LocationAutocompleter').prop('updateQuery')({ location: location });
      });
      wrapper.update();

      act(() => {
        find('AvailabilityDatePicker').prop('updateQuery')({ checkIn: checkInDate, checkOut: checkOutDate });
      });
      wrapper.update();

      act(() => {
        find('SubmitButton').simulate('click');
      });
      wrapper.update();

      expect(decorators.store.dispatch).toHaveBeenCalledWith(
        updateQuery({
          location: location,
          checkIn: checkInDate,
          checkOut: checkOutDate,
          adults: 2,
          children: 0,
          infants: 0,
          id: null,
          isPropertySearch: false,
        }),
      );
    });
  });
});
