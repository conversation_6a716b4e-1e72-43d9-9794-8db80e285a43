import React from 'react';
import { mocked, mountUtils } from 'test-utils';
import { useDataLayer } from 'hooks/useDataLayer';
import TermsAndConditions from './TermsAndConditions';
import { getIsMobileApp } from 'store/ui/uiSelectors';

jest.mock('hooks/useDataLayer');
jest.mock('store/ui/uiSelectors');

mountUtils.mockComponent('Markdown');

const terms = 'Some T&Cs';
const termsShort = 'Terms and conditions';

const emitInteractionEvent = jest.fn();

const decorators = { theme: true, store: true };
const render = (props) => mountUtils(<TermsAndConditions {...props} />, { decorators });

describe('TermsAndConditions component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mocked(getIsMobileApp).mockReturnValue(false);
    mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
  });

  describe('when no props are provided', () => {
    it('does NOT display the terms and conditions text', () => {
      const { findByTestId } = render({});
      // expect(findByTestId('disclaimer-copy')).not.toExist();
      expect(findByTestId('terms-and-conditions-button')).not.toExist();
    });
  });

  describe('when the terms are provided', () => {
    it('the conditions CTA is rendered', () => {
      const { findByTestId } = render({ terms, termsShort });
      expect(findByTestId('disclaimer-copy')).toExist();
      expect(findByTestId('terms-and-conditions-button')).toExist();
    });

    it('emits the userInteractionEvent when clicked sending the correct data', () => {
      const { findByTestId } = render({ terms, termsShort });
      findByTestId('terms-and-conditions-button').simulate('click');

      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Featured Offer Carousel',
        value: 'Terms and Conditions Clicked',
      });
    });
  });
});
