import React from 'react';
import { mocked, mountUtils } from 'test-utils';
import FeaturedOfferBanner from './FeaturedCampaignBanner';
import { useDataLayer } from 'hooks/useDataLayer';

jest.mock('hooks/useDataLayer');

const emitInteractionEvent = jest.fn();

const defaultProps = {
  heading: 'heading text',
  description: [
    {
      _key: '05b73faf7853',
      _type: 'block',
      children: [
        {
          _key: '717dc8cff20d0',
          _type: 'span',
          marks: [],
          text: 'Choose from thousands of hotels and resorts across Australia and the world and use 30% less points when you book a Qantas Hotel using Points Plus Pay. †',
        },
      ],
      markDefs: [],
      style: 'normal',
    },
  ],
  promotionalSash: 'Promotional sash',
  callToAction: {
    text: 'find out more',
    type: 'primary',
    url: '/campaigns/cypress-campaign',
  },
};

const decorators = { theme: true, store: true, router: true };
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const render = (props?: any) => mountUtils(<FeaturedOfferBanner {...defaultProps} {...props} />, { decorators });

describe('<FeaturedOfferBanner />', () => {
  beforeEach(() => {
    mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
  });

  describe('Heading', () => {
    it('when provided it renders the heading', () => {
      const { find } = render();

      expect(find('Heading')).toHaveText(defaultProps.heading);
    });

    it('when not provided it does not render any heading text', () => {
      const { find } = render({ heading: null });

      expect(find('Heading')).not.toExist();
    });
  });

  describe('description', () => {
    it('when provided it renders the description text', () => {
      const { findByTestId } = render();

      expect(findByTestId('offer-description')).toHaveText(defaultProps.description[0].children[0].text);
    });

    it('when not provided it does not render any description text', () => {
      const { findByTestId } = render({ description: null });

      expect(findByTestId('offer-description')).not.toExist();
    });
  });

  describe('callToAction', () => {
    it('when provided it renders the callToAction component that clicked fires a tracking event', () => {
      const { findByTestId } = render();

      findByTestId('carousel-offer-cta').simulate('click');

      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Featured Offer Carousel',
        value: 'Call To Action Selected',
      });
    });

    it('when not provided it does not render any callToAction', () => {
      const { findByTestId } = render({ callToAction: {} });

      expect(findByTestId('featured-offer-cta')).not.toExist();
    });
  });
});
