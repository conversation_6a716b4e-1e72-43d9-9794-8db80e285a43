import React from 'react';
import { mocked, mountUtils } from 'test-utils';
import FeaturedCampaign from './FeaturedCampaign';
import { getIsMobileApp } from 'store/ui/uiSelectors';
import { mockOffers } from './mocks';

jest.mock('store/ui/uiSelectors');
const mockOffer = mockOffers.offers[0];

const decorators = { store: true, router: true, theme: true };
const render = (obj = {}) => mountUtils(<FeaturedCampaign {...{ ...mockOffer, ...obj }} />, { decorators });

describe('<FeaturedCampaign />', () => {
  beforeEach(() => {
    mocked(getIsMobileApp).mockReturnValue(false);
  });

  it('renders the BackgroundImageStyled with the expected props', () => {
    const { find } = render();

    expect(find('HeroImage')).toHaveProp({
      src: 'https://cdn.sanity.io/images/abcdefghi/qh-test/9b5a7bab1952f0428eeef065fd4b89e1b0421038-2500x1464.png?w=1100&q=70&auto=format',
      alt: 'Foo',
    });
  });

  it('renders the FeaturedOfferBanner with the expected props', () => {
    const { find } = render();

    expect(find('FeaturedOfferBanner')).toHaveProp({
      heading: 'Find your next luxury hotel',
      description: mockOffers.offers[0].offerDescription,
      promotionalSash: 'Exclusive offer',
      callToAction: {
        text: 'Find out more',
        url: '/search/list',
        external: false,
      },
    });
  });

  describe('promotionalSash', () => {
    it('when provided it renders the Badge with the expected text', () => {
      const { findByTestId } = render();

      expect(findByTestId('promotional-sash')).toHaveText(mockOffer.promotionalSash);
    });

    it('when not provided it does not render the Badge', () => {
      const { findByTestId } = render({ promotionalSash: null });

      expect(findByTestId('promotional-sash')).not.toExist();
    });
  });
});
