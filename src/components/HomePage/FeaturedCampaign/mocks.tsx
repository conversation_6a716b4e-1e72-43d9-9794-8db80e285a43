export const mockImage = {
  asset: {
    _type: 'sanity.imageCrop',
    bottom: 0.1,
    left: 0.2,
    right: 0.3,
    top: 0.4,
    _ref: 'image-9b5a7bab1952f0428eeef065fd4b89e1b0421038-2500x1464-png',
    height: 0.62,
    width: 0.24,
    x: 0.64,
    y: 0.51,
    asset: {
      _ref: 'image-9b5a7bab1952f0428eeef065fd4b89e1b0421038-2500x1464-png',
    },
  },
  name: 'Foo',
};

const mockMarkdown = (description) => ({
  _key: '05b73faf7853',
  _type: 'block',
  children: [
    {
      _key: '717dc8cff20d0',
      _type: 'span',
      marks: [],
      text: description,
    },
  ],
  markDefs: [],
  style: 'normal',
});

export const mockOffers = {
  title: 'Special offers from Qantas',
  offers: [
    {
      backgroundImage: mockImage,
      callToAction: {
        text: 'Find out more',
        url: '/search/list',
        external: false,
      },
      name: 'Find your next luxury hotel',
      offerDescription: [
        mockMarkdown(
          "Choose from carefully curated hotel getaways in fantastic destinations, exclusive to Qantas Hotels. You'll earn 6 Qantas Points per $1 spent on your booking^ and now use 30% less points when you book your hotel using Points Plus Pay.±",
        ),
      ],
      promotionalSash: 'Exclusive offer',
      saleDateRange: {
        start: {
          local: '2021-12-01T00:00:00+10:00',
        },
        end: {
          local: '2021-12-31T00:00:00+10:00',
        },
      },
      travelDateRange: {
        start: {
          local: '2021-12-01T00:00:00+10:00',
        },
        end: {
          local: '2021-12-31T00:00:00+10:00',
        },
      },
      terms: 'Offer terms',
      termsShort: 'Offer terms',
    },
    {
      backgroundImage: mockImage,
      callToAction: {
        text: 'Find out more',
        url: '/search/list',
        external: false,
      },
      name: 'Travel insurance with up to 10,000 Qantas Points',
      offerDescription: [
        mockMarkdown(
          "Choose from carefully curated hotel getaways in fantastic destinations, exclusive to Qantas Hotels. You'll earn 6 Qantas Points per $1 spent on your booking^ and now use 30% less points when you book your hotel using Points Plus Pay.±",
        ),
      ],
      promotionalSash: 'Exclusive offer',
      saleDateRange: {
        start: {
          local: '2021-12-01T00:00:00+10:00',
        },
        end: {
          local: '2021-12-31T00:00:00+10:00',
        },
      },
      travelDateRange: {
        start: {
          local: '2021-12-01T00:00:00+10:00',
        },
        end: {
          local: '2021-12-31T00:00:00+10:00',
        },
      },
      terms: 'Offer terms',
      termsShort: 'Offer terms',
    },
    {
      backgroundImage: mockImage,
      callToAction: {
        text: 'Find out more',
        url: '/search/list',
        external: false,
      },
      name: 'Travel insurance with up to 10,000 Qantas Points',
      offerDescription: [
        mockMarkdown(
          "Choose from carefully curated hotel getaways in fantastic destinations, exclusive to Qantas Hotels. You'll earn 6 Qantas Points per $1 spent on your booking^ and now use 30% less points when you book your hotel using Points Plus Pay.±",
        ),
      ],
      promotionalSash: 'Sustainable Hotels',
      saleDateRange: {
        start: {
          local: '2021-12-01T00:00:00+10:00',
        },
        end: {
          local: '2021-12-31T00:00:00+10:00',
        },
      },
      travelDateRange: {
        start: {
          local: '2021-12-01T00:00:00+10:00',
        },
        end: {
          local: '2021-12-31T00:00:00+10:00',
        },
      },
      terms: 'Offer terms',
      termsShort: 'Offer terms',
    },
  ],
};
