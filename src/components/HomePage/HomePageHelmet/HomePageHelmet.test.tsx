/* eslint-disable prettier/prettier */
import React from 'react';
import { mocked, mountUtils } from 'test-utils';
import HomePageHelmet from './HomePageHelmet';
import omit from 'lodash/omit';
import { getHomePageMeta } from 'store/homePage/homePageSelectors';
import { HOTELS_URL } from 'config';

jest.mock('store/homePage/homePageSelectors');

const metadata = {
  title: 'Test title for homepage',
  description: 'A description of the homepage',
  tags: [
    { name: 'hotels-homepage-stage', content: 'homepage' },
    { name: 'og:site_name', content: 'HOTELS_BRAND_NAME' },
    { name: 'og:url', content: 'canonicalLink' },
    { name: 'robots', content: 'index, follow' },
  ],
};

const decorators = { store: true, router: true, helmet: true };
const render = () => mountUtils(<HomePageHelmet />, { decorators });

describe('<HomePageHelmet />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mocked(getHomePageMeta).mockReturnValue(metadata);
  });

  it('sets the title', () => {
    const { find } = render();
    expect(find('title')).toHaveText(metadata.title);
  });

  it('sets the default title if no title is provided', () => {
    const metadataWithoutTitle = omit(metadata, 'title');
    mocked(getHomePageMeta).mockReturnValue(metadataWithoutTitle);
    const { find } = render();
    expect(find('title')).toHaveText('Jetstar Hotels');
  });

  it('sets the correct canonical link on helmet', () => {
    const { find } = render();
    expect(find('link[rel="canonical"]')).toHaveProp({ href: HOTELS_URL });
  });

  it('sets the correct description link on helmet', () => {
    const { find } = render();
    expect(find(`meta[name="description"]`)).toHaveProp({ content: metadata.description });
  });

  it('does NOT set a description link if no description is provided', () => {
    const metadataWithoutDescription = omit(metadata, 'description');
    mocked(getHomePageMeta).mockReturnValue(metadataWithoutDescription);
    const { find } = render();
    expect(find(`meta[name="description"]`)).not.toExist();
  });

  it('sets the correct schema for SEO', () => {
    const { find } = render();

    expect(find('script')).toHaveText(
      JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'Organization',
        name: 'Jetstar Hotels',
        url: 'https://www.jetstar.com/hotels',
        logo: 'https://www.jetstar.com/content/dam/qbr/partners/logos/qantas_hotels.svg',
        contactPoint: {
          '@type': 'ContactPoint',
          telephone: '1300 738 206',
          contactType: 'customer service',
          contactOption: 'TollFree',
          areaServed: 'AU',
          availableLanguage: 'en',
        },
        sameAs: ['https://www.instagram.com/Jetstar/', 'https://www.youtube.com/user/Jetstar', 'https://www.facebook.com/Jetstar/'],
      }),
    );
  });

  test.each`
    name                       | content
    ${'hotels-homepage-stage'} | ${'homepage'}
    ${'og:site_name'}          | ${'HOTELS_BRAND_NAME'}
    ${'og:url'}                | ${'canonicalLink'}
    ${'robots'}                | ${'index, follow'}
  `('sets the meta for $name', ({ name, content }) => {
    const { find } = render();
    expect(find(`meta[name="${name}"]`)).toHaveProp({ content });
  });
});
