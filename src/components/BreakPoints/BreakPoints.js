import { Component } from 'react';
import PropTypes from 'prop-types';
import throttle from 'lodash/throttle';
import { BREAK_POINTS_PX } from 'lib/theme';

class BreakPoints extends Component {
  static propTypes = {
    children: PropTypes.func.isRequired,
  };

  state = {
    matchingBreakpoints: [],
  };

  calculateBreakPoints = () => {
    const currentWidth = window.innerWidth;
    const matchingBreakpoints = BREAK_POINTS_PX.filter((minWidth) => currentWidth >= minWidth);
    this.setState({ matchingBreakpoints });
  };

  onResize = throttle(this.calculateBreakPoints, 100);

  componentDidMount = () => {
    window.addEventListener('resize', this.onResize);
    this.calculateBreakPoints();
  };

  componentWillUnmount = () => {
    window.removeEventListener('resize', this.onResize);
  };

  isLessThanBreakpoint = (breakPoint) => !this.state.matchingBreakpoints.includes(BREAK_POINTS_PX[breakPoint]);

  isGreaterThanOrEqualToBreakpoint = (breakPoint) => this.state.matchingBreakpoints.includes(BREAK_POINTS_PX[breakPoint]);

  render() {
    return this.props.children({
      isLessThanBreakpoint: this.isLessThanBreakpoint,
      isGreaterThanOrEqualToBreakpoint: this.isGreaterThanOrEqualToBreakpoint,
    });
  }
}

export default BreakPoints;
