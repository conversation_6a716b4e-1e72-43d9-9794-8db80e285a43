import React from 'react';
import { mountWithTheme } from 'test-utils';
import CalendarKey from './CalendarKey';

let subject;

const props = {
  month: 7,
  year: 2018,
  weekdayNames: ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Sabtu'],
};

beforeEach(() => (subject = mountWithTheme(<CalendarKey {...props} />)));

it('renders the names of the days of the week', () => {
  const htmlText = subject.text();
  props.weekdayNames.forEach((name) => {
    expect(htmlText).toContain(name);
  });
});

it('distributes the width equally', () => {
  expect(subject.find('[data-testid="weekday-box"]').at(0).prop('width')).toEqual(1 / 7);
});
