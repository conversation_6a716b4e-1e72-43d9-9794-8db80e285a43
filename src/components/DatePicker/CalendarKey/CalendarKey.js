import React from 'react';
import PropTypes from 'prop-types';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';

import { Flex, Box, Text } from '@qga/roo-ui/components';

const CalendarKeyWrapper = styled(Flex)`
  padding-bottom: ${themeGet('space.2')};
  margin-top: ${themeGet('space.5')};
  margin-bottom: ${themeGet('space.3')};
  border-bottom: ${themeGet('borders.1')} ${themeGet('colors.greys.alto')};
`;

CalendarKeyWrapper.displayName = 'CalendarKeyWrapper';

const CalendarKey = ({ weekdayNames, month, year }) => (
  <CalendarKeyWrapper>
    {weekdayNames.map((weekday, index) => (
      <Box width={1 / 7} key={`${month}${year}${weekday}${index}`} data-testid="weekday-box">
        <Text>{weekday}</Text>
      </Box>
    ))}
  </CalendarKeyWrapper>
);

CalendarKey.propTypes = {
  weekdayNames: PropTypes.arrayOf(PropTypes.string).isRequired,
  month: PropTypes.number.isRequired,
  year: PropTypes.number.isRequired,
};

export default CalendarKey;
