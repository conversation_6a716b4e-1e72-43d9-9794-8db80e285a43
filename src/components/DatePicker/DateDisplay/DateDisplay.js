import React, { useRef, useEffect, useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import styled from '@emotion/styled';
import noop from 'lodash/noop';
import { Flex, Box } from '@qga/roo-ui/components';
import { Label, LabelText } from 'components/Label';
import { themeGet } from 'styled-system';
import { format } from 'lib/date';
import { FOCUS } from 'components/DatePicker/constants';
import InputWithAddon from 'components/InputWithAddon';

const DEFAULT_DATE_FORMAT = 'EEE d MMM, yyyy';

const InputDivider = styled(Box)`
  position: absolute;
  right: 0;
  bottom: ${(props) => (props.isHighlighted ? 0 : themeGet('space.2')(props))};
  top: ${(props) => (props.isHighlighted ? 0 : themeGet('space.2')(props))};
  border-right-width: ${(props) => (props.isHighlighted ? '2px' : '1px')};
  border-right-color: ${(props) =>
    props.isHighlighted ? themeGet('colors.brand.secondary')(props) : themeGet('colors.greys.alto')(props)};
  border-right-style: solid;
  width: 0px;
`;

InputDivider.defaultProps = {
  ...Box.defaultProps,
};

const CheckOutDateInput = styled(InputWithAddon)`
  padding: 12px;

  font-size: ${themeGet('fontSizes.sm')};
  line-height: 1.5;

  &::placeholder {
    color: ${themeGet('colors.greys.steel')};
  }
`;

const CheckInDateInput = styled(CheckOutDateInput)`
  padding-left: 44px;
`;

const DateDisplay = ({ range, dateFormat, focusedInput, handleFocusedInput, labelOptions, error, ...rest }) => {
  const firstInputRef = useRef();
  const secondInputRef = useRef();

  useEffect(() => {
    if (focusedInput === FOCUS.START_DATE) firstInputRef.current.focus();
    if (focusedInput === FOCUS.END_DATE) secondInputRef.current.focus();
  }, [focusedInput]);

  const handleFocusStartDate = useCallback(() => handleFocusedInput(FOCUS.START_DATE), [handleFocusedInput]);
  const handleFocusEndDate = useCallback(() => handleFocusedInput(FOCUS.END_DATE), [handleFocusedInput]);

  const { startDate, endDate } = range;
  const startDateFocused = useMemo(() => focusedInput === FOCUS.START_DATE, [focusedInput]);
  const endDateFocused = useMemo(() => focusedInput === FOCUS.END_DATE, [focusedInput]);

  return (
    <Flex {...rest}>
      <Box flex="1 1 auto">
        <Label>
          <LabelText {...labelOptions}>Check-in date</LabelText>
          <Box position="relative">
            <CheckInDateInput
              icon="dateRange"
              ref={firstInputRef}
              placeholder="Check-in"
              value={startDate ? format(startDate, dateFormat) : ''}
              onFocus={handleFocusStartDate}
              isHighlighted={startDateFocused}
              readonly
              data-testid="checkin-input"
              borderRadius="defaultRoundLeftOnly"
              border={2}
              noBorder="right"
              cursor="pointer"
              error={error}
            />
            <InputDivider isHighlighted={startDateFocused || endDateFocused} />
          </Box>
        </Label>
      </Box>
      <Box flex="1 1 auto">
        <Label>
          <LabelText {...labelOptions}>Check-out date</LabelText>
          <CheckOutDateInput
            ref={secondInputRef}
            placeholder="Check-out"
            value={endDate ? format(endDate, dateFormat) : ''}
            onFocus={handleFocusEndDate}
            isHighlighted={endDateFocused}
            readonly
            data-testid="checkout-input"
            borderRadius="defaultRoundRightOnly"
            border={2}
            noBorder="left"
            cursor="pointer"
            tabIndex="-1"
            error={error}
          />
        </Label>
      </Box>
    </Flex>
  );
};

DateDisplay.propTypes = {
  range: PropTypes.shape({
    startDate: PropTypes.instanceOf(Date),
    endDate: PropTypes.instanceOf(Date),
  }),
  dateFormat: PropTypes.string,
  handleFocusedInput: PropTypes.func,
  focusedInput: PropTypes.string,
  labelOptions: PropTypes.object,
  error: PropTypes.bool,
};

DateDisplay.defaultProps = {
  dateFormat: DEFAULT_DATE_FORMAT,
  focusedInput: FOCUS.NONE,
  handleFocusedInput: noop,
  range: {},
  labelOptions: {},
  error: false,
};

export default DateDisplay;
