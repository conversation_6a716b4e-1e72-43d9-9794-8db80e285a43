import React from 'react';
import { mountUtils } from 'test-utils';
import DateDisplay from './DateDisplay';
import { FOCUS } from 'components/DatePicker/constants';

const defaultProps = {
  range: { startDate: new Date('2020-07-03'), endDate: new Date('2020-07-05') },
};

const render = (props) => mountUtils(<DateDisplay {...defaultProps} {...props} />, { decorators: { theme: true } });

it('has the correct props when rendering', () => {
  const { findByTestId } = render();

  expect(findByTestId('checkin-input')).toHaveProp({ placeholder: 'Check-in' });
  expect(findByTestId('checkout-input')).toHaveProp({ placeholder: 'Check-out' });
});

it('has the correct range when two dates are provided', () => {
  const { findByTestId } = render();

  expect(findByTestId('checkin-input')).toHaveProp({ value: 'Fri 3 Jul, 2020' });
  expect(findByTestId('checkout-input')).toHaveProp({ value: 'Sun 5 Jul, 2020' });
});

it('only shows the check in value when the check out is missing', () => {
  const { findByTestId } = render({ range: { startDate: defaultProps.range.startDate } });

  expect(findByTestId('checkin-input')).toHaveProp({ value: 'Fri 3 Jul, 2020' });
  expect(findByTestId('checkout-input')).toHaveProp({ value: '' });
});

it('only shows the check out value when the check in is missing', () => {
  const { findByTestId } = render({ range: { endDate: defaultProps.range.endDate } });

  expect(findByTestId('checkin-input')).toHaveProp({ value: '' });
  expect(findByTestId('checkout-input')).toHaveProp({ value: 'Sun 5 Jul, 2020' });
});

it('calls the provided callback with a start value when the checkin date input is focused', () => {
  const handleFocusedInput = jest.fn();
  const { findByTestId } = render({ handleFocusedInput });

  findByTestId('checkin-input').simulate('focus');

  expect(handleFocusedInput).toHaveBeenCalledTimes(1);
  expect(handleFocusedInput).toHaveBeenCalledWith(FOCUS.START_DATE);
});

it('calls the provided callback with an end value when the checkout date input is focused', () => {
  const handleFocusedInput = jest.fn();
  const { findByTestId } = render({ handleFocusedInput });

  findByTestId('checkout-input').simulate('focus');

  expect(handleFocusedInput).toHaveBeenCalledTimes(1);
  expect(handleFocusedInput).toHaveBeenCalledWith(FOCUS.END_DATE);
});
