import React from 'react';
import PropTypes from 'prop-types';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';

import { Flex, NakedButton, Icon } from '@qga/roo-ui/components';

const Wrapper = styled(Flex)`
  width: 100%;
  position: absolute;
  justify-content: space-between;
  padding: 0 ${themeGet('space.4')};
`;

const Button = styled(NakedButton)`
  border-radius: ${themeGet('radii.rounded')};
  background: ${themeGet('colors.white')};
  color: ${themeGet('colors.greys.steel')};
  box-shadow: ${themeGet('shadows.default')};

  &:hover,
  &:focus {
    outline: none;
    color: ${themeGet('colors.brand.primary')};
    box-shadow: ${themeGet('focus.boxShadow')};
  }

  &:disabled {
    cursor: not-allowed;
    box-shadow: none;
    color: ${themeGet('colors.greys.alto')};
  }
`;

const CalendarNav = ({ backProps, forwardProps }) => (
  <Wrapper>
    <Button data-testid="go-back-1-month" {...backProps}>
      <Icon name="chevronLeft" />
    </Button>

    <Button data-testid="go-forward-1-month" {...forwardProps}>
      <Icon name="chevronRight" />
    </Button>
  </Wrapper>
);

CalendarNav.propTypes = {
  backProps: PropTypes.shape().isRequired,
  forwardProps: PropTypes.shape().isRequired,
};

export default CalendarNav;
