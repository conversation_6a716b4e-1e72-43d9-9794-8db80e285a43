import React from 'react';
import { mountWithTheme } from 'test-utils';

import CalendarNav from '.';

describe('<CalendarNav />', () => {
  let wrapper;
  const props = {
    backProps: { onClick: jest.fn() },
    forwardProps: { onClick: jest.fn() },
  };
  beforeEach(() => {
    wrapper = mountWithTheme(<CalendarNav {...props} />);
  });

  it('passes props.prevProps to the prev button', () => {
    expect(wrapper.find('[data-testid="go-back-1-month"]').first().props()).toEqual(expect.objectContaining(props.backProps));
  });

  it('passes props.nextProps to the next button', () => {
    expect(wrapper.find('[data-testid="go-forward-1-month"]').last().props()).toEqual(expect.objectContaining(props.forwardProps));
  });
});
