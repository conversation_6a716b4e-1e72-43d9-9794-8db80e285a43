import React from 'react';
import { mountUtils } from 'test-utils';
import { mount } from 'enzyme';
import { addDays, format as dateFnsFormat } from 'date-fns';
import CalendarMonth from './';
import { SEARCH_DATE_FORMAT } from 'config';
import { unmountComponentAtNode } from 'react-dom';

//mountUtils.mockComponent('CalendarDay');

const startDate = new Date(2018, 7, 1, 10, 0, 0, 0);
const onChangeMonth = jest.fn();
const defaultProps = {
  monthName: 'Jul',
  month: 7,
  year: 2018,
  stacked: true,
  weekdayNames: ['S', 'M', 'T', 'W', 'T', 'F', 'S'],
  weeks: [
    [
      { date: startDate },
      { date: addDays(startDate, 1) },
      { date: addDays(startDate, 2) },
      { date: addDays(startDate, 3) },
      { date: addDays(startDate, 4) },
      { date: addDays(startDate, 5) },
      { date: addDays(startDate, 6) },
    ],
    [
      { date: addDays(startDate, 7) },
      { date: addDays(startDate, 8) },
      { date: addDays(startDate, 9) },
      { date: addDays(startDate, 10) },
      { date: addDays(startDate, 11) },
      { date: addDays(startDate, 12) },
      { date: addDays(startDate, 13) },
    ],
    [
      { date: addDays(startDate, 25) },
      { date: addDays(startDate, 26) },
      { date: addDays(startDate, 27) },
      { date: addDays(startDate, 28) },
      { date: addDays(startDate, 29) },
      { date: addDays(startDate, 30) },
    ],
  ],
  onChangeMonth,
  getDateProps: ({ dateObj }) => ({
    time: dateFnsFormat(dateObj.date, SEARCH_DATE_FORMAT),
    selected: false,
    selectable: true,
    disabled: false,
  }),
};

const render = (props) => mountUtils(<CalendarMonth {...defaultProps} {...props} />, {});

let container;

beforeEach(() => {
  container = document.createElement('div');
  container.setAttribute('tabindex', '0');
  document.body.appendChild(container);
});

afterEach(() => {
  unmountComponentAtNode(container);
  container.remove();
  container = null;
});

describe('<CalendarMonth />', () => {
  beforeEach(() => {
    jest.resetAllMocks();
  });

  describe('<CalendarKey />', () => {
    it('renders CalendarKey', () => {
      const { find } = render();
      expect(find('CalendarKey').prop('month')).toEqual(defaultProps.month);
      expect(find('CalendarKey').prop('year')).toEqual(defaultProps.year);
      expect(find('CalendarKey').prop('weekdayNames')).toEqual(defaultProps.weekdayNames);
    });
  });

  describe('Days', () => {
    it('renders the correct number of days', () => {
      const { find } = render();
      expect(find('CalendarDay')).toHaveLength(20);
    });
  });

  describe('keyboard navigation', () => {
    it('user can focus to the next day', () => {
      const wrapper = mount(<CalendarMonth {...defaultProps} />, { attachTo: container });
      wrapper
        .find(`[data-time='${dateFnsFormat(addDays(startDate, 4), SEARCH_DATE_FORMAT)}']`)
        .last()
        .instance()
        .focus();
      wrapper.find('CalendarDaysWrapper').prop('onKeyDown')({ key: 'ArrowRight', preventDefault: () => {} });
      wrapper.update();
      expect(
        wrapper
          .find(`[data-time='${dateFnsFormat(addDays(startDate, 5), SEARCH_DATE_FORMAT)}']`)
          .last()
          .instance(),
      ).toEqual(document.activeElement);
    });

    it('user can focus to the previous day', () => {
      const wrapper = mount(<CalendarMonth {...defaultProps} />, { attachTo: container });
      wrapper
        .find(`[data-time='${dateFnsFormat(addDays(startDate, 4), SEARCH_DATE_FORMAT)}']`)
        .last()
        .instance()
        .focus();
      wrapper.find('CalendarDaysWrapper').prop('onKeyDown')({ key: 'ArrowLeft', preventDefault: () => {} });
      wrapper.update();
      expect(
        wrapper
          .find(`[data-time='${dateFnsFormat(addDays(startDate, 3), SEARCH_DATE_FORMAT)}']`)
          .last()
          .instance(),
      ).toEqual(document.activeElement);
    });

    it('user can focus to the next week', () => {
      const wrapper = mount(<CalendarMonth {...defaultProps} />, { attachTo: container });
      wrapper
        .find(`[data-time='${dateFnsFormat(addDays(startDate, 4), SEARCH_DATE_FORMAT)}']`)
        .last()
        .instance()
        .focus();
      wrapper.find('CalendarDaysWrapper').prop('onKeyDown')({ key: 'ArrowDown', preventDefault: () => {} });
      wrapper.update();
      expect(
        wrapper
          .find(`[data-time='${dateFnsFormat(addDays(startDate, 11), SEARCH_DATE_FORMAT)}']`)
          .last()
          .instance(),
      ).toEqual(document.activeElement);
    });

    it('user can focus to the previous week', () => {
      const wrapper = mount(<CalendarMonth {...defaultProps} />, { attachTo: container });
      wrapper
        .find(`[data-time='${dateFnsFormat(addDays(startDate, 11), SEARCH_DATE_FORMAT)}']`)
        .last()
        .instance()
        .focus();
      wrapper.find('CalendarDaysWrapper').prop('onKeyDown')({ key: 'ArrowUp', preventDefault: () => {} });
      wrapper.update();
      expect(
        wrapper
          .find(`[data-time='${dateFnsFormat(addDays(startDate, 4), SEARCH_DATE_FORMAT)}']`)
          .last()
          .instance(),
      ).toEqual(document.activeElement);
    });

    it('changes to the next month', () => {
      const wrapper = mount(<CalendarMonth {...defaultProps} />, { attachTo: container });
      wrapper
        .find(`[data-time='${dateFnsFormat(addDays(startDate, 28), SEARCH_DATE_FORMAT)}']`)
        .last()
        .instance()
        .focus();
      wrapper.find('CalendarDaysWrapper').prop('onKeyDown')({ key: 'ArrowDown', preventDefault: () => {} });
      wrapper.update();
      expect(onChangeMonth).toHaveBeenCalledWith(addDays(startDate, 35));
    });

    it('changes to the previous month', () => {
      const wrapper = mount(<CalendarMonth {...defaultProps} />, { attachTo: container });
      wrapper
        .find(`[data-time='${dateFnsFormat(addDays(startDate, 3), SEARCH_DATE_FORMAT)}']`)
        .last()
        .instance()
        .focus();
      wrapper.find('CalendarDaysWrapper').prop('onKeyDown')({ key: 'ArrowUp', preventDefault: () => {} });
      wrapper.update();
      expect(onChangeMonth).toHaveBeenCalledWith(addDays(startDate, -4));
    });
  });
});
