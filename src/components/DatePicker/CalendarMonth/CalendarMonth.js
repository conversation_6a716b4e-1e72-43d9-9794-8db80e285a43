import React, { useCallback, useRef } from 'react';
import PropTypes from 'prop-types';
import { themeGet } from 'styled-system';
import styled from '@emotion/styled';
import { css } from '@emotion/core';
import { addDays, format as dateFnsFormat } from 'date-fns';
import { SEARCH_DATE_FORMAT } from 'config';

import { Flex, Box, Text } from '@qga/roo-ui/components';
import CalendarKey from 'components/DatePicker/CalendarKey';
import { CalendarDay, CalendarEmptyDay } from 'components/DatePicker/CalendarDay';

const MonthWrapper = styled(Box)`
  text-align: center;
  padding: 0 ${themeGet('space.4')};
  width: ${(props) => `${100 / props.monthsToDisplay}%`};

  ${(props) =>
    props.stacked &&
    css`
      width: 100%;
      margin-top: ${themeGet('space.8')(props)};

      &:first-of-type {
        margin-top: 0;
      }
    `};
`;

const CalendarDaysWrapper = styled(Flex)`
  flex-wrap: wrap;
  margin-bottom: 2px;
  margin-right: 2px;
`;

MonthWrapper.displayName = 'MonthWrapper';

const CalendarMonth = ({
  monthsToDisplay,
  month,
  monthName,
  year,
  stacked,
  weekdayNames,
  weeks,
  getDateProps,
  onMouseEnterOfDay,
  getCustomDateProps,
  onChangeMonth,
}) => {
  const monthRef = useRef();
  const onKeyDown = useCallback(
    (event) => {
      if (event.key === 'ArrowUp' || event.key === 'ArrowDown' || event.key === 'ArrowRight' || event.key === 'ArrowLeft') {
        // Prevent scroll up or down the entire page
        event.preventDefault();
      } else {
        return;
      }

      const activeDateElement = document.activeElement.getAttribute('data-time');
      if (!activeDateElement) {
        return;
      }

      const currentDate = new Date(activeDateElement);
      const change = {
        ArrowUp: -7,
        ArrowDown: 7,
        ArrowLeft: -1,
        ArrowRight: 1,
      }[event.key];
      const newDate = addDays(currentDate, change);

      const currentMonth = currentDate.getMonth();
      const newMonth = newDate.getMonth();

      if (currentMonth !== newMonth) {
        onChangeMonth(newDate);
      } else {
        // eslint-disable-next-line no-unused-expressions
        monthRef.current.querySelector(`[data-time='${dateFnsFormat(newDate, SEARCH_DATE_FORMAT)}']`)?.focus();
      }
    },
    [onChangeMonth],
  );

  return (
    <MonthWrapper monthsToDisplay={monthsToDisplay} stacked={stacked} data-testid="calendar-month" data-date={`${year}-${month + 1}-01`}>
      <Text textStyle="caps">
        {monthName} {year}
      </Text>
      <CalendarKey month={month} year={year} weekdayNames={weekdayNames} />
      <CalendarDaysWrapper ref={monthRef} onKeyDown={onKeyDown}>
        {weeks.map((week) =>
          week.map((day, index) => {
            if (!day) return <CalendarEmptyDay key={`${year}${month}${index}`} />;
            const onMouseEnter = () => onMouseEnterOfDay(day);
            const customProps = getCustomDateProps(day);

            return (
              <CalendarDay key={`${year}${month}${index}`} {...getDateProps({ dateObj: day, onMouseEnter, ...customProps })}>
                {day.date.getDate()}
              </CalendarDay>
            );
          }),
        )}
      </CalendarDaysWrapper>
    </MonthWrapper>
  );
};

MonthWrapper.defaultProps = {
  ...Box.defaultProps,
};

CalendarMonth.defaultProps = {
  monthsToDisplay: 1,
  stacked: false,
  getCustomDateProps: () => {},
  onMouseEnterOfDay: () => {},
  onChangeMonth: () => {},
};

CalendarMonth.propTypes = {
  month: PropTypes.number.isRequired,
  year: PropTypes.number.isRequired,
  monthName: PropTypes.string.isRequired,
  weeks: PropTypes.arrayOf(PropTypes.array).isRequired,
  weekdayNames: PropTypes.arrayOf(PropTypes.string).isRequired,
  monthsToDisplay: PropTypes.number,
  stacked: PropTypes.bool,
  getDateProps: PropTypes.func.isRequired,
  onMouseEnterOfDay: PropTypes.func,
  getCustomDateProps: PropTypes.func,
  onChangeMonth: PropTypes.func,
};

export default CalendarMonth;
