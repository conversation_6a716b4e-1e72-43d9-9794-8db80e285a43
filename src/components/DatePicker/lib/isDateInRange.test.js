import isDateInRange from './isDateInRange';

describe('when startDate and endDate are selected', () => {
  it('returns true when date is in the range', () => {
    expect(
      isDateInRange({
        startDate: new Date(2018, 1, 11),
        endDate: new Date(2018, 1, 20),
        date: new Date(2018, 1, 15),
      }),
    ).toEqual(true);
  });

  it('returns false when date is not in the range', () => {
    expect(
      isDateInRange({
        startDate: new Date(2018, 1, 11),
        endDate: new Date(2018, 1, 20),
        date: new Date(2018, 1, 25),
      }),
    ).toEqual(false);
  });
});

describe('when only startDate is selected', () => {
  it('returns false with no hoveredDate', () => {
    expect(
      isDateInRange({
        startDate: new Date(2018, 1, 11),
        date: new Date(2018, 1, 25),
      }),
    ).toEqual(false);
  });

  describe('with hoveredDate and isSettingStartDate is false', () => {
    it('returns true when day is between startDate and hoveredDate', () => {
      expect(
        isDateInRange({
          startDate: new Date(2018, 1, 11),
          hoveredDate: new Date(2018, 1, 18),
          isSettingStartDate: false,
          date: new Date(2018, 1, 14),
        }),
      ).toEqual(true);
    });

    it('returns false when day is not between startDate and hoveredDate', () => {
      expect(
        isDateInRange({
          startDate: new Date(2018, 1, 11),
          hoveredDate: new Date(2018, 1, 18),
          isSettingStartDate: false,
          date: new Date(2018, 1, 20),
        }),
      ).toEqual(false);
    });

    it('returns false when hoveredDate is before the startDate', () => {
      expect(
        isDateInRange({
          startDate: new Date(2018, 1, 11),
          hoveredDate: new Date(2018, 1, 6),
          isSettingStartDate: false,
          date: new Date(2018, 1, 20),
        }),
      ).toEqual(false);
    });
  });

  describe('with hoveredDate and isSettingStartDate is true', () => {
    it('returns false when day is between startDate and hoveredDate', () => {
      expect(
        isDateInRange({
          startDate: new Date(2018, 1, 11),
          hoveredDate: new Date(2018, 1, 18),
          isSettingStartDate: true,
          date: new Date(2018, 1, 14),
        }),
      ).toEqual(false);
    });

    it('returns false when day is not between startDate and hoveredDate', () => {
      expect(
        isDateInRange({
          startDate: new Date(2018, 1, 11),
          hoveredDate: new Date(2018, 1, 18),
          isSettingStartDate: true,
          date: new Date(2018, 1, 20),
        }),
      ).toEqual(false);
    });

    it('returns false when hoveredDate is before the startDate', () => {
      expect(
        isDateInRange({
          startDate: new Date(2018, 1, 11),
          hoveredDate: new Date(2018, 1, 6),
          isSettingStartDate: true,
          date: new Date(2018, 1, 20),
        }),
      ).toEqual(false);
    });
  });
});
