import { isWithinInterval } from 'lib/date';

const isDateInRange = ({ startDate, endDate, isSettingStartDate, hoveredDate, date }) => {
  if (startDate && endDate) return isWithinInterval(date, { start: startDate, end: endDate });
  if (isSettingStartDate || !hoveredDate || hoveredDate < startDate) return false;
  return isWithinInterval(date, { start: startDate, end: hoveredDate });
};

export default isDateInRange;
