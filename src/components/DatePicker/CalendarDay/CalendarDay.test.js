import React from 'react';
import theme from 'lib/theme';
import { mountUtils } from 'test-utils';
import { CalendarDay, CalendarEmptyDay } from './';

const defaultProps = { time: '2021-01-18' };
const decorators = { theme: true };
const render = (props) =>
  mountUtils(
    <CalendarDay {...defaultProps} {...props}>
      7
    </CalendarDay>,
    { decorators },
  );

describe('<CalendarDay/>', () => {
  describe('selected', () => {
    it('passes the selected status to the wrapper element', () => {
      const { find } = render({ selected: true });
      expect(find('DayWrapper').props().selected).toEqual(true);
    });

    it('passes the selected status to the button element', () => {
      const { find } = render({ selected: true });
      expect(find('Button').props().selected).toEqual(true);
    });

    it('renders with the correct colors', () => {
      const { find } = render({ selected: true });
      expect(find('Button')).toHaveStyleRule('background-color', theme.colors.lightBlue);
      expect(find('Button')).toHaveStyleRule('background-color', theme.colors.white, { target: ':hover' });
    });
  });

  describe('selectable', () => {
    it('renders with the correct colors', () => {
      const { find } = render({ selectable: true });
      expect(find('Button')).toHaveStyleRule('background-color', theme.colors.white);
      expect(find('Button')).toHaveStyleRule('background-color', theme.colors.white, { target: ':hover' });
    });
  });

  describe('highlighted', () => {
    it('renders with the correct colors', () => {
      const { find } = render({ highlighted: true });
      expect(find('Button')).toHaveStyleRule('background-color', theme.colors.lightBlue);
      expect(find('Button')).toHaveStyleRule('background-color', theme.colors.white, { target: ':hover' });
    });
  });

  describe('selected and highlighted', () => {
    it('renders with the correct colors', () => {
      const { find } = render({ selected: true, highlighted: true });
      expect(find('Button')).toHaveStyleRule('background-color', theme.colors.brand.secondary);
      expect(find('Button')).toHaveStyleRule('background-color', 'transparent', { target: ':hover' });
    });
  });

  describe('disabled', () => {
    it('renders with the correct colors', () => {
      const { find } = render({ disabled: true, selectable: false });
      expect(find('Button')).toHaveStyleRule('background-color', theme.colors.greys.alto, { target: ':disabled' });
    });
  });
});

describe('<CalendarEmptyDay/>', () => {
  it('renders without border color', () => {
    const renderEmptyDay = () => mountUtils(<CalendarEmptyDay />, { decorators });
    const { wrapper } = renderEmptyDay();
    expect(wrapper).toHaveStyleRule('border-color', 'transparent');
  });
});
