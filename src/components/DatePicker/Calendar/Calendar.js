import React, { useState, useCallback, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { useDayzed } from 'dayzed';
import { subDays, isSameDay, differenceInDays, differenceInCalendarMonths } from 'lib/date/datefns';
import noop from 'lodash/noop';
import throttle from 'lodash/fp/throttle';
import { Flex, Box } from '@qga/roo-ui/components';
import { format as dateFnsFormat, isValid } from 'date-fns';
import { CalendarNav, CalendarMonth } from 'components/DatePicker';
import isDateInRange from 'components/DatePicker/lib/isDateInRange';
import { FOCUS } from 'components/DatePicker/constants';
import { SEARCH_DATE_FORMAT } from 'config';

const MS_IN_A_DAY = 86400000;
const throttled = throttle(10);

const Calendar = ({
  startDate,
  endDate,
  monthNames,
  weekdayNames,
  monthsToDisplay,
  stacked,
  initialDisplayDate,
  focusedInput,
  onChangeDates,
  maxSelectableDays,
  firstDayOfWeek,
  minDate,
  boundaries,
}) => {
  const calendarRef = useRef();
  const focusDate = useRef();
  const [hoveredDate, setHoveredDate] = useState(null);
  const [offset, setOffset] = useState(differenceInCalendarMonths(startDate || endDate || initialDisplayDate, initialDisplayDate));
  const selectedDates = [startDate, endDate];

  const isSettingStartDate = useCallback(() => focusedInput === FOCUS.START_DATE, [focusedInput]);

  const isSettingEndDate = useCallback(() => focusedInput === FOCUS.END_DATE, [focusedInput]);

  const onOffsetChanged = useCallback((offset) => setOffset(offset), []);

  const onMouseLeaveOfCalendar = useCallback(() => setHoveredDate(null), []);

  useEffect(() => {
    if (focusDate.current) {
      const currentDateElement = calendarRef.current.querySelector(`[data-time='${dateFnsFormat(focusDate.current, SEARCH_DATE_FORMAT)}']`);
      if (currentDateElement) {
        currentDateElement.focus();
      }
      focusDate.current = undefined;
    }
  }, [offset]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const onMouseEnterOfDay = useCallback(
    throttled((hoveredDate) => {
      if (startDate && !endDate) {
        setHoveredDate(hoveredDate.date);
      }
    }),
    [startDate, endDate],
  );

  const resetWithStartDate = useCallback(
    (startDate) => {
      onChangeDates({ startDate, endDate: null });
    },
    [onChangeDates],
  );

  const selectStartDate = useCallback(
    (startDate) => {
      const startDateAfterEndDate = startDate >= endDate;
      const rangeAboveMaxRange = differenceInDays(endDate, startDate) >= maxSelectableDays;

      if (startDateAfterEndDate || rangeAboveMaxRange) {
        resetWithStartDate(startDate);
      } else {
        onChangeDates({ startDate, endDate });
      }
    },
    [endDate, maxSelectableDays, onChangeDates, resetWithStartDate],
  );

  const selectEndDate = useCallback(
    (endDate) => {
      if (endDate <= startDate) {
        resetWithStartDate(endDate);
      } else {
        onChangeDates({ startDate, endDate });
      }
    },
    [startDate, onChangeDates, resetWithStartDate],
  );

  const isDateHighlighted = useCallback(
    (date) => {
      if (isSameDay(date, startDate) || isSameDay(date, endDate)) return true;

      return isDateInRange({
        startDate,
        endDate,
        isSettingStartDate: isSettingStartDate(),
        hoveredDate,
        date,
      });
    },
    [startDate, endDate, hoveredDate, isSettingStartDate],
  );

  const isDateOutsideMaxSelectableDays = useCallback(
    (date) => {
      if (startDate == null) return false;

      // compare in MS as this is fast to evaluate and needs to run multiple times per render
      const time = date.getTime();
      const maxSelectableTime = startDate.getTime() + maxSelectableDays * MS_IN_A_DAY;
      return time >= maxSelectableTime;
    },
    [startDate, maxSelectableDays],
  );

  const isDateOutsideDateBoundaries = useCallback(
    (date) => {
      const { start, end } = boundaries;
      if (!start || !end) return false;
      return date.getTime() < start.getTime() || date.getTime() > end.getTime();
    },
    [boundaries],
  );

  const getCustomDateProps = useCallback(
    (day) => {
      const { date } = day;
      const props = {
        selected: day.selected,
        selectable: day.selectable,
        highlighted: isDateHighlighted(date),
        time: dateFnsFormat(date, SEARCH_DATE_FORMAT),
      };

      if ((isSettingEndDate() && isDateOutsideMaxSelectableDays(date)) || isDateOutsideDateBoundaries(date)) {
        props.selectable = false;
        props.highlighted = false;
        props.disabled = true;
      }

      return props;
    },
    [isSettingEndDate, isDateOutsideMaxSelectableDays, isDateHighlighted, isDateOutsideDateBoundaries],
  );

  const onDateSelected = useCallback(
    ({ selectable, date: selectedDate }) => {
      if (!selectable || !isValid(selectedDate)) return;

      if (!startDate || isSettingStartDate()) {
        selectStartDate(selectedDate);
      } else if (!endDate || isSettingEndDate()) {
        selectEndDate(selectedDate);
      } else {
        resetWithStartDate(selectedDate);
      }

      setHoveredDate(null);
    },
    [startDate, endDate, isSettingStartDate, isSettingEndDate, resetWithStartDate, selectEndDate, selectStartDate],
  );

  const dayzedData = useDayzed({
    selected: selectedDates,
    date: initialDisplayDate,
    offset,
    monthsToDisplay,
    firstDayOfWeek,
    minDate,
    onDateSelected,
    onOffsetChanged,
  });

  const { calendars, getBackProps, getForwardProps, getDateProps } = dayzedData;

  const backProps = getBackProps({ calendars });
  const forwardProps = getForwardProps({ calendars });

  const onChangeMonth = useCallback(
    (newDate) => {
      // Check if date is within the first and last valid date range
      if (isSettingEndDate() && isDateOutsideMaxSelectableDays(newDate)) {
        return;
      }

      const calendarYearOffsetInMonths = (calendars[0].year - new Date().getFullYear()) * 12;
      const calendarMonth = calendars[0].month + calendarYearOffsetInMonths;

      const yearOffsetInMonths = (newDate.getFullYear() - new Date().getFullYear()) * 12;
      const monthOffset = newDate.getMonth() + yearOffsetInMonths;
      // changing months in dayzed -needs- an event on which preventDefault() has not been invoked, so create a fake event here
      const event = new Event('dateChanged');
      focusDate.current = newDate;
      if (monthOffset > calendarMonth) {
        forwardProps.onClick(event);
      } else if (monthOffset < calendarMonth) {
        backProps.onClick(event);
      } else {
        const currentDateElement = calendarRef.current.querySelector(`[data-time='${dateFnsFormat(newDate, SEARCH_DATE_FORMAT)}'`);
        if (currentDateElement) {
          currentDateElement.focus();
        }
      }
    },
    [backProps, forwardProps, isDateOutsideMaxSelectableDays, isSettingEndDate, calendars],
  );

  if (!calendars.length) return null;

  return (
    <Box onMouseLeave={onMouseLeaveOfCalendar} position="relative">
      <CalendarNav backProps={backProps} forwardProps={forwardProps} />

      <Flex flexWrap="wrap" ref={calendarRef}>
        {calendars.map((calendar) => {
          return (
            <CalendarMonth
              key={`${calendar.month}${calendar.year}`}
              monthsToDisplay={monthsToDisplay}
              monthName={monthNames[calendar.month]}
              month={calendar.month}
              year={calendar.year}
              stacked={stacked}
              weekdayNames={weekdayNames}
              weeks={calendar.weeks}
              getDateProps={getDateProps}
              onMouseEnterOfDay={onMouseEnterOfDay}
              getCustomDateProps={getCustomDateProps}
              onChangeMonth={onChangeMonth}
            />
          );
        })}
      </Flex>
    </Box>
  );
};

Calendar.defaultProps = {
  monthsToDisplay: 1,
  firstDayOfWeek: 1,
  stacked: false,
  minDate: subDays(new Date(), 1),
  monthNames: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
  weekdayNames: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
  startDate: null,
  endDate: null,
  initialDisplayDate: new Date(),
  maxSelectableDays: 29,
  focusedInput: FOCUS.NONE,
  onChangeDates: noop,
  boundaries: {},
};

Calendar.propTypes = {
  monthsToDisplay: PropTypes.number,
  firstDayOfWeek: PropTypes.number,
  stacked: PropTypes.bool,
  minDate: PropTypes.instanceOf(Date),
  monthNames: PropTypes.arrayOf(PropTypes.string),
  weekdayNames: PropTypes.arrayOf(PropTypes.string),
  startDate: PropTypes.instanceOf(Date),
  endDate: PropTypes.instanceOf(Date),
  initialDisplayDate: PropTypes.instanceOf(Date),
  maxSelectableDays: PropTypes.number,
  focusedInput: PropTypes.oneOf(Object.values(FOCUS)),
  onChangeDates: PropTypes.func,
  boundaries: PropTypes.shape({
    start: PropTypes.instanceOf(Date),
    end: PropTypes.instanceOf(Date),
  }),
};

export default Calendar;
