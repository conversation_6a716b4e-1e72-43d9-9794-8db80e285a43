import React, { useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import styled from '@emotion/styled';
import noop from 'lodash/noop';
import { Wrapper } from '@qga/roo-ui/components';
import { Label, LabelText } from 'components/Label';
import { themeGet } from 'styled-system';
import { format } from 'lib/date';
import { FOCUS } from 'components/DatePicker/constants';
import InputWithAddon from 'components/InputWithAddon';

const DEFAULT_DATE_FORMAT = 'd MMM';

const formatDate = (date, dateFormat) => {
  if (date) {
    return format(date, dateFormat);
  } else {
    return '';
  }
};

const DateInput = styled(InputWithAddon)`
  &:focus {
    border-color: ${themeGet('colors.ui.linkFocus')};
  }
`;

const UpdatedDateInput = styled(DateInput)`
  padding-left: 44px;
  padding-right: 12px;

  font-size: ${themeGet('fontSizes.sm')};
  line-height: 1.5;

  &::placeholder {
    color: ${themeGet('colors.greys.steel')};
  }
`;

const PhoneDateDisplay = ({ range, dateFormat, labelOptions, handleFocusedInput, error, ...rest }) => {
  const handleFocus = useCallback(() => handleFocusedInput(FOCUS.START_DATE), [handleFocusedInput]);
  const onKeyUp = useCallback(
    ({ keyCode }) => {
      if (keyCode === 13) handleFocus();
    },
    [handleFocus],
  );

  const { startDate, endDate } = range;
  const dateInputValue = useMemo(
    () => (startDate || endDate ? `${formatDate(startDate, dateFormat)} - ${formatDate(endDate, dateFormat)}` : 'Select Dates'),
    [startDate, endDate, dateFormat],
  );

  return (
    <Wrapper {...rest}>
      <Label data-testid="phone-date-display">
        <LabelText {...labelOptions} hidden={true}>
          Check in and Check out dates
        </LabelText>

        <UpdatedDateInput
          left={2}
          icon="dateRange"
          borderRadius="default"
          border="2"
          value={dateInputValue}
          onClick={handleFocus}
          onFocus={handleFocus}
          onKeyUp={onKeyUp}
          readonly
          error={error}
        />
      </Label>
    </Wrapper>
  );
};

PhoneDateDisplay.propTypes = {
  range: PropTypes.shape({
    startDate: PropTypes.instanceOf(Date),
    endDate: PropTypes.instanceOf(Date),
  }),
  dateFormat: PropTypes.string,
  handleFocusedInput: PropTypes.func,
  labelOptions: PropTypes.object,
  error: PropTypes.bool,
};

PhoneDateDisplay.defaultProps = {
  dateFormat: DEFAULT_DATE_FORMAT,
  range: {},
  labelOptions: {},
  handleFocusedInput: noop,
  error: false,
};

export default PhoneDateDisplay;
