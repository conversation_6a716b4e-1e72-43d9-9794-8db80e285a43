import React from 'react';
import { mountUtils } from 'test-utils';
import PhoneDateDisplay from './PhoneDateDisplay';
import { FOCUS } from 'components/DatePicker/constants';

const defaultProps = {
  range: { startDate: new Date('2020-07-03'), endDate: new Date('2020-07-05') },
};

const render = (props) => mountUtils(<PhoneDateDisplay {...defaultProps} {...props} />, { decorators: { theme: true } });

it('has the correct range when two dates are provided', () => {
  const { find } = render();

  expect(find('UpdatedDateInput')).toHaveProp({ value: '3 Jul - 5 Jul' });
});

it('only shows the check in value when the check out is missing', () => {
  const { find } = render({ range: { startDate: defaultProps.range.startDate } });

  expect(find('UpdatedDateInput')).toHaveProp({ value: '3 Jul - ' });
});

it('only shows the check out value when the check in is missing', () => {
  const { find } = render({ range: { endDate: defaultProps.range.endDate } });

  expect(find('UpdatedDateInput')).toHaveProp({ value: ' - 5 Jul' });
});

it('shows "Select Dates" when no dates are provided', () => {
  const { find } = render({ range: {} });

  expect(find('UpdatedDateInput')).toHaveProp({ value: 'Select Dates' });
});

it('calls the provided callback with a start value when the input is focused', () => {
  const handleFocusedInput = jest.fn();
  const { find } = render({ handleFocusedInput });

  find('UpdatedDateInput input').simulate('focus');

  expect(handleFocusedInput).toHaveBeenCalledTimes(1);
  expect(handleFocusedInput).toHaveBeenCalledWith(FOCUS.START_DATE);
});
