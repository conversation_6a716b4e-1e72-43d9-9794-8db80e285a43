import React from 'react';
import { act } from 'react-dom/test-utils';
import { mountUtils } from 'test-utils';
import DatePicker from './';
import { FOCUS } from './constants';
import { useBreakpoints } from 'hooks/useBreakpoints';
import MockDate from 'mockdate';
import { subDays } from 'date-fns';

MockDate.set('July 20, 2020');

jest.useFakeTimers();
jest.mock('hooks/useBreakpoints', () => ({ useBreakpoints: jest.fn() }));
jest.mock('store/ui/uiSelectors');

const startDate = new Date('2021-07-03');
const endDate = new Date('2021-07-05');
const defaultSelectedDates = { startDate, endDate };
const updateQuery = jest.fn();
const setSelectedDates = jest.fn();

const boundaries = {
  start: new Date('2021-06-01'),
  end: new Date('2021-12-01'),
};

const defaultProps = {
  selectedDates: defaultSelectedDates,
  setSelectedDates,
  updateQuery,
  viewThreshold: 0.75,
  minDate: subDays(new Date(), 1),
  initialDisplayDate: new Date(),
  boundaries,
};

const render = (props) => mountUtils(<DatePicker {...defaultProps} {...props} />, { decorators: { store: true, theme: true } });

const newDateRange = { startDate: new Date('2021-07-09'), endDate: new Date('2021-07-11') };

describe('<DatePicker />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    useBreakpoints.mockReturnValue({ isLessThanBreakpoint: () => false });
  });

  describe('<DatePicker />', () => {
    describe('initial state', () => {
      it('renders the correct date display on different screen sizes', () => {
        const { find } = render();
        expect(find('PhoneDateDisplay')).toHaveProp({ display: ['block', 'none'] });
        expect(find('DateDisplay')).toHaveProp({ display: ['none', 'flex'] });
      });

      it('sets the submit button text on the Footer', () => {
        const { find } = render();
        expect(find('Footer')).toHaveProp({ buttonText: 'Apply' });
      });

      it('renders the skip to content button', () => {
        const { find } = render({ adults: 1 });
        find('input').first().simulate('focus');

        expect(find('SkipToContentButton')).toExist();
      });
    });

    describe('when focusing on an input field', () => {
      it('shows the calendar', () => {
        const { find } = render();
        expect(find('Calendar')).not.toExist();

        find('input').first().simulate('focus');

        expect(find('Calendar')).toExist();
      });

      it('sets the title on the ResponsiveModal', () => {
        const { find } = render();

        find('input').first().simulate('focus');

        expect(find('ResponsiveModal')).toHaveProp({ title: 'Select dates' });
      });

      it('hides the PhoneDateDisplay', () => {
        const { find } = render();

        find('input').first().simulate('focus');

        expect(find('PhoneDateDisplay')).not.toExist();
      });

      it('renders the calendar with the correct props', () => {
        const { find, wrapper } = render();

        find('input').first().simulate('focus');

        expect(find('Calendar')).toHaveProp({
          minDate: wrapper.props().minDate,
          startDate: defaultSelectedDates.startDate,
          endDate: defaultSelectedDates.endDate,
          monthsToDisplay: 2,
          boundaries,
        });
      });

      it('passes the viewThreshold to the Dropdown component', () => {
        const { find } = render();

        find('input').first().simulate('focus');

        expect(find('Dropdown')).toHaveProp({ viewThreshold: 0.75 });
      });

      it('focuses on the second input after updating the start date', () => {
        const { find } = render();

        find('input').first().simulate('focus');
        find('Calendar').invoke('onChangeDates')({ startDate: new Date('2021-07-09') });

        expect(find('Calendar')).toHaveProp({ focusedInput: FOCUS.END_DATE });
      });

      it('unfocuses after updating the second date', () => {
        const { find } = render();

        find('input').first().simulate('focus');
        find('Calendar').invoke('onChangeDates')({ startDate: defaultSelectedDates.startDate, endDate: new Date('2021-07-11') });

        expect(find('Calendar')).toHaveProp({ focusedInput: FOCUS.NONE });
      });

      describe('after setting new dates', () => {
        describe('and clicking the cancel button', () => {
          it('closes the dropdown', () => {
            const { find, findByTestId, wrapper } = render();

            find('input').first().simulate('focus');
            find('Calendar').invoke('onChangeDates')(newDateRange);
            findByTestId('cancel-button').simulate('click');

            act(() => {
              jest.runAllTimers();
              wrapper.setProps({});
            });

            expect(find('Calendar')).not.toExist();
          });

          it('blurs the focus', () => {
            const { find, findByTestId, wrapper } = render();

            find('input').first().simulate('focus');
            find('Calendar').invoke('onChangeDates')(newDateRange);
            findByTestId('cancel-button').simulate('click');

            act(() => {
              jest.runAllTimers();
              wrapper.setProps({});
            });

            expect(find('DateDisplay')).toHaveProp({ focusedInput: FOCUS.NONE });
          });

          it('resets the selectedDates', () => {
            const { find, findByTestId, wrapper } = render();

            find('input').first().simulate('focus');
            find('Calendar').invoke('onChangeDates')(newDateRange);
            findByTestId('cancel-button').simulate('click');

            act(() => {
              jest.runAllTimers();
              wrapper.setProps({});
            });

            expect(find('DateDisplay')).toHaveProp({ range: defaultSelectedDates });
          });
        });

        describe('and clicking the apply button in the ResponsiveModal', () => {
          it('closes the dropdown', () => {
            const { find, findByTestId, wrapper } = render();

            find('input').first().simulate('focus');
            find('Calendar').invoke('onChangeDates')(newDateRange);
            findByTestId('done-button').simulate('click');

            act(() => {
              jest.runAllTimers();
              wrapper.setProps({});
            });

            expect(find('Calendar')).not.toExist();
          });

          it('blurs the focus', () => {
            const { find, findByTestId, wrapper } = render();

            find('input').first().simulate('focus');
            find('Calendar').invoke('onChangeDates')(newDateRange);
            findByTestId('done-button').simulate('click');

            act(() => {
              jest.runAllTimers();
              wrapper.setProps({});
            });

            expect(find('DateDisplay')).toHaveProp({ focusedInput: FOCUS.NONE });
          });

          it('updates the query params', () => {
            const { find, findByTestId, wrapper } = render();

            find('input').first().simulate('focus');
            find('Calendar').invoke('onChangeDates')(newDateRange);
            findByTestId('done-button').simulate('click');

            act(() => {
              jest.runAllTimers();
              wrapper.setProps({});
            });

            expect(updateQuery).toHaveBeenCalledWith({ checkIn: newDateRange.startDate, checkOut: newDateRange.endDate });
          });
        });

        describe('and clicking the done button (not mobile)', () => {
          it('closes the dropdown', () => {
            const { find, findByTestId, wrapper } = render();

            find('input').first().simulate('focus');
            find('Calendar').invoke('onChangeDates')(newDateRange);
            findByTestId('done-button').simulate('click');

            act(() => {
              jest.runAllTimers();
              wrapper.setProps({});
            });

            expect(find('Calendar')).not.toExist();
          });

          it('blurs the focus', () => {
            const { find, findByTestId, wrapper } = render();

            find('input').first().simulate('focus');
            find('Calendar').invoke('onChangeDates')(newDateRange);
            findByTestId('done-button').simulate('click');

            act(() => {
              jest.runAllTimers();
              wrapper.setProps({});
            });

            expect(find('DateDisplay')).toHaveProp({ focusedInput: FOCUS.NONE });
          });

          it('updates the query params', () => {
            const { find, findByTestId, wrapper } = render();

            find('input').first().simulate('focus');
            find('Calendar').invoke('onChangeDates')(newDateRange);
            findByTestId('done-button').simulate('click');

            act(() => {
              jest.runAllTimers();
              wrapper.setProps({});
            });

            expect(updateQuery).toHaveBeenCalledWith({ checkIn: newDateRange.startDate, checkOut: newDateRange.endDate });
          });
        });
      });
    });

    describe('when applying the new dates', () => {
      it('works correctly when the dates have not changed', () => {
        const { find, findByTestId, wrapper } = render();

        find('input').first().simulate('focus');
        find('Calendar').invoke('onChangeDates')(defaultSelectedDates);
        findByTestId('done-button').simulate('click');

        act(() => {
          jest.runAllTimers();
          wrapper.setProps({});
        });

        expect(find('Calendar')).not.toExist();
        expect(updateQuery).not.toHaveBeenCalled();
      });

      it('works correctly when the dates have changed', () => {
        const newStartDate = new Date('2021-07-09');
        const newEndDate = new Date('2021-07-11');
        const { find, findByTestId, wrapper } = render();

        find('input').first().simulate('focus');
        find('Calendar').invoke('onChangeDates')({ startDate: newStartDate, endDate: newEndDate });
        findByTestId('done-button').simulate('click');

        act(() => {
          jest.runAllTimers();
          wrapper.setProps({});
        });

        expect(find('Calendar')).not.toExist();
        expect(updateQuery).toHaveBeenCalledWith({ checkIn: newStartDate, checkOut: newEndDate });
      });

      it('works correctly when only one date is set', () => {
        const newStartDate = new Date('2021-07-09');
        const newEndDate = null;
        const { find, findByTestId, wrapper } = render();

        find('input').first().simulate('focus');
        find('Calendar').invoke('onChangeDates')({ startDate: newStartDate, endDate: newEndDate });

        act(() => {
          jest.runAllTimers();
          wrapper.setProps({});
        });

        expect(find('Calendar')).toExist();
        expect(findByTestId('done-button')).not.toExist();
        expect(updateQuery).not.toHaveBeenCalledWith();
      });
    });
  });

  describe('when browser width is less than the first breakpoint', () => {
    it('renders the calendar with the correct monthsToDisplay', () => {
      useBreakpoints.mockReturnValue({ isLessThanBreakpoint: () => true });
      const { find, wrapper } = render();

      find('input').first().simulate('focus');

      act(() => {
        jest.runAllTimers();
        wrapper.setProps({});
      });

      expect(find('Calendar')).toHaveProp({ monthsToDisplay: 1 });
    });
  });

  describe('clicking outside the dropdown', () => {
    it('calls update query', async () => {
      const newStartDate = new Date('2021-07-09');
      const newEndDate = new Date('2021-07-11');
      const { find, wrapper } = render();

      find('input').first().simulate('focus');
      find('Calendar').invoke('onChangeDates')({ startDate: newStartDate, endDate: newEndDate });
      find('ResponsiveModal').invoke('onBlur')();

      act(() => {
        jest.runAllTimers();
        wrapper.setProps({});
      });

      expect(updateQuery).toHaveBeenCalledTimes(1);
    });
  });
});
