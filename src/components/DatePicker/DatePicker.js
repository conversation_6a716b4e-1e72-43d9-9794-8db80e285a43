import React, { Fragment, useEffect, useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import { subDays } from 'date-fns';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { Flex, Box } from '@qga/roo-ui/components';
import isEqual from 'lodash/isEqual';
import every from 'lodash/every';
import { useBreakpoints } from 'hooks/useBreakpoints';
import { mediaQuery } from 'lib/styledSystem';
import ResponsiveModal from 'components/ResponsiveModal';
import Calendar from './Calendar';
import DateDisplay from './DateDisplay';
import PhoneDateDisplay from './PhoneDateDisplay';
import { FOCUS } from './constants';
import Dropdown from 'components/Dropdown';
import TextButton from 'components/TextButton';

const Actions = styled(Box)`
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: ${themeGet('space.4')};
  border: none;
  background: ${themeGet('colors.greys.porcelain')};

  ${mediaQuery.minWidth.sm} {
    position: relative;
    left: auto;
    right: auto;
    bottom: auto;
    background: transparent;
    padding: ${themeGet('space.5')} ${themeGet('space.14')};
    border-top: 1px solid ${themeGet('colors.greys.alto')};
  }
`;

const DatePicker = ({
  selectedDates,
  labelOptions,
  updateQuery,
  anchorX,
  viewThreshold,
  submitOnChange,
  minDate,
  initialDisplayDate,
  boundaries,
  error,
}) => {
  const [nextDates, setNextDates] = useState(selectedDates);
  const [focusedInput, setFocusedInput] = useState(FOCUS.NONE);
  const { isLessThanBreakpoint } = useBreakpoints();

  useEffect(() => {
    setNextDates(selectedDates);
  }, [selectedDates]);

  const isDirty = useCallback(() => !isEqual(nextDates, selectedDates), [nextDates, selectedDates]);

  const isValid = useCallback(() => every(nextDates, (date) => date instanceof Date), [nextDates]);

  const applyChange = useCallback(() => {
    updateQuery({
      checkIn: nextDates.startDate,
      checkOut: nextDates.endDate,
    });
  }, [updateQuery, nextDates]);

  const cancelChange = useCallback(() => {
    setFocusedInput(FOCUS.NONE);
    setNextDates(selectedDates);
  }, [setFocusedInput, setNextDates, selectedDates]);

  const nextInputFocus = useCallback(
    ({ startDate, endDate }) => {
      const noEndDateSet = endDate === null;
      const isUpdatingStartDate = startDate && !isEqual(startDate, nextDates.startDate);

      return noEndDateSet || isUpdatingStartDate ? FOCUS.END_DATE : FOCUS.NONE;
    },
    [nextDates],
  );

  const handleFocusedInput = useCallback(
    ({ openModal }) =>
      (nextFocusedInput) => {
        setFocusedInput(nextFocusedInput);
        openModal();
      },
    [setFocusedInput],
  );

  const handleApply = useCallback(
    (event) => {
      if (event && !submitOnChange) event.preventDefault();
      if (!isDirty()) return;

      if (isValid()) {
        applyChange();
      } else {
        cancelChange();
      }

      setFocusedInput(FOCUS.NONE);
    },
    [isDirty, applyChange, cancelChange, isValid, submitOnChange],
  );

  const handleCancel = useCallback(() => {
    cancelChange();
  }, [cancelChange]);

  const handleChangeDateRange = useCallback(
    (dates) => {
      const nextFocusedInput = nextInputFocus(dates);
      setNextDates(dates);
      setFocusedInput(nextFocusedInput);
    },
    [nextInputFocus],
  );

  return (
    <ResponsiveModal title="Select dates" onSubmit={handleApply} onCancel={handleCancel} onBlur={handleApply}>
      {({ isOpen, openModal, cancelModal, submitModal }) => (
        <Fragment>
          <Box position="relative">
            {!isOpen && (
              <PhoneDateDisplay
                display={['block', 'none']}
                range={nextDates}
                handleFocusedInput={handleFocusedInput({ openModal })}
                labelOptions={labelOptions}
                error={error}
              />
            )}
            <DateDisplay
              display={['none', 'flex']}
              range={nextDates}
              handleFocusedInput={handleFocusedInput({ openModal })}
              focusedInput={focusedInput}
              labelOptions={labelOptions}
              error={error}
            />
            {isOpen && (
              <Dropdown anchorX={anchorX} viewThreshold={viewThreshold} skipToContent="#main-content">
                <Box px={[0, 10, 10]} py={[0, 5, 5]} data-testid="date-range-picker">
                  <Calendar
                    minDate={minDate}
                    initialDisplayDate={initialDisplayDate}
                    monthsToDisplay={isLessThanBreakpoint(1) ? 1 : 2}
                    startDate={nextDates.startDate}
                    endDate={nextDates.endDate}
                    onChangeDates={handleChangeDateRange}
                    focusedInput={focusedInput}
                    boundaries={boundaries}
                  />
                </Box>
                <Actions display={['none', 'block']}>
                  <Flex justifyContent="space-between">
                    <TextButton onClick={cancelModal} textStyle="cancel" fontSize="base" data-testid="cancel-button">
                      Cancel
                    </TextButton>
                    {isValid() && (
                      <TextButton onClick={submitModal} fontSize="base" textDecoration="none" data-testid="done-button">
                        Done
                      </TextButton>
                    )}
                  </Flex>
                </Actions>
              </Dropdown>
            )}
          </Box>
        </Fragment>
      )}
    </ResponsiveModal>
  );
};

DatePicker.propTypes = {
  selectedDates: PropTypes.shape({
    startDate: PropTypes.instanceOf(Date),
    endDate: PropTypes.instanceOf(Date),
  }),
  labelOptions: PropTypes.object,
  updateQuery: PropTypes.func.isRequired,
  anchorX: PropTypes.oneOf(['left', 'right']),
  viewThreshold: PropTypes.number,
  submitOnChange: PropTypes.bool,
  minDate: PropTypes.instanceOf(Date),
  initialDisplayDate: PropTypes.instanceOf(Date),
  boundaries: PropTypes.shape({
    start: PropTypes.instanceOf(Date),
    end: PropTypes.instanceOf(Date),
  }),
  error: PropTypes.bool,
};

DatePicker.defaultProps = {
  selectedDates: {},
  labelOptions: {},
  anchorX: 'right',
  viewThreshold: 0.5,
  submitOnChange: true,
  minDate: subDays(new Date(), 1),
  initialDisplayDate: new Date(),
  boundaries: {},
  error: false,
};

export default DatePicker;
