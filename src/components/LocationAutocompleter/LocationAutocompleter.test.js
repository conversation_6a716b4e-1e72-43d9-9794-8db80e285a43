import React from 'react';
import LocationAutocompleter from './LocationAutocompleter';
import TextHighlighter from 'components/TextHighlighter';
import { mountUtils } from 'test-utils';
import ResponsiveModal from 'components/ResponsiveModal';
import { useLocationData } from './useLocationData';
import { when } from 'jest-when';

jest.mock('./useLocationData');
jest.mock('store/ui/uiSelectors');

mountUtils.mockComponent(TextHighlighter);

jest.useFakeTimers();

const initialLocationName = 'Sydney, NSW, Australia';
const searchText = 'mel';
const updateQuery = jest.fn();
const routeToProperty = jest.fn();

const defaultProps = {
  locationName: initialLocationName,
  updateQuery: updateQuery,
  routeToProperty: routeToProperty,
};

const render = (props = {}) =>
  mountUtils(<LocationAutocompleter {...defaultProps} {...props} />, { decorators: { store: true, theme: true } });

beforeEach(() => {
  jest.clearAllMocks();
  useLocationData.mockReturnValue([]);
});

describe('initial rendering', () => {
  it('renders the location name', () => {
    const { findByTestId } = render();
    expect(findByTestId('location-search-input')).toHaveProp({ placeholder: initialLocationName, value: '' });
  });

  it('sets the title on the ResponsiveModal', () => {
    const { find } = render();
    expect(find(ResponsiveModal)).toHaveProp({ title: 'Find destination or hotel' });
  });

  it('does not render the Footer', () => {
    const { find } = render();
    expect(find('Footer')).not.toExist();
  });
});

describe('entering the search input', () => {
  it('clears the input field on enter', () => {
    const { findByTestId } = render();

    expect(findByTestId('location-search-input')).toHaveProp({ placeholder: initialLocationName, value: '' });

    findByTestId('location-search-input').simulate('focus');

    expect(findByTestId('location-search-input')).toHaveProp({ placeholder: 'Destination, city, hotel name', value: '' });
  });

  it('opens the responsive modal for mobile devices', () => {
    const { findByTestId } = render();

    findByTestId('location-search-input').simulate('focus');

    expect(findByTestId('close-modal')).toHaveLength(1);
  });
});

describe('with a successful search', () => {
  const locationResult = { type: 'location', fullName: 'Melbourne, VIC, Australia' };
  const propertyResult = { type: 'property', fullName: 'Hilton Melbourne, Melbourne, Australia', id: 1 };

  beforeEach(async () => {
    when(useLocationData)
      .mockReturnValue([])
      .calledWith({ locationName: searchText, returnProperties: true })
      .mockReturnValue([locationResult, propertyResult]);
  });

  it('displays the results', () => {
    const { find, findByTestId } = render();

    findByTestId('location-search-input').simulate('focus', { target: {} });
    findByTestId('location-search-input').simulate('change', { target: { value: searchText } });

    expect(find('UpdatedResultItem').at(0).find('Icon').prop('name')).toEqual('place');
    expect(find('UpdatedResultItem').at(0).find('TextHighlighter')).toHaveProp({ text: locationResult.fullName });
    expect(find('UpdatedResultItem').at(1).find('Icon').prop('name')).toEqual('localHotel');
    expect(find('UpdatedResultItem').at(1).find('TextHighlighter')).toHaveProp({ text: propertyResult.fullName });
  });

  it('highlights the first item in the results', () => {
    const { find, findByTestId } = render();

    findByTestId('location-search-input').simulate('focus', { target: {} });
    findByTestId('location-search-input').simulate('change', { target: { value: searchText } });

    expect(find('UpdatedResultItem').at(0)).toHaveProp({ highlighted: true });
    expect(find('UpdatedResultItem').at(1)).toHaveProp({ highlighted: false });
  });

  it('highlights the search text in the result item text', () => {
    const { find, findByTestId } = render();

    findByTestId('location-search-input').simulate('focus', { target: {} });
    findByTestId('location-search-input').simulate('change', { target: { value: searchText } });

    expect(find('TextHighlighter').at(0)).toHaveProp({ text: locationResult.fullName, highlightText: searchText });
    expect(find('TextHighlighter').at(1)).toHaveProp({ text: propertyResult.fullName, highlightText: searchText });
  });

  describe('selecting a location', () => {
    it('calls updateQuery with new location', () => {
      const { find, findByTestId } = render();
      const searchInputElement = { blur: jest.fn() };

      findByTestId('location-search-input').simulate('focus', { target: searchInputElement });
      findByTestId('location-search-input').simulate('change', { target: { value: searchText } });
      find('UpdatedResultItem').at(0).simulate('click');

      expect(updateQuery).toHaveBeenCalledWith({ location: locationResult.fullName });
    });

    it('sets the search input field value to the selected item name', () => {
      const { find, findByTestId } = render();
      const searchInputElement = { blur: jest.fn() };

      findByTestId('location-search-input').simulate('focus', { target: searchInputElement });
      findByTestId('location-search-input').simulate('change', { target: { value: searchText } });
      find('UpdatedResultItem').at(0).simulate('click');

      expect(findByTestId('location-search-input')).toHaveProp({ value: locationResult.fullName });
    });
  });

  describe('When focusing the input with existing value', () => {
    it('opens the responsive modal', () => {
      const { find, findByTestId } = render();
      const searchInputElement = { blur: jest.fn() };

      findByTestId('location-search-input').simulate('focus', { target: searchInputElement });
      findByTestId('location-search-input').simulate('change', { target: { value: searchText } });
      find('UpdatedResultItem').at(0).simulate('click');
      findByTestId('location-search-input').simulate('focus', { target: searchInputElement });

      expect(find('ResponsiveContainer')).toHaveProp({ transitionState: 'entering' });
    });
  });

  describe('When the input is blurred', () => {
    it('resets the input value', async () => {
      const { findByTestId } = render();

      findByTestId('location-search-input').simulate('focus', { target: {} });
      findByTestId('location-search-input').simulate('change', { target: { value: 'Devonport' } });

      expect(findByTestId('location-search-input')).toHaveProp({ value: 'Devonport' });

      findByTestId('location-search-input').simulate('blur', { target: {} });

      expect(findByTestId('location-search-input')).toHaveProp({ placeholder: initialLocationName, value: '' });
    });
  });

  describe('selecting a property', () => {
    it('routes to the property', () => {
      const { find, findByTestId } = render();

      findByTestId('location-search-input').simulate('focus', { target: {} });
      findByTestId('location-search-input').simulate('change', { target: { value: searchText } });
      find('UpdatedResultItem').at(1).simulate('click');

      expect(routeToProperty).toHaveBeenCalledWith({
        id: 1,
        propertyName: 'Hilton Melbourne, Melbourne, Australia',
        excludeParams: ['location', 'propertyName'],
      });
    });
  });
});

describe('with an unsuccessful search', () => {
  it('does not display any results', async () => {
    useLocationData.mockReturnValue([]);

    const { find } = render();

    expect(find('UpdatedResultItem')).not.toExist();
  });
});
