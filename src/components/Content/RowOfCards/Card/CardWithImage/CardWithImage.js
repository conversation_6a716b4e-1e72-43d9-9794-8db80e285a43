import SanityImage from 'components/Content/SanityImage';
import startCase from 'lodash/startCase';
import PropTypes from 'prop-types';
import React from 'react';
import { Flex, Heading } from '@qga/roo-ui/components';
import BlockMarkdown from 'components/Content/BlockMarkdown';
import getMarkdownForBlockContent from 'lib/sanity/getMarkdownForBlockContent';
import { imageSingleLayout } from '../primitives';
import { useDataLayer } from 'hooks/useDataLayer';

const CardWithImage = ({ image, heading, summary, callToAction = [], cardCount, termsUrl }) => {
  const { emitInteractionEvent } = useDataLayer();
  const mdContent = getMarkdownForBlockContent(summary);

  const handleCallToActionClick = () => {
    emitInteractionEvent({
      type: 'Card With Image',
      value: `${startCase(heading)} Selected`,
    });
  };

  return (
    <>
      {image.asset && <SanityImage {...imageSingleLayout(cardCount)} {...image} showCaption={false} size="small" mb={0} />}

      <Flex px={[5, 5, 7]} pt={5} height="100%" width="100%" flexDirection="column">
        {heading && (
          <Heading.h2 display="block" fontSize="lg" mb={3}>
            {heading}
          </Heading.h2>
        )}

        <BlockMarkdown
          mdContent={mdContent}
          callToAction={callToAction}
          onCallToActionClick={handleCallToActionClick}
          termsAndConditionsUrl={termsUrl}
        />
      </Flex>
    </>
  );
};

CardWithImage.propTypes = {
  heading: PropTypes.string.isRequired,
  image: PropTypes.object.isRequired,
  summary: PropTypes.array.isRequired,
  callToAction: PropTypes.array,
  termsUrl: PropTypes.string,
  cardCount: PropTypes.number.isRequired,
};

export default CardWithImage;
