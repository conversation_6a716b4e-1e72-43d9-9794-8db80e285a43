import React from 'react';
import { mountUtils } from 'test-utils';
import CardWithImage from './CardWithImage';
import { useDataLayer } from 'hooks/useDataLayer';
import { Heading } from '@qga/roo-ui/components';
import { act } from 'react-test-renderer';
import { mockCardWithImage, mockCardWithoutImage } from 'components/Content/RowOfCards/mocks';

jest.mock('hooks/useDataLayer');
const emitInteractionEvent = jest.fn();

mountUtils.mockComponent('BlockMarkdown');
mountUtils.mockComponent('SanityImage');

const decorators = { theme: true };
const render = () => mountUtils(<CardWithImage {...mockCardWithImage} cardCount={1} termsUrl="#terms-and-conditions" />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();

  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

it('shows the heading', () => {
  const { find } = render();

  expect(find(Heading.h2)).toHaveText('Heading text');
});

it('shows the image', () => {
  const { find } = render();

  expect(find('SanityImage')).toHaveProp({
    ...mockCardWithImage.image,
    height: ['220px', '220px', '100%'],
    minHeight: ['220px', '220px', '310px'],
    size: 'small',
    width: '100%',
  });
});

it('shows the content', () => {
  const { find } = render();

  expect(find('BlockMarkdown')).toHaveProp({
    mdContent: 'Summary text!',
    callToAction: mockCardWithImage.callToAction,
    onCallToActionClick: expect.any(Function),
  });
});

describe('with clicking the call to action', () => {
  it('emits an event to the data layer', () => {
    const { find } = render();

    act(() => {
      find('BlockMarkdown').prop('onCallToActionClick')();
    });

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Card With Image',
      value: 'Heading Text Selected',
    });
  });
});

describe('when the content contains terms and conditions', () => {
  it('renders with a disclaimerUrl', () => {
    const { find } = render();
    expect(find('BlockMarkdown')).toHaveProp({
      termsAndConditionsUrl: '#terms-and-conditions',
    });
  });
});

describe('when the image is missing', () => {
  it('renders without errors', () => {
    const { find } = mountUtils(<CardWithImage {...mockCardWithoutImage} cardCount={1} />, { decorators });

    expect(find('SanityImage')).not.toExist();
  });
});
