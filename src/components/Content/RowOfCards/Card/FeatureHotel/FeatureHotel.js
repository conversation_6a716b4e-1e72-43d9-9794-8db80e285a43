import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useMeasure } from 'react-use';
import { Flex, Box, Heading, Text, ImageGallery } from '@qga/roo-ui/components';
import { useModal } from 'lib/hooks';
import PromotionalSash from 'components/PromotionalSash';
import BlockMarkdown from 'components/Content/BlockMarkdown';
import FullscreenImageGallery from 'components/FullscreenImageGallery';
import getMarkdownForBlockContent from 'lib/sanity/getMarkdownForBlockContent';
import { useDataLayer } from 'hooks/useDataLayer';

const MINIMUM_SINGLE_CARD_HEIGHT = 480;

const FeatureHotel = ({ callToAction, content, cardCount, promotionName, property, termsUrl, index }) => {
  const { openModal, modalProps } = useModal();
  const [startIndex, setStartIndex] = useState(0);
  const [boxHeightRef, { height: boxHeight }] = useMeasure();
  const { emitInteractionEvent } = useDataLayer();
  const mdContent = getMarkdownForBlockContent(content);
  const galleryHeight = boxHeight >= MINIMUM_SINGLE_CARD_HEIGHT ? boxHeight : MINIMUM_SINGLE_CARD_HEIGHT;
  const imageGalleryList = property?.images.map(({ urlLarge, urlSmall, caption }, index) => ({
    src: urlLarge,
    alt: caption,
    thumbnail: urlSmall,
    index: index + 1,
    total: property?.images?.length,
  }));

  const handleCallToActionClick = () => {
    emitInteractionEvent({
      type: 'Feature Hotel',
      value: `${property?.name} Selected`,
      customAttributes: {
        order: index + 1,
        promotionName,
        propertyName: property?.name,
        propertyId: property?.id,
      },
    });
  };

  const onFullscreenOpened = (startIndex) => {
    setStartIndex(startIndex);
    openModal();
  };

  return (
    <>
      {promotionName && (
        <Box position="absolute" zIndex={1} ml={4}>
          <PromotionalSash promotionName={promotionName} />
        </Box>
      )}
      <Flex
        flexDirection={cardCount === 1 ? ['column', 'column', 'row'] : 'column'}
        data-testid="card-count-1"
        justifyContent="space-between"
        ref={boxHeightRef}
      >
        <Box width={cardCount === 1 ? ['100%', '100%', '50%'] : '100%'}>
          {property?.images?.length > 0 && (
            <ImageGallery
              images={imageGalleryList}
              mainImageIndex={0}
              onOpen={onFullscreenOpened}
              height={cardCount === 4 ? 195 : cardCount === 1 ? [220, 220, galleryHeight] : 220}
            />
          )}
        </Box>

        <Flex px={[5, 5, 7]} pt={5} height="100%" width={cardCount === 1 ? ['100%', '100%', '50%'] : '100%'} flexDirection="column">
          {property?.name && (
            <>
              <Heading.h3 mb={0} data-testid="property-name">
                {property?.name}
              </Heading.h3>
              <Text color="greys.steel" data-testid="property-suburb">
                {property?.suburb}
              </Text>
            </>
          )}

          {content && (
            <BlockMarkdown
              mdContent={mdContent}
              callToAction={callToAction}
              onCallToActionClick={handleCallToActionClick}
              termsAndConditionsUrl={termsUrl}
            />
          )}
        </Flex>
      </Flex>

      <FullscreenImageGallery {...modalProps} images={imageGalleryList} startIndex={startIndex} />
    </>
  );
};

FeatureHotel.propTypes = {
  callToAction: PropTypes.array,
  cardCount: PropTypes.number,
  content: PropTypes.array,
  promotionName: PropTypes.string,
  property: PropTypes.shape({
    name: PropTypes.string,
    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    suburb: PropTypes.string,
    images: PropTypes.arrayOf(
      PropTypes.shape({
        caption: PropTypes.string,
        urlLarge: PropTypes.string,
      }),
    ),
  }),
  termsUrl: PropTypes.string,
  index: PropTypes.number,
};

export default FeatureHotel;
