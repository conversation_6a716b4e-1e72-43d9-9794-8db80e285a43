import React from 'react';
import { mountUtils } from 'test-utils';
import FeatureHotel from './FeatureHotel';
import { useDataLayer } from 'hooks/useDataLayer';
import { mockFeatureHotel } from 'components/Content/RowOfCards/mocks';

jest.mock('hooks/useDataLayer');
const emitInteractionEvent = jest.fn();

mountUtils.mockComponent('ImageGallery');
mountUtils.mockComponent('FullscreenImageGallery');
mountUtils.mockComponent('BlockMarkdown');

const decorators = { theme: true };
const render = (props) => mountUtils(<FeatureHotel {...mockFeatureHotel} {...props} />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

it('renders the image gallery with images from sanity content', () => {
  const { find } = render();

  const mockParsedImages = mockFeatureHotel.property.images.map(({ urlLarge, urlSmall, caption }, index) => ({
    src: urlLarge,
    alt: caption,
    thumbnail: urlSmall,
    index: index + 1,
    total: 2,
  }));

  expect(find('ImageGallery')).toExist();
  expect(find('ImageGallery')).toHaveProp({
    images: mockParsedImages,
  });
});

it('renders the property name and suburb', () => {
  const { findByTestId } = render();

  expect(findByTestId('property-name')).toHaveText('The Langham Melbourne');
  expect(findByTestId('property-suburb')).toHaveText('Southbank');
});

it('renders the BlockMarkdown with mdContent and callToAction props', () => {
  const { find } = render();

  expect(find('BlockMarkdown')).toHaveProp('mdContent', mockFeatureHotel.content[0].children[0].text);
  expect(find('BlockMarkdown')).toHaveProp('callToAction', mockFeatureHotel.callToAction);
});

it('passes a call to action click handler as prop', () => {
  const { find } = render();
  expect(find('BlockMarkdown')).toHaveProp('onCallToActionClick', expect.any(Function));
});

describe('when there is no promotion', () => {
  it('does not render <PromotionalSash />', () => {
    const { find } = render();
    expect(find('PromotionalSash')).not.toExist();
  });
});

describe('when there is a promotion', () => {
  it('shows <PromotionalSash /> with the promotion name', () => {
    const { find } = render({ promotionName: 'Mantra Sale' });

    expect(find('PromotionalSash')).toExist();
    expect(find('PromotionalSash')).toHaveProp('promotionName', 'Mantra Sale');
  });
});

describe('when there is a 4up hotel layout', () => {
  it('displays <ImageGallery /> with a medium imageSize', () => {
    const { find } = render({ cardCount: 4 });
    expect(find('ImageGallery')).toHaveProp('height', 195);
  });
});

describe('when there is a two hotel layout', () => {
  it('displays <ImageGallery /> with a large imageSize', () => {
    const { find } = render({ cardCount: 2 });
    expect(find('ImageGallery')).toHaveProp('height', 220);
  });
});

describe('when there is a three hotel layout', () => {
  it('displays <ImageGallery /> with a large imageSize', () => {
    const { find } = render({ cardCount: 3 });
    expect(find('ImageGallery')).toHaveProp('height', 220);
  });
});

describe('when there is a single hotel layout', () => {
  it('displays <ImageGallery /> with the default height for desktop', () => {
    const { find } = render({ cardCount: 1 });
    expect(find('ImageGallery')).toHaveProp('height', [220, 220, 480]);
  });
});

describe('when the content does not contain terms and conditions', () => {
  it('renders <BlockMarkdown /> without a disclaimer url', () => {
    const { find } = render();
    expect(find('BlockMarkdown')).not.toHaveProp({
      termsAndConditionsUrl: '#terms-and-conditions',
    });
  });
});

describe('when the content contains terms and conditions', () => {
  it('renders <BlockMarkdown /> with a disclaimerUrl', () => {
    const { find } = render({ termsUrl: '#terms-and-conditions' });
    expect(find('BlockMarkdown')).toHaveProp({
      termsAndConditionsUrl: '#terms-and-conditions',
    });
  });
});
