import React from 'react';
import PropTypes from 'prop-types';
import get from 'lodash/get';
import startCase from 'lodash/startCase';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { OutlineButton, Flex, Button, Icon, NakedButton } from '@qga/roo-ui/components';
import { useDataLayer } from 'hooks/useDataLayer';

const LinkButton = styled(NakedButton)`
  color: ${themeGet('colors.ui.link')};
  font-weight: ${themeGet('fontWeights.bold')};
  text-decoration: none;
`;

const ArrowDownward = () => {
  return <Icon name="arrowDownward" ml={2} mt={-1} />;
};
ArrowDownward.displayName = 'ArrowDownwardIcon';

const ArrowForward = () => {
  return <Icon name="arrowForward" ml={2} mt={-1} />;
};
ArrowForward.displayName = 'ArrowForwardIcon';

const ICON_FOR_BUTTON = {
  anchor: ArrowDownward,
  link: ArrowForward,
};

const BUTTON_TYPES = {
  primary: Button,
  secondary: OutlineButton,
  link: LinkButton,
};

const ButtonContent = ({ text, isNewTab, url, linkType, anchorLink, alignment, buttonType, onClick, trackingEvent }) => {
  const { emitInteractionEvent } = useDataLayer();
  const Component = get(BUTTON_TYPES, buttonType);
  const IconComponent = get(ICON_FOR_BUTTON, buttonType) || get(ICON_FOR_BUTTON, linkType);

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      const { category, action } = trackingEvent;
      emitInteractionEvent({ type: category || 'Call to Action', value: action || `${startCase(text)} Selected` });
    }
  };

  const buttonProps = {
    as: 'a',
    variant: 'primary',
    width: ['100%', 'auto'],
    href: linkType === 'anchor' ? `#${anchorLink}` : url,
    onClick: handleClick,
    ...(isNewTab && { target: '_blank', rel: 'noopener noreferrer' }),
  };

  return (
    <Flex width="100%" justifyContent={alignment} flex={1}>
      <Component {...buttonProps}>
        {text} {IconComponent && <IconComponent />}
      </Component>
    </Flex>
  );
};

ButtonContent.propTypes = {
  text: PropTypes.string,
  linkType: PropTypes.string,
  url: PropTypes.string,
  buttonType: PropTypes.string,
  alignment: PropTypes.string,
  anchorLink: PropTypes.string,
  isNewTab: PropTypes.bool,
  onClick: PropTypes.func,
  trackingEvent: PropTypes.shape({
    category: PropTypes.string,
    action: PropTypes.string,
  }),
};

ButtonContent.defaultProps = {
  text: '',
  linkType: '',
  url: '',
  buttonType: 'primary',
  alignment: 'flex-start',
  anchorLink: '',
  isNewTab: false,
  trackingEvent: {},
};

export default ButtonContent;
