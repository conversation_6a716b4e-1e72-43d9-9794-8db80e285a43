import styled from '@emotion/styled';
import AppLink from 'components/AppLink';
import { Box } from '@qga/roo-ui/components';

export const StyledAppLink = styled(AppLink)`
  display: flex;
  flex-direction: column;
  text-decoration: none;
  transition: all 0.25s ease-in;
  width: 100%;

  &:hover {
    h3 {
      text-decoration: underline;
      text-decoration-color: inherit;
    }
  }
`;

export const ImageWrapper = styled(Box)`
  flex-grow: 1;
`;
