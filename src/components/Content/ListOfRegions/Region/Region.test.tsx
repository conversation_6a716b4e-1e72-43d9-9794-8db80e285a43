import React from 'react';
import { mountUtils, mocked } from 'test-utils';
import Region from './Region';
import { useDataLayer } from 'hooks/useDataLayer';
import { Heading } from '@qga/roo-ui/components';
import { mockRegion } from '../mocks';

mountUtils.mockComponent('Image');
mountUtils.mockComponent('ImageFallback');

jest.mock('hooks/useDataLayer');
jest.mock('@qga/sanity-components');
const emitInteractionEvent = jest.fn();

const decorators = { store: true, theme: true };
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const render = (props?: any) => mountUtils(<Region {...(mockRegion as any)} {...props} />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();

  mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
});

it('shows the heading', () => {
  const { find } = render();

  expect(find(Heading.h3)).toHaveText('Sydney');
});

it('shows the image', () => {
  const { find } = render();

  expect(find('Image')).toHaveProp({
    alt: 'Foo',
    src: 'https://cdn.sanity.io/images/abcdefghi/qh-test/9b5a7bab1952f0428eeef065fd4b89e1b0421038-2500x1464.png?rect=0,33,2500,1397&w=340&h=190&q=70&auto=format',
  });
});

it('shows the fallback image', () => {
  const { find } = render({ image: undefined });

  expect(find('ImageFallback')).toExist();
});

it('shows the breadcrumbs', () => {
  const { findByTestId } = render();

  expect(findByTestId('region-crumbs')).toHaveText('NSW, Australia');
});

describe('when clicking the link', () => {
  it('emits an event to the data layer', () => {
    const { findByTestId } = render();

    findByTestId('region-link').simulate('click');

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Region Link',
      value: 'Sydney, NSW, Australia Selected',
    });
  });
});
