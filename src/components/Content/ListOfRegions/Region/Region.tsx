import React, { useCallback } from 'react';
import { Flex, Heading, Icon, Image, Text } from '@qga/roo-ui/components';
import { Region as Props } from 'types/content';
import { useDataLayer } from 'hooks/useDataLayer';
import { getImage } from 'lib/sanity';
import { StyledAppLink, ImageWrapper } from './Region.style';
import ImageFallback from 'components/Image/ImageFallback';

const Region = ({ fullName, image: imageAsset, url }: Props) => {
  const { emitInteractionEvent } = useDataLayer();

  const handleCallToActionClick = useCallback(() => {
    emitInteractionEvent({
      type: 'Region Link',
      value: `${fullName} Selected`,
    });
  }, [emitInteractionEvent, fullName]);

  const [regionName, ...crumbs] = (fullName || '').split(', ');
  const searchUrl = url ? url : `/search/list?location=${fullName}`;

  const { name: alt, asset } = imageAsset || {};
  const image = asset ? getImage(asset.asset, { width: 340, height: 190 }) : null;

  return (
    <Flex>
      <StyledAppLink href={null} target={null} to={searchUrl} onClick={handleCallToActionClick} data-testid="region-link">
        <ImageWrapper>
          {image && <Image alt={alt} src={image.custom} width="100%" />}
          {!image && <ImageFallback alt="Region image placeholder" width="100%" height="100%" />}
        </ImageWrapper>

        <Flex pt={2} width="100%" flexDirection="column">
          <Heading.h3 display="block" fontSize={['base', 'md']} mb={1} color="greys.charcoal">
            {regionName}
            <Icon name="chevronRight" ml={2} />
          </Heading.h3>
          <Text display="block" fontSize="sm" mb={3} color="greys.steel" textDecoration="none" data-testid="region-crumbs">
            {crumbs.join(', ')}
          </Text>
        </Flex>
      </StyledAppLink>
    </Flex>
  );
};

export default Region;
