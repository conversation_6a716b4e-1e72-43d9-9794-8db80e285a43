import { useDataLayer } from 'hooks/useDataLayer';
import React from 'react';
import { Heading } from '@qga/roo-ui/components';
import { mocked, mountUtils } from 'test-utils';
import ListOfRegions from './ListOfRegions';
import { mockRegions, mockRegion } from './mocks';

mountUtils.mockComponent('Region');
jest.mock('hooks/useDataLayer');
const emitInteractionEvent = jest.fn();

const decorators = { store: true, theme: true };
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const render = () => mountUtils(<ListOfRegions {...(mockRegions[0] as any)} />, { decorators });

const firstLinkName = mockRegion.name;
const firstLinkFullName = mockRegion.fullName;

describe('with three cards', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
  });

  it('renders the wrapper with the correct styles', () => {
    const { find } = render();

    expect(find(Heading.h2)).toHaveText(mockRegions[0].title);
  });

  it('render the regions', () => {
    const { findByTestId } = render();
    const results = findByTestId('region-link');

    expect(results).toHaveLength(2);
    expect(results.at(0)).toHaveText(firstLinkName + ' hotels');
  });

  describe('when clicking a link', () => {
    it('emits an event to the data layer', () => {
      const { findByTestId } = render();

      findByTestId('region-link').first().children().first().simulate('click');

      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Destination links',
        value: `${firstLinkFullName} Selected`,
      });
    });
  });
});
