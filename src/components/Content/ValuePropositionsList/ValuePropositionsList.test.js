import React from 'react';
import ValuePropositionsList from './ValuePropositionsList';
import { mountUtils } from 'test-utils';
import { getImage } from 'lib/sanity';

jest.mock('lib/sanity');
const cardsContent = [
  {
    description: 'Earn 3 Qantas Points per $1 spent^, or use points to book**',
    iconName: {
      _type: 'common.hotels.image',
      asset: { _ref: 'image-639b67cdfb46e4277246894f8a78c1ab33570a5f-1516x592-jpg', _type: 'reference' },
      attribution: 'Attribution text',
      caption: 'Image caption',
    },
    title: 'Point Plus Pay',
  },
  {
    description: 'Search thousands of hotels across Australia and New Zealand',
    iconName: {
      _type: 'common.hotels.image',
      asset: { _ref: 'image-639b67cdfb46e4277246894f8a78c1ab33570a5f-1516x592-jpg', _type: 'reference' },
      attribution: 'Attribution text',
      caption: 'Image caption',
    },
    title: 'Hotels ANZ',
  },
  {
    description: 'No booking fees',
    iconName: {
      _type: 'common.hotels.image',
      asset: { _ref: 'image-639b67cdfb46e4277246894f8a78c1ab33570a5f-1516x592-jpg', _type: 'reference' },
      attribution: 'Attribution text',
      caption: 'Image caption',
    },
    title: 'No fees',
  },
];

const mockAsset = {
  _type: 'common.hotels.image',
  asset: {
    _ref: 'image-639b67cdfb46e4277246894f8a78c1ab33570a5f-1516x592-jpg',
    _type: 'reference',
  },
  attribution: 'Attribution text',
  caption: 'Image caption',
};

const mockImage = {
  alt: 'image caption',
  height: '100%',
  onLoad: jest.fn(),
  src: 'https://cdn.sanity.io/images/abcdefghi/qh-test/id-50x20.jpg?w=1100',
  srcSet:
    'https://cdn.sanity.io/images/abcdefghi/qh-test/id-50x20.jpg?w=480 480w, https://cdn.sanity.io/images/abcdefghi/qh-test/id-50x20.jpg?w=768 768w, https://cdn.sanity.io/images/abcdefghi/qh-test/id-50x20.jpg?w=1100 1100w, https://cdn.sanity.io/images/abcdefghi/qh-test/id-50x20.jpg?w=1280',
  width: '100%',
  attribution: true,
};

const valuePropositions = [
  {
    iconName: mockAsset,
    title: 'Point Plus Pay',
    description: 'Earn 3 Qantas Points per $1 spent^, or use points to book**',
  },
  { iconName: mockAsset, title: 'Hotels ANZ', description: 'Search thousands of hotels across Australia and New Zealand' },
  { iconName: mockAsset, title: 'No fees', description: 'No booking fees' },
];

const defaultProps = {
  title: 'Value Propositions',
  valuePropositions: valuePropositions,
};

const slimlineProps = {
  title: 'Value Propositions',
  valuePropositions: valuePropositions,
  layout: 'slimline',
};
const decorators = { theme: true };
const render = (props) => mountUtils(<ValuePropositionsList {...props} />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
  getImage.mockReturnValue(mockImage);
});

describe.skip('<ValuePropositionList />', () => {
  it('renders the heading', () => {
    const { find } = render(defaultProps);

    expect(find('Heading')).toExist();
  });

  it('renders all 3 cards with the correct icon and text', () => {
    const { findByTestId } = render(defaultProps);

    findByTestId('elements-wrapper')
      .children()
      .map((node, index) => expect(node.props()).toEqual(cardsContent[index]));
  });

  it('renders <Image />', () => {
    const { find } = render(defaultProps);
    const result = find('Image');

    expect(result).toExist();
  });

  it('calls getImage() for each item in valuePropositions', () => {
    render(defaultProps);
    expect(getImage).toHaveBeenCalledTimes(3);
  });

  describe('with layout type as standard', () => {
    it('renders the standard version of QVP', () => {
      expect(render(defaultProps).find('[data-testid="standard-layout"]')).toExist();
    });
  });

  describe('with layout type as slimline', () => {
    it('renders the slimline version of QVP', () => {
      expect(render(slimlineProps).find('[data-testid="slimline-layout"]')).toExist();
    });
  });
});
