import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { Link, Flex } from '@qga/roo-ui/components';
import ButtonContent from 'components/Content/ButtonContent';
import Markdown from 'components/Markdown';
import { useDataLayer } from 'hooks/useDataLayer';

export const TermsLink = styled(Link)`
  color: ${themeGet('colors.greys.dusty')};
  font-size: ${themeGet('fontSizes.xs')};
  text-decoration: underline;
  padding-top: ${themeGet('space.4')};

  &:hover {
    color: ${themeGet('colors.greys.dusty')};
  }
`;

const BlockMarkdown = React.memo(({ mdContent, height, callToAction, onCallToActionClick, termsAndConditionsUrl, isStacked }) => {
  const { emitInteractionEvent } = useDataLayer();

  const handleTermsClick = useCallback(() => {
    emitInteractionEvent({ type: 'Terms and Conditions Link', value: 'Conditions Apply Selected' });
  }, [emitInteractionEvent]);

  return (
    <Flex height={height} flexDirection="column" justifyContent="space-between" pt={2}>
      <Markdown content={mdContent} isCustomHeading={false} />

      {(callToAction?.length > 0 || termsAndConditionsUrl) && (
        <Flex
          pt={3}
          pb={6}
          alignItems={[isStacked ? 'center' : 'flex-end', 'flex-end']}
          flexDirection={[isStacked ? 'column' : 'row', 'row']}
        >
          {callToAction?.map((button) => (
            <ButtonContent data-testid="button-content" key={`call-to-action-${button.key}`} onClick={onCallToActionClick} {...button} />
          ))}

          {termsAndConditionsUrl && (
            <TermsLink href={termsAndConditionsUrl} onClick={handleTermsClick}>
              Conditions apply
            </TermsLink>
          )}
        </Flex>
      )}
    </Flex>
  );
});

BlockMarkdown.displayName = 'BlockMarkdown';

BlockMarkdown.propTypes = {
  callToAction: PropTypes.array,
  height: PropTypes.string,
  isStacked: PropTypes.bool,
  mdContent: PropTypes.string,
  onCallToActionClick: PropTypes.func,
  termsAndConditionsUrl: PropTypes.string,
};

BlockMarkdown.defaultProps = {
  callToAction: [],
  height: '100%',
  isStacked: false,
  mdContent: '',
  termsAndConditionsUrl: '',
};

export default BlockMarkdown;
