import PropTypes from 'prop-types';
import styled from '@emotion/styled';
import React, { useState } from 'react';
import { Flex, Text } from '@qga/roo-ui/components';
import Image from 'components/Image';
import { getImage } from 'lib/sanity';

const Caption = styled(Text)`
  align-self: flex-start;
  display: block;
`;

/**
 * The default props are for the component's usage as its own independent Section
 */
const SanityImage = ({
  asset,
  caption,
  showCaption = false,
  size = 'large',
  maxWidth = ['100%', '100%', '1600px'],
  width = '100%',
  height = [220, 440],
  ...rest
}) => {
  const [ready, setReady] = useState(false);
  const { srcSet, ...imageUrls } = getImage(asset);

  return (
    <Flex maxWidth={maxWidth} flexDirection="column" alignItems="center" mb={5} width={width} {...rest}>
      <Image
        maxWidth="100%"
        width={width}
        height={height}
        src={imageUrls[size]}
        srcSet={srcSet}
        alt={caption}
        onLoad={() => setReady(true)}
      />
      {showCaption && ready && <Caption>{caption}</Caption>}
    </Flex>
  );
};

SanityImage.propTypes = {
  asset: PropTypes.shape({
    asset: PropTypes.shape({
      _ref: PropTypes.string.isRequired,
      _type: PropTypes.string.isRequired,
    }),
    crop: PropTypes.object,
    hotspot: PropTypes.object,
  }).isRequired,
  caption: PropTypes.string.isRequired,
  showCaption: PropTypes.bool,
  size: PropTypes.string,
  height: PropTypes.oneOfType([PropTypes.string, PropTypes.number, PropTypes.array]),
  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number, PropTypes.array]),
  maxWidth: PropTypes.oneOfType([PropTypes.string, PropTypes.number, PropTypes.array]),
};

export default SanityImage;
