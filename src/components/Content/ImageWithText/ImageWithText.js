import React from 'react';
import PropTypes from 'prop-types';
import { Box, Flex, Image } from '@qga/roo-ui/components';
import { getImage } from 'lib/sanity';
import getMarkdownForBlockContent from 'lib/sanity/getMarkdownForBlockContent';
import Markdown from 'components/Markdown';
import Button from 'components/Content/ButtonContent';

const ImageWithText = ({
  callToAction,
  content,
  image,
  imageAlign = 'left',
  backgroundColor = 'greys.porcelain',
  padding = 0,
  imagePadding = 0,
}) => {
  const mdContent = getMarkdownForBlockContent(content);
  const { asset, caption } = image;
  const sanityImage = getImage(asset);

  const horizontalAlignment = imageAlign === 'left' ? 'row' : 'row-reverse';
  const textPadding = imageAlign === 'left' ? { ml: [4, 6], mr: [4, 0] } : { ml: [4, 0], mr: [4, 6] };

  return (
    <Flex
      flexDirection={['column', horizontalAlignment]}
      alignItems="center"
      maxWidth="default"
      backgroundColor={backgroundColor}
      padding={padding}
    >
      <Image width={['100%', '50%']} mb={[6, 0]} alt={caption} src={sanityImage.medium} padding={imagePadding} />
      <Box {...textPadding}>
        <Markdown content={mdContent} isCustomHeading={false} />
        <Button {...callToAction} alignment="left" />
      </Box>
    </Flex>
  );
};

ImageWithText.propTypes = {
  content: PropTypes.array.isRequired,
  image: PropTypes.object.isRequired,
  callToAction: PropTypes.object.isRequired,
  imageAlign: PropTypes.string,
  backgroundColor: PropTypes.string,
  padding: PropTypes.number,
  imagePadding: PropTypes.number,
};

export default ImageWithText;
