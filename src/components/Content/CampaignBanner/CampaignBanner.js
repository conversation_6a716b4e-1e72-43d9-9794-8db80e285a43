import PropTypes from 'prop-types';
import React from 'react';
import { getPromotionMessage } from './utils/getPromotionMessage';
import styled from '@emotion/styled';
import { Box } from '@qga/roo-ui/components';
import BlockMarkdown from '../BlockMarkdown/BlockMarkdown';
import Hero from '../Hero';
import { themeGet } from 'styled-system';
import { mediaQuery } from 'lib/styledSystem';
import HeadingWithSlug from 'components/Content/HeadingWithSlug';
import getMarkdownForBlockContent from 'lib/sanity/getMarkdownForBlockContent';
import PromotionalSash from 'components/PromotionalSash';
import { CONTENT_TERMS_ANCHOR_ID } from 'components/Content/TermsAndConditions';

const CampaignBannerWrapper = styled(Box)`
  max-width: 100%;
  padding: ${themeGet('space.4')} ${themeGet('space.5')} 0;
  position: relative;
  text-align: left;
  background-color: white;
  ${mediaQuery.minWidth.md} {
    top: 0;
    left: 185px;
    position: absolute;
    padding: ${themeGet('space.4')} ${themeGet('space.10')} 0;
    max-width: 600px;
  }
`;

const CampaignBanner = ({ heading, description, heroImage, endDate, promotionMessage, callToAction, termsAndConditions, children }) => {
  const hasHeroImage = !!heroImage?.asset;
  const mdContent = getMarkdownForBlockContent(description);

  return (
    <Box data-testid="image-container" position="relative" width="100%">
      {hasHeroImage && <Hero image={heroImage} showsAttributionText={false} height={[220, 440]} />}

      <CampaignBannerWrapper>
        {(endDate || promotionMessage) && <PromotionalSash promotionName={endDate ? getPromotionMessage(endDate) : promotionMessage} />}
        {heading?.name && <HeadingWithSlug {...heading} textAlign={'left'} mt={2} />}
        <BlockMarkdown
          mdContent={mdContent}
          callToAction={callToAction}
          termsAndConditionsUrl={termsAndConditions?.disclaimers?.length > 0 ? `#${CONTENT_TERMS_ANCHOR_ID}` : ''}
          isStacked
        />
        {children}
      </CampaignBannerWrapper>
    </Box>
  );
};

CampaignBanner.propTypes = {
  heading: PropTypes.object.isRequired,
  heroImage: PropTypes.object,
  description: PropTypes.array,
  endDate: PropTypes.string.isRequired,
  promotionMessage: PropTypes.string,
  callToAction: PropTypes.array,
  termsAndConditions: PropTypes.shape({
    disclaimers: PropTypes.array,
  }),
  children: PropTypes.node,
};

CampaignBanner.defaultProps = {
  description: null,
  heroImage: null,
  promotionMessage: null,
  callToAction: [],
  termsAndConditions: {
    disclaimers: [],
  },
  children: null,
};

export default CampaignBanner;
