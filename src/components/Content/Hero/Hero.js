import styled from '@emotion/styled';
import { css } from '@emotion/core';
import { getImage } from 'lib/sanity';
import PropTypes from 'prop-types';
import React from 'react';
import { BackgroundImage, Container, Image, Text } from '@qga/roo-ui/components';
import { mediaQuery } from 'lib/styledSystem';
import { rem } from 'polished';

const FullHeightContainer = styled(Container)`
  align-items: flex-end;
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: ${(props) => (props.showBrandImage ? 'space-between' : 'flex-end')};
  max-width: ${rem('858px')};
  padding-bottom: ${rem('60px')};

  ${mediaQuery.minWidth.sm} {
    padding-bottom: ${rem('88px')};
  }
`;

const getBackgroundImage = (size) => css`
  background-image: linear-gradient(-180deg, rgba(147, 147, 147, 0) 0%, rgba(0, 0, 0, 0.2) 100%), url(${size});
`;

const ResponsiveBackgroundImage = styled(BackgroundImage)`
  ${(props) => getBackgroundImage(props.small)}

  ${mediaQuery.minWidth.sm} {
    ${(props) => getBackgroundImage(props.medium)}
  }

  ${mediaQuery.minWidth.md} {
    ${(props) => getBackgroundImage(props.large)}
  }

  ${mediaQuery.minWidth.lg} {
    ${(props) => getBackgroundImage(props.xLarge)}
  }
`;

const Hero = ({ brandLogo, image, showsAttributionText, ...rest }) => {
  const { asset, attribution, caption } = image;

  const backgroundImage = getImage(asset);
  const brandImage = getImage(brandLogo);

  return (
    <ResponsiveBackgroundImage {...rest} {...backgroundImage} role="img" aria-label={caption} mx={0} px={0} width={1}>
      <FullHeightContainer showBrandImage={!!brandImage}>
        {brandImage && <Image alt={brandLogo.caption} display="block" src={brandImage.small} height={40} mt={2} width="auto" />}
        {attribution && showsAttributionText && (
          <Text color="white" fontSize="md" display="block">
            {attribution}
          </Text>
        )}
      </FullHeightContainer>
    </ResponsiveBackgroundImage>
  );
};

Hero.propTypes = {
  attributionText: PropTypes.string,
  brandLogo: PropTypes.object,
  image: PropTypes.object.isRequired,
  showsAttributionText: PropTypes.bool,
};

Hero.defaultProps = {
  attributionText: null,
  brandLogo: null,
  showsAttributionText: true,
};

export default Hero;
