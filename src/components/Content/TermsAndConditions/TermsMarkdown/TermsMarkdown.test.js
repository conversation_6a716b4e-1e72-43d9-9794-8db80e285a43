import React from 'react';
import { HOTELS_HOST, HOTELS_PATH } from 'config';
import { mountUtils } from 'test-utils';
import TermsMarkdown from './TermsMarkdown';
import { useDataLayer } from 'hooks/useDataLayer';

jest.mock('hooks/useDataLayer');
const emitInteractionEvent = jest.fn();

const decorators = { theme: true, store: true };
const render = (props) => mountUtils(<TermsMarkdown {...props} />, { decorators });

it('renders markdown', () => {
  const content = 'This is a *sample* sentence';
  const { find } = render({ content });

  expect(find('TermsMarkdown').first().html()).toMatch(/This is a <em>sample<\/em> sentence/i);
});

describe('with custom components', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    useDataLayer.mockReturnValue({ emitInteractionEvent });
  });

  it('renders inline text as <DisclaimerText />', () => {
    const content = 'This is some inline text';
    const { find } = render({ content });

    expect(find('DisclaimerText').text()).toEqual('This is some inline text');
  });

  it('renders html paragraphs as <DisclaimerText />', () => {
    const content = '<p>This is some paragraph text</p>';
    const { find } = render({ content });

    expect(find('DisclaimerText').text()).toEqual('This is some paragraph text');
  });

  it('renders links as <Link />', () => {
    const content = `[Qantas](${HOTELS_HOST})`;
    const { find } = render({ content });

    const firstLink = find('InlineLink').first();

    expect(firstLink.text()).toEqual('Qantas');
    expect(firstLink.prop('href')).toMatchInlineSnapshot(`"http://test"`);
  });

  describe('when a link is clicked', () => {
    it('emits an event to the data layer for a hotels property link', () => {
      const content = `[Test Hotel](${HOTELS_HOST}${HOTELS_PATH}/properties/1234)`;
      const { find } = render({ content });

      const firstLink = find('InlineLink').first();

      firstLink.simulate('click');

      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Hotel Text Link',
        value: 'Test Hotel Selected',
        customAttributes: {
          href: `http://test${HOTELS_PATH}/properties/1234`,
        },
      });
    });

    it('emits an event to the data layer for any other terms and conditions link', () => {
      const content = `[Qantas](${HOTELS_HOST})`;
      const { find } = render({ content });

      const firstLink = find('InlineLink').first();

      firstLink.simulate('click');
      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Terms and Conditions Link',
        value: 'Qantas Selected',
        customAttributes: {
          href: 'http://test',
        },
      });
    });
  });
});
