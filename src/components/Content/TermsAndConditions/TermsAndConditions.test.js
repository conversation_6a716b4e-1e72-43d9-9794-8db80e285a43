import React from 'react';
import { mountUtils } from 'test-utils';
import TermsAndConditions from './TermsAndConditions';

mountUtils.mockComponent('TermsDisclaimerItem');

const mockSanityTermsAndConditions = [
  [
    {
      _type: 'common.hotels.termsAndConditions',
      disclaimers: [{ content: [{ children: [{ text: 'Hotel only disclaimer:' }] }] }],
      title: 'Paradise deal',
    },
  ],
  [
    {
      _type: 'common.hotels.termsAndConditions',
      title: 'Park Hyatt Melbourne',
    },
    {
      _type: 'common.hotels.termsAndConditions',
      disclaimers: [{ content: [{ children: [{ text: 'Hotel only offers for Langham' }] }] }],
      title: 'The Langham Melbourne',
    },
  ],
];

const decorators = { theme: true };
const render = (props) => mountUtils(<TermsAndConditions termsAndConditions={mockSanityTermsAndConditions} {...props} />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
});

const { find } = render();
it('renders PageBlock container with an id', () => {
  expect(find('PageBlock')).toExist();
  expect(find('PageBlock')).toHaveProp({
    id: 'terms-and-conditions',
  });
});

it('renders main Heading with correct text', () => {
  expect(find('Text')).toExist();
  expect(find('Text')).toHaveText('Offer Terms and Conditions');
});

it('renders 2 disclaimer titles with the correct hotel names', () => {
  expect(find('DisclaimerTitle')).toHaveLength(2);
  expect(find('DisclaimerTitle').first()).toHaveText('Paradise deal');
  expect(find('DisclaimerTitle').last()).toHaveText('The Langham Melbourne');
});

it('renders grouped TermsDisclaimerItems', () => {
  expect(find('TermsDisclaimerItem')).toHaveLength(2);
});

describe('when there are no valid terms disclaimers', () => {
  const { find } = render({
    termsAndConditions: [[{ _type: 'common.hotels.termsAndConditions', title: 'Paradise deal' }]],
  });
  it('does not render', () => {
    expect(find('PageBlock')).not.toExist();
  });
});
