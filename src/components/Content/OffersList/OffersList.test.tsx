import React from 'react';
import { mocked, mountUtils } from 'test-utils';
import OffersList from './OffersList';
import { useDataLayer } from 'hooks/useDataLayer';
import { Heading } from '@qga/roo-ui/components';
import { mockOffers } from './mocks';

mountUtils.mockComponent('Offer');

jest.mock('hooks/useDataLayer');
const emitInteractionEvent = jest.fn();

const decorators = { store: true, theme: true };
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const render = () => mountUtils(<OffersList {...(mockOffers as any)} />, { decorators });

describe('OffersList', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
  });

  it('renders the heading', () => {
    const { find } = render();

    expect(find(Heading.h2)).toHaveText('Special offers from Qantas');
  });

  it('render the offers', () => {
    const { find } = render();
    const results = find('Offer');

    expect(results).toHaveLength(3);
    expect(results.at(0)).toHaveProp({ ...mockOffers.offers[0] });
    expect(results.at(1)).toHaveProp({ ...mockOffers.offers[1] });
    expect(results.at(2)).toHaveProp({ ...mockOffers.offers[2] });
  });
});
