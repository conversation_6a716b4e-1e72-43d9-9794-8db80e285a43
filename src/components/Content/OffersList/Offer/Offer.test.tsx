import React from 'react';
import { mountUtils, mocked } from 'test-utils';
import Offer from './Offer';
import { useDataLayer } from 'hooks/useDataLayer';
import { Heading } from '@qga/roo-ui/components';
import { mockOffers } from '../mocks';

mountUtils.mockComponent('Image');
mountUtils.mockComponent('ImageFallback');

jest.mock('hooks/useDataLayer');
jest.mock('@qga/sanity-components');
const emitInteractionEvent = jest.fn();

const mockOffer = mockOffers.offers[0];

const decorators = { store: true, theme: true };
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const render = (props?: any) => mountUtils(<Offer {...(mockOffer as any)} {...props} />, { decorators });

describe('Offer', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
  });

  it('renders the heading', () => {
    const { find } = render();

    expect(find(Heading.h3)).toHaveText('Find your next luxury hotel');
  });

  it('renders the image', () => {
    const { find } = render();

    expect(find('Image')).toHaveProp({
      alt: 'Foo',
      src: 'https://cdn.sanity.io/images/abcdefghi/qh-test/9b5a7bab1952f0428eeef065fd4b89e1b0421038-2500x1464.png?rect=225,0,2050,1464&w=392&h=280&q=70&auto=format',
    });
  });

  it('renders the fallback image', () => {
    const { find } = render({ image: undefined });

    expect(find('ImageFallback')).toExist();
  });

  it('renders the promotional sash', () => {
    const { find } = render();

    expect(find('PromotionalSash')).toExist();
  });

  it('does NOT render the promotional sash', () => {
    const { find } = render({ promotionalSash: undefined });

    expect(find('PromotionalSash')).not.toExist();
  });

  it('renders markdown', () => {
    const content = 'This is a *sample* sentence';
    const { find } = render({ content });

    expect(find('Markdown').first().html()).toMatch(
      /Choose from carefully curated hotel getaways in fantastic destinations, exclusive to Qantas Hotels/i,
    );
  });

  describe('when clicking the link', () => {
    it('emits an event to the data layer', () => {
      const { findByTestId } = render();

      findByTestId('offer-link').simulate('click');

      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Offer Link',
        value: 'Find your next luxury hotel Selected',
      });
    });
  });
});
