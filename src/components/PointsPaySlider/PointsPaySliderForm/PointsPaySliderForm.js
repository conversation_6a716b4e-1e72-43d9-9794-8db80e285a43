import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import PointsPaySlider from 'components/PointsPaySlider/PointsPaySlider';
import SetQffBalanceButton from 'components/SetQffBalanceButton';
import { Box, Flex } from '@qga/roo-ui/components';
import usePointsConverters from 'hooks/points/usePointsConverters';
import { useSelector } from 'react-redux';
import formatNumber from 'lib/formatters/formatNumber';
import InputWithAddon from 'components/InputWithAddon';
import { MIN_POINTS_AMOUNT } from 'config';
import { usePointsPaySlider } from 'lib/hooks/slider';
import PriceBeforeDiscount from 'components/PriceBeforeDiscount';
import { DEFAULT_POINTS_AMOUNT } from 'lib/hooks/slider/usePointsPaySlider/usePointsPaySlider';
import { getPointsBalance } from 'store/user/userSelectors';

const zero = new Decimal(0);

const adjustCashAmountWithinRange = ({ cashAmount, minCashAmount, maxCashAmount }) => {
  return Decimal.min(Decimal.max(cashAmount, minCashAmount), maxCashAmount);
};

const adjustPointsAmountWithinRange = ({ value, maxValue }) => {
  if (value.equals(zero)) return value;

  return Decimal.min(Decimal.max(MIN_POINTS_AMOUNT, value), maxValue);
};

const formattedStringToNumber = (formatted) => (formatted ? Number(formatted.replace(/(,|\$)/g, '')) : 0);
const maskPoints = (cash) => cash.replace(/[^0-9,]/g, '');
const maskCash = (cash) => cash.replace(/[^0-9.,$]/g, '');
const formatPoints = (pointsAmount) => formatNumber({ number: pointsAmount.toNumber(), decimalPlaces: 0 });
const formatCash = (cashAmount) => formatNumber({ number: cashAmount.toNumber(), decimalPlaces: 2 });

const PointsPaySliderForm = ({
  initialPointsAmount = DEFAULT_POINTS_AMOUNT,
  offerType,
  showPriceBeforeDiscount = false,
  showLoginBanner = false,
  totalCashAmount,
  totalPointsAmount = 0,
  updatePointsAndCash,
}) => {
  const { convertPointsToCash } = usePointsConverters();

  const {
    cashAmount,
    hasDiscount,
    initialCashAmount,
    minCashAmount,
    maxCashAmount,
    onChange,
    originalPointsAmount,
    pointsAmount,
    pointsAmountInCash,
  } = usePointsPaySlider({
    totalCash: { amount: totalCashAmount },
    initialPointsAmount,
  });

  const pointsBalance = useSelector(getPointsBalance);
  const usePointsBalance = totalPointsAmount < pointsBalance ? new Decimal(totalPointsAmount) : pointsBalance;

  const [bufferedPointsAmount, setBufferedPointsAmount] = useState(formatPoints(pointsAmount));
  const [bufferedCashAmount, setBufferedCashAmount] = useState(formatCash(cashAmount));

  useEffect(() => {
    updatePointsAndCash({
      points: {
        amount: pointsAmount,
        amountInCash: pointsAmountInCash,
      },
      cash: {
        amount: cashAmount,
      },
    });
    setBufferedPointsAmount(formatPoints(pointsAmount));
    setBufferedCashAmount(formatCash(cashAmount));
  }, [pointsAmount.toNumber(), pointsAmountInCash.toNumber(), cashAmount.toNumber(), updatePointsAndCash]); // eslint-disable-line react-hooks/exhaustive-deps

  const updatePointsAmount = () => {
    const value = bufferedPointsAmount ? new Decimal(formattedStringToNumber(bufferedPointsAmount)) : zero;

    const adjustedPointsAmount = adjustPointsAmountWithinRange({ value, maxValue: pointsAmount });
    setBufferedPointsAmount(formatPoints(adjustedPointsAmount));

    const cashAmount = totalCashAmount.minus(convertPointsToCash({ points: value }));
    const adjustedCashAmount = adjustCashAmountWithinRange({ cashAmount, minCashAmount, maxCashAmount });

    onChange(adjustedCashAmount);
  };

  const updateCashAmount = () => {
    const cashAmount = bufferedCashAmount ? new Decimal(formattedStringToNumber(bufferedCashAmount)) : zero;
    const adjustedCashAmount = adjustCashAmountWithinRange({ cashAmount, minCashAmount, maxCashAmount });
    onChange(adjustedCashAmount);
  };

  const updatePointsAmountToBalance = () => {
    const value = usePointsBalance;

    const adjustedPointsAmount = adjustPointsAmountWithinRange({ value, maxValue: pointsAmount });
    setBufferedPointsAmount(formatPoints(adjustedPointsAmount));

    const cashAmount = totalCashAmount.minus(convertPointsToCash({ points: value }));
    const adjustedCashAmount = adjustCashAmountWithinRange({ cashAmount, minCashAmount, maxCashAmount });

    onChange(adjustedCashAmount);
  };

  return (
    <Box>
      <Flex justifyContent="space-between" mb={5}>
        <Box width="130px">
          <InputWithAddon
            id="points"
            name="points"
            prefix="PTS"
            value={bufferedPointsAmount}
            onChange={(e) => setBufferedPointsAmount(maskPoints(e.target.value))}
            onBlur={updatePointsAmount}
            onKeyUp={(e) => e.keyCode === 13 && updatePointsAmount()}
            textAlign="right"
            aria-label="Amount in Qantas Points (PTS)"
            data-testid="points-input"
          />
        </Box>
        <Box textAlign="right" width="130px">
          <InputWithAddon
            id="cash"
            name="cash"
            prefix="$"
            value={bufferedCashAmount}
            onChange={(e) => setBufferedCashAmount(maskCash(e.target.value))}
            onBlur={updateCashAmount}
            onKeyUp={(e) => e.keyCode === 13 && updateCashAmount()}
            paddingLeft={4}
            textAlign="right"
            aria-label="Amount in cash (AUD)"
            data-testid="cash-input"
          />
        </Box>
      </Flex>
      {showPriceBeforeDiscount && hasDiscount && (
        <PriceBeforeDiscount offerType={offerType} total={{ amount: originalPointsAmount.toNumber(), currency: 'PTS' }} />
      )}
      <PointsPaySlider
        cashAmount={formattedStringToNumber(bufferedCashAmount)}
        initialCashAmount={initialCashAmount.toNumber()}
        minCashAmount={minCashAmount.toNumber()}
        maxCashAmount={maxCashAmount.toNumber()}
        onChange={onChange}
      />

      {showLoginBanner && <SetQffBalanceButton pointsBalance={usePointsBalance} onClick={updatePointsAmountToBalance} />}
    </Box>
  );
};

PointsPaySliderForm.propTypes = {
  initialPointsAmount: PropTypes.instanceOf(Decimal),
  offerType: PropTypes.string,
  showLoginBanner: PropTypes.bool,
  showPriceBeforeDiscount: PropTypes.bool,
  totalCashAmount: PropTypes.instanceOf(Decimal).isRequired,
  totalPointsAmount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  updatePointsAndCash: PropTypes.func.isRequired,
};

export default PointsPaySliderForm;
