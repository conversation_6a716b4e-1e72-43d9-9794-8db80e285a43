/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import Image from './Image';
import { mountUtils } from 'test-utils';

mountUtils.mockComponent('ImageLazyLoader');
mountUtils.mockComponent('ImageLoader');

const alt = 'Some test image';
const src = 'some/image.jpg';
const srcSet = 'some/image.jpg 1x, some/image-x2.jpg x2';
const defaultProps = { src, srcSet, alt };

const render = (props = {}) => mountUtils(<Image {...defaultProps} {...props} />);

it('renders an instance of <ImageLazyLoader> when lazy is true', () => {
  const { find } = render({ lazy: true });
  expect(find('ImageLazyLoader')).toExist();
});

it('renders an instance of <ImageLoader> when lazy is false', () => {
  const { find } = render({ lazy: false });
  expect(find('ImageLoader')).toExist();
});

it('passes props to children', () => {
  const { find } = render({ lazy: false });
  expect(find('ImageLoader')).toHaveProp({ alt, src, srcSet });
});
