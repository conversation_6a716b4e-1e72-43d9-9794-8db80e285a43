import React from 'react';
import Filters from './Filters';
import { mountUtils } from 'test-utils';
import { getResultCount, getIsLoading } from 'store/search/searchSelectors';
import { getQueryPayWith } from 'store/router/routerSelectors';
import { updateQuery } from 'store/search/searchActions';
import { useDataLayer } from 'hooks/useDataLayer';
import Modal from 'components/Modal';
import { useModal } from 'lib/hooks';
import * as config from 'config';

jest.useFakeTimers();
jest.mock('config');
jest.mock('store/search/searchSelectors');
jest.mock('store/router/routerSelectors');
jest.mock('store/ui/uiSelectors');
jest.mock('hooks/useDataLayer', () => ({ useDataLayer: jest.fn() }));
jest.mock('lib/hooks');

mountUtils.mockComponent('ClearFilters');
mountUtils.mockComponent('PropertyTypeFilter');
mountUtils.mockComponent('FacilityFilter');
mountUtils.mockComponent('StarRatingFilter');
mountUtils.mockComponent('PaymentPolicyFilter');
mountUtils.mockComponent('TripadvisorRatingFilter');
mountUtils.mockComponent('PriceFilter');
mountUtils.mockComponent('PaymentPolicyFilter');
mountUtils.mockComponent('SubRegionFilter');
mountUtils.mockComponent('PayWith');

const title = 'filter modal title';
const showSortingOptions = true;
const resultCount = 10;
let emitInteractionEvent;
let openModal;
let closeModal;
let modalProps;

const baseProps = {
  showSortingOptions,
  title,
};

const render = (props) => mountUtils(<Filters {...baseProps} {...props} />, { decorators: { store: true, theme: true } });

beforeEach(() => {
  jest.clearAllMocks();
  emitInteractionEvent = jest.fn();

  getQueryPayWith.mockReturnValue('cash');
  getResultCount.mockReturnValue(resultCount);
  getIsLoading.mockReturnValue(false);

  openModal = jest.fn();
  closeModal = jest.fn();

  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

describe('with the modal closed', () => {
  beforeEach(() => {
    modalProps = { isOpen: false };
    useModal.mockReturnValue({ openModal, closeModal, modalProps });
  });

  it('does not render the modal', () => {
    const { find } = render();
    expect(find(Modal)).not.toExist();
  });
});

describe('with the modal opened', () => {
  beforeEach(() => {
    modalProps = { isOpen: true };
    useModal.mockReturnValue({ openModal, closeModal, modalProps });
  });

  it('sets the title on the Modal', () => {
    const { find } = render();
    expect(find(Modal)).toHaveProp({ title });
  });

  describe('when NOT loading', () => {
    it('renders the SearchFiltersButton with the correct props', () => {
      const { find } = render();
      expect(find('SearchFiltersButton')).toHaveProp({
        onClick: expect.any(Function),
      });
    });

    it('does NOT render the LoadingIndicator', () => {
      const { find } = render();
      expect(find('LoadingIndicator')).not.toExist();
    });
  });

  describe('when loading', () => {
    beforeEach(() => {
      getIsLoading.mockReturnValue(true);
    });

    it('does NOT render the FiltersButton', () => {
      const { find } = render();
      expect(find('FiltersButton')).not.toExist();
    });

    it('renders the LoadingIndicator', () => {
      const { find } = render();
      expect(find('LoadingIndicator')).toExist();
    });
  });
});

describe('when the filters are opened', () => {
  beforeEach(() => {
    useModal.mockReturnValue({ openModal, closeModal, modalProps });
  });

  describe('ClearFilters', () => {
    it('renders <ClearFilters /> with onChangeHandler', () => {
      const { find } = render();
      find('FiltersButton').simulate('click');
      expect(find('ClearFilters')).toHaveProp({ hideTitle: true });
    });
  });

  describe('PayWith', () => {
    describe('when PAYWITH_TOGGLE_ENABLED is true', () => {
      beforeEach(() => {
        config.PAYWITH_TOGGLE_ENABLED = true;
      });

      it('renders <PayWith /> with expected props', () => {
        const { find } = render();
        find('FiltersButton').simulate('click');
        expect(find('PayWith')).toHaveProp({ hideClear: true });
      });

      it('is rendered with onChange handler', () => {
        const { find, decorators } = render();
        find('FiltersButton').simulate('click');

        const query = { payWith: 'points' };
        find('PayWith').prop('onChange')(query);
        expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery(query));
      });
    });

    describe('when PAYWITH_TOGGLE_ENABLED is false', () => {
      beforeEach(() => {
        config.PAYWITH_TOGGLE_ENABLED = false;
      });

      it('does NOT render <PayWith />', () => {
        const { find } = render();
        find('FiltersButton').simulate('click');

        expect(find('PayWith')).not.toExist();
      });
    });
  });

  describe('<Sort />', () => {
    describe('when showSortingOptions is true', () => {
      it('is rendered with expected props', () => {
        const { find } = render({ showSortingOptions: true });
        find('FiltersButton').simulate('click');
        expect(find('Sort')).toExist();
      });

      it('is rendered with onChange handler', () => {
        const { find, decorators } = render({ showSortingOptions: true });
        find('FiltersButton').simulate('click');
        const query = { sortBy: 'popularity' };
        find('Sort').prop('onChange')(query);
        expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery(query));
      });
    });

    describe('when showSortingOptions is false', () => {
      it('is rendered', () => {
        const { find } = render({ showSortingOptions: false });
        find('FiltersButton').simulate('click');
        expect(find('Sort')).not.toExist();
      });
    });
  });

  describe('PriceFilter', () => {
    it('renders <PriceFilter /> with onChangeHandler', () => {
      const { find, decorators } = render();
      find('FiltersButton').simulate('click');

      const query = { minPrice: 100 };
      find('PriceFilter').prop('onChange')(query);
      expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery(query));
    });
  });

  describe('PaymentPolicyFilter', () => {
    it('is rendered with onChange handler', () => {
      const { find, decorators } = render();
      find('FiltersButton').simulate('click');

      const query = { depositPay: true };
      find('PaymentPolicyFilter').prop('onChange')(query);
      expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery(query));
    });
  });

  describe('StarRatingFilter', () => {
    it('is rendered with onChange handler', () => {
      const { find, decorators } = render();
      find('FiltersButton').simulate('click');

      const query = { rating: 3 };
      find('StarRatingFilter').prop('onChange')(query);
      expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery(query));
    });
  });

  describe('TripadvisorRatingFilter', () => {
    it('is rendered with onChange handler', () => {
      const { find, decorators } = render();
      find('FiltersButton').simulate('click');

      const query = { rating: 3 };
      find('TripadvisorRatingFilter').prop('onChange')(query);
      expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery(query));
    });
  });

  describe('PropertyTypeFilter', () => {
    it('is rendered with onChange handler', () => {
      const { find, decorators } = render();
      find('FiltersButton').simulate('click');

      const query = { propertyTypes: ['hotel'] };
      find('PropertyTypeFilter').prop('onChange')(query);
      expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery(query));
    });
  });

  describe('FacilityFilter', () => {
    it('is rendered with onChange handler', () => {
      const { find, decorators } = render();
      find('FiltersButton').simulate('click');

      const query = { facility: ['wifi'] };
      find('FacilityFilter').prop('onChange')(query);
      expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery(query));
    });
  });

  describe('SubRegionFilter', () => {
    it('is rendered with onChange handler', () => {
      const { find, decorators } = render();
      find('FiltersButton').simulate('click');

      const query = { subRegion: ['melbourne'] };
      find('SubRegionFilter').prop('onChange')(query);
      expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery(query));
    });
  });

  describe('opening the modal', () => {
    it('calls openModal', () => {
      const { find } = render();
      find('FiltersButton').simulate('click');
      expect(openModal).toHaveBeenCalled();
    });

    it('emits the expected interactionEvent', () => {
      const { find } = render();
      find('FiltersButton').simulate('click');
      expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Mobile Filters', value: 'Filters Button Selected' });
      expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Filter Toggle', value: 'Show Selected' });
    });
  });

  describe('closing the modal', () => {
    it('calls closeModal', () => {
      const { find } = render();
      find('FiltersButton').simulate('click');
      find(Modal).prop('onRequestClose')();
      expect(closeModal).toHaveBeenCalled();
    });

    it('emits the expected interactionEvent', () => {
      const { find } = render();
      find('SearchFiltersButton').simulate('click');
      find(Modal).prop('onRequestClose')();
      expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Filter Toggle', value: 'Hide Selected' });
    });
  });
  // });
});
