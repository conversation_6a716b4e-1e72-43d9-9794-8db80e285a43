import React, { Fragment, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import PropTypes from 'prop-types';
import { Box, LoadingIndicator } from '@qga/roo-ui/components';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import Modal, { ModalFooter } from 'components/Modal';
import { useModal } from 'lib/hooks';
import Sort from 'components/Filters/Search/Sort';
import SearchFiltersButton from 'components/Filters/Search/SearchFiltersButton';
import PropertyTypeFilter from 'components/Filters/Search/PropertyTypeFilter';
import FacilityFilter from 'components/Filters/Search/FacilityFilter';
import SubRegionFilter from 'components/Filters/Search/SubRegionFilter';
import PaymentPolicyFilter from 'components/Filters/Search/PaymentPolicyFilter';
import StarRatingFilter from 'components/Filters/Search/RatingFilter/StarRatingFilter';
import TripadvisorRatingFilter from 'components/Filters/Search/RatingFilter/TripadvisorRatingFilter';
import ClearFilters from 'components/Filters/Search/ClearFilters';
import PriceFilter from 'components/Filters/Search/PriceFilter';
import { getIsLoading, getResultCount } from 'store/search/searchSelectors';
import { updateQuery } from 'store/search/searchActions';
import { useDataLayer } from 'hooks/useDataLayer';
import PayWith from 'components/Filters/Search/PayWith';
import { PAYWITH_TOGGLE_ENABLED } from 'config';

const FilterItem = styled(Box)`
  border-bottom: 1px solid ${themeGet('colors.greys.alto')};
  padding-bottom: ${themeGet('space.4')};
  margin-bottom: ${themeGet('space.4')};
`;

const LoaderOverlay = styled(Box)`
  position: fixed;
  display: flex;
  justify-content: center;
  align-items: center;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 2;
  pointer-events: none;
`;

const Filters = ({ showSortingOptions, title }) => {
  const dispatch = useDispatch();

  const isLoading = useSelector(getIsLoading);
  const resultCount = useSelector(getResultCount);
  const { openModal, closeModal, modalProps } = useModal();

  const { emitInteractionEvent } = useDataLayer();

  const onCloseModal = useCallback(() => {
    closeModal();
    emitInteractionEvent({ type: 'Filter Toggle', value: 'Hide Selected' });
  }, [emitInteractionEvent, closeModal]);

  const onChange = useCallback(
    (query) => {
      dispatch(updateQuery(query));
    },
    [dispatch],
  );

  const onOpenModal = useCallback(() => {
    openModal();
    emitInteractionEvent({ type: 'Mobile Filters', value: 'Filters Button Selected' });
    emitInteractionEvent({ type: 'Filter Toggle', value: 'Show Selected' });
  }, [emitInteractionEvent, openModal]);

  const submitButtonText = `View ${resultCount} result${resultCount === 1 ? '' : 's'}`;

  return (
    <Fragment>
      {!isLoading && <SearchFiltersButton onClick={onOpenModal} />}
      {modalProps.isOpen && (
        <Modal
          {...modalProps}
          onRequestClose={onCloseModal}
          title={title}
          padding={0}
          footerComponent={() => <ModalFooter submitModal={onCloseModal} buttonText={submitButtonText} />}
        >
          {isLoading && (
            <LoaderOverlay>
              <LoadingIndicator />
            </LoaderOverlay>
          )}
          <Box padding={4} pb={15} bg="greys.porcelain">
            <FilterItem>
              <ClearFilters hideTitle />
            </FilterItem>
            {showSortingOptions && (
              <FilterItem>
                <Sort onChange={onChange} />
              </FilterItem>
            )}
            {PAYWITH_TOGGLE_ENABLED && (
              <FilterItem mt={4}>
                <PayWith onChange={onChange} hideClear />
              </FilterItem>
            )}
            <FilterItem>
              <PriceFilter onChange={onChange} />
            </FilterItem>
            <FilterItem>
              <PaymentPolicyFilter onChange={onChange} />
            </FilterItem>
            <FilterItem>
              <StarRatingFilter onChange={onChange} />
            </FilterItem>
            <FilterItem>
              <TripadvisorRatingFilter onChange={onChange} />
            </FilterItem>
            <FilterItem>
              <PropertyTypeFilter onChange={onChange} />
            </FilterItem>
            <FilterItem>
              <FacilityFilter onChange={onChange} />
            </FilterItem>
            <FilterItem>
              <SubRegionFilter onChange={onChange} />
            </FilterItem>
          </Box>
        </Modal>
      )}
    </Fragment>
  );
};

Filters.propTypes = {
  showSortingOptions: PropTypes.bool,
  title: PropTypes.string.isRequired,
};

Filters.defaultProps = {
  showSortingOptions: true,
};

export default Filters;
