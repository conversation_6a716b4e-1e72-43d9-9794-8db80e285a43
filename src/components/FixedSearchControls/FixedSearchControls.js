import React, { Fragment } from 'react';
import { useSelector } from 'react-redux';
import styled from '@emotion/styled';
import { css } from '@emotion/core';
import PropTypes from 'prop-types';
import { themeGet } from 'styled-system';
import { Box } from '@qga/roo-ui/components';
import { getIsLoading } from 'store/search/searchSelectors';
import Filters from './Filters';
import MapSearchButton from './MapSearchButton';

import ListSearchButton from './ListSearchButton';

const boxShadow = '0px -4px 8px 0px rgba(51, 51, 51, 0.15), 0px 4px 8px 0px rgba(51, 51, 51, 0.15)';

const FixedPositionControls = styled(Box)`
  position: fixed;
  bottom: 24px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
`;

const ControlsWrapper = styled(Box)`
  border-radius: ${themeGet('radii.rounded')};
  ${(props) =>
    !props.isLoading &&
    css`
      background-color: ${themeGet('colors.greys.charcoal')(props)};
      box-shadow: ${boxShadow};
    `};
  display: flex;
  height: 48px;
  justify-content: center;
`;

const Divider = styled(Box)`
  width: 2px;
  height: 100%;
  margin: ${themeGet('spacing.3')} 0;
  border-top: 12px solid ${themeGet('colors.greys.charcoal')};
  border-bottom: 12px solid ${themeGet('colors.greys.charcoal')};
  background-color: ${themeGet('colors.greys.dusty')};
`;

const FixedSearchControls = ({ isListSearch, isMapSearch }) => {
  const isLoading = useSelector(getIsLoading);

  return (
    <FixedPositionControls>
      <ControlsWrapper isLoading={isLoading} data-testid="phone-fixed-controls">
        {!isLoading && (
          <Fragment>
            {isListSearch && <MapSearchButton />}
            {isMapSearch && <ListSearchButton />}
            <Divider />
          </Fragment>
        )}
        <Box minWidth="126px">
          <Filters showSortingOptions={!isMapSearch} title="Filter properties" />
        </Box>
      </ControlsWrapper>
    </FixedPositionControls>
  );
};

FixedSearchControls.propTypes = {
  isListSearch: PropTypes.bool,
  isMapSearch: PropTypes.bool,
};

FixedSearchControls.defaultProps = {
  isListSearch: false,
  isMapSearch: false,
};

export default FixedSearchControls;
