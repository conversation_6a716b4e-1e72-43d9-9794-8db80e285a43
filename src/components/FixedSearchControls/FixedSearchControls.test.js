import React from 'react';
import FixedSearchControls from './FixedSearchControls';
import { mountUtils } from 'test-utils';
import { useDataLayer } from 'hooks/useDataLayer';
import { getIsLoading } from 'store/search/searchSelectors';

jest.mock('hooks/useDataLayer');
jest.mock('store/search/searchSelectors');

mountUtils.mockComponent('Filters');
mountUtils.mockComponent('MapSearchButton');
mountUtils.mockComponent('ListSearchButton');

let props;

const decorators = { store: true };
const render = () => mountUtils(<FixedSearchControls {...props} />, { decorators });

const mockDataLayer = { emitInteractionEvent: jest.fn() };

describe('<FixedSearchControls /> component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    useDataLayer.mockReturnValue(mockDataLayer);
    getIsLoading.mockReturnValue(false);
    props = {
      isListSearch: true,
      isMapSearch: false,
    };
  });

  describe('with the isListSearch prop is true', () => {
    beforeEach(() => {
      props = {
        isListSearch: true,
        isMapSearch: false,
      };
    });

    describe('when is not loading', () => {
      it('renders the <MapSearchButton />', () => {
        const { find } = render();

        expect(find('MapSearchButton')).toExist();
      });
    });

    describe('when is loading', () => {
      beforeEach(() => {
        getIsLoading.mockReturnValue(true);
      });

      it('does NOT render the <MapSearchButton />', () => {
        const { find } = render();

        expect(find('MapSearchButton')).not.toExist();
      });
    });

    it('renders the <Filters /> with the correct props', () => {
      const { find } = render();

      expect(find('Filters')).toHaveProp({
        showSortingOptions: true,
        title: 'Filter properties',
      });
    });
  });

  describe('with the isMapSearch prop true', () => {
    beforeEach(() => {
      props = {
        isListSearch: false,
        isMapSearch: true,
      };
    });

    describe('when is not loading', () => {
      it('renders the <ListSearchButton />', () => {
        const { find } = render();

        expect(find('ListSearchButton')).toExist();
      });
    });

    describe('when is loading', () => {
      beforeEach(() => {
        getIsLoading.mockReturnValue(true);
      });

      it('does NOT render the <ListSearchButton />', () => {
        const { find } = render();

        expect(find('ListSearchButton')).not.toExist();
      });
    });

    it('renders the <Filters /> with the correct props', () => {
      const { find } = render();

      expect(find('Filters')).toHaveProp({
        showSortingOptions: false,
        title: 'Filter properties',
      });
    });
  });
});
