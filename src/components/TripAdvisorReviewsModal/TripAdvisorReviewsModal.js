import React, { memo } from 'react';
import PropTypes from 'prop-types';
import styled from '@emotion/styled';
import Modal from 'components/Modal';
import { mediaQuery } from 'lib/styledSystem';
import { TRIPADVISOR_PARTNER_ID } from 'config';

const IFrame = styled('iframe')`
  border: 0;
  width: 100%;
  height: 100%;

  ${mediaQuery.minWidth.sm} {
    height: 70vh;
  }
`;

IFrame.displayName = 'IFrame';

const TripAdvisorReviewsModal = ({ locationId, ...modalProps }) => {
  const tripAdvisorUrl = `//www.tripadvisor.com/WidgetEmbed-cdspropertydetail?locationId=${locationId}&lang=en_AU&partnerId=${TRIPADVISOR_PARTNER_ID}&display=true`;

  return (
    <Modal {...modalProps} padding="0" title="Guest reviews">
      <IFrame src={tripAdvisorUrl} title="Guest reviews" />
    </Modal>
  );
};

TripAdvisorReviewsModal.propTypes = {
  locationId: PropTypes.string.isRequired,
};

export default memo(TripAdvisorReviewsModal);
