import React from 'react';
import { mountUtils } from 'test-utils';
import PayWithToggleMessage from './PayWithToggleMessage';
import { getPayWithToggleMessage } from 'store/campaign/campaignSelectors';

jest.mock('store/campaign/campaignSelectors');

const props = { pointerDirection: 'left', pointerPosition: '50%' };

const render = () => mountUtils(<PayWithToggleMessage {...props} />, { decorators: { store: true, theme: true } });

describe('<PayWithToggleMessage />', () => {
  const message = 'message';

  beforeEach(() => {
    getPayWithToggleMessage.mockReturnValue(message);
  });

  it('renders the message', () => {
    const { find } = render();
    expect(find('Tooltip')).toHaveText(message);
  });

  it('passes props', () => {
    const { find } = render();
    expect(find('Tooltip')).toHaveProp(props);
  });

  describe('without a message', () => {
    it('renders nothing', () => {
      getPayWithToggleMessage.mockReturnValue(null);
      const { wrapper } = render();
      expect(wrapper).toBeEmptyRender();
    });
  });
});
