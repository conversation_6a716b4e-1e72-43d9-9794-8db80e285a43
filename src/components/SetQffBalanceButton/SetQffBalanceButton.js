import React from 'react';
import PropTypes from 'prop-types';
import { NakedButton, Text, Flex, Box, Link } from '@qga/roo-ui/components';
import { FormatNumber } from 'components/formatters';
import { useIsAuthenticated } from 'lib/oauth';
import { useLoginUrl } from 'lib/qffAuth';

const SetQffBalanceButton = ({ pointsBalance, onClick }) => {
  const { loginUrl } = useLoginUrl();
  const isAuthenticated = useIsAuthenticated();

  return (
    <Flex>
      {isAuthenticated && pointsBalance.toNumber() >= 5000 && (
        <Box data-testid="points-balance-message">
          <NakedButton onClick={onClick}>
            <Text color="greys.steel" fontSize="sm" textDecoration="underline" pr={1}>
              Set Slider
            </Text>
          </NakedButton>
          <Text color="greys.steel" fontSize="sm">
            to total of <FormatNumber number={pointsBalance} decimal={0} /> PTS. Minimum 5,000 PTS required.
          </Text>
        </Box>
      )}
      {!isAuthenticated && (
        <Box data-testid="login-prompt">
          <NakedButton as={Link} href={loginUrl} aria-label="Log in to your frequent flyer account" data-testid="login-button">
            <Text color="greys.steel" fontSize="sm" textDecoration="underline" pr={1}>
              Login
            </Text>
          </NakedButton>
          <Text color="greys.steel" fontSize="sm">
            to see your balance and use Qantas Points. Minimum 5,000 PTS required.
          </Text>
        </Box>
      )}
    </Flex>
  );
};

SetQffBalanceButton.propTypes = {
  pointsBalance: PropTypes.object.isRequired,
  onClick: PropTypes.func.isRequired,
};

export default SetQffBalanceButton;
