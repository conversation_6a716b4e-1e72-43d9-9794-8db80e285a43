import React from 'react';
import { mountUtils } from 'test-utils';
import SetQffBalanceButton from './SetQffBalanceButton';
import { useLoginUrl } from 'lib/qffAuth';
import { useIsAuthenticated } from 'lib/oauth';
import { useDataLayer } from 'hooks/useDataLayer';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';

jest.mock('store/user/userSelectors');
jest.mock('lib/qffAuth');
jest.mock('lib/oauth');
jest.mock('hooks/useDataLayer');
const emitInteractionEvent = jest.fn();

const defaultProps = {
  pointsBalance: new Decimal(10000),
  onClick: jest.fn(),
};
const render = () => mountUtils(<SetQffBalanceButton {...defaultProps} />, { decorators: { store: true, theme: true } });

beforeEach(() => {
  emitInteractionEvent.mockClear();
  useIsAuthenticated.mockReturnValue(true);
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  useLoginUrl.mockReturnValue({ loginUrl: '/hotels/auth/callback?state=/foo' });
});

describe('SetQffBalanceButton', () => {
  describe('if authenticated', () => {
    it('renders the Set Slider Message', () => {
      const { findByTestId } = render();
      expect(findByTestId('points-balance-message')).toHaveText('Set Sliderto total of 10,000 PTS. Minimum 5,000 PTS required.');
    });
  });

  describe('if NOT authenticated', () => {
    beforeEach(() => {
      useIsAuthenticated.mockReturnValue(false);
    });
    it('renders the login message', () => {
      const { findByTestId } = render();
      expect(findByTestId('login-prompt')).toHaveText('Loginto see your balance and use Qantas Points. Minimum 5,000 PTS required.');
    });
  });
});
