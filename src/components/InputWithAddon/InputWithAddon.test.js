import React from 'react';
import { mountWithThemeAndRouter } from 'test-utils';
import theme from 'lib/theme';
import InputWithAddon from './';

const render = (props = {}) => mountWithThemeAndRouter(<InputWithAddon {...props} />);

it('defaults to a grey border', async () => {
  const input = render({ theme: theme }).find('input');
  expect(input).toHaveStyleRule('border-color', theme.colors.greys.alto);
});

it('shows a teal border when isHighlighted it true', async () => {
  const input = render({ theme: theme, isHighlighted: true }).find('input');
  expect(input).toHaveStyleRule('border-color', theme.colors.brand.secondary);
});

it('shows a teal border when focused', async () => {
  const input = render({ theme: theme }).find('input');
  expect(input).toHaveStyleRule('border-color', '#8DE2E0', { target: ':focus' });
});

it('can render with no icon', () => {
  expect(() => render()).not.toThrow();
});

it('can render with an icon', () => {
  expect(() => render({ icon: 'person' })).not.toThrow();
});

it('can render with a prefix', () => {
  expect(() => render({ prefix: '$' })).not.toThrow();
});
