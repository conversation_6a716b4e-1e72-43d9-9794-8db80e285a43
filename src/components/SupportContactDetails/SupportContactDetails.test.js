import React from 'react';
import { mountUtils } from 'test-utils';
import SupportContactDetails from './SupportContactDetails';
import { DOMESTIC_PHONE_NUMBER, INTERNATIONAL_PHONE_NUMBER } from 'config';

const decorators = { theme: true };
const render = () => mountUtils(<SupportContactDetails />, { decorators });

it('shows the Australian contact number', () => {
  const { findByTestId } = render();

  expect(findByTestId('domestic-phone-number')).toExist();
  expect(findByTestId('domestic-phone-number')).toHaveProp({
    href: `tel:${DOMESTIC_PHONE_NUMBER}`,
  });
});

it('shows the international contact number', () => {
  const { findByTestId } = render();

  expect(findByTestId('international-phone-number')).toExist();
  expect(findByTestId('international-phone-number')).toHaveProp({
    href: `tel:${INTERNATIONAL_PHONE_NUMBER}`,
  });
});

it('shows the call centre hours', () => {
  const { findByTestId } = render();

  expect(findByTestId('opening-hours')).toExist();
});

it('shows the timezone', () => {
  const { findByTestId } = render();

  expect(findByTestId('timezone')).toExist();
});
