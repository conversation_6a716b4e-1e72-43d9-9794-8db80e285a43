import React, { Fragment } from 'react';
import { Text, Link } from '@qga/roo-ui/components';
import { DOMESTIC_PHONE_NUMBER, INTERNATIONAL_PHONE_NUMBER } from 'config';

const SupportContactDetails = () => (
  <Fragment>
    <Link
      color="greys.charcoal"
      href={`tel:${DOMESTIC_PHONE_NUMBER}`}
      data-testid="domestic-phone-number"
      display="inline"
      fontWeight="bold"
    >
      {DOMESTIC_PHONE_NUMBER}&nbsp;
    </Link>
    <Text display="inline">
      (for outside Australia:&nbsp;
      <Link color="greys.charcoal" href={`tel:${INTERNATIONAL_PHONE_NUMBER}`} display="inline" data-testid="international-phone-number">
        {INTERNATIONAL_PHONE_NUMBER}
      </Link>
      ).&nbsp;
    </Text>
    <Text display="inline">We are available from&nbsp;</Text>
    <Text data-testid="opening-hours" display="inline" fontWeight="bold">
      7am - 10pm&nbsp;
    </Text>
    <Text data-testid="timezone" display="inline" fontWeight="bold">
      AET
    </Text>
    <Text display="inline">&nbsp;(7 days a week).</Text>
  </Fragment>
);

export default SupportContactDetails;
