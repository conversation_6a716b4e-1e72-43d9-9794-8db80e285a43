import React from 'react';
import { act } from 'react-dom/test-utils';
import { mountUtils } from 'test-utils';
import SentryContextHelper from './SentryContextHelper';
import * as Sentry from '@sentry/nextjs';

jest.mock('@sentry/nextjs');

const eventMap = {};
const render = () => mountUtils(<SentryContextHelper />);

beforeEach(() => {
  jest.clearAllMocks();
  Object.defineProperty(window.navigator, 'onLine', { value: false });
  window.addEventListener = (eventType, callback) => {
    eventMap[eventType] = callback;
  };
});

it('adds a breadcrumb when the network status changes', () => {
  render();

  expect(Sentry.addBreadcrumb).toHaveBeenLastCalledWith({ category: 'network', level: 'info', message: 'Network status: offline' });
  act(() => eventMap.online());
  expect(Sentry.addBreadcrumb).toHaveBeenLastCalledWith({ category: 'network', level: 'info', message: 'Network status: online' });
  act(() => eventMap.offline());
  expect(Sentry.addBreadcrumb).toHaveBeenLastCalledWith({ category: 'network', level: 'info', message: 'Network status: offline' });
});
