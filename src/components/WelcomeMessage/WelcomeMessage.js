import React, { useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Flex, Icon, Text } from '@qga/roo-ui/components';
import { rooRed } from '@qga/roo-ui/assets';
import { CircleWithRoo, Card } from './primitives';
import FormatNumber from 'components/formatters/FormatNumber';
import { getFirstName, getPointsBalance, getHasDismissedWelcomeMessage } from 'store/user/userSelectors';
import { dismissWelcomeMessage } from 'store/user/userActions';
import { useDataLayer } from 'hooks/useDataLayer';
import { WELCOME_MESSAGE_ENABLED } from 'config';
import { useIsAuthenticated } from 'lib/oauth';

const WelcomeMessage = () => {
  const { emitInteractionEvent } = useDataLayer();
  const dispatch = useDispatch();
  const isAuthenticated = useIsAuthenticated();
  const userFirstName = useSelector(getFirstName);
  const pointsBalance = useSelector(getPointsBalance);
  const hasDismissedWelcomeMessage = useSelector(getHasDismissedWelcomeMessage);

  const handleDismiss = useCallback(() => {
    dispatch(dismissWelcomeMessage());

    emitInteractionEvent({ type: 'Welcome message', value: `Welcome Message Dismissed` });
  }, [dispatch, emitInteractionEvent]);

  if (!WELCOME_MESSAGE_ENABLED || !isAuthenticated || hasDismissedWelcomeMessage) return null;

  return (
    <Flex data-testid="welcome-message" flexDirection="column" display={['block', 'none']}>
      <Card justifyContent="flex-start">
        <Flex alignItems="flex-start">
          <CircleWithRoo alt={rooRed} src={rooRed} />
          <Flex display="block" fontSize="base" data-testid="welcome-message-text">
            <Text fontWeight="bold" fontSize="base">
              Welcome {userFirstName}{' '}
            </Text>
            enjoy your Qantas Frequent Flyer member deals.{' '}
            <Flex fontWeight="bold" color="brand.primary">
              <FormatNumber number={pointsBalance} /> points
            </Flex>
          </Flex>
        </Flex>
        <Flex alignItems="flex-start">
          <Icon name="close" mt={1} size={20} color="greys.dusty" onClick={handleDismiss} data-testid="close-welcome-message" />
        </Flex>
      </Card>
    </Flex>
  );
};

export default WelcomeMessage;
