import styled from '@emotion/styled';
import React, { ReactNode } from 'react';
import { useTheme } from 'emotion-theming';
import { Box, Card, Flex } from '@qga/roo-ui/components';
import { themeGet } from 'styled-system';
import Image from 'components/Image';
import PromotionalSash from 'components/PromotionalSash';
import AppLink from 'components/AppLink';
import { useSelector } from 'react-redux';
import { getPageName } from 'store/router/routerSelectors';
import { getCampaignDefaultSash } from 'store/campaign/campaignSelectors';

const BaseCard = styled(Card)`
  width: 100%;
  background-color: #fff;
  padding: 0;
  transition: box-shadow 0.2s;
  position: relative;
  box-shadow: ${themeGet('shadows.default')};

  &:hover {
    box-shadow: ${themeGet('shadows.hover')};
  }
`;

const InlineCardWrapper = styled(BaseCard)`
  ${themeGet('card.inline.root')};
`;

const InlineBadge = styled(Box)`
  ${themeGet('card.inline.badge')}
`;

const InlineText = styled(Flex)`
  ${themeGet('card.inline.text')}
`;

const StackedCardWrapper = styled(BaseCard)`
  ${themeGet('card.stacked.root')};
`;

const StackedBadge = styled(Box)`
  ${themeGet('card.stacked.badge')}
`;

const StackedImage = styled(Image)`
  ${themeGet('card.stacked.image')}
`;

const ImageWrapper = styled(Box)`
  overflow: hidden;
  width: 100%;
  height: 100%;
  border-top-right-radius: ${themeGet('radii.default')};
  border-top-left-radius: ${themeGet('radii.default')};
`;

const NakedAppLink = styled(AppLink)`
  display: block;
  text-decoration: none;
`;

type Props = {
  compact?: boolean;
  children: ReactNode;
  imageAltTag: string;
  imageSrc: string;
  imageSrcSet?: string;
  onClick?: () => void;
  promotionName?: string;
  target?: string;
  to: string;
};

export const InlineCard = ({ compact, children, imageSrc, imageSrcSet, imageAltTag, promotionName, ...rest }: Props) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const theme: any = useTheme();
  const pageName = useSelector(getPageName);
  const isDealsPage = pageName === 'deals';
  const imageSize = compact ? { ...theme.imageSizes.mapSearchResultPhone } : { ...theme.imageSizes.mapSearchResult };

  const globalCampaignDefaultSash = useSelector(getCampaignDefaultSash);
  const useGlobalCampaignDefaultSash = globalCampaignDefaultSash && !isDealsPage;
  const promotionSash = useGlobalCampaignDefaultSash ? globalCampaignDefaultSash : promotionName;

  return (
    <InlineCardWrapper as={NakedAppLink} rel="noopener noreferrer" {...rest} data-testid="inline-card">
      <Flex flexDirection="row" height="100%" minHeight="203px">
        <Box width={compact ? 96 : 135}>
          <Image src={imageSrc} srcSet={imageSrcSet} alt={imageAltTag} {...imageSize} />
        </Box>

        <InlineText flexDirection="column" justifyContent="space-between" width={1}>
          {promotionSash && (
            <InlineBadge>
              <PromotionalSash promotionName={promotionSash} />
            </InlineBadge>
          )}
          {children}
        </InlineText>
      </Flex>
    </InlineCardWrapper>
  );
};

export const StackedCard = ({ children, imageSrc, imageSrcSet, imageAltTag, promotionName, ...rest }: Props) => {
  const globalCampaignDefaultSash = useSelector(getCampaignDefaultSash);
  const promotionSash = globalCampaignDefaultSash ?? promotionName;

  return (
    <StackedCardWrapper as={NakedAppLink} rel="noopener noreferrer" width={300} {...rest} data-testid="stacked-card">
      <Flex flexDirection="column" justifyContent="space-between" height="100%">
        <ImageWrapper>
          {promotionSash && (
            <StackedBadge position="absolute">
              <PromotionalSash promotionName={promotionSash} type="corner" />
            </StackedBadge>
          )}
          <Box>
            <StackedImage lazy src={imageSrc} srcSet={imageSrcSet} alt={imageAltTag} height={120} width={1} />
          </Box>
        </ImageWrapper>
        <Box m={3} height="100%">
          {children}
        </Box>
      </Flex>
    </StackedCardWrapper>
  );
};
