import React from 'react';
import OfferBreakdown from './OfferBreakdown';
import { mountUtils } from 'test-utils';
import { getQueryCheckIn, getQueryCheckOut } from 'store/router/routerSelectors';

jest.mock('store/router/routerSelectors');

const decorators = { store: true, theme: true };
const render = (props) => mountUtils(<OfferBreakdown {...props} />, { decorators });
const checkIn = new Date('2019-07-22');
const checkOut = new Date('2019-07-24');

mountUtils.mockComponent('NumberOfNights');

beforeEach(() => {
  getQueryCheckIn.mockReturnValue(checkIn);
  getQueryCheckOut.mockReturnValue(checkOut);
});

describe('with query check in and check out', () => {
  describe('when paying with AUD', () => {
    let result;

    beforeEach(() => {
      result = render({
        checkIn: null,
        checkOut: null,
        total: {
          amount: 499,
          currency: 'AUD',
        },
      });
    });

    it('shows the number of nights', () => {
      const { find } = result;

      expect(find('NumberOfNights')).toHaveProp({ checkIn, checkOut });
    });

    it('does not show the formatted dates', () => {
      const { findByTestId } = result;
      expect(findByTestId('formatted-dates')).not.toExist();
    });

    it('shows the from currency', () => {
      const { findByTestId } = result;

      expect(findByTestId('from-currency').text()).toEqual('from (AUD)');
    });

    it('shows the price', () => {
      const { findByTestId } = result;

      expect(findByTestId('price').text()).toEqual('$499');
    });
  });

  describe('when paying using QFF points', () => {
    let result;

    beforeEach(() => {
      result = render({
        total: {
          amount: 29950,
          currency: 'PTS',
        },
        offerType: 'classic',
      });
    });

    it('shows the number of nights', () => {
      const { find } = result;

      expect(find('NumberOfNights')).toHaveProp({ checkIn, checkOut });
    });

    it('shows the from without currency', () => {
      const { findByTestId } = result;

      expect(findByTestId('from-currency').text()).toEqual('from ');
    });

    it('shows the price with the *', () => {
      const { findByTestId } = result;

      expect(findByTestId('price').text()).toEqual('29,950PTS*');
    });

    describe('when the offer is a classic offer', () => {
      it('shows the classic ribbon', () => {
        const { findByTestId } = result;

        expect(findByTestId('classic-ribbon')).toExist();
      });
    });
  });

  describe('when the price includes a discount', () => {
    describe('and paying with currency', () => {
      it('shows <PriceBeforeDiscount /> with the correct props', () => {
        const { find } = render({
          hasDiscount: true,
          total: {
            amount: 299,
            currency: 'AUD',
          },
          totalBeforeDiscount: {
            amount: 499,
            currency: 'AUD',
          },
        });

        expect(find('PriceBeforeDiscount')).toHaveProp({
          total: {
            amount: 499,
            currency: 'AUD',
          },
          roundToCeiling: true,
          hideCurrency: true,
        });
      });
    });

    describe('and paying with points', () => {
      describe('and it is NOT a classic reward offer', () => {
        it('shows <PriceBeforeDiscount /> with the correct props', () => {
          const { find } = render({
            hasDiscount: true,
            total: {
              amount: 30000,
              currency: 'PTS',
            },
            totalBeforeDiscount: {
              amount: 45000,
              currency: 'PTS',
            },
          });

          expect(find('PriceBeforeDiscount')).toHaveProp({
            total: {
              amount: 45000,
              currency: 'PTS',
            },
            roundToCeiling: true,
            hideCurrency: false,
          });
        });
      });
      describe('and it is a classic reward offer', () => {
        it('does not show <PriceBeforeDiscount /> ', () => {
          const { find } = render({
            hasDiscount: true,
            offerType: 'classic',
            total: {
              amount: 30000,
              currency: 'PTS',
            },
            totalBeforeDiscount: {
              amount: 45000,
              currency: 'PTS',
            },
          });
          expect(find('PriceBeforeDiscount')).not.toExist();
        });
      });
    });
  });
});

describe('with deals hub check in and check out', () => {
  describe('when paying with AUD', () => {
    let result;

    beforeEach(() => {
      result = render({
        checkIn: checkIn,
        checkOut: checkOut,
        total: {
          amount: 499,
          currency: 'AUD',
        },
      });
    });

    it('shows the number of nights', () => {
      const { find } = result;

      expect(find('NumberOfNights')).toHaveProp({ checkIn, checkOut });
    });

    it('shows the formatted dates', () => {
      const { findByTestId } = result;
      expect(findByTestId('formatted-dates')).toExist();
    });
  });
});
