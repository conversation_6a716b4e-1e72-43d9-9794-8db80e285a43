import React from 'react';
import { useSelector } from 'react-redux';
import { Box, Flex, Icon, Text, Hide } from '@qga/roo-ui/components';
import PropTypes from 'prop-types';
import { format as formatDate } from 'date-fns';
import NumberOfNights from 'components/NumberOfNights';
import Currency from 'components/Currency';
import PriceBeforeDiscount from 'components/PriceBeforeDiscount';
import { getQueryCheckIn, getQueryCheckOut } from 'store/router/routerSelectors';
import { CARD_DATE_FORMAT } from 'config';
import { themeGet } from 'styled-system';
import styled from '@emotion/styled';
import { mediaQuery } from 'lib/styledSystem';

const OfferWrapper = styled(Text)`
  ${themeGet('card.propertyCard.offerWrapper')}
  flex-direction: column;
  ${mediaQuery.minWidth.sm} {
    flex-direction: row;
    align-items: center;
  }
`;

const OfferBreakdownText = styled(Text)`
  ${themeGet('card.propertyCard.offerBreakdown')}
`;

const OfferPrice = styled(Text)`
  ${themeGet('card.propertyCard.offerPrice')}
`;

const ConnectedNumberOfNights = ({ checkIn, checkOut, total, isCashCurrency }) => {
  const queryCheckIn = useSelector(getQueryCheckIn);
  const queryCheckOut = useSelector(getQueryCheckOut);

  if (!checkIn || !checkOut) {
    return (
      <Box>
        <NumberOfNights checkIn={queryCheckIn} checkOut={queryCheckOut} color="greys.steel" pr={1} data-testid="number-of-nights" />
        <OfferBreakdownText color="greys.steel" data-testid="from-currency">
          from {isCashCurrency ? `(${total.currency})` : ''}
        </OfferBreakdownText>
      </Box>
    );
  } else {
    const formattedCheckIn = formatDate(checkIn, CARD_DATE_FORMAT);
    const formattedCheckOut = formatDate(checkOut, CARD_DATE_FORMAT);

    return (
      <OfferWrapper color="greys.steel">
        <OfferBreakdownText data-testid="formatted-dates">
          {formattedCheckIn} - {formattedCheckOut}
          <Hide as="span" xs={true} md={false}>
            &nbsp;•&nbsp;
          </Hide>
        </OfferBreakdownText>
        <Box>
          <NumberOfNights checkIn={checkIn} checkOut={checkOut} />
          <OfferBreakdownText data-testid="from-currency">&nbsp;from {isCashCurrency ? `(${total.currency})` : ''}</OfferBreakdownText>
        </Box>
      </OfferWrapper>
    );
  }
};

ConnectedNumberOfNights.propTypes = {
  checkIn: PropTypes.instanceOf(Date),
  checkOut: PropTypes.instanceOf(Date),
  total: PropTypes.shape({
    amount: PropTypes.string.isRequired,
    currency: PropTypes.string.isRequired,
  }).isRequired,
  isCashCurrency: PropTypes.bool.isRequired,
};

const OfferBreakdown = ({ total, totalBeforeDiscount, hasDiscount, offerType, checkIn, checkOut }) => {
  const isCashCurrency = total.currency !== 'PTS';
  const isClassic = offerType === 'classic';

  return (
    <Flex flexDirection="column" alignItems="flex-end">
      <ConnectedNumberOfNights checkIn={checkIn} checkOut={checkOut} total={total} isCashCurrency={isCashCurrency} />
      <OfferPrice>
        <Flex alignItems="flex-end" data-testid="price">
          {isClassic && <Icon name="ribbon" size={[28, 32]} color="greys.charcoal" data-testid="classic-ribbon" />}
          <Currency
            {...total}
            color="greys.charcoal"
            alignCurrency="superscript"
            fontSize={['28px', 'xl']}
            fontWeight="bold"
            roundToCeiling
            hideCurrency
          />
          {!isCashCurrency && (
            <Text fontSize={['xs', 'sm']} fontWeight="bold" color="greys.charcoal" px={1} data-testid="currency-points">
              PTS<sup>*</sup>
            </Text>
          )}
        </Flex>
        {hasDiscount && !isClassic && (
          <Box>
            <PriceBeforeDiscount
              total={totalBeforeDiscount}
              offerType={offerType}
              roundToCeiling
              hideCurrency={isCashCurrency}
              mr={1}
              hidePointsStrikeThrough
            />
          </Box>
        )}
      </OfferPrice>
    </Flex>
  );
};

OfferBreakdown.propTypes = {
  total: PropTypes.object.isRequired,
  totalBeforeDiscount: PropTypes.object,
  hasDiscount: PropTypes.bool,
  offerType: PropTypes.string,
  checkIn: PropTypes.instanceOf(Date),
  checkOut: PropTypes.instanceOf(Date),
};

OfferBreakdown.defaultProps = {
  totalBeforeDiscount: {},
  hasDiscount: false,
  offerType: undefined,
  checkIn: null,
  checkOut: null,
};

export default OfferBreakdown;
