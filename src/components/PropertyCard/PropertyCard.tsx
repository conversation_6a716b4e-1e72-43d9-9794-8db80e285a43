import styled from '@emotion/styled';
import React, { useRef } from 'react';
import { Flex, Heading, Text } from '@qga/roo-ui/components';
import { themeGet } from 'styled-system';
import PointsEarnSummary from 'components/PointsEarnSummary';
import Truncate from 'components/Truncate';
import PropertyRatings from './PropertyRatings';
import OfferBreakdown from './OfferBreakdown';
import CampaignPriceMessage from 'components/CampaignPriceMessage';
import { useBreakpoints } from 'hooks/useBreakpoints';
import { getPropertyLinkQuery } from 'store/search/searchSelectors';
import stringifyQueryValues from 'lib/search/stringifyQueryValues';
import { useSelector } from 'react-redux';
import { getIsMobileApp } from 'store/ui/uiSelectors';
import { POINTS_PER_DOLLAR_DEFAULT, POINTS_EARN_ENABLED } from 'config';
import PointsPerDollar from 'components/PointsPerDollar';
import { InlineCard, StackedCard } from './StyledCard';
import { PropertyCardProps } from 'types/property';
import useViewPromotionEvent from 'hooks/useViewPromotionEvent';
import useSelectPromotionEvent from 'hooks/useSelectPromotionEvent';

const PropertyName = styled(Heading.h4)`
  ${themeGet('card.propertyCard.propertyName')};
`;

const OfferBreakdownText = styled(Text)`
  ${themeGet('card.propertyCard.offerBreakdown')};
`;

const FreeCancellationWrapper = styled(Flex)`
  ${themeGet('card.propertyCard.freeCancellationWrapper')};
`;

const PropertyCard = ({
  id: propertyId,
  inline,
  propertyName,
  rating: starRating,
  ratingType: starRatingType,
  customerRating,
  total,
  checkIn = null,
  checkOut = null,
  hasDiscount,
  totalBeforeDiscount,
  isNonRefundable,
  pointsEarned,
  offerType,
  imageSrc,
  imageSrcSet,
  imageAltTag,
  promotionName = '',
  onClick,
  showCampaignMessage = true,
  showRatingTooltip = true,
  featuredOfferId,
  recommended = false,
  country,
  ...rest
}: PropertyCardProps) => {
  const ref = useRef(null);
  const { isLessThanBreakpoint } = useBreakpoints();
  const isMobile = isLessThanBreakpoint(0);
  const isMobileOrTablet = isLessThanBreakpoint(1);
  const isMobileApp = useSelector(getIsMobileApp);

  const shouldOpenInSameTab = isMobileOrTablet || isMobileApp;

  const promotion = { name: promotionName, slot: 'property_card' };
  const { fireSelectPromotionEvent } = useSelectPromotionEvent({ promotion });
  useViewPromotionEvent({ ref, promotion });

  const propertyLinkQuery = useSelector(getPropertyLinkQuery);
  if (!propertyId || !propertyName) return null;

  const updatedPropertyLinkQuery = {
    ...propertyLinkQuery,
    ...(featuredOfferId && {
      featuredOfferId,
      payWith: offerType === 'classic' ? 'points' : 'cash',
    }),
    ...(recommended && { recommended: 'true' }),
  };
  const propertyLinkQueryString = stringifyQueryValues(updatedPropertyLinkQuery);
  const propertyUrl = `/properties/${propertyId}?${propertyLinkQueryString}`;

  const Component = inline ? InlineCard : StackedCard;
  const currency = total.currency;
  const isCurrencyCash = total.currency !== 'PTS';

  const pointsPerDollar = pointsEarned?.maxQffEarnPpd ? pointsEarned.maxQffEarnPpd : POINTS_PER_DOLLAR_DEFAULT;

  const handleClick = () => {
    fireSelectPromotionEvent();
    onClick && onClick();
  };

  return (
    <div ref={ref} style={{ width: '100%' }}>
      <Component
        compact={isMobile}
        imageAltTag={imageAltTag}
        imageSrc={imageSrc}
        imageSrcSet={imageSrcSet}
        onClick={handleClick}
        promotionName={promotionName}
        target={shouldOpenInSameTab ? '_self' : '_blank'}
        to={propertyUrl}
        {...rest}
      >
        <Flex flexDirection="column" justifyContent="space-between" height="100%">
          <Flex flexDirection="column">
            <Truncate textAlign="left" lines={2} mt={1}>
              <PropertyName lineHeight="1.25">{propertyName}</PropertyName>
            </Truncate>
            <PropertyRatings
              starRating={starRating}
              starRatingType={starRatingType}
              customerRating={customerRating}
              showTooltip={showRatingTooltip}
            />
          </Flex>

          <Flex alignItems="flex-end" justifyContent="flex-end" flexDirection="column">
            <OfferBreakdown
              hasDiscount={hasDiscount}
              offerType={offerType}
              total={total}
              totalBeforeDiscount={totalBeforeDiscount}
              checkIn={checkIn}
              checkOut={checkOut}
            />
            {showCampaignMessage && <CampaignPriceMessage currency={currency} offerType={offerType} country={country} />}
            {!featuredOfferId && currency !== 'PTS' && POINTS_EARN_ENABLED && isCurrencyCash && (
              <PointsPerDollar pointsPerDollar={pointsPerDollar} fontSize={['xs', 'sm']} />
            )}
            {featuredOfferId && currency !== 'PTS' && <PointsEarnSummary {...pointsEarned.qffPoints} fontSize="sm" />}
            <FreeCancellationWrapper alignItems="flex-end">
              {!isNonRefundable && (
                <OfferBreakdownText color="green" display="block" data-testid="free-cancellation-text">
                  Free cancellation
                </OfferBreakdownText>
              )}
            </FreeCancellationWrapper>
          </Flex>
        </Flex>
      </Component>
    </div>
  );
};

export default PropertyCard;
