import React from 'react';
import PropertyCard from './PropertyCard';
import { useBreakpoints } from 'hooks/useBreakpoints';
import { mocked, mountUtils } from 'test-utils';
import { propertyWithPoints as property } from './mocks';
import { act } from 'react-dom/test-utils';
import { getPropertyLinkQuery } from 'store/search/searchSelectors';

jest.mock('config', () => ({
  POINTS_EARN_ENABLED: true,
}));

jest.mock('hooks/useBreakpoints');
jest.mock('store/search/searchSelectors');
jest.mock('store/router/routerSelectors');
jest.mock('store/ui/uiSelectors');

mountUtils.mockComponent('Image');
mountUtils.mockComponent('OfferBreakdown');
mountUtils.mockComponent('PropertyRatings');
mountUtils.mockComponent('CampaignPriceMessage');
mountUtils.mockComponent('PointsPerDollar');

const decorators = { router: true, theme: true, store: true };
const render = (props) => mountUtils(<PropertyCard {...props} />, { decorators });

describe('PropertyCard Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    mocked(getPropertyLinkQuery).mockReturnValue({ query: 'string' });
    mocked(useBreakpoints).mockReturnValue({
      isLessThanBreakpoint: () => true,
    });
  });

  it('should contains <PointsPerDollar>', () => {
    const { find } = render(property);
    expect(find('PointsPerDollar')).toExist();
  });

  it('shows the property name', () => {
    const { find } = render(property);

    expect(find('PropertyName').text()).toEqual('Sydney Harbour Marriott Hotel');
  });

  it('shows the property image', () => {
    const { find } = render(property);

    expect(find('Image')).toHaveProp({
      alt: 'imageAltTag',
      src: 'imageSrc',
    });
  });

  it('shows the property ratings', () => {
    const { find } = render(property);

    expect(find('PropertyRatings')).toHaveProp({
      customerRating: {
        averageRating: 4,
        source: 'trip_advisor',
        reviewCount: 1234,
      },
      starRating: 5,
      starRatingType: 'AAA',
    });
  });

  it('shows the offer breakdown', () => {
    const { find } = render({ ...property, offerType: 'classic', total: { amount: 21000, currency: 'PTS' } });

    expect(find('OfferBreakdown')).toHaveProp({
      offerType: 'classic',
      total: {
        amount: 21000,
        currency: 'PTS',
      },
    });
  });

  it('shows the CampaignPriceMessage', () => {
    const { find } = render({ ...property, offerType: 'no-classic', showCampaignMessage: true });

    expect(find('CampaignPriceMessage')).toExist();
  });

  it('renders a link to a new tab', () => {
    const { find } = render({ ...property });

    expect(find('StackedCardWrapper')).toHaveProp({
      to: '/properties/1234?query=string',
      rel: 'noopener noreferrer',
      target: '_self',
    });
  });

  describe('when the property is non-existant', () => {
    it('does NOT render the card', () => {
      const { find } = render({ ...property, id: null });

      expect(find('BaseCard')).not.toExist();
    });
  });

  describe('when `featuredOfferId` is set', () => {
    describe('and the offer is classic', () => {
      it('includes the featuredOfferId + payWith in the property link', () => {
        const { find } = render({ ...property, featuredOfferId: '321', offerType: 'classic' });

        expect(find('StackedCardWrapper')).toHaveProp({
          to: '/properties/1234?featuredOfferId=321&payWith=points&query=string',
          rel: 'noopener noreferrer',
        });
      });
    });

    describe('and the offer is non-classic', () => {
      it('includes the featuredOfferId in the property link', () => {
        const { find } = render({ ...property, featuredOfferId: '321' });

        expect(find('StackedCardWrapper')).toHaveProp({
          to: '/properties/1234?featuredOfferId=321&payWith=cash&query=string',
          rel: 'noopener noreferrer',
        });
      });
    });
  });

  describe('when a promotional offer is available', () => {
    it('shows the promotional sash', () => {
      const { find } = render({
        ...property,
        promotionName: 'Member Deals',
      });

      expect(find('PromotionalSash').text()).toEqual('Member Deals');
    });
  });

  describe('when free cancellation is available', () => {
    it('shows the free cancellation text', () => {
      const { findByTestId } = render({
        ...property,
        isNonRefundable: false,
      });

      expect(findByTestId('free-cancellation-text')).toHaveText('Free cancellation');
    });
  });

  describe('when the card is inline', () => {
    it('renders the inline card', () => {
      const { findByTestId } = render({ ...property, inline: true });

      expect(findByTestId('inline-card')).toExist();
    });

    it('renders a link to a new tab', () => {
      const { find } = render({ ...property, inline: true });

      expect(find('InlineCardWrapper')).toHaveProp({
        to: '/properties/1234?query=string',
        rel: 'noopener noreferrer',
        target: '_self',
      });
    });
  });

  describe('when the card is stacked', () => {
    it('renders the stacked card', () => {
      const { findByTestId } = render({ ...property, inline: false });

      expect(findByTestId('stacked-card')).toExist();
    });
  });

  describe('when the property card is selected', () => {
    it('triggers the onClick handler', () => {
      const mockOnClick = jest.fn();
      const { find } = render({ ...property, onClick: mockOnClick });

      act(() => {
        find('StackedCardWrapper').simulate('click');
      });

      expect(mockOnClick).toHaveBeenCalled();
    });
  });
});
