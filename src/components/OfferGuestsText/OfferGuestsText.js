import React from 'react';
import PropTypes from 'prop-types';
import { Text } from '@qga/roo-ui/components';
import pluralize from 'pluralize';
import styled from '@emotion/styled';

const UppercaseText = styled(Text)`
  text-transform: uppercase;
`;

const OfferGuestsText = ({ guests, numberOfNights, from = true, ...rest }) => {
  const message = `${pluralize('guest', guests, true)} • ${pluralize('night', numberOfNights, true)}${from ? ' from' : ''}`;

  return (
    <UppercaseText mb={2} fontWeight="bold" textTransform="uppercase" {...rest}>
      {message}
    </UppercaseText>
  );
};

OfferGuestsText.propTypes = {
  guests: PropTypes.number.isRequired,
  numberOfNights: PropTypes.number.isRequired,
  from: PropTypes.bool,
};

export default OfferGuestsText;
