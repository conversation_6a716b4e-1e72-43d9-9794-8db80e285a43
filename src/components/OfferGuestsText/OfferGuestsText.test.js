import React from 'react';
import OfferGuestsText from './OfferGuestsText';
import { mountUtils } from 'test-utils';

const decorators = { theme: true };
const defaultProps = { guests: 2, numberOfNights: 2 };
const render = (props) => mountUtils(<OfferGuestsText {...defaultProps} {...props} />, { decorators });

describe('OfferGuestsText', () => {
  describe('with From', () => {
    it('renders the OfferGuestsText', () => {
      const { find } = render();
      expect(find('UppercaseText')).toHaveText('2 guests • 2 nights from');
    });
  });

  describe('without From', () => {
    it('renders the OfferGuestsText', () => {
      const { find } = render({ from: false });
      expect(find('UppercaseText')).toHaveText('2 guests • 2 nights');
    });
  });
});
