import React from 'react';
import PropTypes from 'prop-types';
import { PromotionalBadge } from './PromotionalSash.style';

const promoIcons = {
  'classic reward': 'ribbon',
  'flyer deal': 'flightRight',
  'member deal': 'star',
};

const PromotionalSash = ({ promotionName, type }) => {
  const iconName = promoIcons[promotionName?.toLowerCase()];
  return <PromotionalBadge text={promotionName} iconName={iconName} size="rg" color="greys.charcoal" fontWeight="bold" type={type} />;
};

PromotionalSash.propTypes = {
  promotionName: PropTypes.string,
  type: PropTypes.string,
};

PromotionalSash.defaultProps = {
  promotionName: undefined,
  type: 'default',
};

export default PromotionalSash;
