import React from 'react';
import { mocked, mountUtils } from 'test-utils';
import NavigationMenu from './NavigationMenu';
import { useDataLayer } from 'hooks/useDataLayer';
import { useMediaQuery } from 'react-responsive';
import { HOTELS_PATH } from 'config';

jest.mock('hooks/useDataLayer');
jest.mock('react-responsive');

const props: React.ComponentProps<typeof NavigationMenu> = {
  children: 'Sydney',
  items: [
    {
      name: 'Most Popular',
      regions: [
        {
          fullName: 'Gold Coast, QLD, Australia',
          id: '51782',
          name: 'Gold Coast',
          slug: 'gold-coast',
        },
        {
          fullName: 'Melbourne, VIC, Australia',
          id: '51770',
          name: 'Melbourne',
          slug: 'melbourne',
        },
      ],
    },
  ],
  dealType: 'bonus-points',
};

const render = () => mountUtils(<NavigationMenu {...props} />, { decorators: { store: true, theme: true } });
const emitInteractionEvent = jest.fn();

describe('<NavigationMenu /> Desktop', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
    mocked(useMediaQuery).mockReturnValue(false);
  });

  it('renders with expected props', () => {
    const { find } = render();

    expect(find(NavigationMenu)).toHaveProp(props);
  });

  it('renders the <MenuButton />', () => {
    const { find, findByTestId } = render();

    expect(findByTestId('desktop-nav-trigger-button')).toExist();
    expect(find('TriggerButtonText').text()).toEqual('Sydney');
  });

  it('renders the <Accordion />', () => {
    const { find } = render();

    expect(find('Accordion')).toExist();
  });

  for (const item of props.items) {
    it('renders <AcccordionItem />', () => {
      const { find } = render();

      expect(find('AccordionItem')).toExist();
    });

    it('renders <AccordionButton />', () => {
      const { find } = render();

      expect(find('AccordionButton')).toExist();
      expect(find('AccordionButton').text()).toEqual(item.name);
    });

    for (const region of item.regions) {
      describe(region.name, () => {
        it('renders the link', () => {
          const { findByText } = render();

          expect(findByText(region.name)).toHaveProp({
            href: `${HOTELS_PATH}/deals/${region.slug}/bonus-points`,
          });
        });

        describe('when clicking the link', () => {
          it('emits an event to the data layer', () => {
            const { findByText } = render();
            findByText(region.name).simulate('click');

            expect(emitInteractionEvent).toHaveBeenCalledWith({
              type: 'Navigation Menu',
              value: `${region.fullName} Selected`,
            });
          });
        });
      });
    }
  }
});

describe('<NavigationMenu /> Mobile', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
    mocked(useMediaQuery).mockReturnValue(true);
  });

  it('renders with expected props', () => {
    const { find } = render();

    expect(find(NavigationMenu)).toHaveProp(props);
  });

  it('renders the <DisclosureButton />', () => {
    const { find, findByTestId } = render();

    expect(findByTestId('mobile-nav-trigger-button')).toExist();
    expect(find('TriggerButtonText').text()).toEqual('Sydney');
  });

  it('can open and close the modal', () => {
    const { findByTestId, findByText } = render();
    findByTestId('mobile-nav-trigger-button').simulate('click');
    findByTestId('close-modal-button').simulate('click');
    expect(findByText('Select a destination')).not.toExist();
  });

  it('renders the Accordion', () => {
    const { find } = render();

    expect(find('Accordion')).toExist();
  });

  for (const item of props.items) {
    it('renders <AcccordionItem />', () => {
      const { find } = render();

      expect(find('AccordionItem')).toExist();
    });

    it('renders <AccordionButton />', () => {
      const { find } = render();

      expect(find('AccordionButton')).toExist();
      expect(find('AccordionButton').text()).toEqual(item.name);
    });

    for (const region of item.regions) {
      describe(region.name, () => {
        it('renders the link', () => {
          const { findByText } = render();

          expect(findByText(region.name)).toHaveProp({
            href: `${HOTELS_PATH}/deals/${region.slug}/bonus-points`,
          });
        });

        describe('when clicking the link', () => {
          it('emits an event to the data layer', () => {
            const { findByText } = render();

            findByText(region.name).simulate('click');
            expect(emitInteractionEvent).toHaveBeenCalledWith({
              type: 'Navigation Menu',
              value: `${region.fullName} Selected`,
            });
          });
        });
      });
    }
  }
});
