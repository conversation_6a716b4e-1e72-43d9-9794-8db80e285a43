import React, { createRef, useCallback, useMemo, useRef, useState } from 'react';
import { Accordion, AccordionButton, AccordionItem, AccordionPanel } from '@reach/accordion';
import { Disclosure, DisclosureButton, DisclosurePanel } from '@reach/disclosure';
import { useId } from '@reach/auto-id';
import { Menu, MenuButton, MenuPopover } from '@reach/menu-button';
import { Position, positionDefault } from '@reach/popover';
import { useMediaQuery } from 'react-responsive';
import { Box, Flex, NakedButton } from '@qga/roo-ui/components';
import { theme } from '@qga/roo-ui';
import { useDropdownContext } from '@reach/dropdown';
import { HOTELS_PATH } from 'config';

import {
  Category,
  CategoryButton,
  GlobalStyles,
  Icon,
  Link,
  Links,
  LinksPanel,
  MenuLink,
  MenuItems,
  Root,
  TriggerButton,
  TriggerButtonText,
  Modal,
  ModalTitle,
} from './NavigationMenu.styles';
import { useDataLayer } from 'hooks/useDataLayer';
type Props = {
  children: React.ReactNode;
  items: {
    name: string;
    regions: {
      fullName: string;
      id: string;
      name: string;
      slug: string;
    }[];
    defaultOpen?: boolean;
  }[];
  targetRef?: React.RefObject<HTMLElement>;
  truncateText?: boolean;
  dealType: string;
};

const clamp = (value: number, max: number) => (value + max) % max;

const NavigationMenuInner = ({ children, items, targetRef, truncateText, dealType }: Props) => {
  const { emitInteractionEvent } = useDataLayer();
  const [index, setIndex] = useState(() => {
    const defaultOpenIndex = items.findIndex((category) => category.defaultOpen);
    return defaultOpenIndex > -1 ? defaultOpenIndex : 0;
  });
  const accordionRefs = useMemo(() => items.map(() => createRef<HTMLButtonElement>()), [items]);
  const firstLinkRef = useRef<HTMLAnchorElement>();
  const triggerRef = useRef<HTMLButtonElement>();
  const { dispatch } = useDropdownContext('NavigationMenuInner');
  const isSmallScreen = useMediaQuery({ query: `(max-width: ${theme.breakpoints[0]})` });
  const titleId = `title-${useId()}`;

  const position: Position = (targetRect, popoverRect) =>
    isSmallScreen
      ? positionDefault(new DOMRect(), popoverRect)
      : positionDefault(targetRef?.current?.getBoundingClientRect() || targetRect, popoverRect);

  const [isCollapsed, setIsCollapsed] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const closeMenu = () => {
    triggerRef.current.focus();
    triggerRef.current.click();
    // normally we shouldn't dispatch this action but in Safari triggering focus programmatically
    // doesn't work as expected
    dispatch({ type: 'CLOSE_MENU' });
  };

  const handleButtonKeyDown = (event: React.KeyboardEvent) => {
    switch (event.key) {
      case 'Escape':
        return closeMenu();
      case 'Enter':
        // we've changed the category, so reset the selection
        return dispatch({ type: 'OPEN_MENU_CLEARED' });
      case 'ArrowRight':
        // focus on the list of links
        return firstLinkRef.current.focus();
      default:
        return;
    }
  };
  const handlePanelKeyDown = (categoryIndex: number) => (event: React.KeyboardEvent) => {
    switch (event.key) {
      case 'Tab': {
        const nextCategoryIndex = clamp(categoryIndex + (event.shiftKey ? -1 : 1), accordionRefs.length);
        return accordionRefs[nextCategoryIndex].current.focus();
      }
      case 'ArrowLeft':
        // back to category selection
        return accordionRefs[categoryIndex].current.focus();
      default:
        return;
    }
  };

  const handleClick = useCallback(
    (regionName: string) => {
      emitInteractionEvent({ type: 'Navigation Menu', value: `${regionName} Selected` });
    },
    [emitInteractionEvent],
  );

  const handleDisclosure = useCallback(() => {
    setIsCollapsed(!isCollapsed);
  }, [isCollapsed]);

  const handleModalMenuToggle = useCallback(() => {
    setIsModalOpen(!isModalOpen);
  }, [isModalOpen]);

  return (
    <>
      <GlobalStyles />
      {!isSmallScreen && (
        <>
          <MenuButton
            as={TriggerButton}
            ref={triggerRef}
            aria-labelledby={titleId}
            truncateText={truncateText}
            data-testid="desktop-nav-trigger-button"
            onClick={handleDisclosure}
          >
            <TriggerButtonText truncateText={truncateText}>{children}</TriggerButtonText>
            <Icon name={isCollapsed ? 'expandMore' : 'expandLess'} />
          </MenuButton>
          <MenuPopover as={Root} categories={items.length} position={position}>
            <Accordion index={index} onChange={setIndex}>
              {items.map((category, categoryIndex) => (
                <AccordionItem key={category.name} as={Category}>
                  <AccordionButton as={CategoryButton} ref={accordionRefs[categoryIndex]} onKeyDown={handleButtonKeyDown}>
                    <Flex alignItems="center">
                      <Box fontSize="base" flex="1 0">
                        {category.name}
                      </Box>
                      <Icon
                        name="chevronRight"
                        // we want the icon to affect the layout but not to be visible
                        hidden={index !== categoryIndex}
                      />
                    </Flex>
                  </AccordionButton>
                  <AccordionPanel as={LinksPanel} onKeyDown={handlePanelKeyDown(categoryIndex)}>
                    {categoryIndex === index && (
                      // render only for the current category because we can't have multiple MenuItems
                      <MenuItems as={Links} data-testid="desktop-nav-menu-links">
                        {category.regions.map((region, linkIndex) => (
                          <MenuLink
                            key={linkIndex}
                            // eslint-disable-next-line react/no-children-prop
                            children={region.name}
                            href={`${HOTELS_PATH}/deals/${region.slug}/${dealType}`}
                            onClick={() => handleClick(region.fullName)}
                            as={Link}
                            ref={linkIndex === 0 ? firstLinkRef : undefined}
                            block
                          />
                        ))}
                      </MenuItems>
                    )}
                  </AccordionPanel>
                </AccordionItem>
              ))}
            </Accordion>
          </MenuPopover>
        </>
      )}
      {isSmallScreen && (
        <Disclosure open={isModalOpen}>
          <DisclosureButton
            as={TriggerButton}
            ref={triggerRef}
            aria-labelledby={titleId}
            truncateText={truncateText}
            data-testid="mobile-nav-trigger-button"
            onClick={handleModalMenuToggle}
          >
            <TriggerButtonText truncateText={truncateText}>{children}</TriggerButtonText>
            <Icon name={!isModalOpen ? 'expandMore' : 'expandLess'} />
          </DisclosureButton>
          <Modal isOpen={isModalOpen}>
            <ModalTitle justifyContent="space-between">
              Select a destination
              <NakedButton onClick={handleModalMenuToggle} data-testid="close-modal-button">
                <Icon name="close" />
              </NakedButton>
            </ModalTitle>
            <DisclosurePanel>
              <Accordion index={index} onChange={setIndex}>
                {items.map((category, categoryIndex) => (
                  <AccordionItem key={category.name} as={Category}>
                    <AccordionButton as={CategoryButton} ref={accordionRefs[categoryIndex]}>
                      <Flex alignItems="center">
                        <Box fontSize="base" flex="1 0">
                          {category.name}
                        </Box>
                        <Icon
                          name="expandMore"
                          // we want the icon to affect the layout but not to be visible
                          hidden={index !== categoryIndex}
                        />
                      </Flex>
                    </AccordionButton>
                    <AccordionPanel as={LinksPanel}>
                      {categoryIndex === index && (
                        // render only for the current category because we can't have multiple MenuItems
                        <MenuItems as={Links} data-testid="nav-menu-links" fontSize="sm">
                          {category.regions.map((region, linkIndex) => (
                            <MenuLink
                              key={linkIndex}
                              // eslint-disable-next-line react/no-children-prop
                              children={region.name}
                              href={`${HOTELS_PATH}/deals/${region.slug}/${dealType}`}
                              onClick={() => handleClick(region.fullName)}
                              as={Link}
                              ref={linkIndex === 0 ? firstLinkRef : undefined}
                              block
                            />
                          ))}
                        </MenuItems>
                      )}
                    </AccordionPanel>
                  </AccordionItem>
                ))}
              </Accordion>
            </DisclosurePanel>
          </Modal>
        </Disclosure>
      )}
    </>
  );
};

const NavigationMenu = (props: Props) => (
  <Menu>
    {/* we do this so we have access to menu-button's context in our component (via useDropdownContext) */}
    <NavigationMenuInner {...props} />
  </Menu>
);

export default NavigationMenu;
