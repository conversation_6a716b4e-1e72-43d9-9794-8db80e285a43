import React, { useCallback, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Flex, Box, Tooltip } from '@qga/roo-ui/components';
import { useBreakpoints } from 'hooks/useBreakpoints';
import { getQueryPayWith, getQueryMinPrice, getQueryMaxPrice } from 'store/router/routerSelectors';
import { updateQuery as updateQueryAction } from 'store/search/searchActions';
import useResponsiveProp from 'hooks/useResponsiveProp';
import PayWithButtonGroup from 'components/PayWithButtonGroup';
import { getPayWithToggleMessage } from 'store/campaign/campaignSelectors';

type Props = {
  name: string;
};

const PayWith = ({ name, ...rest }: Props) => {
  const dispatch = useDispatch();
  const payWith = useSelector(getQueryPayWith);
  const minPrice = useSelector(getQueryMinPrice);
  const maxPrice = useSelector(getQueryMaxPrice);
  const payWithToggleMessage = useSelector(getPayWithToggleMessage);
  const [showTooltip, setShowTooltip] = useState(true);
  const pointerPosition = useResponsiveProp(['92.4%', '50%', '50%', '92.3%']);
  const pointerDirection = useResponsiveProp(['bottom', 'right', 'right', 'bottom']);

  const { isLessThanBreakpoint } = useBreakpoints();
  const isDesktop = isLessThanBreakpoint(3);

  const updateQuery = useCallback(
    (query) => {
      dispatch(updateQueryAction(query));
    },
    [dispatch],
  );

  const isDesktopAndTooltipDismissed = !showTooltip && isDesktop;

  return (
    <Flex
      flexDirection={['column', 'row', 'row', 'column']}
      alignItems={['center', 'center', 'center', 'flex-end']}
      width="100%"
      py={[4, 4, 4, 4]}
      px={[4, 4, 4, 0]}
      mt={isDesktopAndTooltipDismissed ? '56px' : 'inherit'}
    >
      {showTooltip && !!payWithToggleMessage && (
        <Tooltip
          data-testid="pay-with-tooltip"
          pointerPosition={pointerPosition}
          pointerDirection={pointerDirection}
          mb={[4, 0, 0, 4]}
          mr={[0, 4, 4, 0]}
          onClose={() => setShowTooltip(false)}
        >
          {payWithToggleMessage}
        </Tooltip>
      )}
      <Box width={['100%', 'auto']}>
        <PayWithButtonGroup {...rest} name={name} payWith={payWith} minPrice={minPrice} maxPrice={maxPrice} updateQuery={updateQuery} />
      </Box>
    </Flex>
  );
};

export default PayWith;
