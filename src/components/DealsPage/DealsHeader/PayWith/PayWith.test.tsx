import React from 'react';
import { mountUtils, mocked } from 'test-utils';
import PayWith from './PayWith';
import { getQueryPayWith, getQueryMinPrice, getQueryMaxPrice } from 'store/router/routerSelectors';
import { updateQuery as updateQueryAction } from 'store/search/searchActions';
import { getPayWithToggleMessage } from 'store/campaign/campaignSelectors';

jest.mock('store/campaign/campaignSelectors');
jest.mock('store/router/routerSelectors');
jest.mock('store/search/searchActions');
mountUtils.mockComponent('PayWithButtonGroup');

const queryPayWith = 'cash';
const queryMinPrice = 0;
const queryMaxPrice = 10000;

describe('<PayWith />', () => {
  const render = () => mountUtils(<PayWith name="test" />, { decorators: { store: true, theme: true } });

  beforeAll(() => {
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(() => ({
        matches: false,
      })),
    });
    mocked(updateQueryAction).mockReturnValue(jest.fn());
    mocked(getQueryPayWith).mockReturnValue(queryPayWith);
    mocked(getQueryMinPrice).mockReturnValue(queryMinPrice);
    mocked(getQueryMaxPrice).mockReturnValue(queryMaxPrice);
  });

  it('renders <PayWithButtonGroup>', () => {
    const { find } = render();
    expect(find('PayWithButtonGroup')).toExist();
  });

  describe('when there is no pay with toggle message', () => {
    beforeEach(() => {
      mocked(getPayWithToggleMessage).mockReturnValue(null);
    });

    it('does NOT render <Tooltip>', () => {
      const { find } = render();

      expect(find('Tooltip')).not.toExist();
    });
  });

  describe('when there is a pay with toggle message', () => {
    beforeEach(() => {
      mocked(getPayWithToggleMessage).mockReturnValue('30% better value when you use points');
    });

    it('renders <Tooltip> with correct text', () => {
      const { findByTestId } = render();
      expect(findByTestId('pay-with-tooltip').text()).toEqual('30% better value when you use points');
    });

    it('can close the tooltip', () => {
      const { wrapper, findByTestId } = render();
      wrapper.find('NakedButton').simulate('click');
      expect(findByTestId('pay-with-tooltip')).not.toExist();
    });
  });
});
