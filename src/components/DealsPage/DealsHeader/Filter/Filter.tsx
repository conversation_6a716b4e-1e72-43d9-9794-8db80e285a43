import React, { useState, useRef, useEffect } from 'react';
import { Box, Flex, Radio, Icon, NakedButton } from '@qga/roo-ui/components';
import { useDispatch, useSelector } from 'react-redux';
import { getDealType, getRegion, getSortedAvailableDealTypes } from 'store/deal/dealSelectors';
import useScroll from 'hooks/useScroll';
import {
  FilterWrapper,
  FilterButton,
  OptionWrapper,
  OptionItems,
  OptionButton,
  Modal,
  ModalTitle,
  ModalBody,
  ModalOption,
  ModalLabel,
  ViewDealsButton,
} from './Filter.style';
import { useDataLayer } from 'hooks/useDataLayer';
import startCase from 'lodash/startCase';
import find from 'lodash/find';
import { useRouter } from 'next/router';
import { setDealType } from 'store/deal/dealActions';
import { getQueryPayWith } from 'store/router/routerSelectors';
import FilterSkeleton from './FilterSkeleton';
import { useBreakpoints } from 'hooks/useBreakpoints';

const Filter = () => {
  const scroll = useScroll();
  const dispatch = useDispatch();
  const { emitInteractionEvent } = useDataLayer();
  const filterRef = useRef<HTMLElement>();
  const dealType = useSelector(getDealType);
  const payWith = useSelector(getQueryPayWith);
  const slider = useRef<HTMLDivElement>(null);
  const [isDown, setIsDown] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalValue, setModalValue] = useState(dealType?.code);
  const [isSticky, setIsSticky] = useState(false);
  const router = useRouter();
  const { slug } = useSelector(getRegion);
  const { isLessThanBreakpoint } = useBreakpoints();
  const isTablet = isLessThanBreakpoint(1);
  const sortedAvailableDealTypes = useSelector(getSortedAvailableDealTypes);

  useEffect(() => {
    if (!isTablet || !filterRef.current || typeof window === 'undefined') {
      setIsSticky(false);
    } else {
      const stickyDistance = window.pageYOffset + filterRef.current.getBoundingClientRect().top;
      setIsSticky(scroll > stickyDistance);
    }
  }, [filterRef, isTablet, scroll]);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleStart = (event: any) => {
    setIsDown(true);
    setStartX(event.pageX || event.touches[0].pageX - slider.current.offsetLeft);
    setScrollLeft(slider.current.scrollLeft);
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleMove = (event: any) => {
    if (!isDown) return;
    event.preventDefault();

    const x = event.pageX || event.touches[0].pageX - slider.current.offsetLeft;
    const dist = x - startX;
    slider.current.scrollLeft = scrollLeft - dist;
  };

  const handleEnd = () => {
    setIsDown(false);
  };

  const handleNavigation = (code: string) => {
    const nextDealType = find(sortedAvailableDealTypes, ['code', code]);
    const nextPath = nextDealType ? `/deals/${slug}/${nextDealType?.slug}` : `/deals/${slug}`;

    if (typeof nextDealType !== 'undefined') dispatch(setDealType(nextDealType));
    router.push({ pathname: nextPath, query: { payWith, page: 1 } }, undefined, { shallow: true });
  };

  const handleClick = (code: string) => {
    const nextDealType = find(sortedAvailableDealTypes, ['code', code]);
    const name = nextDealType?.name;

    emitInteractionEvent({
      type: 'Deal Type Filters',
      value: `${startCase(name)} Selected`,
    });

    setIsDown(false);
    handleNavigation(code);
  };

  const handleMenuToggle = () => {
    setIsModalOpen(!isModalOpen);
    const interaction = {
      type: 'Deal Type Filters',
      value: `Menu ${isModalOpen ? 'Closed' : 'Opened'}`,
    };

    emitInteractionEvent(interaction);
  };

  const handleModalSubmit = () => {
    setIsModalOpen(false);

    emitInteractionEvent({
      type: 'Deal Type Filters',
      value: `${startCase(modalValue)} Selected`,
    });

    handleNavigation(modalValue);
  };

  return (
    <>
      <Modal isOpen={isModalOpen} top={scroll}>
        <ModalTitle justifyContent="space-between">
          Filter deals
          <NakedButton onClick={handleMenuToggle} data-testid="close-modal-button">
            <Icon name="close" />
          </NakedButton>
        </ModalTitle>
        <ModalBody flexDirection="column" justifyContent="space-between">
          <Flex flexDirection="column">
            {sortedAvailableDealTypes.map((type) => (
              <ModalOption key={type.code}>
                <ModalLabel textStyle="h5" mb={0}>
                  <Radio
                    name="filter"
                    defaultChecked={modalValue === type.code}
                    onClick={() => setModalValue(type.code)}
                    data-testid={`filter-radio-${type.code}`}
                  />
                  {type.name}
                </ModalLabel>
              </ModalOption>
            ))}
          </Flex>
          <Box position="relative">
            <ViewDealsButton variant="primary" onClick={handleModalSubmit} data-testid="submit-modal-button">
              View Deals
            </ViewDealsButton>
          </Box>
        </ModalBody>
      </Modal>
      <Box ref={filterRef} height={isSticky ? 42 : 0} />
      <FilterWrapper isSticky={isSticky}>
        {!sortedAvailableDealTypes.length ? (
          <FilterSkeleton />
        ) : (
          <>
            <Box py={1}>
              <FilterButton onClick={handleMenuToggle} data-testid="open-modal-button">
                <Icon name="filterList" />
              </FilterButton>
            </Box>
            <OptionWrapper>
              <OptionItems
                ref={slider}
                onMouseDown={handleStart}
                onTouchStart={handleStart}
                onMouseMove={handleMove}
                onTouchMove={handleMove}
                onMouseLeave={handleEnd}
                onTouchEnd={handleEnd}
                isDragging={isDown}
              >
                {sortedAvailableDealTypes.map((type) => (
                  <OptionButton
                    key={type.code}
                    isActive={dealType?.code === type.code}
                    onClick={() => handleClick(type.code)}
                    data-testid={`filter-option-${type.code}`}
                  >
                    {type.name}
                  </OptionButton>
                ))}
              </OptionItems>
            </OptionWrapper>
          </>
        )}
      </FilterWrapper>
    </>
  );
};

export default Filter;
