import React from 'react';
import { mocked, mountUtils } from 'test-utils';
import Filter from './Filter';
import { updateQuery } from 'store/search/searchActions';
import { setDealType } from 'store/deal/dealActions';
import { getSortedAvailableDealTypes, getDealType, getRegion } from 'store/deal/dealSelectors';
import { getQueryPayWith } from 'store/router/routerSelectors';
import useScroll from 'hooks/useScroll';
import { useDataLayer } from 'hooks/useDataLayer';
import { ALLOWED_DEAL_TYPES, DEFAULT_DEAL_TYPE } from 'config';

jest.mock('store/search/searchActions');
jest.mock('store/deal/dealSelectors');
jest.mock('store/router/routerSelectors');
jest.mock('hooks/useDataLayer');
jest.mock('hooks/useScroll');
const mockRouter = {
  push: jest.fn(),
};

jest.mock('next/router', () => ({
  useRouter: jest.fn(() => mockRouter),
}));

const mockEmitInteractionEvent = jest.fn();

const dealType = {
  name: 'All Deals',
  code: 'all-deals',
};

const mockRegion = {
  id: '1234',
  name: 'Sydney',
  fullName: 'Sydney, NSW, Australia',
  slug: 'sydney',
};

let wrapper;

describe('<Filter />', () => {
  const render = () => mountUtils(<Filter />, { decorators: { store: true, theme: true } });

  beforeEach(() => {
    jest.clearAllMocks();

    mocked(updateQuery).mockReturnValue(jest.fn());
    mocked(getSortedAvailableDealTypes).mockReturnValue([DEFAULT_DEAL_TYPE, ...ALLOWED_DEAL_TYPES]);
    mocked(getDealType).mockReturnValue(dealType);
    mocked(useDataLayer).mockReturnValue({ emitInteractionEvent: mockEmitInteractionEvent });
    mocked(useScroll).mockReturnValue(0);
    mocked(getQueryPayWith).mockReturnValue('points');
    mocked(getRegion).mockReturnValue(mockRegion);
  });

  it('renders each deal type as a button', () => {
    const { findByTestId } = render();
    for (const type of ALLOWED_DEAL_TYPES) {
      expect(findByTestId(`filter-option-${type.code}`)).toHaveText(type.name);
    }
  });

  describe('all deals', () => {
    it('renders all deals as a button', () => {
      const { findByTestId } = render();
      expect(findByTestId(`filter-option-all_deals`)).toHaveText('All deals');
    });

    describe('when clicking the all deals option', () => {
      beforeEach(() => {
        wrapper = render();
        wrapper.findByTestId('filter-option-all_deals').simulate('click');
      });

      it('emits an event to the data layer', () => {
        expect(mockEmitInteractionEvent).toHaveBeenCalledWith({
          type: 'Deal Type Filters',
          value: 'All Deals Selected',
        });
      });
    });
  });

  describe('when clicking an option', () => {
    beforeEach(() => {
      wrapper = render();
      wrapper.findByTestId('filter-option-best_deals').simulate('click');
    });

    it('emits an event to the data layer', () => {
      expect(mockEmitInteractionEvent).toHaveBeenCalledWith({
        type: 'Deal Type Filters',
        value: 'Best Deals Selected',
      });
    });

    it('sets the next deal type', () => {
      expect(wrapper.decorators.store.dispatch).toHaveBeenCalledWith(setDealType(ALLOWED_DEAL_TYPES[0]));
    });

    it('handles router navigation', () => {
      expect(mockRouter.push).toHaveBeenCalledWith(
        {
          pathname: '/deals/sydney/best-deals',
          query: { payWith: 'points', page: 1 },
        },
        undefined,
        { shallow: true },
      );
    });
  });

  it('renders the filter button correctly', () => {
    const { findByTestId } = render();
    expect(findByTestId('open-modal-button')).toExist();
  });

  it('can open and close the modal', () => {
    const { findByTestId, findByText } = render();
    findByTestId('open-modal-button').simulate('click');
    findByTestId('close-modal-button').simulate('click');
    expect(findByText('Filter deals')).not.toExist();
  });

  xit('can change the value from the modal', () => {
    const { findByTestId } = render();
    findByTestId('open-modal-button').simulate('click');
    findByTestId('filter-radio-luxury-offers').simulate('click');
    findByTestId('submit-modal-button').simulate('click');
    expect(updateQuery).toHaveBeenCalledWith({ dealType: 'luxury-offers' });
  });

  describe('when clicking an option', () => {
    beforeEach(() => {
      wrapper = render();
      wrapper.findByTestId('open-modal-button').simulate('click');
      wrapper.findByTestId('filter-radio-luxury_offers').simulate('click');
      wrapper.findByTestId('submit-modal-button').simulate('click');
    });

    it('emits an event to the data layer', () => {
      expect(mockEmitInteractionEvent).toHaveBeenCalledWith({
        type: 'Deal Type Filters',
        value: 'Luxury Offers Selected',
      });
    });

    it('sets the next deal type', () => {
      expect(wrapper.decorators.store.dispatch).toHaveBeenCalledWith(setDealType(ALLOWED_DEAL_TYPES[1]));
    });

    it('handles router navigation', () => {
      expect(mockRouter.push).toHaveBeenCalledWith(
        {
          pathname: '/deals/sydney/luxury-offers',
          query: { payWith: 'points', page: 1 },
        },
        undefined,
        { shallow: true },
      );
    });
  });
});
