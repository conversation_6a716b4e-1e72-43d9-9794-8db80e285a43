import React from 'react';
import { mocked, mountUtils } from 'test-utils';
import { getDealType, getHeroImage, getRegionName, getCampaignBanner } from 'store/deal/dealSelectors';
import DealsHeader from './DealsHeader';
import { getImage } from 'lib/sanity';
import { PAYWITH_TOGGLE_ENABLED } from 'config';

jest.mock('store/deal/dealSelectors');
mountUtils.mockComponent('NavigationMenu');
mountUtils.mockComponent('Filter');
mountUtils.mockComponent('PayWith');
mountUtils.mockComponent('CampaignBanner');

const dealType = {
  name: 'All Deals',
  code: 'all-deals',
};

const heroImage = {
  asset: {
    _ref: 'image-4bf004ffa6b38474026debfb71a8ef64571b11b7-1000x588-jpg',
    _type: 'reference',
  },
  caption: 'A picture of a crystal blue pool, surrounded by recliner chairs',
};

const regionName = 'Sydney';

const campaignBanner = {
  heroImage: {
    asset: {
      _ref: 'image-d72c046d0faffb58a7138d832ac4e899a80ad18e-5464x3640-jpg',
      _type: 'reference',
    },
    caption: 'Example image caption',
  },
};

describe('<DealsHeader />', () => {
  const render = () => mountUtils(<DealsHeader />, { decorators: { store: true, theme: true } });

  beforeEach(() => {
    mocked(getDealType).mockReturnValue(dealType);
    mocked(getRegionName).mockReturnValue(regionName);
    mocked(getHeroImage).mockReturnValue(heroImage);
    mocked(getCampaignBanner).mockReturnValue(null);
  });

  it('renders heading with selected deal type', () => {
    const { findByTestId } = render();
    expect(findByTestId('deals-header-heading').getDOMNode()).toHaveTextContent(`${dealType.name} in`);
  });

  it('renders heading body text for mobile and desktop', () => {
    const { findByText } = render();
    expect(findByText('These are the best deals available for the next 30 days. Prices are for 2 adults and 2 nights.')).toHaveLength(2);
  });

  it('renders NavigationMenu component', () => {
    const { find } = render();
    expect(find('NavigationMenu')).toExist();
  });

  it('renders Filter component', () => {
    const { find } = render();
    expect(find('Filter')).toExist();
  });

  it('renders PayWith component if enabled', () => {
    const { find } = render();
    if (PAYWITH_TOGGLE_ENABLED) {
      expect(find('PayWith')).toExist();
    } else {
      expect(find('PayWith')).not.toExist();
    }
  });

  describe('without CampaignBanner', () => {
    it('renders default hero image', () => {
      const { find } = render();
      const image = getImage(heroImage.asset);
      expect(find('BackgroundImage')).toHaveProp({ src: image.large });
    });
  });

  describe('with CampaignBanner', () => {
    beforeEach(() => {
      mocked(getCampaignBanner).mockReturnValue(campaignBanner);
      mocked(getHeroImage).mockReturnValue(campaignBanner.heroImage);
    });

    it('renders campaign banner', () => {
      const { find } = render();
      expect(find('CampaignBanner')).toExist();
    });

    it('renders campaign hero image', () => {
      const { find } = render();
      const image = getImage(campaignBanner.heroImage.asset);
      expect(find('BackgroundImage')).toHaveProp({ src: image.large });
    });
  });
});
