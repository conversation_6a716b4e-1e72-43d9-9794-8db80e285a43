import React from 'react';
import { Badge, Heading, OutlineButton, Paragraph } from '@qga/roo-ui/components';
import Markdown from 'components/Markdown';
import { CampaignBannerWrapper, CampaignBannerCard } from './CampaignBanner.style';
import getMarkdownForBlockContent from 'lib/sanity/getMarkdownForBlockContent';
import { CampaignBanner as CampaignBannerProps, CallToAction } from 'types/content';
import { useRouter } from 'next/router';
import { useDataLayer } from 'hooks/useDataLayer';

const CampaignBanner = ({ heading, description, promotionalSash, callToAction }: CampaignBannerProps) => {
  const router = useRouter();
  const { emitInteractionEvent } = useDataLayer();

  const handleCtaClick = (cta: CallToAction) => {
    emitInteractionEvent({
      type: cta.trackingEvent.category || 'Campaign Banner',
      value: cta.trackingEvent.action || 'Call To Action Selected',
      customAttributes: { label: cta.trackingEvent.label },
    });
    if (cta.linkType === 'external' && typeof window !== 'undefined') {
      window.open(cta.url, cta.isNewTab && '_blank');
    } else {
      router.push(cta.url);
    }
  };

  return (
    <CampaignBannerWrapper>
      <CampaignBannerCard p={4}>
        {promotionalSash && <Badge text={promotionalSash} mb={4} />}
        {heading && <Heading fontSize="lg">{heading.name}</Heading>}
        {description && (
          <Markdown
            content={getMarkdownForBlockContent(description)}
            customComponents={{
              span: { component: Paragraph, props: { color: 'alto', mb: 4, mr: 10 } },
            }}
          />
        )}
        {callToAction &&
          callToAction.map((cta) => (
            <OutlineButton
              key={cta._key}
              variant={cta.buttonType}
              onClick={() => handleCtaClick(cta)}
              width={['100%', '100%', 'inherit']}
              data-testid={`campaign-banner-cta-${cta._key}`}
            >
              {cta.text}
            </OutlineButton>
          ))}
      </CampaignBannerCard>
    </CampaignBannerWrapper>
  );
};

export default CampaignBanner;
