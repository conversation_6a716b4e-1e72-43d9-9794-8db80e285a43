import React from 'react';
import { mountUtils, mocked } from 'test-utils';
import DealsHelmet from './DealsHelmet';
import { HOTELS_BRAND_NAME } from 'config';
import { getRegionName } from '../../../store/deal/dealSelectors';

jest.mock('store/deal/dealSelectors');

const regionName = 'Melbourne';
const description = `Find Incredible Hotel Deals with ${HOTELS_BRAND_NAME} today.`;

const decorators = { helmet: true, store: true };
const render = () => mountUtils(<DealsHelmet />, { decorators });

describe('Content Helmet', () => {
  beforeEach(() => {
    mocked(getRegionName).mockReturnValue(regionName);
  });

  it('sets the correct page title with helmet', () => {
    const { find } = render();
    expect(find('title')).toHaveText(`Hotels deals in ${regionName} | ${HOTELS_BRAND_NAME}`);
  });

  test.each`
    name              | content
    ${'description'}  | ${description}
    ${'og:site_name'} | ${HOTELS_BRAND_NAME}
    ${'og:title'}     | ${'Hotels deals in ' + regionName + ' | ' + HOTELS_BRAND_NAME}
  `('sets the meta for $name', ({ name, content }) => {
    const { find } = render();
    expect(find(`meta[name="${name}"]`)).toHaveProp({ content });
  });
});
