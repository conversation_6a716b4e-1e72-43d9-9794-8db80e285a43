import PropertyCard from 'components/PropertyCard';
import React, { useCallback } from 'react';
import { AvaCurrency, AvaPointsEarn } from 'types/ava';
import { useDataLayer } from 'hooks/useDataLayer';
import { useSelector } from 'react-redux';
import { getRegion } from 'store/deal/dealSelectors';
import { useBreakpoints } from 'hooks/useBreakpoints';
import useSelectItemEvent from 'hooks/useSelectItemEvent';
import { ALLOWED_DEAL_TYPES } from 'config/constants';

type Props = {
  country: string;
  checkIn: string;
  checkOut: string;
  customerRating: {
    averageRating: number;
    ratingImageUrl: string;
    reviewCount: number;
    source: string;
  };
  dealType: string;
  id: string;
  isNonrefundable: boolean;
  hasDiscount: boolean;
  imageAltTag: string;
  imageSrc: string;
  offerId: string;
  offerType?: string;
  offerName: string;
  pointsEarned: AvaPointsEarn;
  promotionName?: string;
  propertyName: string;
  propertyUrl?: string;
  rating: number;
  ratingType: string;
  total: AvaCurrency;
  totalBeforeDiscount: AvaCurrency;
};

const Deals = ({
  checkIn,
  checkOut,
  country,
  customerRating,
  dealType,
  id,
  hasDiscount,
  imageAltTag,
  imageSrc,
  isNonrefundable,
  offerId,
  offerType,
  offerName,
  pointsEarned,
  promotionName,
  propertyName,
  propertyUrl,
  rating,
  ratingType,
  total,
  totalBeforeDiscount,
}: Props) => {
  const { emitInteractionEvent } = useDataLayer();
  const { isLessThanBreakpoint } = useBreakpoints();
  const { fireSelectItemEvent } = useSelectItemEvent();
  const dealsRegion = useSelector(getRegion);
  const dealsPropertyUrl = `${propertyUrl}&dealsRegion=${dealsRegion.slug}&dealType=${dealType}`;
  const isMobile = isLessThanBreakpoint(0);

  const handleClick = useCallback(() => {
    const currentDealType = ALLOWED_DEAL_TYPES.find(({ slug }) => slug === dealType);
    fireSelectItemEvent({
      listName: `${currentDealType?.name ?? 'All Deals'} in ${dealsRegion.name}`,
      location: dealsRegion.name,
      type: 'list',
      property: {
        id,
        name: propertyName,
        category: 'hotels',
        propertyFacilities: [],
      },
      offer: {
        name: offerName,
        charges: {
          total,
        },
        promotion: {
          name: promotionName,
        },
      },
      roomType: null,
      query: null,
    });
    emitInteractionEvent({ type: 'Property Card', value: `${propertyName} Selected` });
  }, [dealType, dealsRegion.name, emitInteractionEvent, fireSelectItemEvent, id, offerName, promotionName, propertyName, total]);

  return (
    <>
      <PropertyCard
        checkIn={new Date(checkIn)}
        checkOut={new Date(checkOut)}
        country={country}
        customerRating={customerRating}
        id={id}
        inline={isMobile}
        hasDiscount={hasDiscount}
        imageAltTag={imageAltTag}
        imageSrc={imageSrc}
        isNonRefundable={isNonrefundable}
        offerId={offerId}
        offerType={offerType}
        onClick={handleClick}
        pointsEarned={pointsEarned}
        promotionName={promotionName}
        propertyName={propertyName}
        rating={rating}
        ratingType={ratingType}
        recommended={false}
        total={total}
        totalBeforeDiscount={totalBeforeDiscount}
        to={dealsPropertyUrl}
      />
    </>
  );
};

export default Deals;
