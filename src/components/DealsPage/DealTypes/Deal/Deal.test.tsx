import React from 'react';
import { act } from 'react-dom/test-utils';
import { useDataLayer } from 'hooks/useDataLayer';
import { mocked, mountUtils } from 'test-utils';
import Deal from './Deal';
import { mockDeal } from '../mocks';
import { getRegion } from 'store/deal/dealSelectors';

jest.mock('hooks/useDataLayer');
jest.mock('store/deal/dealSelectors');

mountUtils.mockComponent('PropertyCard');

const decorators = { store: true, theme: true };
const render = () => mountUtils(<Deal {...mockDeal} />, { decorators });

const emitInteractionEvent = jest.fn();

describe('<Deal />', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
    mocked(getRegion).mockReturnValue({ slug: 'sydney' });
  });

  it('renders PropertyCard with the expected props', () => {
    const { find } = render();

    expect(find('PropertyCard')).toHaveProp({
      checkIn: new Date('2022-05-29'),
      checkOut: new Date('2022-05-31'),
      country: 'Australia',
      customerRating: {
        averageRating: 4,
        ratingImageUrl: 'www.tripadvisor.com/img/cdsi/img2/ratings/traveler/4.0-15969-4.png',
        reviewCount: 5788,
        source: 'trip_advisor',
      },
      hasDiscount: true,
      id: '7340',
      imageAltTag: 'Exterior',
      imageSrc: 'https://staging-images-cdn.qantashotels.com/insecure/desktop_search/plain/media/60652fa9-1a86-45ae-9ba7-4c7b4b20ed54.png',
      offerId: '34761',
      offerType: 'standard',
      pointsEarned: {
        maxQbrEarnPpd: 1,
        maxQffEarnPpd: 6,
        promotionMultiplier: 2,
        propertyPpd: 3,
        qbrPoints: {
          total: 593,
        },
        qffPoints: {
          base: 1779,
          bonus: 1779,
          qffPointsClub: 0,
          total: 3558,
        },
      },
      promotionName: 'Best deals',
      propertyName: 'Property Name',
      rating: 4.5,
      ratingType: 'AAA',
      to: '/properties/7340?checkIn=2022-05-29&checkOut=2022-05-31&adults=2&featuredOfferId=34761&dealsRegion=sydney&dealType=classic-rewards',
      total: {
        amount: '35000',
        currency: 'PTS',
      },
      totalBeforeDiscount: {
        amount: '41000',
        currency: 'PTS',
      },
    });
  });

  describe('onClick', () => {
    it('emits an event to the data layer', () => {
      const { find } = render();

      act(() => {
        find('PropertyCard').prop('onClick')();
      });

      expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Property Card', value: 'Property Name Selected' });
    });
  });
});
