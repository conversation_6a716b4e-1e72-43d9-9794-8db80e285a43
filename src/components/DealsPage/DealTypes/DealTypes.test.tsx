import React from 'react';
import { useDataLayer } from 'hooks/useDataLayer';
import { mocked, mountUtils } from 'test-utils';
import DealTypes from './DealTypes';
import { mockDeal, mockDealType } from './mocks';
import { getDealsByType, getStatus, getDealType, getRegion } from 'store/deal/dealSelectors';
import { STORE_STATUS } from 'lib/enums/store';
import { DEFAULT_DEAL_TYPE, DEFAULT_DEAL_TYPE_ORDER, ALLOWED_DEAL_TYPES } from 'config';
import { intersectionWith } from 'lodash';

jest.mock('hooks/useDataLayer');
jest.mock('store/deal/dealSelectors');
mountUtils.mockComponent('PropertyCard');
mountUtils.mockComponent('DealTypeSkeleton');
mountUtils.mockComponent('Header');
mountUtils.mockComponent('ViewMoreButton');
mountUtils.mockComponent('Pagination');

const emitInteractionEvent = jest.fn();

export const LUXURY_OFFERS_ORDER = {
  best_deals: 3,
  bonus_points: 4,
  classic_rewards: 2,
  luxury_offers: 1,
  chain_deals: 5,
  flyer_deals: 6,
};

const mockDealsByType = intersectionWith(
  [
    {
      dealType: { ...mockDealType, code: 'best_deals', name: 'Best Deals' },
      results: Array.from({ length: 20 }).map(() => mockDeal),
      hasMore: true,
      count: 20,
    },
    {
      dealType: { ...mockDealType, code: 'bonus_points', name: 'Bonus Points' },
      results: Array.from({ length: 20 }).map(() => mockDeal),
      hasMore: true,
      count: 20,
    },
    {
      dealType: { ...mockDealType, code: 'classic_rewards', name: 'Classic Rewards' },
      results: Array.from({ length: 20 }).map(() => mockDeal),
      hasMore: true,
      count: 20,
    },
    {
      dealType: { ...mockDealType, code: 'luxury_offers', name: 'Luxury Offers' },
      results: Array.from({ length: 20 }).map(() => mockDeal),
      hasMore: true,
      count: 20,
    },
    {
      dealType: { ...mockDealType, code: 'chain_deals', name: 'Chain Deals' },
      results: Array.from({ length: 20 }).map(() => mockDeal),
      hasMore: true,
      count: 20,
    },
  ],
  ALLOWED_DEAL_TYPES,
  ({ dealType }, allowed) => dealType.code === allowed.code,
);

const unorderedMockDealsByType = [
  {
    dealType: { ...mockDealType, code: 'classic_rewards', name: 'Classic Rewards' },
    results: Array.from({ length: 20 }).map(() => mockDeal),
    hasMore: true,
    count: 20,
  },
  {
    dealType: { ...mockDealType, code: 'bonus_points', name: 'Bonus Points' },
    results: Array.from({ length: 20 }).map(() => mockDeal),
    hasMore: true,
    count: 20,
  },
  {
    dealType: { ...mockDealType, code: 'chain_deals', name: 'Chain Deals' },
    results: Array.from({ length: 20 }).map(() => mockDeal),
    hasMore: true,
    count: 20,
  },
  {
    dealType: { ...mockDealType, code: 'best_deals', name: 'Best Deals' },
    results: Array.from({ length: 20 }).map(() => mockDeal),
    hasMore: true,
    count: 20,
  },
  {
    dealType: { ...mockDealType, code: 'luxury_offers', name: 'Luxury Offers' },
    results: Array.from({ length: 20 }).map(() => mockDeal),
    hasMore: true,
    count: 20,
  },
];

const decorators = { store: true, theme: true };
const render = () => mountUtils(<DealTypes />, { decorators });

describe('<DealsByType />', () => {
  beforeAll(() => {
    jest.clearAllMocks();

    mocked(getDealType).mockReturnValue(DEFAULT_DEAL_TYPE);
    mocked(getDealsByType).mockReturnValue(unorderedMockDealsByType);
    mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
    mocked(getRegion).mockReturnValue({ slug: 'sydney' });
  });

  describe('when the status is pending', () => {
    beforeEach(() => {
      mocked(getStatus).mockReturnValue(STORE_STATUS.PENDING);
    });

    it('renders the skeleton', () => {
      const { findByTestId } = render();

      expect(findByTestId('skeleton')).toExist();
    });

    it('does NOT render the Header', () => {
      const { find } = render();

      expect(find('Header')).not.toExist();
    });

    it('does NOT render results', () => {
      const { find } = render();

      expect(find('PropertyCard')).not.toExist();
    });
  });

  describe('when the status is resolved', () => {
    beforeEach(() => {
      mocked(getStatus).mockReturnValue(STORE_STATUS.RESOLVED);
    });

    it('does NOT render the skeleton', () => {
      const { findByTestId } = render();

      expect(findByTestId('skeleton')).not.toExist();
    });
    // using mockDealsByType ensures that unorderedMockDealsByType is sorted correctly
    for (const [index, mockDealByType] of mockDealsByType.entries()) {
      const { dealType, results } = mockDealByType;

      it('renders each <Header /> in the correct order', () => {
        const { find } = render();

        expect(find('Header').at(index)).toHaveProp({
          name: dealType.name,
          icon: dealType.icon,
          description: dealType.description,
        });
      });

      it('renders all results', () => {
        const { findByTestId } = render();
        const wrapper = findByTestId(`deal-type-${dealType.code}`);
        expect(wrapper.find('PropertyCard')).toHaveLength(results.length);
      });

      describe('when dealType is all-deals', () => {
        beforeEach(() => {
          mocked(getDealType).mockReturnValue(DEFAULT_DEAL_TYPE);
        });

        it('renders the view more button', () => {
          const { findByTestId } = render();
          const wrapper = findByTestId(`deal-type-${dealType.code}`);
          expect(wrapper.find('ViewMoreButton')).toExist();
        });
      });

      describe('when dealType is not all-deals', () => {
        beforeEach(() => {
          mocked(getDealType).mockReturnValue(dealType);
        });

        it('renders the pagination component', () => {
          const { findByTestId } = render();
          const wrapper = findByTestId(`deal-type-${dealType.code}`);
          expect(wrapper.find('Pagination')).toExist();
        });
      });
    }

    it('renders the deals with the standard order', () => {
      const { find } = render();
      const bestDealsIndex = DEFAULT_DEAL_TYPE_ORDER.best_deals - 1;
      const luxeOffersIndex = DEFAULT_DEAL_TYPE_ORDER.luxury_offers - 1;
      expect(find('Header').at(bestDealsIndex)).toHaveProp({
        name: 'Best Deals',
      });
      expect(find('Header').at(luxeOffersIndex)).toHaveProp({
        name: 'Luxury Offers',
      });
    });
  });
});
