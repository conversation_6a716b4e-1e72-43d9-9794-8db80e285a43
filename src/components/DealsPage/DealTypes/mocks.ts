import { iconPin } from '@qga/roo-ui/assets';

export const mockDeal = {
  checkIn: '2022-05-29',
  checkOut: '2022-05-31',
  country: 'Australia',
  customerRating: {
    averageRating: 4,
    ratingImageUrl: 'www.tripadvisor.com/img/cdsi/img2/ratings/traveler/4.0-15969-4.png',
    reviewCount: 5788,
    source: 'trip_advisor',
  },
  dealType: 'classic-rewards',
  hasDiscount: true,
  id: '7340',
  imageAltTag: 'Exterior',
  imageSrc: 'https://staging-images-cdn.qantashotels.com/insecure/desktop_search/plain/media/60652fa9-1a86-45ae-9ba7-4c7b4b20ed54.png',
  isNonrefundable: false,
  offerId: '34761',
  offerType: 'standard',
  offerName: 'Test',
  pointsEarned: {
    maxQbrEarnPpd: 1,
    maxQffEarnPpd: 6,
    promotionMultiplier: 2,
    propertyPpd: 3,
    qbrPoints: {
      total: 593,
    },
    qffPoints: {
      base: 1779,
      bonus: 1779,
      qffPointsClub: 0,
      total: 3558,
    },
  },
  promotionName: 'Best deals',
  propertyName: 'Property Name',
  propertyUrl: '/properties/7340?checkIn=2022-05-29&checkOut=2022-05-31&adults=2&featuredOfferId=34761',
  rating: 4.5,
  ratingType: 'AAA',
  total: {
    amount: '35000',
    currency: 'PTS',
  },
  totalBeforeDiscount: {
    amount: '41000',
    currency: 'PTS',
  },
};

export const mockDealType = {
  code: 'code',
  description: 'some description',
  name: 'Bonus points',
  icon: iconPin,
  slug: 'slug',
};
