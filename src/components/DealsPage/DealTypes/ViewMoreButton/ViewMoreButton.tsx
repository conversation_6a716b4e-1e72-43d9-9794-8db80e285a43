import startCase from 'lodash/startCase';
import { useRouter } from 'next/router';
import React, { useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { useDataLayer } from 'hooks/useDataLayer';
import { setDealType } from 'store/deal/dealActions';
import { DealType } from 'types/deals';
import { Button } from './ViewMoreButton.style';

type Props = {
  dealType: DealType;
};

const ViewMoreButton = ({ dealType }: Props) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { emitInteractionEvent } = useDataLayer();

  const handleViewMoreClick = useCallback(() => {
    const { slug, ...query } = router.query;
    const nextPath = `/deals/${slug[0]}/${dealType.slug}`;

    emitInteractionEvent({
      type: 'View More Button',
      value: `${startCase(dealType.name)} Selected`,
    });

    dispatch(setDealType(dealType));
    router.push({ pathname: nextPath, query }, null, { shallow: true, scroll: true });
  }, [dealType, dispatch, emitInteractionEvent, router]);

  return (
    <Button mt={4} onClick={handleViewMoreClick} data-testid="button">
      View more deals
    </Button>
  );
};

export default ViewMoreButton;
