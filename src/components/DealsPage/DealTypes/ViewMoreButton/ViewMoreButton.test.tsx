import { useRouter } from 'next/router';
import React from 'react';
import { useDataLayer } from 'hooks/useDataLayer';
import { mocked, mountUtils } from 'test-utils';
import ViewMoreButton from './ViewMoreButton';
import { mockDealType } from '../mocks';
import { setDealType } from 'store/deal/dealActions';

jest.mock('hooks/useDataLayer');

const mockRouter = {
  push: jest.fn(),
  query: {
    page: 1,
    adults: 2,
    slug: ['sydney'],
  },
};

jest.mock('next/router', () => ({
  useRouter: jest.fn(() => mockRouter),
}));

const emitInteractionEvent = jest.fn();

const decorators = { store: true, theme: true };
const render = () => mountUtils(<ViewMoreButton dealType={mockDealType} />, { decorators });

describe('<DealsByType />', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    mocked(useRouter).mockReturnValue(mockRouter);
    mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
  });

  it('renders the link text', () => {
    const { findByTestId } = render();

    expect(findByTestId('button')).toHaveText('View more deals');
  });

  describe('when clicking the link', () => {
    it('emits an event to the data layer', () => {
      const { findByTestId } = render();
      findByTestId('button').simulate('click');

      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'View More Button',
        value: 'Bonus Points Selected',
      });
    });

    it('dispatches setDealType with the updated deal type', () => {
      const { findByTestId, decorators } = render();
      findByTestId('button').simulate('click');

      expect(decorators.store.dispatch).toHaveBeenCalledWith(setDealType(mockDealType));
    });

    it('updates the URL', () => {
      const { findByTestId } = render();
      findByTestId('button').simulate('click');

      expect(mockRouter.push).toHaveBeenCalledWith(
        {
          pathname: '/deals/sydney/slug',
          query: {
            page: 1,
            adults: 2,
          },
        },
        null,
        {
          scroll: true,
          shallow: true,
        },
      );
    });
  });
});
