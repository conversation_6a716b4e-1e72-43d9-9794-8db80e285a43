import React from 'react';
import { useSelector } from 'react-redux';
import { Flex } from '@qga/roo-ui/components';
import { getDealsByType, getDealType, getStatus } from 'store/deal/dealSelectors';
import { STORE_STATUS } from 'lib/enums/store';
import { DEFAULT_DEAL_TYPE, DEFAULT_DEAL_TYPE_ORDER, LIST_SEARCH_LIMIT } from 'config';
import DealTypeSkeleton from './DealTypeSkeleton';
import Header from './Header';
import PageBlock from 'components/PageBlock';
import { Grid } from './DealTypes.style';
import ViewMoreButton from './ViewMoreButton';
import Deal from './Deal';
import Pagination from 'components/Pagination';
import sortBy from 'lodash/sortBy';

const DealTypes = () => {
  const status = useSelector(getStatus);
  const isLoading = status === STORE_STATUS.PENDING;
  const dealsByType = useSelector(getDealsByType) || [];
  const dealType = useSelector(getDealType);
  const isAllDeals = dealType.code === DEFAULT_DEAL_TYPE.code;
  const sortedResults = sortBy(dealsByType, ({ dealType }) => DEFAULT_DEAL_TYPE_ORDER[dealType.code]);

  if (isLoading) {
    return (
      <PageBlock data-testid="skeleton">
        <DealTypeSkeleton />
        <DealTypeSkeleton />
        <DealTypeSkeleton />
      </PageBlock>
    );
  }

  return (
    <>
      {sortedResults.map(({ dealType, results, hasMore, totalCount }) => (
        <PageBlock key={`deal-type-${dealType.code}`} py={[4, 6]} px={[2, 2, 1, 0]} data-testid={`deal-type-${dealType.code}`}>
          <Flex alignItems="center" flexDirection="column">
            <Header name={dealType.name} icon={dealType.icon} description={dealType.description} />

            <Grid>
              {results.map((result, index) => (
                <Deal key={`result-${index}`} {...result} data-testid="deal" dealType={dealType.slug} />
              ))}
            </Grid>

            {isAllDeals && hasMore && <ViewMoreButton dealType={dealType} />}

            {!isAllDeals && (
              <Flex justifyContent={['center', 'space-between', 'space-between']} width="100%" py={8}>
                <Pagination total={totalCount} pageSize={LIST_SEARCH_LIMIT} />
              </Flex>
            )}
          </Flex>
        </PageBlock>
      ))}
    </>
  );
};

export default DealTypes;
