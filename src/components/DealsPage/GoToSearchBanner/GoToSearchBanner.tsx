import React from 'react';
import { useRouter } from 'next/router';
import { useSelector } from 'react-redux';
import { GO_TO_SEARCH_BANNER } from 'config';
import { Text, Image } from '@qga/roo-ui/components';
import { getRegionName } from 'store/deal/dealSelectors';
import { useDataLayer } from 'hooks/useDataLayer';
import { rem } from 'polished';
import { BannerCard, BannerContainer, BannerText, ButtonContainer, CtaButton } from './GoToSearchBanner.style';

const GoToSearchBanner = () => {
  const { emitInteractionEvent } = useDataLayer();
  const router = useRouter();
  const regionName = useSelector(getRegionName);

  const regionSearchUrl = '/search/list';
  const { icon, cta } = GO_TO_SEARCH_BANNER;

  const handleBannerClick = () => {
    emitInteractionEvent({ type: 'Go To Search Banner', value: 'Search Jetstar Hotels Selected' });

    router.push({
      pathname: regionSearchUrl,
      query: `location=${regionName}`,
    });
  };

  return (
    <BannerCard>
      <Image src={icon.src} alt={icon.alt} size={rem('48px')} mr={4} mb={[4, 0]} />

      <BannerContainer>
        <BannerText pb={[4, 4, 0]}>
          <Text fontSize={rem('18px')} fontWeight="bold" data-testid="heading-text">
            Want to see more options?
          </Text>
          <Text color="greys.steel" data-testid="body-text">
            Choose from 500,000+ hotels worldwide with an extensive range to suit any occasion or budget
          </Text>
        </BannerText>
        <ButtonContainer>
          <CtaButton onClick={handleBannerClick}>{cta.label}</CtaButton>
        </ButtonContainer>
      </BannerContainer>
    </BannerCard>
  );
};

export default GoToSearchBanner;
