import React from 'react';
import { mountUtils, mocked } from 'test-utils';
import { useRouter } from 'next/router';
import { useDataLayer } from 'hooks/useDataLayer';
import { getRegionName } from 'store/deal/dealSelectors';
import GoToSearchBanner from './GoToSearchBanner';

const mockRouter = {
  push: jest.fn(),
};
jest.mock('next/router', () => ({
  useRouter: jest.fn(() => mockRouter),
}));
jest.mock('hooks/useDataLayer');
jest.mock('store/deal/dealSelectors');
jest.mock('store/router/routerSelectors');
const emitInteractionEvent = jest.fn();

const headingText = 'Want to see more options?';
const bodyText = 'Choose from 500,000+ hotels worldwide with an extensive range to suit any occasion or budget';
const cta = { label: 'Search all hotels', type: 'text', iconName: 'arrowForward' };
const regionSearchUrl = '/search/list';
const regionName = 'Melbourne';
const query = `location=${regionName}`;

describe('GoToSearchBanner', () => {
  const render = () => mountUtils(<GoToSearchBanner />, { decorators: { store: true, theme: true } });

  beforeEach(() => {
    jest.clearAllMocks();
    mocked(getRegionName).mockReturnValue(regionName);
    mocked(useRouter).mockReturnValue(mockRouter);
    mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
  });

  it('has the correct heading text', () => {
    const { findByTestId } = render();

    expect(findByTestId('heading-text').text()).toEqual(headingText);
  });

  it('renders the correct body text', () => {
    const { findByTestId } = render();

    expect(findByTestId('body-text').text()).toEqual(bodyText);
  });

  it('has the correct text and icon on the button', () => {
    const { find } = render();

    expect(find('CtaButton').text()).toEqual(cta.label);
  });

  describe('when the button is clicked', () => {
    it('redirects the user to the search page for the correct region', () => {
      const { find } = render();
      find('button').simulate('click');

      expect(mockRouter.push).toHaveBeenCalledWith({ pathname: regionSearchUrl, query: query });
    });

    it('emits an interaction event', () => {
      const { find } = render();
      find('button').simulate('click');

      expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Go To Search Banner', value: 'Search Jetstar Hotels Selected' });
    });
  });
});
