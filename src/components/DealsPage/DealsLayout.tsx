import CampaignMessaging from 'components/CampaignMessaging';
import PageBlock from 'components/PageBlock';
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getHasValidQuery, getPageContent, getFooterLinksByCategory } from 'store/deal/dealSelectors';
import { updateQuery } from 'store/search/searchActions';
import DealsFetcher from './DealsFetcher';
import DealsHelmet from './DealsHelmet';
import DealsHeader from './DealsHeader';
import GoToSearchBanner from './GoToSearchBanner';
import ListOfRegions from 'components/Content/ListOfRegions';
import DealTypes from './DealTypes';
import PopularDestinationFooter from 'components/PopularDestinationFooter';
import { useMount } from 'react-use';
import { fetchCampaign } from 'store/campaign/campaignActions';
import useDealsGa4Event from 'hooks/useDealsGa4Event';

const DEFAULT_QUERY = {
  payWith: 'cash',
  page: 1,
};

const DealsLayout = () => {
  const dispatch = useDispatch();
  const isQueryValid = useSelector(getHasValidQuery);
  const { regionLinks = [] } = useSelector(getPageContent);
  const footerLinks = useSelector(getFooterLinksByCategory);

  useMount(() => {
    dispatch(fetchCampaign({}));
  });

  useEffect(() => {
    if (!isQueryValid) {
      dispatch(updateQuery(DEFAULT_QUERY));
    }
  }, [dispatch, isQueryValid]);

  useDealsGa4Event();

  return (
    <>
      <DealsHelmet />
      <DealsFetcher />
      <CampaignMessaging showDefaultMessage />
      <DealsHeader />

      <DealTypes />

      <PageBlock py={[4, 6]}>
        <GoToSearchBanner />
      </PageBlock>

      {regionLinks?.map((config, index) => (
        <PageBlock py={[4, 6]} key={`region-link-${index}`}>
          <ListOfRegions {...config} />
        </PageBlock>
      ))}

      {!!footerLinks?.length && (
        <PageBlock>
          <PopularDestinationFooter links={footerLinks} />
        </PageBlock>
      )}
    </>
  );
};

export default DealsLayout;
