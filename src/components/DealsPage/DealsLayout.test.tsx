import React from 'react';
import DealsLayout from './DealsLayout';
import { mocked, mountUtils } from 'test-utils';
import { getHasValidQuery, getPageContent } from 'store/deal/dealSelectors';
import { updateQuery } from 'store/search/searchActions';
import { fetchCampaign } from 'store/campaign/campaignActions';
import { Masonry } from 'react-masonry';

jest.mock('react-masonry', () => ({
  Masonry: jest.fn(),
}));

jest.mock('store/content/contentSelectors');
jest.mock('store/deal/dealSelectors');
jest.mock('hooks/useDealsGa4Event.js');

mountUtils.mockComponent('CampaignMessaging');
mountUtils.mockComponent('DealsFetcher');
mountUtils.mockComponent('DealsHeader');
mountUtils.mockComponent('DealsHelmet');
mountUtils.mockComponent('DealTypes');
mountUtils.mockComponent('GoToSearchBanner');
mountUtils.mockComponent('ListOfRegions');
mountUtils.mockComponent('PopularDestinationFooter');

const result = {
  regionLinks: [
    {
      title: 'Top destinations',
      links: [
        {
          id: '123',
          name: 'Sydney',
          fullName: 'Sydney, NSW, Australia',
        },
        {
          id: '234',
          name: 'Melbourne',
          fullName: 'Melbourne, VIC, Australia',
        },
        {
          id: '345',
          name: 'Newcastle',
          fullName: 'Newcastle, NSW, Australia',
        },
        {
          id: '456',
          name: 'Orange',
          fullName: 'Orange, NSW, Australia',
        },
      ],
      callToAction: {
        external: false,
        text: 'See all destinations',
        url: '/australia',
      },
    },
  ],
  navigation: [
    {
      name: 'Most Popular',
      regions: [
        {
          fullName: 'Sydney, NSW, Australia',
          id: '51789',
          name: 'Sydney',
          slug: 'sydney',
        },
        {
          fullName: 'Melbourne, VIC, Australia',
          id: '51770',
          name: 'Melbourne',
          slug: 'melbourne',
        },
      ],
    },
  ],
};

describe('<DealsLayout />', () => {
  const render = () => mountUtils(<DealsLayout />, { decorators: { helmet: true, router: true, store: true, theme: true } });

  beforeEach(() => {
    jest.clearAllMocks();
    mocked(getHasValidQuery).mockReturnValue(true);
    mocked(getPageContent).mockReturnValue(result);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    Masonry.mockImplementation(({ children }) => <div>{children}</div>);
  });

  describe('on mount', () => {
    it('fetches campaign messaging', () => {
      const { decorators } = render();

      expect(decorators.store.dispatch).toHaveBeenCalledWith(fetchCampaign({}));
    });
  });

  it('renders the DealsHelmet component', () => {
    const { find } = render();
    expect(find('DealsHelmet')).toExist();
  });

  it('renders the DealsFetcher component', () => {
    const { find } = render();
    expect(find('DealsFetcher')).toExist();
  });

  it('renders the CampaignMessaging component', () => {
    const { find } = render();
    expect(find('CampaignMessaging')).toExist();
  });

  it('renders the DealsHeader component', () => {
    const { find } = render();
    expect(find('DealsHeader')).toExist();
  });

  it('renders the DealTypes component', () => {
    const { find } = render();
    expect(find('DealTypes')).toExist();
  });

  it('renders the GoToSearchBanner component', () => {
    const { find } = render();
    expect(find('GoToSearchBanner')).toExist();
  });

  it('renders the ListOfRegions component with the correct props', () => {
    const { find } = render();

    expect(find('ListOfRegions')).toHaveProp({ ...result.regionLinks[0] });
  });

  describe('with invalid query params', () => {
    it('updates the query', () => {
      mocked(getHasValidQuery).mockReturnValue(false);
      const { decorators } = render();
      expect(decorators.store.dispatch).toHaveBeenCalledWith(updateQuery({ page: 1, payWith: 'cash' }));
    });
  });
});
