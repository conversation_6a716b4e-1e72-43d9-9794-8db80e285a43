import React from 'react';
import { mountUtils } from 'test-utils';
import PointsPerDollar from 'components/PointsPerDollar';

const render = (props) => mountUtils(<PointsPerDollar {...props} />);

describe('<PointsPerDollar />', () => {
  it('displays the default points per dollar message', () => {
    const { find } = render({ pointsPerDollar: 3 });
    expect(find('Text[data-testid="points-per-dollar"]').text()).toEqual('3 PTS');
  });
});
