import React from 'react';
import PropTypes from 'prop-types';
import { Box, Text } from '@qga/roo-ui/components';

const PointsPerDollar = ({ pointsPerDollar, fontSize, color }) => {
  return (
    <Box justifyContent="flex-end" lineHeight="tight">
      <Text data-testid="points-per-dollar" color={color} fontSize={fontSize} fontWeight="bold" textAlign="end">
        {pointsPerDollar} PTS
      </Text>
      <Text color={color} fontSize={fontSize} textAlign="end">
        {' '}
        per $1 spent^
      </Text>
    </Box>
  );
};

PointsPerDollar.propTypes = {
  pointsPerDollar: PropTypes.number.isRequired,
  fontSize: Text.propTypes.fontSize,
  strikeThroughDefaultPoints: PropTypes.bool,
  color: PropTypes.string,
};

PointsPerDollar.defaultProps = {
  fontSize: 'base',
  strikeThroughDefaultPoints: false,
  color: 'greys.charcoal',
};

export default PointsPerDollar;
