# Your pull request title ✌️  

Add a short description.

## Screenshots / Video / Demos (optional)

<!-- Visually showcase changes -->

## How Has This Been Tested?

<!-- Mandatory: provide clear testing details -->

- Unit test coverage updated/added
- Cypress end-to-end test included  
- Manual validation completed

> 💡 For any user flow changes, include Cypress tests validating functionality and key edge cases.

## Checklist:

<!-- Go over all the points, and put an `x` in all the boxes that apply. -->

- [ ] This change has been fully tested
- [ ] Documentation updated 
- [ ] Potential risks addressed
- [ ] Accessibility considered
- [ ] This change has gone through a walkthrough (with stakeholders)

<!-- Please clean up and update/refactor any code you touch -->

- [ ] Code has been updated/refactored

## PR Guidance

**Testing:** Provide clear details on validation approach - unit vs e2e vs manual.

**Risks:** Call out any risks uncovered and mitigations.

**Accessibility:** Show accessibility was considered.

**Code Quality:** Tidy up code touched to improve readability, reuse, etc.

💡 Describe the why over the what in commit message and PR details.
