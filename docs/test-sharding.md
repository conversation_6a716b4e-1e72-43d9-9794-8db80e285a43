# Jest Test Sharding

This project uses Jest test sharding to improve CI performance by running tests in parallel across multiple workers.

## Overview

- **Non-server tests**: Split into 4 shards
- **Server tests**: Split into 2 shards
- **CI Concurrency**: Matches the number of shards for optimal parallelism

## Configuration

### Jest Configuration

The sharding is implemented in the Jest configuration files:

- `tests/jest/jest.config.browser.ts` - Browser/client tests with 4-shard support
- `tests/jest/jest.config.server.ts` - Server tests with 2-shard support

Sharding is controlled by environment variables:
- `JEST_SHARD_INDEX`: Zero-based index of the current shard (0, 1, 2, 3 for browser tests)
- `JEST_TOTAL_SHARDS`: Total number of shards (4 for browser, 2 for server)

### Package.json Scripts

New scripts have been added for running sharded tests:

```bash
# Run a specific shard of browser tests
yarn test:shard

# Run a specific shard of server tests  
yarn test:server:shard
```

These scripts expect `SHARD_INDEX` and `TOTAL_SHARDS` environment variables.

## CI Integration (Buildkite)

The Buildkite pipeline has been updated to run tests in parallel:

### Browser Tests (4 shards)
- `:jest: Test Shard 1/4` - `SHARD_INDEX=0 TOTAL_SHARDS=4`
- `:jest: Test Shard 2/4` - `SHARD_INDEX=1 TOTAL_SHARDS=4`
- `:jest: Test Shard 3/4` - `SHARD_INDEX=2 TOTAL_SHARDS=4`
- `:jest: Test Shard 4/4` - `SHARD_INDEX=3 TOTAL_SHARDS=4`

### Server Tests (2 shards)
- `:jest: Test Server Shard 1/2` - `SHARD_INDEX=0 TOTAL_SHARDS=2`
- `:jest: Test Server Shard 2/2` - `SHARD_INDEX=1 TOTAL_SHARDS=2`

Each shard runs with concurrency matching the total number of shards to maximise parallelism.

## Local Development

### Running Individual Shards

```bash
# Run browser test shard 1 of 4
SHARD_INDEX=0 TOTAL_SHARDS=4 yarn test:shard

# Run server test shard 2 of 2
SHARD_INDEX=1 TOTAL_SHARDS=2 yarn test:server:shard
```

### Using the Test Shards Script

A utility script is provided for easier local testing:

```bash
# Run all browser test shards sequentially
./scripts/test-shards.sh browser

# Run all server test shards sequentially
./scripts/test-shards.sh server

# Run a specific browser test shard
./scripts/test-shards.sh browser 0 4

# Run a specific server test shard
./scripts/test-shards.sh server 1 2
```

### Running All Tests (Non-sharded)

The original test commands still work for running all tests:

```bash
# Run all browser tests
yarn test

# Run all server tests
yarn test:server

# Watch mode for browser tests
yarn test:watch
```

## How Sharding Works

Jest's built-in sharding feature distributes tests across shards using a deterministic algorithm based on test file paths. This ensures:

1. **Consistent distribution**: The same files always go to the same shard
2. **Balanced load**: Tests are distributed as evenly as possible
3. **No overlap**: Each test file runs in exactly one shard
4. **No gaps**: All test files are covered across all shards

## Performance Benefits

With ~848 browser tests and ~20 server tests:

- **Before**: Browser tests ran with concurrency 2, server tests ran sequentially
- **After**: Browser tests run across 4 parallel shards, server tests across 2 parallel shards
- **Expected improvement**: Approximately 2x faster test execution in CI

## Troubleshooting

### Shard Imbalance

If one shard takes significantly longer than others, it may indicate:
1. A few test files with many slow tests
2. Tests with heavy setup/teardown
3. Tests that don't clean up properly

Monitor shard execution times in CI and consider refactoring slow tests.

### Missing Tests

If tests seem to be missing from CI runs:
1. Verify all shards are running successfully
2. Check that `JEST_SHARD_INDEX` and `JEST_TOTAL_SHARDS` are set correctly
3. Ensure test file patterns match the Jest configuration

### Local vs CI Differences

If tests pass locally but fail in CI shards:
1. Run the specific shard locally using the same environment variables
2. Check for test interdependencies or shared state issues
3. Verify that tests are properly isolated
