import baseConfig from './jest.config.base';

process.env.BUILD_TARGET = 'client';

const shardIndex = process.env.JEST_SHARD_INDEX ? parseInt(process.env.JEST_SHARD_INDEX, 10) : undefined;
const totalShards = process.env.JEST_TOTAL_SHARDS ? parseInt(process.env.JEST_TOTAL_SHARDS, 10) : undefined;

export default {
  ...baseConfig,
  testMatch: ['<rootDir>/src/**/*.test.js', '<rootDir>/src/**/*.test.ts', '<rootDir>/src/**/*.test.tsx'],
  testPathIgnorePatterns: ['/src/server/'],
  testEnvironment: 'jsdom',
  transformIgnorePatterns: ['/node_modules/(?!connected-next-router|preact).+\\.js$'],
  ...(shardIndex !== undefined && totalShards !== undefined && {
    testNamePattern: `.*`,
    testPathPattern: `.*`,
    // Use Jest's built-in sharding capability
    shard: `${shardIndex + 1}/${totalShards}`,
  }),
};
