import baseConfig from './jest.config.base';

process.env.BUILD_TARGET = 'server';

const shardIndex = process.env.JEST_SHARD_INDEX ? parseInt(process.env.JEST_SHARD_INDEX, 10) : undefined;
const totalShards = process.env.JEST_TOTAL_SHARDS ? parseInt(process.env.JEST_TOTAL_SHARDS, 10) : undefined;

export default {
  ...baseConfig,
  testMatch: ['<rootDir>/src/server/**/*.test.js', '<rootDir>/src/server/**/*.test.ts', '<rootDir>/src/server/**/*.test.tsx'],
  testEnvironment: 'node',
  ...(shardIndex !== undefined && totalShards !== undefined && {
    testNamePattern: `.*`,
    testPathPattern: `.*`,
    // Use Je<PERSON>'s built-in sharding capability
    shard: `${shardIndex + 1}/${totalShards}`,
  }),
};
