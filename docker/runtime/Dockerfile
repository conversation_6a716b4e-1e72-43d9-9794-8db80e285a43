FROM 730011650125.dkr.ecr.ap-southeast-2.amazonaws.com/base/node:20.9.0-runtime-144

USER root

# To improve memory performance with sharp - https://sharp.pixelplumbing.com/install#linux-memory-allocator
RUN apt-get update && apt-get install -y libjemalloc2 && rm -rf /var/lib/apt/lists/*
ENV LD_PRELOAD=/usr/lib/x86_64-linux-gnu/libjemalloc.so.2

USER hooroo

ENV APPLICATION=jetstar-hotels-ui
ENV NODE_ENV=production

ENV PORT 8080
EXPOSE 8080

ARG BUILD_ID
ENV BUILD_ID=${BUILD_ID}

ARG BASE_PATH
ENV BASE_PATH=${BASE_PATH}

ARG ENVIRONMENT
ENV ENVIRONMENT=${ENVIRONMENT}

# To improve image optimization performance and resolve https://nextjs.org/docs/messages/sharp-missing-in-production
RUN yarn global add sharp
ENV NEXT_SHARP_PATH='/home/<USER>/.config/yarn/global/node_modules/sharp'


COPY --chown=hooroo:hooroo package.json yarn.lock ./

RUN yarn install --frozen-lockfile --ignore-scripts

COPY --chown=hooroo:hooroo . .

#ENTRYPOINT [ "./docker/runtime/entrypoint.sh" ]

CMD [ "./docker/runtime/start.sh" ]
