FROM 730011650125.dkr.ecr.ap-southeast-2.amazonaws.com/base/node:20.9.0-builder-144

USER root
RUN apt-get update && apt-get install -y procps jq zip wget

USER hooroo

# Install app dependencies
COPY --chown=hooroo:hooroo package.json yarn.lock ./

RUN CYPRESS_INSTALL_BINARY=0 yarn install

# Bundle app source
COPY --chown=hooroo:hooroo . .

# Set build number, fallback to dev if not supplied
ARG BUILD_NUMBER=dev
ENV BUILD_NUMBER=${BUILD_NUMBER} \
    NODE_ENV=production \
    PORT=8080

EXPOSE 8080
